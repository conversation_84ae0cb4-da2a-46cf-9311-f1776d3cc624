#mdLapHoSo_us .modal-dialog {
    min-width: 1000px;
}

#LoaiHoSo_us .form-selectgroup-label {
    font-size: 12px;
}

#LoaiHoSo_us .form-selectgroup-label {
    padding-right: 90px;
}

.line {
    flex-grow: 2;
    border-bottom: 1px solid #dadcde;
    margin: 0 10px;
    border-color: #07a607;
}

.label-text {
    color: #07a607;
}

.form-check .form-check-input {
    margin-left: unset !important;
}

.steps-vertical .step-item:before {
    top: var(--tblr-steps-dot-offset);
    left: -8px !important;
    transform: translate(0, 0);
    width: 35px;
    height: 35px;
}

#gridDonVi .tabulator-footer {
    display: none !important;
}

#gridDonVi .tabulator-data-tree-control {
    display: none !important;
}

.modal {
    z-index: 1202 !important;
}

/* Child modals higher z-index */
#mdThemMoiHocSinh.modal,
#mdThemMoiHocSinh.modal .modal-dialog,
#mdChonHocSinh.modal,
#mdChonHocSinh.modal .modal-dialog {
    z-index: 1500 !important;
}

.modal-backdrop {
    z-index: 1201 !important;
}

.full-screen-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5); /* dark semi-transparent overlay */
    z-index: 1200; /* above other content */
    overflow-y: auto; /* scroll if content too tall */
    display: none; /* hidden by default */
}

.panel-content {
    background: white;
    border-radius: 0;
    padding-inline: 1rem;
    height: auto;
    overflow-y: auto;
}

#customSelect {
    position: relative; /* Make sure parent is positioned */
}

#customDropdown.show {
    display: block;
}

#mdXemDinhKem {
    z-index: 2000 !important;
}

/* make all step‐items non‐active text & counter orange */
.steps.steps-counter.steps-vertical .step-item:not(.active) {
    /* change the text */
    color: var(--main-color) !important;
}

/* and if you have a circle indicator, e.g. .step-counter */
.steps.steps-counter.steps-vertical .step-item:not(.active) .step-counter {
    background-color: var(--main-color) !important;
    border-color: var(--main-color) !important;
}

.steps.steps-counter.steps-vertical .step-item .step-title {
    font-size: 15;
    font-weight: 700 !important; /* semi-bold */
}
.steps.steps-counter.steps-vertical .step-item .step-desc {
    font-size: 14;
    font-weight: 400;
}
.steps.steps-counter.steps-vertical .step-item.active .step-title {
    color: rgba(var(--tblr-primary-rgb), 1) !important;
}
/* leave the desc as-is, or give it a lighter weight/color if you like */
.steps.steps-counter.steps-vertical .step-item.active .step-desc {
    color: rgba(var(--tblr-primary-rgb), 0.8) !important;
}

.steps.steps-counter.steps-vertical .step-desc {
    text-align: justify;
    text-justify: inter-word;
}

.nts-color-them {
}

.nts-color-them:hover {
}

/* Button lưu (nts-color-luu) */
.nts-color-luu {
}

.nts-color-luu:hover {
}

/* Button đóng (nts-color-dong) */
.nts-color-dong {
}

.nts-color-dong:hover {
}

.nts-color-menu-bg {
}

/* Class cho màu nền */
.nts-bg-primary {
}

.nts-bg-primary:hover {
}

#KhungTimKiem .fw-bold {
    color: #f76707 !important;
}

.list-item {
    border-radius: 6px;
    box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
}

#Grid1.tabulator {
    border: 1px solid #ffffff00 !important;
    background-color: #ffffff00 !important;
}

#Grid1.tabulator .tabulator-tableholder {
    background-color: #ffffff00 !important;
}

#Grid1.tabulator .tabulator-tableholder .tabulator-table {
    background-color: #ffffff00 !important;
}

#Grid1 .tabulator-row.tabulator-selected {
    background-color: #ffffff00 !important;
}

.TieuDeLabel span {
    font-weight: bold;
}

#Grid1 .tabulator-row {
    background-color: #ffffff00 !important;
    border: unset !important;
}

#Grid1 .tabulator-row > .tabulator-cell {
    border-right: 0px solid #ffffff00 !important;
    padding: 2px 4px 2px 2px !important;
}

#Grid1.tabulator .tabulator-footer {
    border: 1px solid #dedede !important;
    padding: 0px !important;
}

.tabulator-row.tabulator-selectable:hover > .tabulator-cell .show-or-hide {
    display: block !important;
    background: white;
    padding: 5px 13px;
}

.tabulator .tabulator-cell.tabulator-editable {
    padding: 7px 7px !important;
    outline: 1px solid #ddd;
    outline-offset: -6px; /* pull the outline 6px _inside_ the box */
    transition: box-shadow 0.2s;
}

/* hover state */
.tabulator .tabulator-cell.tabulator-editable:hover {
    box-shadow: inset 0 0 0 1px #bbb;
}

/* focus state (when the editor is open) */
.tabulator .tabulator-cell.tabulator-editable:focus-within {
    box-shadow: inset 0 0 0 1px #999;
}

.nav-link.active {
    color: var(--main-color) !important;
    background-color: transparent !important;
    /* border-bottom: 2px solid var(--main-color) !important; */
}

.nav-link:not(.active) {
    color: var(--tblr-info) !important;
}

.stats-bar {
    display: inline-block;
    width: 100px;
    padding: 2px auto;
    border-radius: 0.25rem;
    font-weight: 600;
    text-align: center;
    color: #fff;
}
.stats-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}
.stats-item:last-child {
    margin-bottom: 0;
}
.stats-label {
    margin-left: 0.75rem;
    white-space: nowrap;
}

#mdXemChiTietQD {
    z-index: 1202;
}

.fs-big {
    /* allow breaking long words */
    white-space: normal; /* allow wrapping */
    overflow-wrap: normal; /* don't break inside words */
    word-wrap: normal; /* legacy fallback */
    word-break: normal; /* default break behavior */
}
.tabulator-cell.wrap-cell {
    white-space: normal !important;
}

.wrap-cell-content {
    white-space: normal;
    word-wrap: normal; /* legacy fallback */
}

#ocrImagePreview {
    position: relative;
    width: 100%;
    height: 100%;
    background: #f5f8fa;
    /* resize: both;  */
    overflow: auto; /* scrollbars when zoomed */
}

#ocrImagePreview img {
    display: block;
    width: 100;
    height: auto;
    width: 100%; /* fill the container’s width */
    height: auto; /* preserve aspect ratio */
    user-select: none; /* optional: prevent accidental text selection */
}

#ocrImagePreview .box {
    position: absolute;
    border: 2px solid #dc3545;
    background: rgba(220, 53, 69, 0.15);
    cursor: move;
    box-sizing: border-box;
    border-radius: 0.25rem;
}

#ocrImagePreview .resize-handle {
    position: absolute;
    width: 12px;
    height: 12px;
    background: #dc3545;
    right: 0;
    bottom: 0;
    cursor: se-resize;
    border-radius: 2px;
}

/* dashed preview while drawing */
.box.drawing {
    position: absolute;
    border: 2px dashed #007bff;
    pointer-events: none;
}

#ocrToolbar .btn {
    padding: 5px 10px;
}

#col-1-ocr {
    overflow: scroll;
}

#btnTrichXuatOCR svg {
    flex-shrink: 0; /* prevent shrinking */
    width: 20px !important; /* enforce fixed size */
    height: 20px !important;
}

.ocr-image-container.selected {
    outline: 3px solid #0d6efd; /* blue highlight */
    box-shadow: 0 0 10px #0d6efd;
    /* or any style that clearly shows selection */
}

.ocr-image-container {
    pointer-events: auto;
    transform-origin: top left;
}
/* but its children (the image and boxes) will */
.ocr-image-container img,
.ocr-image-container .box {
    pointer-events: auto;
}

#fieldSelectorContainer {
    position: fixed; /* you already have this */
    z-index: 10000; /* higher than .ocr-image-container */
    pointer-events: auto; /* ensure it’s clickable */
}
.paginate {
    position: relative;
    margin: 0 1rem;
    width: 30px;
    height: 30px;
    background: transparent;
    border: 0;
    cursor: pointer;
    filter: drop-shadow(0 1px 0 rgba(0, 0, 0, 0.2));
}
.paginate i {
    position: absolute;
    top: 50%;
    left: 0;
    width: 30px;
    height: 3px;
    border-radius: 1.5px;
    background: #375a7f; /* or whatever color you like */
    transition: all 0.15s ease;
}
.paginate.left i {
    transform-origin: 0% 50%;
}
.paginate.left i:first-child {
    transform: translate(0, -1px) rotate(40deg);
}
.paginate.left i:last-child {
    transform: translate(0, 1px) rotate(-40deg);
}
.paginate.left:hover i:first-child {
    transform: translate(0, -1px) rotate(30deg);
}
.paginate.left:hover i:last-child {
    transform: translate(0, 1px) rotate(-30deg);
}
.paginate.left:active i:first-child {
    transform: translate(1px, -1px) rotate(25deg);
}
.paginate.left:active i:last-child {
    transform: translate(1px, 1px) rotate(-25deg);
}
.paginate.left[data-state="disabled"] i {
    transform: translate(-5px, 0) rotate(0deg);
}
.paginate.right i {
    transform-origin: 100% 50%;
}
.paginate.right i:first-child {
    transform: translate(0, 1px) rotate(40deg);
}
.paginate.right i:last-child {
    transform: translate(0, -1px) rotate(-40deg);
}
.paginate.right:hover i:first-child {
    transform: translate(0, 1px) rotate(30deg);
}
.paginate.right:hover i:last-child {
    transform: translate(0, -1px) rotate(-30deg);
}
.paginate.right:active i:first-child {
    transform: translate(1px, 1px) rotate(25deg);
}
.paginate.right:active i:last-child {
    transform: translate(1px, -1px) rotate(-25deg);
}
.paginate.right[data-state="disabled"] i {
    transform: translate(5px, 0) rotate(0deg);
}
.paginate[data-state="disabled"] {
    opacity: 0.3;
    cursor: default;
}

.counter {
    font-size: 1.5rem !important;
    font-family: Helvetica, sans-serif;
    color: #375a7f;
    text-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
    user-select: none;
}

#dinhKemOCR_list .file-preview {
    cursor: pointer;
}
