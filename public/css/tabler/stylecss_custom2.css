
:root {
    /* <PERSON><PERSON><PERSON> nền chính */
    --nts-bg-primary: #FFFFFF;
    --nts-bg-secondary: #F76707;
    --nts-bg-danger: #FF0000;
    --nts-bg-blue: #4265B6;
    --nts-bg-green: #146654;
    --nts-bg-niendo: #ecf1f8;
    --nts-bg-white: #ffffff;
    --nts-bg-maunen: #F4F5F5;

    /* <PERSON><PERSON><PERSON> chữ chính */
    --nts-text-secondary: #F76707;
    --nts-text-primary: #ffffff;
    --nts-text-black: #000000;
    --nts-text-danger: #FF0000;
    --nts-text-blue: #4265B6;
    --nts-text-green: #146654;

    /* Màu hover */
    --nts-hover-primary: #FA703F52;
    --nts-hover-secondary: #f57c00;
    --nts-hover-danger: #FF0000;
    --nts-hover-blue: #4b72cf;
    --nts-hover-green: #1a7561;

    /* Border (tu<PERSON> chọn) */
    --nts-border-primary: #DADFE5;
    --nts-border-black: #000000;
    --nts-border-radius: 2px;
}

/* Start Dark Mode */

/* ===== DARK THEME ===== */
body.dark-mode{
  /* NỀN CHUNG */
  --nts-bg-maunen:       #13151c;   /* nền body */
  --nts-bg-primary:      #1b1e27;   /* card, input, bảng */
  --nts-bg-secondary:    #ff914d;   /* ACCENT cam */
  --nts-bg-blue:         #4fc3f7;   /* accent phụ */
  --nts-bg-danger:       #ff6363;
  --nts-bg-green:        #4ad29b;
  --nts-bg-niendo:       #242735;
  --nts-bg-white:        #1b1e27;   /* tránh trắng chói */

  /* CHỮ */
  --nts-text-primary:    #f1f3f6;   /* chữ trắng dịu */
  --nts-text-black:      #c8ced9;   /* chữ chính */
  --nts-text-secondary:  var(--nts-bg-secondary);
  --nts-text-blue:       var(--nts-bg-blue);
  --nts-text-danger:     var(--nts-bg-danger);
  --nts-text-green:      var(--nts-bg-green);

  /* HOVER (nền nhấn nhá) */
  --nts-hover-primary:   rgba(255,145,77,.12); /* cam nhạt */
  --nts-hover-secondary: var(--nts-bg-secondary);
  --nts-hover-blue:      rgba(79,195,247,.15);
  --nts-hover-green:     rgba(74,210,155,.15);
  --nts-hover-danger:    rgba(255,99,99,.15);

  /* BORDER */
  --nts-border-primary:  #353a48;
  --nts-border-black:    #444c56;
}

body{
  background: var(--nts-bg-maunen) !important;
  color:       var(--nts-text-black) !important;
}
body.dark-mode .card,
body.dark-mode .form-control,
body.dark-mode .select2-selection,
body.dark-mode .select2-results,
body.dark-mode .select2-dropdown,
body.dark-mode .modal-content,
body.dark-mode .tabulator-tableholder,
body.dark-mode .tabulator-row,
body.dark-mode .modal-footer,
body.dark-mode .nav.nav-tabs.card-header-tabs,
body.dark-mode .KhungVien{
  background: var(--nts-bg-primary) !important;
  color:      var(--nts-text-black) !important;
  border-color: var(--nts-border-primary) !important;
}

/* header */
body.dark-mode .tabulator .tabulator-header,
body.dark-mode .tabulator .tabulator-header .tabulator-col{
  background: #242735 !important;  /* đậm hơn bg-primary */
  color: var(--nts-text-primary) !important;
  border-color: var(--nts-border-primary) !important;
}

/* footer */
body.dark-mode .tabulator .tabulator-footer{
  background: #1d1f29 !important;
  color: var(--nts-text-primary) !important;
}
body.dark-mode .tabulator .tabulator-footer .tabulator-page,
body.dark-mode .tabulator .tabulator-footer .tabulator-page.active{
  background: transparent !important;
  color: var(--nts-text-blue) !important;
}
body.dark-mode .tabulator .tabulator-footer .tabulator-page.active{
  background: var(--nts-hover-blue) !important;
}

body.dark-mode .lblTenVietTatPM_LeftBar,
body.dark-mode #hovaten_layout,
body.dark-mode #tendonvi_layout_1
{
    color: var(--nts-text-primary) !important;
}

/* header modal */
body.dark-mode .modal .modal-header{
  background: var(--nts-bg-niendo) !important;
  color: var(--nts-text-primary) !important;
}

/* navbar top */
body.dark-mode .navbar{
  background: var(--nts-bg-niendo) !important;
  color: var(--nts-text-primary) !important;
  box-shadow: 0 1px 4px rgba(0,0,0,.4);
}

/* body.dark-mode #sidebar-menu .nav-link:hover {
    background: linear-gradient(...);
} */

/* End Dark Mode */

/* Button thêm (nts-color-them) */
.nts-color-them {
    background-color: var(--nts-bg-secondary) !important;
    color: var(--nts-text-primary) !important;
    /* border: 1px solid var(--nts-bg-secondary) !important; */
}

.nts-color-them:hover {
    background-color: var(--nts-hover-secondary) !important;
    color: var(--nts-text-primary) !important;
}

/* Button lưu (nts-color-luu) */
.nts-color-luu {
    background-color: var(--nts-bg-blue) !important;
    color: var(--nts-text-primary) !important;
    /* border: 1px solid var(--nts-border-primary); */
}

.nts-color-luu:hover {
    background-color: var(--nts-hover-blue) !important;
    color: var(--nts-text-primary) !important;
}

/* Button lưu (nts-color-luu) */
.nts-color-timkiem {
    background-color: var(--nts-bg-secondary) !important;
    color: var(--nts-text-primary) !important;
    /* border: 1px solid var(--nts-border-primary); */
}

.nts-color-timkiem:hover {
    background-color: var(--nts-hover-secondary) !important;
    color: var(--nts-text-primary) !important;
}

/* Button đóng (nts-color-dong) */
.nts-color-dong {
    background-color: var(--nts-bg-white) !important;
    color: var(--nts-text-danger) !important;
    border: 1px solid var(--nts-bg-danger) !important;
}

.nts-color-dong:hover {
    background-color: var(--nts-hover-danger) !important;
    color: var(--nts-text-primary) !important;
}

.nts-color-menu-bg {
    background-color: var(--nts-bg-primary) !important;
    color: var(--nts-text-primary) !important;

}

/* Class cho màu nền */
.nts-bg-primary {
    background-color: var(--nts-bg-primary) !important;
    color: var(--nts-text-primary) !important;

}

.nts-bg-header-primary{
    background: var(--nts-bg-secondary) !important;
    color: var(--nts-text-primary) !important;
}
.nts-bg-header-primary {
    background: var(--nts-bg-secondary) !important;
    color:  var(--nts-text-primary) !important;
}


.nts-bg-secondary {
    background-color: var(--nts-bg-secondary) !important;
    color: var(--nts-text-primary) !important;

}

/* Default color for all .nts-color-menu-text and its children */
.nts-color-menu-text,
.nts-color-menu-text > span,
.nts-color-menu-text > span > span > i,
.nts-color-menu-text > span > i,
.nts-color-menu-text > i {
    color: var(--nts-text-black) !important;
}

.nts-color-menu-text.active,
.nts-color-menu-text.active > span,
.nts-color-menu-text.active > span > span > i,
.nts-color-menu-text.active > span > i,
.nts-color-menu-text.active > i {
    color: var(--nts-hover-secondary) !important;
}

.nts-color-menu-text:not(.active) > span {
    color: var(--nts-text-black) !important;
}
.navbar-vertical.navbar-expand-lg .navbar-brand {
    padding: 0.5rem calc(calc(var(--tblr-page-padding) * 2) / 2) 0px calc(calc(var(--tblr-page-padding) * 2) / 2) !important;
    justify-content: left !important;
}

.nts-leftbar-menu{
    text-decoration: none;
    text-align: left;
    padding: 4px 4px 4px 8px;
    width: 100%;
}

.nts-leftbar-menu.active{
    text-decoration: none;
    text-align: left;
    background: var(--nts-hover-primary) !important ;
    padding: 15px;
    width: 100%;
    border-radius: 10px;
    color: #ff7a00 !important;
    font-weight: bold;
}

header.navbar.navbar-header.navbar-expand-md.d-none.d-lg-flex.d-print-none {
z-index: 10000;
}
.navbar-menu {
    overflow: auto;         /* Cho phép cuộn */
    scrollbar-width: none;  /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
}

.navbar-menu::-webkit-scrollbar {
    display: none;
}

.sidebar-collapse {
    border-bottom: 1px solid #e0e0e0;
    background-color: var(--nts-bg-secondary);
    text-align: center;
    padding: 3px 0;
    position: relative;
    margin-top: 30px;
}
.sidebar-collapse:before {
    content: "";
    display: inline-block;
    height: 0;
    border-top: 1px solid #e0e0e0;
    position: absolute;
    left: 15px;
    right: 15px;
    top: 13px;
}

.sidebar-collapse > [class*="icon-"] {
    display: inline-block;
    cursor: pointer;
    font-size: 14px;
    color: var(--nts-bg-secondary);
    border: 1px solid #bbb;
    padding: 0 5px;
    line-height: 18px;
    border-radius: 16px;
    background-color: #fff;
    position: relative;
}

.navbar-vertical.navbar-expand-lg > [class^="container"] {
    min-height: auto !important;
}

aside.navbar.navbar-leftbar.navbar-vertical.navbar-expand-lg.navbar-menu {
    height: fit-content !important;
    padding-bottom: 75px;
    border-bottom: 1px solid #dadada !important;
}

.fa-angle-double-left:before
{
    font-weight: 600 !important;
}

.sidebar-collapse > [class*="icon-"]{
    border: none !important;
}

.classFooter-hr {
    border-bottom: 1.5px solid #e6e6e6;
    margin: 10px 10px 0 2px;
    width: 98%;
}

.classHead-hr {
    border-bottom: 1.5px solid #e6e6e6;
    margin: 4px 10px 10px 2px;
    width: 98%;
}

span.nav-link-title.ms-2 {
    margin-left: 5px !important;
}


/* css menu con cấp 1 */
/* Giao diện dropdown cấp 1 nằm bên phải */
.navbar-menu-nts {
    position: relative;
    overflow: visible !important; /* Cho phép ::before tràn ra ngoài */
    z-index: 1; /* Đảm bảo hiển thị trên nền */
}

.navbar-menu-nts::before {
    content: "";
    position: absolute;
    top: 0;
    left: 100%; /* Ngay sát phải của menu cha */
    width: 230px;
    height: 100%;
    background: var(--nts-bg-primary);
    box-shadow: 4px 2px 12px rgba(0, 0, 0, 0.1);
    z-index: 10000;
    display: none;
}


.flyout-menu.full-height-from-header {
    position: fixed;
    top: 45px; /* Chiều cao header, chỉnh theo header thực tế */
    left: 180px; /* Bằng width của menu cha bên trái */
    width: 220px;
    height: calc(100vh - 46px); /* Toàn bộ chiều cao trừ header */
    background: var(--nts-bg-primary);
    box-shadow: 4px 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    display: none;
    overflow-y: auto;
    padding-top: 4px;
    transition: all 0.3s ease-in-out;
}

.nav-item:hover .flyout-menu {
    display: block;
}

/* Title trong menu con */
.flyout-title {
    font-size: 16px;
    padding: 10px 16px;
    color: var(--nts-hover-secondary);
    text-transform: uppercase;
}

/* Danh sách item */
.flyout-list {
    display: flex;
    flex-direction: column;
}

.flyout-item {
    padding: 10px 16px;
    text-decoration: none;
    color: var(--nts-hover-secondary);
    display: flex;
    align-items: center;
    transition: background-color 0.3s ease;
    font-size: 15px;
    font-weight: normal;
}

.flyout-item:hover {
    background-color: var(--nts-hover-primary);
    color: var(--nts-hover-secondary);
    font-weight: bold;
}

.flyout-menu::-webkit-scrollbar {
    width: 6px;
}

.flyout-menu::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 10px;
}

.flyout-item.has-submenu {
    position: relative;
    cursor: pointer;
}

.flyout-item.has-submenu > .fa-chevron-right {
    margin-left: auto;
}

/* Chinh menu bi dung css */
 .dropdown-menu-leftbar {
    position: static !important;
    float: none !important;
    background-color: transparent;
    border: none;
    box-shadow: none;
}

.dropdown-menu-leftbar .dropdown-item {
    padding-top: 8px;
    padding-bottom: 8px;
    font-size: 13px;
    color: var(--nts-hover-secondary);
    transition: background-color 0.2s;
}

.dropdown-menu-leftbar .dropdown-item:hover {
    background-color: var(--nts-hover-primary);
    color: var(--nts-hover-secondary);
}

.dropdown-menu-leftbar .dropdown-menu {
    margin-top: 0;
    margin-left: 10px;
}

.dropdown-toggle::after {
    content: '\f105';
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    float: right;
    margin-top: 3px;
}

/* Áp dụng chung cho tất cả item menu con */
.dropdown-menu-leftbar .dropdown-item {
    padding: 8px 0px;
    color: var(--nts-hover-secondary);
    font-size: 13px;
    font-weight: normal;
    background: transparent;
    border-radius: 6px;
    transition: all 0.2s ease;
}


.dropdown-menu-leftbar .dropdown-item:hover {
    background-color: var(--nts-hover-primary);
    color: var(--nts-hover-secondary);
}

.dropdown-menu-leftbar .dropdown-item.active {
    background-color: var(--nts-hover-primary);
    color: var(--nts-hover-secondary);
    font-weight: bold;
}

.dropdown-menu-leftbar .dropdown-item i {
    color: var(--nts-hover-secondary);
    margin-right: 6px;
}

.dropdown-menu-leftbar {
    background-color: transparent;
    border: none;
    box-shadow: none;
    padding: 0;
}

.flyout-menu.full-height-from-header {
    overflow: auto;         /* Cho phép cuộn */
    scrollbar-width: none;  /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
}

.flyout-menu.full-height-from-header::-webkit-scrollbar {
    display: none;
}

.dropdown-item i.fa-chevron-right {
    margin-left: auto;
}

#sidebar-menu .nav-link:hover,
#sidebar-menu .nav-link:hover .nav-link-icon, #sidebar-menu .dropdown-item:hover{
    background: var(--nts-hover-primary) !important;
    border-radius: 10px;
    color: #ff7a00 !important;

}

.nav-item{
    padding: 8px 8px 0 8px !important;
}


.dropdown-toggle.active{
    background: var(--nts-hover-primary) !important;
    border-radius: 10px !important;
    color: var(--nts-bg-secondary) !important;
    font-weight: bold;
}

.dropdown-toggle.active{
    background: var(--nts-hover-primary) !important;
    border-radius: 10px !important;
    color: var(--nts-bg-secondary) !important;
    font-weight: bold;
}


#sidebar-menu .nav-link.active,
#sidebar-menu .dropdown-item.active,
#sidebar-menu .dropdown-item:hover {
    background: var(--nts-hover-primary) !important;
    border-radius: 10px !important;
    color: var(--nts-bg-secondary) !important;
}

#sidebar-menu .nav-link.active i,
#sidebar-menu .dropdown-item.active i {
    color: var(--nts-bg-secondary) !important;
}

#sidebar-menu .nav-link.active .nav-link-icon i,
#sidebar-menu .nav-link.active .nav-link-title{
    color: var(--nts-bg-secondary) !important;
}

#sidebar-menu .nav-link.active{
    background-color: var(--nts-hover-primary) !important;
    border-radius: 10px !important;
}

#sidebar-menu .nav-link.active,
#sidebar-menu .dropdown-item.active,
#sidebar-menu .dropdown-item:hover  span {
    color: var(--nts-bg-secondary) !important;
}

#sidebar-menu .nav-link.active,
#sidebar-menu .dropdown-item.active,
#sidebar-menu .dropdown-item:hover  span > i {
    color: var(--nts-bg-secondary) !important;
}

#sidebar-menu .nav-link.active,
#sidebar-menu .dropdown-item.active,
#sidebar-menu .dropdown-item:hover  i {
    color: var(--nts-bg-secondary) !important;
}


.nav-item:hover .nts-color-menu-text > span > span > i,
.nav-item:hover .nts-color-menu-text > span > span,
.nav-item.dropdown:hover .nts-color-menu-text > span.w-auto.ms-auto > i
{
    color: var(--nts-hover-secondary) !important;
}

    .navbar-vertical.navbar-expand-lg {
        z-index: 10 !important;
    }

/* text */

#sidebar-menu .nav-link.active,
#sidebar-menu .dropdown-toggle.active,
#sidebar-menu .dropdown-item.active {
    background: var(--nts-hover-primary) !important;
    border-radius: 10px !important;
    color: var(--nts-hover-secondary) !important;
}

#sidebar-menu .nav-link.active i,
#sidebar-menu .dropdown-toggle.active i,
#sidebar-menu .dropdown-item.active i {
    color: var(--nts-hover-secondary) !important;
}

#sidebar-menu .nav-link:hover,
#sidebar-menu .dropdown-item:hover,
#sidebar-menu .dropdown-toggle:hover {
    background: var(--nts-hover-primary) !important;
    border-radius: 10px;
    color: var(--nts-hover-secondary) !important;

}

#sidebar-menu .nav-link:hover i,
#sidebar-menu .dropdown-item:hover i,
#sidebar-menu .dropdown-toggle:hover i {
    color: var(--nts-hover-secondary) !important;
}

.dropdown-item.active{
    border-right:none !important;
}

.dropdown-item.dropdown-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0px;
    width: 100%;
}

.dropdown-item.dropdown-toggle .fa-chevron-down {
    margin-left: 0 !important;
    margin-right: 0;
    font-size: 12px;
    color: var(--text-muted);
}

.dropdown-item.dropdown-toggle > span {
    flex-grow: 1;
}

/* css đóng mở leftbar */
/* Sidebar collapse button styling */
.sidebar-collapse {
    border-bottom: 1px solid #e0e0e0;
    background-color: var(--nts-bg-secondary);
    text-align: center;
    padding: 3px 0;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.sidebar-collapse:before {
    content: attr(data-label);
    display: inline-block;
    height: 0;
    border-top: 1px solid #e0e0e0;
    position: absolute;
    left: 15px;
    right: 15px;
    top: 13px;
}

.sidebar-collapse .menu-collapse {
    display: inline-block;
    cursor: pointer;
    font-size: 14px;
    color: var(--nts-bg-secondary);
    border: 1px solid #bbb;
    padding: 0 5px;
    line-height: 18px;
    border-radius: 16px;
    background-color: #fff;
    transition: transform 0.3s ease;
}

/* Hover tooltip for collapse button */
.sidebar-collapse .menu-collapse:hover::after {
    content: attr(data-hover-text);
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.sidebar-collapse .menu-collapse:hover::after {
    opacity: 1;
}

aside.navbar.navbar-leftbar.navbar-vertical.collapsed {
    width: 60px;
    transition: all 0.3s ease;
}

/* Hide text and icons when collapsed */
aside.navbar-leftbar.navbar-vertical.collapsed .navbar-brand,
aside.navbar-leftbar.navbar-vertical.collapsed .nav-link-title,
aside.navbar-leftbar.navbar-vertical.collapsed .flyout-title,
aside.navbar-leftbar.navbar-vertical.collapsed .dropdown-item,
aside.navbar-leftbar.navbar-vertical.collapsed .fa-chevron-right {
    display: none;
}

/* Hide "Tổng quan" text and related elements */
aside.navbar-leftbar.navbar-vertical.collapsed .navbar-brand .nts-leftbar-menu span {
    display: none;
}

aside.navbar-leftbar.navbar-vertical.collapsed .nav-link-icon {
    margin: 0 auto;
}

/* Hover effect for individual menu items in collapsed state */
aside.navbar-leftbar.navbar-vertical.collapsed .nav-item:hover .nav-link,
aside.navbar-leftbar.navbar-vertical.collapsed .dropdown:hover .dropdown-toggle {
    position: relative;
}
/* Hover effect for individual menu items in collapsed state */
aside.navbar-leftbar.navbar-vertical.collapsed .nav-item:hover .nav-link,
aside.navbar-leftbar.navbar-vertical.collapsed .dropdown:hover .dropdown-toggle {
    position: relative;
}

aside.navbar-leftbar.navbar-vertical.collapsed .nav-item:hover .nav-link::after,
aside.navbar-leftbar.navbar-vertical.collapsed .dropdown:hover .dropdown-toggle::after {
    content: attr(data-label);
    position: absolute;
    left: 70px;
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--nts-bg-primary);
    color: var(--nts-text-black);
    min-height: 30px;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.2s ease;
}

aside.navbar-leftbar.navbar-vertical.collapsed .nav-item:hover .nav-link::after,
aside.navbar-leftbar.navbar-vertical.collapsed .dropdown:hover .dropdown-toggle::after {
    opacity: 1;
}

aside.navbar-leftbar.navbar-vertical.collapsed .nav-item:hover .nav-link-icon,
aside.navbar-leftbar.navbar-vertical.collapsed .dropdown:hover .nav-link-icon {
    display: inline-block;
}

.page-wrapper {
    background-color: var(--nts-bg-primary);
    transition: margin-left 0.3s ease;
    margin-left: 5rem;
    min-height: 100vh;
}

.page-wrapper.sidebar-collapsed {
    margin-left: 0;
}

.page-wrapper.sidebar-collapsed {
    margin-left: 4.5rem !important;
}

.flyout-menu.full-height-from-header.flyout-menu-collapsed{
    left: 60px !important;
}

aside.navbar-leftbar.navbar-vertical.collapsed .nav-item:hover .nav-link::after, aside.navbar-leftbar.navbar-vertical.collapsed .dropdown:hover .dropdown-toggle::after {
    opacity: 0 !important;
}

a.nav-link.px-0 {
    color: var(--nts-text-secondary) !important;
}


#sidebar-menu .nav-link:hover,
#sidebar-menu .dropdown-item:hover,
#sidebar-menu .dropdown-toggle:hover .nts-color-menu-text.nav-link-icon.d-md-none.d-lg-inline-block{
    background: none !important;
}

.dropdown-menu-arrow.dropdown-menu-end:before {
    display: none !important;
}

.navbar-brand.navbar-brand-autodark {
    padding: 8px 8px 0px 8px !important;
}

.navlink-sidebar_collapse {
    text-align: center !important;
    padding-left: 4px;
}



.flyout-title_collapse {
    display: flex !important;
    text-align: left !important;
}


.form-check-input:checked {
    background-color: var(--nts-bg-secondary) !important;
}

.form-check-input:focus {
    border-color: var(--nts-bg-secondary) !important;
}

div.navbar-brand
{
    font-weight: 0 !important;
}

.tabulator .tabulator-footer {
    background-color: var(--nts-bg-primary) !important;
}
.tabulator .tabulator-footer .tabulator-page.active {
    color: var(--nts-text-primary) !important;
    background: #6596da !important;
}

.tabulator .tabulator-footer .tabulator-page{
    color: #6596da !important;
    background: var(--nts-text-primary) !important;
}


.container-fluid.leftbar {
    max-height: calc(100vh - 90px); /* Giảm 200px từ chiều cao màn hình để chừa không gian dưới */
    overflow-y: auto; /* Cho phép cuộn dọc */
}

/* Bỏ ẩn thanh cuộn để kiểm tra */
.container-fluid.leftbar {
    overflow: auto; /* Cho phép cuộn */
    scrollbar-width: none;
    -ms-overflow-style: none
}

/* Nếu muốn tùy chỉnh thanh cuộn thay vì ẩn */
.container-fluid.leftbar::-webkit-scrollbar {
    width: 8px; /* Hiển thị thanh cuộn */
    display: block; /* Đảm bảo hiển thị */
}

.container-fluid.leftbar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.container-fluid.leftbar::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}


.nts-border-secondary {
    border: 1px solid var(--nts-text-secondary) !important;
}

.nts-border-blue {
    border: 1px solid var(--nts-text-blue) !important;
}
.nts-secondary{
    color: var(--nts-text-secondary) !important;
}

.nts-blue{
    color: var(--nts-text-blue) !important;
}

a#btnPrint,
a#btnExcel,
a.btntienich {
    color: var(--nts-text-black) !important;
}

a#btnPrint:hover,
a#btnExcel:hover,
a.btntienich:hover{
    font-weight: 400 !important;
}

.tabulator .tabulator-header{
    background: var(--nts-bg-primary) !important;
    color: var(--nts-text-black) !important;
}

.tabulator-tableholder, .form-control, .select2-selection, .select2-results, .select2-dropdown, .modal-footer, .KhungVien{
    background: var(--nts-bg-primary) !important;
    color: var(--nts-text-black) !important;
}

.nts-text-backround, .nts-name{
    color: var(--nts-bg-secondary) !important;
}


#sidebar-menu .dropdown-toggle.show {
    font-weight: 0 !important;
}

#sidebar-menu .nav-link:hover,
#sidebar-menu .nav-link:hover .nav-link-icon,
#sidebar-menu .dropdown-item:hover {

    color: var(--nts-text-secondary) !important;
    border-radius: 10px;
    background: transparent !important;
}

/* Giao diện gọn khi thu nhỏ */
.li-sidebar_collapse {
    position: relative;
    text-align: center;
    overflow: visible !important; /* đảm bảo không bị cắt */
}

aside.navbar-leftbar,
.navbar-menu,
.leftbar {
    overflow: visible !important;
    z-index: auto !important;
}


/* Tooltip mặc định ẩn */
.li-sidebar_collapse::before {
    content: attr(data-label);
    position: absolute;
    left: 100%;
    top: 68%;
    transform: translateY(-50%);
    background: var(--nts-bg-primary);
    color: var(--nts-hover-secondary);
    padding: 10px;
    border-radius: 4px;
    box-shadow: 1px 1px 3px rgba(0,0,0,0.3);
    z-index: 9999;
    min-width: 160px;
    min-height: 40px;
    display: none;
    text-align: left;
    font-weight: bold;
    cursor: pointer;
    opacity: 1;
}


.li-sidebar_collapse[data-label="Tổng quan"]::before {
    top: 45% !important;
}


/* ✅ Chỉ hover khi KHÔNG phải là .dropdown */
.li-sidebar_collapse:not(.dropdown):hover::before {
    display: block !important;
}


.link-sidebar_tongquan:hover .navlink-sidebar_collapse > i{
    color: var(--nts-hover-secondary) !important;
}

.li-sidebar_collapse:hover .sidebar-collapse:before {
    content: attr(data-label);
}

.tabulator{
    background-color: var(--nts-bg-primary) !important;
}

body{
    background-color: var(--nts-bg-maunen) !important;
    color: var(--nts-text-black) !important;
}

.tabulator .tabulator-header .tabulator-col{
    background: var(--nts-bg-primary) !important;
}

button.btn-dropdown_luoitabu {
    border: none !important;
}

.tabulator-tableholder .tabulator-table .tabulator-row:hover .btn-dropdown_luoitabu{
    background-color: #f4f4f4  !important;
}


.dropdown-menu > li{
    padding: 6px 8px 0 8px !important;
}

a.active .nav-link-title {
    color: inherit !important;
}


#sidebar-menu .nav-link:not(.dropdown-toggle).active{
    background: var(--nts-hover-primary) !important;
    padding: 10px 12px;
    width: 100%;
    border-radius: 10px;
    color: var(--nts-hover-secondary) !important;
    font-weight: bold;
}

a.nts-color-menu-text.h-auto.nav-link.d-flex.justify-content-between.align-items-center.dropdown-toggle{
    padding: 8px 0px 10px 12px;
}

.li-sidebar_collapse>a.nts-color-menu-text.h-auto.nav-link.d-flex.justify-content-between.align-items-center.dropdown-toggle{
    padding: 8px 0px 10px 10px;
}

li.nav-item.nav-item-leftbar.dropdown.li-sidebar_collapse:hover {
    padding-right: 14px !important;
}

li.dropdown {
    width: 100%;
}

.page-body{
    margin-bottom:0px!important;
    margin-top: 55px !important;
}

@media (max-width: 992px) {
    .sidebar-collapse {
        display: none;
    }

    aside.navbar.navbar-leftbar.navbar-vertical.navbar-expand-lg.navbar-menu.navbar-menu-nts.nts-color-menu-bg {
        padding: 0;
        margin-top: 0 !important;
    }

    .container-fluid.leftbar {
        padding-bottom: 30px;
    }

    button.navbar-toggler {
        top: 15px;
    }

    div.page-wrapper > div.page-body {
        margin-top: 0px !important;
    }

    #navbar-menu {
        display: block !important;
        padding: 12px 0 10px 0;
    }
}

a.nav-link.active span.nts-color-menu-text.nav-link-title.ms-2{
    color: var(--nts-text-secondary) !important;
}


/* 1. Cho phép phần tử con tràn khỏi ô Tabulator */
.tabulator-cell {
    overflow: visible !important;
}

/* 2. Giữ đường kẻ nhưng cho phần tử dropdown nằm trên */
.dropdown-menu.dropdown-thao-tac {
    z-index: 1052 !important;       /* cao hơn .modal-backdrop (1050) */
}

.flyout-title.nts-color-menu-text {
    font-weight: 600;
}

a.nts-color-menu-text.dropdown-item.dropdown-toggle{
    white-space: normal;
    padding-left: 1rem !important;

}

a.nts-color-menu-text.dropdown-item{
    padding-left: 1rem !important;
    padding-right: 5px;
}

fieldset.KhungVien>legend{
        padding: 2px 6px !important;
}

fieldset{
        padding: 10px 14px !important;
}

.flyout-title.nts-color-menu-text {
    border-bottom: 1px solid #eeeeee;
}

.input-icon{
    width: 350px !important;
}
    /* Áp dụng cho tất cả các <td> trong tbody, trừ cột đầu tiên */
    #exportPreviewModal tbody tr td {
        text-align: left;
    }

    .nts-color-dongCanhBao{
        background-color: var(--nts-bg-danger);
        color: var(--nts-text-primary);
        font-size: 14px !important;
    }


    .nts-color-dongCanhBao:hover{
        color: var(--nts-text-danger);
        border: 1px solid var(--nts-bg-danger) !important;
        background-color: var(--nts-bg-white);
    }


    .nts-color-CoCanhBao{
        background-color: var(--nts-bg-white);
        color: var(--nts-text-blue);
        border: 1px solid var(--nts-text-blue) !important;
        font-size: 14px !important;
    }

    .nts-color-CoCanhBao:hover{
        color: var(--nts-text-primary);
        border: 1px solid var(--nts-bg-blue) !important;
        background-color: var(--nts-bg-blue);
    }

    .jconfirm.jconfirm-material .jconfirm-box .jconfirm-buttons button
    {
        text-transform: unset !important;
    }

    .jconfirm.jconfirm-material .jconfirm-box div.jconfirm-title-c span.text-dark {
        font-size: 16px !important;
        color: #EA1818 !important;
    }

    .jconfirm.jconfirm-material .jconfirm-box div.jconfirm-title-c .jconfirm-icon-c>i{
        color: #EA1818 !important;
    }

    .jconfirm .jconfirm-box.jconfirm-type-red
     {
            border-top: 7px solid #EA1818 !important;
    }

    .jconfirm.jconfirm-material .jconfirm-box div.jconfirm-title-c span.canhbaocapnhat {
        font-size: 16px !important;
        color: #F78B2D !important;
    }

    .jconfirm.jconfirm-material .jconfirm-box div.jconfirm-title-c .jconfirm-icon-c>i.canhbaocapnhat{
        color: #F78B2D !important;
    }

    .jconfirm .jconfirm-box.jconfirm-type-blue {
        border-top: solid 7px #EA1818 !important;
    }

    #KhungTimKiem{
    z-index: 1;
    }

    .nts-iconThaoTacs{
        font-size: 12px;
    }

    .nts-iconTT-xem{
        color: var(--nts-text-green);
    }

    .nts-iconTT-sua{
        color: var(--nts-text-blue);
    }

    .nts-iconTT-xoa{
        color: var(--nts-text-danger);
    }

    .nts-iconTT-in{
        color: var(--nts-text-blue);
    }

    .nts-iconTT-excel{
        color: var(--nts-text-green);
    }

    .nts-iconTT-guipheduyet{
        color: var(--nts-text-blue);
    }

    .nts-iconTT-pheduyet{
        color: var(--nts-text-blue);
    }

    .nts-iconTT-banhanh{
        color: var(--nts-text-secondary);
    }

    .nts-iconTT-thuhoiBH{
        color: var(--nts-text-secondary);
    }

    .nts-iconTT-giao{
        color: var(--nts-text-secondary);
    }
