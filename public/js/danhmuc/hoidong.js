//#region Globals
var btnThaoTac = function (cell) {
    return formaterbtnThaoTac(cell.getData().id);
};
var fmDangSD = function (cell) {
    return formaterDangSD(cell.getValue(), cell.getData().id);
};

const commonColumnConfig = {
    headerHozAlign: "center",
    HeaderVertAlign: "center",
    vertAlign: "middle",
};

const btnThaoTacg2 = function (cell, formatterParams, onRendered) {
    return formaterbtnThaoTac2(cell.getRow().getData().id, "btnXoaGrid2");
};

const R = window.Laravel;

// State
let modeMain = "them";
let modeCT = "them";
let currentHoiDongId = null;

let stagedMembers = [];

//#endregion

//#region Grids
// double‐click row to edit

//#region Grid1
const table = new Tabulator("#Grid1", {
    ...commonTableConfig,
    height: 500,
    // 1) Tell Tabulator where to load from:
    ajaxURL: <PERSON><PERSON>hoiDongGetAll, // your GET endpoint
    ajaxConfig: "GET",
    // 2) If your API wraps the array in { Err, Result }, pull out Result:
    ajaxResponse: (url, params, response) => response.Result,

    columns: [
        // Actions column
        {
            title: "<i class='fa fa-ellipsis-h'></i>",
            formatter: btnThaoTac,
            hozAlign: "center",
            headerSort: false,
            width: 40,
            ...commonColumnConfig,
        },

        // Hidden primary key
        { title: "ID", field: "HoiDongID", visible: false, print: false },

        // Visible fields
        {
            title: "Tên hội đồng",
            field: "TenHoiDong",
            formatter: "textarea",
            headerWordWrap: true,
            minWidth: 200,
            ...commonColumnConfig,
        },
        {
            title: "Số quyết định",
            field: "SoQDThanhLap",
            formatter: "textarea",
            headerWordWrap: true,
            width: 200,
            ...commonColumnConfig,
        },
        {
            title: "Ngày ký",
            field: "NgayKy",
            formatter: "textarea",
            hozAlign: "center",
            width: 120,
            ...commonColumnConfig,
        },
        {
            title: "Người ký",
            field: "NguoiKy",
            formatter: "textarea",
            headerWordWrap: true,
            minWidth: 150,
            ...commonColumnConfig,
            visible: false,
        },
        {
            title: "Chức vụ người ký",
            field: "ChucVuNguoiKy",
            formatter: "textarea",
            headerWordWrap: true,
            minWidth: 150,
            ...commonColumnConfig,
            visible: false,
        },
        {
            title: "Cơ quan ban hành",
            field: "CQBanHanh",
            formatter: "textarea",
            headerWordWrap: true,
            minWidth: 180,
            ...commonColumnConfig,
        },
        {
            title: "Trích yếu",
            field: "TrichYeu",
            formatter: "textarea",
            headerWordWrap: true,
            minWidth: 300,
            ...commonColumnConfig,
        },

        {
            title: "Đính kèm",
            field: "DinhKem",
            headerSort: false,
            visible: false,
            print: false,
        },
        {
            title: "Trạng thái hoạt động",
            field: "TrangThai",
            headerWordWrap: true,
            formatter: fmDangSD,
            headerSort: false,
            width: 135,
            hozAlign: "center",
            ...commonColumnConfig,
        },
    ],
});

$("#trangThai").on("change", () => {
    UpdateLabel("#trangThai", "Đang hoạt động", "Ngừng hoạt động");
});
table.on("rowDblClick", async (e, row) => {
    modeMain = "sua";
    currentHoiDongId = row.getData().id;
    $("#tieuDeModal").text("Cập nhật hội đồng chấm thi tốt nghiệp");

    // fill form from server
    const rec = await ajaxAsync(
        R.hoiDongLoad,
        { HoiDongID: currentHoiDongId },
        "GET"
    );
    if (rec.Err) {
        return alertErr("Không thể tải dữ liệu: " + rec.Msg);
    }
    $("#tenHoiDong").val(rec.Result.TenHoiDong);
    $("#soQD").val(rec.Result.SoQDThanhLap);
    $("#ngayKy").val(rec.Result.NgayKy);
    $("#trichYeu").val(rec.Result.TrichYeu);
    $("#coQuanBanHanh").val(rec.Result.CQBanHanh);
    $("#nguoiKy").val(rec.Result.NguoiKy);
    $("#chucDanhNguoiKy").val(rec.Result.ChucVuNguoiKy);
    debugger;
    $("#trangThai").value(rec.Result.TrangThai);
    $("#trangThai").trigger("change");
    // If you have a hidden field to keep the ID:
    $("#HoiDongID").val(rec.Result.id);

    // Handle attachments preview (if you want)
    uploadedFileUrls = rec.Result.DinhKem
        ? rec.Result.DinhKem.split("|").filter((u) => u)
        : [];

    $("#dinhKem").value(rec.Result.DinhKem);
    $("#list-file").empty();
    console.log(uploadedFileUrls);

    uploadedFileUrls.forEach((url) => {
        renderAttachment(url);
    });

    resetModalThaoTac();
    $("#mdThemMoi").modal("show");
});
$(document).on("click", ".btnSuaGrid1", async function () {
    resetFileInput();
    // 1) Switch to edit mode
    modeMain = "sua";
    const id = $(this).attr("data");
    currentHoiDongId = id;

    $("#tieuDeModal").text("Cập nhật hội đồng");
    // 3) Load thông tin chung
    const rec = await ajaxAsync(R.hoiDongLoad, { HoiDongID: id }, "GET");
    if (rec.Err) {
        return alertErr("Không thể tải dữ liệu: " + rec.Msg);
    }
    $("#tenHoiDong").val(rec.Result.TenHoiDong);
    $("#soQD").val(rec.Result.SoQDThanhLap);
    $("#ngayKy").val(rec.Result.NgayKy);

    $("#trichYeu").val(rec.Result.TrichYeu);
    $("#coQuanBanHanh").val(rec.Result.CQBanHanh);
    $("#nguoiKy").val(rec.Result.NguoiKy);
    $("#chucDanhNguoiKy").val(rec.Result.ChucVuNguoiKy);
    $("#trangThai").value(rec.Result.TrangThai);
    $("#trangThai").trigger("change");

    // If you have a hidden field to keep the ID:
    $("#HoiDongID").val(rec.Result.id);

    // Handle attachments preview (if you want)
    uploadedFileUrls = rec.Result.DinhKem
        ? rec.Result.DinhKem.split("|").filter((u) => u)
        : [];

    $("#dinhKem").value(rec.Result.DinhKem);
    $("#list-file").empty();
    console.log(uploadedFileUrls);

    uploadedFileUrls.forEach((url) => {
        renderAttachment(url);
    });

    resetModalThaoTac();
    $("#mdThemMoi").modal("show");
});

checkDangSD(".checkDangSD", "hoi_dongs", "TrangThai");
$(document).on("keyup", "#timKiem", function (e) {
    var data = $(this).val();
    if (e.keyCode == "13") {
        if (data == "" || data == undefined) {
            data = $("#timKiem").value();
        }
        dulieuloc = data;
        table.setFilter([
            [
                {
                    field: "HopSo",
                    type: "like",
                    value: data,
                },

                {
                    field: "MoTa",
                    type: "like",
                    value: data,
                },
            ],
        ]);
    }
});
//#endregion

//#region Grid 2
const table2 = new Tabulator("#grid_thanhphan", {
    ...commonTableConfig,
    height: 450,
    ajaxURL: R.hoiDongGetAllCT, // GET /your/api
    ajaxConfig: "GET",
    initialLoad: false, // don’t auto‐load on init
    ajaxParams: () => ({
        HoiDongID: currentHoiDongId, // always grab the up-to-date variable
    }),
    ajaxResponse: (_url, _params, response) => response.Result,

    columns: [
        {
            title: "<button id='btnThemMoiThanhVien' class='btn btn-xs btn-primary'><i class='fa fa-plus'></i></button>",
            hozAlign: "center",
            formatter: btnThaoTacg2,
            width: 60,
            headerSort: false,
            ...commonColumnConfig,
            field: "actions",
        },
        { field: "HoiDong_ThanhPhanID", visible: false },
        {
            title: "Họ tên",
            field: "HoTen",
            editor: "input",
            formatter: "plaintext",
            minWidth: 200,
            ...commonColumnConfig,
        },
        {
            title: "Chức vụ",
            field: "ChucVu",
            editor: "input",
            formatter: "plaintext",
            minWidth: 200,
            ...commonColumnConfig,
        },
        {
            title: "Chức danh",
            field: "ChucDanh",
            editor: "input",
            formatter: "plaintext",
            minWidth: 200,
            ...commonColumnConfig,
        },
        {
            title: "Là chủ tịch",
            field: "ChuTich",
            hozAlign: "center",
            width: 100,
            editor: "tickCross",
            formatter: function (cell) {
                const checked = cell.getValue() ? "checked" : "";
                return `<input type="checkbox" ${checked}>`;
            },
            ...commonColumnConfig,
        },
    ],
});

table2.on("rowDblClick", async function () {
    // open edit‐member modal
    modeCT = "sua";
    const data = row.getData();
    currentCouncilId = data.HoiDongID; // ensure we know which council
    $("#HoiDong_ThanhPhanID").val(data.HoiDong_ThanhPhanID);
    $("#txtHoTen").val(data.HoTen);
    $("#chucVuThanhVien").val(data.ChucVu);
    $("#txtChucDanh").val(data.ChucDanh);
    $("#ChuTichHD").prop("checked", data.ChuTich);
    $("#tieuDeModalCT").text("Cập nhật thành viên");
    $("#mdThemMoi2").modal("show");
});
// 1) Thêm thành viên
// ----------------------------------------------------
$("#grid_thanhphan").on("click", "#btnThemMoiThanhVien", () => {
    table2.addRow({}, false).then((row) => row.getCell("HoTen").edit());
});

// 2) Sửa thành viên:
table2.on("cellDblClick", (e, cell) => {
    if (cell.getColumn().getDefinition().editor) {
        cell.edit();
    }
});

// 3) Lưu thành viên:
table2.on("cellEdited", async (cell) => {
    /**
     * Dữ liệu thành viên hội đồng.
     *
     * @typedef {Object} RowData
     *
     */
    let rowData = cell.getRow().getData();

    rowData = {
        ...rowData,
        HoiDongID: currentHoiDongId,
    };
    try {
        if (rowData.id) {
            // existing member → update
            await ajaxAsync(R.hoiDongSaveCT, rowData, "PUT");
        } else {
            // new member → create
            const res = await ajaxAsync(R.hoiDongSaveCT, rowData, "POST");

            if (!res.Err && res.Result?.id) {
                const newId = res.Result.id;
                const row = cell.getRow();
                // set the returned ID on the row so future edits will update
                row.update({ id: newId });

                cell.getTable().redraw(true);
            }
        }
        thanhcong("Lưu thành công");
    } catch (e) {
        loi("Lỗi khi lưu: " + e.message);
    }
});
//#endregion

// Xoá thành viên hội đồng
$(document).on("click", ".btnXoaGrid2", async function () {
    if (!(await ntspermiss.xoa)) {
        NTS.canhbao(
            "User bạn đang sử dụng không thể thực hiện thao tác xoá. Vui lòng kiểm tra lại."
        );
        return false;
    }
    let idXoa = $(this).attr("data");
    const result_ktxoa = {
        Err: false,
        Result: "",
    };
    if (!result_ktxoa.Err) {
        if (result_ktxoa.Result == null || result_ktxoa.Result == "") {
            CanhBaoXoa(async () => {
                await NTS.getAjaxAPIAsync(
                    "DELETE",
                    window.Laravel.hoiDongDeleteCT + `?id=${idXoa}`
                );
                thanhcong("Xóa dữ liệu thành công!");
                table2.setData();
            });
        } else CanhBaoDuLieuTrangThai(result_ktxoa.Result);
    }
    // Lỗi khi kiểm tra xóa
    else
        result_ktxoa.CanhBao
            ? NTS.canhbao(result_ktxoa.Msg)
            : NTS.loi(result_ktxoa.Msg);
});
//#endregion

//#region Main
(() => {
    // Mở modal "Thêm mới"
    $("#btnThemMoi").on("click", async () => {
        resetFileInput();
        resetMainForm();
        $("#tieuDeModal").text("Thêm mới hội đồng chấm thi tốt nghiệp");
        new bootstrap.Tab($("#tab-thongtin-btn")).show();
        resetModalThaoTac();
        $("#trangThai").attr("checked", "checked").trigger("change");
        let ma = await ajaxAsync(window.Laravel.maTuTangUrl, {}, "GET");
        $("#soQD").val(ma["SoChungTu"]);
        uploadedFileUrls = []; // reset uploaded files
        $("#list-file").empty();
        $("#mdThemMoi").modal("show");
    });

    $('#hoiDongTabs a[data-bs-toggle="tab"]').on("show.bs.tab", function (e) {
        // Nếu user cố chuyển sang tab Thành phần hội đồng
        if (e.target.id === "tab-thanhphan-btn") {
            if (!$("#tenHoiDong").val().trim()) {
                e.preventDefault();
                return;
            }
        }
    });
    // Chuyển sang tab "Thành phần"
    $("#tab-thanhphan-btn").on("click", (e) => {
        const ten = $("#tenHoiDong").val().trim();
        if (!ten) {
            e.preventDefault();
            canhbao("Vui lòng nhập tên hội đồng");
            return;
        }
        if (!$("#soQD").val().trim()) {
            e.preventDefault();
            canhbao("Vui lòng nhập số quyết định thành lập");
            return;
        }
        // Tạo payload một lần
        const payload = {
            HoiDongID: currentHoiDongId,
            data: {
                TenHoiDong: $("#tenHoiDong").val(),
                SoQDThanhLap: $("#soQD").val(),
                NgayKy: $("#ngayKy").val(),
                TrichYeu: $("#trichYeu").val(),
                CQBanHanh: $("#coQuanBanHanh").val(),
                CapQD: $("#capHoiDong").val(),
                NguoiKy: $("#nguoiKy").val(),
                ChucVuNguoiKy: $("#chucDanhNguoiKy").val(),
                DinhKem: $("#dinhKem")[0].files, // files qua FormData ở nơi khác
            },
        };

        if (currentHoiDongId) {
            // ─── Đã có ID ───
            // 1) Lưu dữ liệu (background)
            ajaxAsync(R.hoiDongSave, payload, "POST")
                .then((res) => {
                    if (res.Err) {
                        return loi("Lỗi lưu hội đồng (background): " + res.Msg);
                    }
                    // Trong trường hợp lưu trả về ID mới
                    currentHoiDongId = res.Result.id || currentHoiDongId;
                })
                .catch((e) =>
                    loi("Lỗi lưu hội đồng (background): " + e.message)
                );

            // 2) Tải ngay bảng thành viên
            loadTableThanhVien();
        } else {
            // ─── Chưa có ID: phải lưu trước ───
            (async () => {
                const resMain = await ajaxAsync(R.hoiDongSave, payload, "POST");
                if (resMain.Err) {
                    e.preventDefault();
                    return loi("Lỗi lưu hội đồng: " + resMain.Msg);
                }
                currentHoiDongId = resMain.Result.id;
                loadTableThanhVien();
            })();
        }
        debugger;
        table.setData();
    });

    // Lưu hội đồng (thêm/cập nhật)
    $("#btnLuuVaDong").on("click", async () => {
        debugger;
        const payload = {
            HoiDongID: currentHoiDongId,
            data: {
                TenHoiDong: $("#tenHoiDong").val(),
                SoQDThanhLap: $("#soQD").val(),
                NgayKy: $("#ngayKy").val(),
                TrichYeu: $("#trichYeu").val(),
                CQBanHanh: $("#coQuanBanHanh").val(),
                CapQD: $("#capHoiDong").val(),
                NguoiKy: $("#nguoiKy").val(),
                ChucVuNguoiKy: $("#chucDanhNguoiKy").val(),
                DinhKem: $("#dinhKem").val(), // nếu xử lý files qua FormData
                TrangThai: $("#trangThai").prop("checked"),
            },
        };

        // Kiểm tra dữ liệu...
        if (!payload.data.TenHoiDong) {
            return canhbao("Tên hội đồng không được để trống!");
        }

        // 1) Lưu hội đồng chính
        const resMain = await ajaxAsync(R.hoiDongSave, payload, "POST");
        if (resMain.Err) {
            return loi("Lỗi lưu hội đồng: " + resMain.Msg);
        }
        const newId = resMain.Result.id; // Mongo’s _id
        currentHoiDongId = newId;

        thanhcong("Lưu hội đồng và thành viên thành công");
        resetFileInput();
        $("#mdThemMoi").modal("hide");
        table.replaceData(); // tải lại dữ liệu từ ajaxURL và vẽ lại bảng
    });

    // Xóa một hội đồng
    $(document).on("click", ".btnXoaGrid1", async function () {
        if (!(await ntspermiss.xoa)) {
            NTS.canhbao(
                "User bạn đang sử dụng không thể thực hiện thao tác thêm xoá. Vui lòng kiểm tra lại."
            );
            return false;
        }
        let idXoa = $(this).attr("data");
        const result_ktxoa = {
            Err: false,
            Result: "",
        };
        if (!result_ktxoa.Err) {
            if (result_ktxoa.Result == null || result_ktxoa.Result == "") {
                CanhBaoXoa(async () => {
                    await NTS.getAjaxAPIAsync(
                        "DELETE",
                        window.Laravel.hoiDongDelete + `?id=${idXoa}`
                    );
                    NTS.thanhcong("Xóa dữ liệu thành công!");
                    table.replaceData(); // tải lại dữ liệu từ ajaxURL và vẽ lại bảng
                });
            } else CanhBaoDuLieuTrangThai(result_ktxoa.Result);
        }
        // Lỗi khi kiểm tra xóa
        else
            result_ktxoa.CanhBao
                ? NTS.canhbao(result_ktxoa.Msg)
                : NTS.loi(result_ktxoa.Msg);
    });

    // Initialize both grids + lookups on page load
    $(() => {
        // table.replaceData(); // re-fetches ajaxURL and re-draws
    });
})();

//#endregion

//#region Helper functions
/**
 * Thông báo lỗi bằng NTS.loi.
 *
 * @param {string} msg - Nội dung thông báo.
 */
function loi(msg) {
    NTS.loi(msg);
}
/**
 * Cảnh báo bằng NTS.canhbao.
 *
 * @param {string} msg - Nội dung thông báo.
 */
function canhbao(msg) {
    NTS.canhbao(msg);
}
/**
 * Thông báo thành công bằng NTS.thanhcong.
 *
 * @param {string} msg - Nội dung thông báo.
 */
function thanhcong(msg) {
    NTS.thanhcong(msg);
}

function ajaxAsync(url, data, method = "GET") {
    return NTS.getAjaxAPIAsync(method, url, data);
}

// clear main form
/**
 * Đặt lại biểu mẫu chính về trạng thái ban đầu.
 *
 * Chức năng này sẽ xóa tất cả giá trị trong các trường biểu mẫu,
 * đặt lại ID hội đồng hiện tại và chế độ chính, đồng thời xóa dữ liệu trong bảng liên quan.
 *
 * @function resetMainForm
 */
function resetMainForm() {
    resetModalThaoTac();
    $("#tenHoiDong").val("");
    $("#soQD").val("");
    $("#ngayKy").val("");
    $("#trichYeu").val("");
    $("#coQuanBanHanh").val("");
    $("#nguoiKy").val("");
    $("#chucDanhNguoiKy").val("");
    $("#dinhKem").val("");
    currentHoiDongId = null;
    modeMain = "them";
}

/**
 * loadMainGrid
 * -------------
 * Mô tả:
 *   Gọi API để lấy toàn bộ danh sách hội đồng và gán kết quả vào bảng chính.
 *
 * Thao tác:
 *   1. Gọi ajaxAsync với endpoint R.hoiDongGetAll và body rỗng.
 *   2. Nếu trả về { Err: false }, gọi table.setData(res.Result) để hiển thị dữ liệu.
 *   3. Nếu Err: true, hiển thị lỗi bằng hàm loi(res.Msg).
 *
 * Giá trị trả về:
 *   Promise<void>
 */
async function loadTableThongTinChung() {
    const res = await ajaxAsync(R.hoiDongGetAll, {});
    if (res.Err === false) {
        table.setData(res.Result);
    } else {
        loi(res.Msg);
    }
}

/**
 * loadMemberGrid
 * --------------
 * Mô tả:
 *   Gọi API để lấy danh sách thành viên của một hội đồng dựa trên currentHoiDongId.
 *
 * Thao tác:
 *   1. Nếu currentHoiDongId chưa được thiết lập (falsy), gọi table2.clearData() và return.
 *   2. Gọi ajaxAsync với endpoint R.hoiDongGetAllCT, truyền vào { HoiDongID: currentHoiDongId }.
 *   3. Nếu Err: false, gán res.Result cho table2.setData().
 *   4. Nếu Err: true, hiển thị lỗi bằng hàm loi(res.Msg).
 *
 * Giá trị trả về:
 *   Promise<void>
 */
async function loadTableThanhVien() {
    debugger;
    if (!currentHoiDongId) return table2.clearData();
    const res = await ajaxAsync(R.hoiDongGetAllCT, {
        HoiDongID: currentHoiDongId,
    });
    if (res.Err === false) {
        table2.setData(res.Result);
    } else {
        loi(res.Msg);
    }
}
/**
 * toIsoDate
 * ---------
 * Mô tả:
 *   Chuyển chuỗi ngày tháng định dạng "dd/mm/yyyy" sang "yyyy-mm-dd".
 *
 * Tham số:
 *   @param {string} ddmmyyyy — Chuỗi ngày tháng kiểu "dd/mm/yyyy"
 *
 * Trả về:
 *   {string} — Chuỗi ngày tháng kiểu ISO "yyyy-mm-dd"
 *
 * Ví dụ:
 *   toIsoDate("5/4/2025")  // => "2025-04-05"
 *   toIsoDate("15/11/2024") // => "2024-11-15"
 */
function toIsoDate(ddmmyyyy) {
    if (!ddmmyyyy) return "";
    const [d, m, y] = ddmmyyyy.split("/");
    return `${y}-${m.padStart(2, "0")}-${d.padStart(2, "0")}`;
}

/**
 * Đặt lại trạng thái của modal thao tác.
 *
 * Chức năng:
 * - Chuyển về tab đầu tiên trong modal.
 * - Xóa dữ liệu trong bảng `table2` để đảm bảo dữ liệu luôn mới.
 *
 * Không có tham số đầu vào và không trả về giá trị.
 */
function resetModalThaoTac() {
    // switch back to the first tab
    const tabTriggerEl = document.querySelector("#tab-thongtin-btn");
    bootstrap.Tab.getOrCreateInstance(tabTriggerEl).show();

    // clear out table2 so it's always fresh
    table2.clearData();
}

async function uploadFile(file) {
    const formData = new FormData();
    formData.append("file", file);

    const res = await NTS.getAjaxAPIAsync(
        "POST",
        window.Laravel.uploadFile,
        formData
    );

    if (res.err) {
        return alert(res.msg);
    }

    // save the returned path
    Path = res.url[0];
    const fullUrl = new URL(`${Path}`, window.location.origin).href;

    const saveRes = await NTS.getAjaxAPIAsync(
        "POST",
        window.Laravel.updateAvatar,
        { avatarUrl: fullUrl }
    );

    if (saveRes.Err) {
        return alert(saveRes.Msg || "Không thể cập nhật avatar.");
    }

    // 4) Update the DOM preview
    const preview = document.getElementById("avatarPreview");
    if (preview) {
        preview.src = fullUrl;
        NTS.thanhcong("Cập nhật avatar thành công.");
    }
}

//#endregion
