//#region GLOBALS
let hotKey = 0;
let tempAction = "them";

//FORMATTERS
const fmThaoTac = (cell) => formaterbtnThaoTac(cell.getData().id);
const fmDangSD = (cell) => formaterDangSD(cell.getValue(), cell.getData().id);

checkDangSD(".checkDangSD", "nhan_viens", "trangThai");
$("#TrangThai").on("change", function () {
    UpdateLabelDangSD(this);
});
// const commonTableConfig = {
//     layout: "fitColumns",
//     pagination: true,
//     paginationSize: 50,
//     paginationSizeSelector: [50, 100, 150, 200, 500, true],
//     height: "550px",
//     locale: true,
//     langs: TabulatorLangsVi,
//     placeholder: "Không có dữ liệu",
//     HeaderVertAlign: "center",
// };

const commonColumnConfig = {
    headerHozAlign: "center",
    vertAlign: "middle",
};

var grid1 = new Tabulator("#Grid1", {
    ...commonTableConfig,
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',

            formatter: fmThaoTac,
            width: 60,
            hozAlign: "center",
            headerSort: false,
            ...commonColumnConfig,
        },
        {
            title: "NhanVienID",
            field: "id",
            visible: false,
        },
        {
            title: "Mã",
            field: "maNhanVien",
            headerHozAlign: "center",
            vertAlign: "middle",
            width: 150,
            formatter: "textarea",
        },
        {
            title: "Họ và tên",
            field: "tenNhanVien",
            formatter: "textarea",
            minWidth: 250,
            ...commonColumnConfig,
        },
        { field: "phongBanID", visible: false },
        { field: "chucVuID", visible: false },
        {
            title: "Phòng ban",
            field: "tenPhongBan",
            formatter: "textarea",
            width: 300,
            ...commonColumnConfig,
        },
        {
            title: "Chức vụ",
            field: "tenChucVu",
            width: 180,
            ...commonColumnConfig,
        },
        { field: "gioiTinhID", visible: false },
        {
            title: "Giới tính",
            field: "tenGioiTinh",
            formatter: "textarea",
            width: 120,
            ...commonColumnConfig,
        },
        {
            title: "Ngày sinh",
            field: "ngaySinh",
            formatter: "textarea",
            width: 120,
            ...commonColumnConfig,
        },
        {
            title: "Trạng thái sử dụng",
            field: "trangThai",
            headerWordWrap: true,
            hozAlign: "center",
            formatter: fmDangSD,
            headerSort: false,
            width: 135,
            ...commonColumnConfig,
        },
    ],
});
//#endregion
grid1.on("rowDblClick", function (_, row) {
    var rowData = row.getData();
    suaDuLieu(rowData.id);
});
//#region COMBOS
async function loadCombos() {
    let commonConfig = {
        columns: 2,
        indexValue: 0,
        indexText: 1, // assuming your result rows are [id, code, name]
        indexText1: 2, // assuming your result rows are [id, code, name]
        textShowTatCa: "-Chọn-",
        showTatCa: true,
    };
    await loadDataCombos([
        {
            name: "#PhongBanID",
            ajaxUrl: window.Laravel.nhanvien.listPhongBanUrl,
            ...commonConfig,
        },
        {
            name: "#ChucVuID",
            ajaxUrl: window.Laravel.nhanvien.listChucVuUrl,
            ...commonConfig,
        },
        {
            name: "#DonViID",
            ajaxUrl: window.Laravel.nhanvien.listDonViUrl,
            ...commonConfig,
        },
        {
            name: "#GioiTinhID",
            ajaxUrl: window.Laravel.nhanvien.listGioiTinhUrl,
            ...commonConfig,
        },
    ]);
}
//#endregion COMBOS

//#region LOGIC HOẠT ĐỘNG
$(document).ready(function () {
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    $("#btnXuatPdf").on("click", () => xemTruocExport("pdf"));
    $("#btnXuatExcel").on("click", () => xemTruocExport("excel"));
    $("#btnPrint").on("click", () => (grid1.print(false, true), false));

    // keyboard shortcuts
    $(document).on("keydown", function (e) {
        switch (e.keyCode) {
            case 113: // F2
                if (hotKey === 0) $("#btnThemMoi").click();
                return e.preventDefault();
            case 115: // F4
                if (hotKey === 1) $("#mdThemMoi").modal("hide");
                return e.preventDefault();
            case 120: // F9
                if (hotKey === 1) $("#btnLuuVaDong").click();
                return e.preventDefault();
        }
    });

    $("#mdThemMoi").on("shown.bs.modal", () => (hotKey = 1));
    $("#mdThemMoi").on("hidden.bs.modal", () => (hotKey = 0));

    $("#btnThemMoi").on("click", showCreateModal);
    $(document).on("click", ".btnSuaGrid1", function () {
        const id = $(this).attr("data"); // -> "681b2a37739f7cfa7f027413"
        suaDuLieu(id);
    });
    $(document).on("click", ".btnXoaGrid1", function () {
        const id = $(this).attr("data"); // -> "681b2a37739f7c
        XoaDuLieu(id);
    });

    $("#timKiem").on("keyup", function (e) {
        if (e.keyCode === 13) {
            let v = $(this).val();
            grid1.setFilter([
                { field: "maNhanVien", type: "like", value: v },
                { field: "tenNhanVien", type: "like", value: v },
                { field: "soDienThoai", type: "like", value: v },
            ]);
        }
    });

    $("#btnLuuVaDong").on("click", luuVaDong);

    loadDuLieu();
});
//#endregion LOGIC HOẠT ĐỘNG

//#region FUNCTIONS
//
//
//
//
//
//#region XEM
async function loadDuLieu() {
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        `${window.location.pathname}/getall`,
        {}
    );
    if (!result.Err) {
        debugger;
        grid1.setData(result.Result);
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

async function showCreateModal() {
    if (!(await ntspermiss.them)) {
        return NTS.canhbao("Bạn không có quyền thêm");
    }
    tempAction = "them";
    resetForm("#mdThemMoi");
    $("#TrangThai").prop("checked", true);
    $("#tieuDeModal").text("Thêm mới cán bộ, công chức, viên chức");
    $("#NhanVienID").val("");
    // fill auto-increment code
    loadCombos();
    try {
        let r = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.nhanvien.maTuTangUrl,
            {}
        );
        $("#MaNhanVien").val(r.SoChungTu);
    } catch {}
    $("#mdThemMoi").modal("show");
}

async function suaDuLieu(id) {
    if (!(await ntspermiss.sua)) {
        return NTS.canhbao("Bạn không có quyền sửa");
    }
    tempAction = "sua";
    $("#tieuDeModal").text("Cập nhật cán bộ, công chức, viên chức");
    await loadCombos();

    let result = await NTS.getAjaxAPIAsync(
        "GET",
        `${window.location.pathname}/loaddulieusua`,
        { id }
    );
    if (result.Err) {
        return result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
    let d = result.Result[0];
    $("#MaNhanVien").val(d.maNhanVien);
    $("#TenNhanVien").val(d.tenNhanVien);
    $("#Email").val(d.email || "");
    $("#SoDienThoai").val(d.soDienThoai);
    $("#DiaChi").val(d.diaChi);
    $("#PhongBanID").value(d.phongBanID);
    $("#ChucVuID").value(d.chucVuID);
    $("#GioiTinhID").value(d.gioiTinhID);
    $("#NgaySinh").val(d.ngaySinh || "");
    $("#CMND").val(d.cmnd || "");
    $("#NoiCap").val(d.noiCap || "");
    $("#NgayCap").val(d.ngayCap || "");
    $("#TrangThai").prop("checked", d.trangThai);
    $("#NhanVienID").val(id);
    UpdateLabelDangSD("#TrangThai");
    $("#mdThemMoi").modal("show");
}
//#endregion

//#region Lưu thông tin
async function luuVaDong() {
    debugger;
    // Validate required fields
    const validate = new NTSValidate("#mdThemMoi");
    if (!validate.trim().check()) {
        return false;
    }
    const isCreate = tempAction === "them";

    if (tempAction == "them" && !(await ntspermiss.them)) {
        return NTS.canhbao("Bạn không có quyền thêm!");
    }

    if (tempAction == "sua" && !(await ntspermiss.sua)) {
        return NTS.canhbao("Bạn không có quyền thêm!");
    }

    const soDienThoai = $("#SoDienThoai").val().replace(/\D/g, ""); // Loại bỏ mọi ký tự không phải số
    if (soDienThoai !== "" && !/^\d{10,}$/.test(soDienThoai)) {
        return NTS.canhbao("Số điện thoại phải có ít nhất 10 số!");
    }
    const nhanVienId = $("#NhanVienID").val();
    const method = isCreate ? "POST" : "PUT";
    const url = `/danhmuc/nhanvien/luuthongtin`;

    const payload = {
        nhanVienId,
        maNhanVien: $("#MaNhanVien").val(),
        tenNhanVien: $("#TenNhanVien").val(),
        soDienThoai: $("#SoDienThoai").val(),
        diaChi: $("#DiaChi").val(),
        phongBanID: $("#PhongBanID").val(),
        chucVuID: $("#ChucVuID").val(),
        gioiTinhID: $("#GioiTinhID").val(),
        ngayBatDauLamViec: $("#NgayBatDauLamViec").val(),
        ngaySinh: $("#NgaySinh").val(),
        cmnd: $("#CMND").val(),
        ngayCap: $("#NgayCap").val(),
        noiCap: $("#NoiCap").val(),
        trangThai: $("#TrangThai").is(":checked"),
    };

    let result = await NTS.getAjaxAPIAsync(method, url, payload);
    if (!result.Err) {
        await loadDuLieu();
        NTS.thanhcong(result.Msg);
        $("#mdThemMoi").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}
//#endregion

//#region Xoá thông tin
async function XoaDuLieu(ID) {
    var result_ktxoa = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.layouts.KiemTraXoa,
        {
            ma: ID,
            model: "DanhMuc\\NhanVien",
        }
    );
    if (!result_ktxoa.Err) {
        CanhBaoXoa(async () => {
            var result = await NTS.getAjaxAPIAsync(
                "DELETE",
                window.location.pathname + "/xoa",
                { ma: ID }
            );
            if (!result.Err) {
                loadDuLieu();
                NTS.thanhcong(result.Msg);
            } else {
                result.canhbao ? NTS.canhbao(result.msg) : NTS.loi(result.msg);
            }
        });
        return false;
    } else {
        NTS.canhbao(result_ktxoa.Msg);
        return false;
    }
}
//#endregion

//#region Export
function layDuLieuLuoi() {
    const data = grid1.getData(); // assumes your Tabulator instance is in `table`
    const cols = grid1
        .getColumns()
        .filter((c) => c.getField() && c.getField() !== "actions");
    const headings = cols.map((c) => c.getDefinition().title);
    const rows = data.map((row) => {
        const obj = {};
        cols.forEach((col) => {
            const d = col.getDefinition();
            obj[d.title] =
                d.field === "fullName"
                    ? `${row.firstName} ${row.lastName}`
                    : row[d.field];
        });
        return obj;
    });
    return { headings, rows };
}

async function postAndDownload(url, data, filename) {
    const token = document.querySelector('meta[name="csrf-token"]').content;
    const res = await fetch(url, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": token,
        },
        body: JSON.stringify(data),
    });
    if (!res.ok) return alert("Export lỗi: " + res.statusText);
    const blob = await res.blob();
    const blobUrl = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = blobUrl;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(blobUrl);
}

async function xemTruocExport(type) {
    const { headings, rows } = layDuLieuLuoi();

    if (type === "pdf") {
        const res = await fetch(window.Laravel.nhanvien.exportPdfUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document.querySelector(
                    'meta[name="csrf-token"]'
                ).content,
            },
            body: JSON.stringify({ headings, rows }),
        });
        if (!res.ok) return alert("Error generating PDF");
        const blob = await res.blob();
        const blobUrl = URL.createObjectURL(blob);
        document.getElementById("exportPreviewTitle").textContent =
            "Xem trước khi in";
        document.getElementById(
            "previewContent"
        ).innerHTML = `<iframe style="width:100%;height:75vh;border:none" src="${blobUrl}"></iframe>`;
        document.getElementById("confirmDownloadBtn").onclick = () => {
            postAndDownload(
                window.Laravel.nhanvien.exportPdfUrl,
                { headings, rows },
                `nhanvien_${Date.now()}.pdf`
            );
            bootstrap.Modal.getInstance(
                document.getElementById("exportPreviewModal")
            ).hide();
        };
    } else {
        // excel
        let html =
            '<div class="table-responsive"><table class="table table-bordered"><thead><tr>';
        headings.forEach((h) => (html += `<th>${h}</th>`));
        html += "</tr></thead><tbody>";
        rows.forEach((r) => {
            html += "<tr>";
            headings.forEach((h) => (html += `<td>${r[h] || ""}</td>`));
            html += "</tr>";
        });
        html += "</tbody></table></div>";
        document.getElementById("exportPreviewTitle").textContent =
            "Xem trước dữ liệu";
        document.getElementById("previewContent").innerHTML = html;
        document.getElementById("confirmDownloadBtn").onclick = () => {
            postAndDownload(
                window.Laravel.nhanvien.exportExcelUrl,
                { headings, rows },
                `nhanvien_${Date.now()}.xlsx`
            );
            bootstrap.Modal.getInstance(
                document.getElementById("exportPreviewModal")
            ).hide();
        };
    }

    bootstrap.Modal.getOrCreateInstance(
        document.getElementById("exportPreviewModal")
    ).show();
}
//#endregion

//#endregion FUNCTIONS
