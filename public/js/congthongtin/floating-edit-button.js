/**
 * Floating Edit Button for Cổng Thông Tin Index Page
 * Integrates with ThietLapWebsite->ck_QuanTriNgayTrenWebsite setting
 */

class FloatingEditButton {
    constructor() {
        this.isEditMode = false;
        this.button = null;
        this.thietLapWebsite = null;
        this.isAdmin = false;

        this.init();
    }

    init() {
        // Get website settings from global variable or data attribute
        this.getWebsiteSettings();

        // Get admin status from global variable
        this.getAdminStatus();

        // Always create floating button (show by default)
        this.createFloatingButton();
        this.bindEvents();
    }

    /**
     * Get website settings
     */
    getWebsiteSettings() {
        // Try to get from global variable first
        if (typeof window.thietLapWebsite !== 'undefined') {
            this.thietLapWebsite = window.thietLapWebsite;
        } else {
            // Try to get from data attribute on body
            const settingsData = document.body.getAttribute('data-thiet-lap-website');
            if (settingsData) {
                try {
                    this.thietLapWebsite = JSON.parse(settingsData);
                } catch (e) {
                    console.error('Error parsing website settings:', e);
                }
            }
        }
    }

    /**
     * Get admin status from global variable
     */
    getAdminStatus() {
        // Try to get from global variable first
        if (typeof window.isAdmin !== 'undefined') {
            this.isAdmin = window.isAdmin;
        } else {
            // Try to get from data attribute on body
            const adminData = document.body.getAttribute('data-is-admin');
            if (adminData) {
                this.isAdmin = adminData === 'true' || adminData === '1';
            }
        }
    }

    /**
     * Check if edit button should be shown
     */
    shouldShowEditButton() {
        return this.thietLapWebsite &&
            this.thietLapWebsite.ck_QuanTriNgayTrenWebsite === true;
    }

    /**
     * Create floating edit button
     */
    createFloatingButton() {
        // Create floating button container
        const floatingContainer = document.createElement('div');
        floatingContainer.className = 'floating-edit-container';
        floatingContainer.innerHTML = `
            <div class="floating-edit-buttons">
                <button type="button" class="floating-edit-btn edit-toggle" id="editModeToggle" title="Bật/Tắt chế độ chỉnh sửa">
                    <i class="fas fa-edit"></i>
                </button>
                <div class="edit-mode-indicator" id="editModeIndicator" style="display: none;">
                    <span>Chế độ chỉnh sửa</span>
                </div>
            </div>
        `;

        // CSS styles are now handled by edit-mode.css file

        // Add container to body
        document.body.appendChild(floatingContainer);

        // Store reference to button
        this.button = document.getElementById('editModeToggle');

        // Update button title based on admin status
        this.updateButtonTitle();
    }

    /**
     * Bind events
     */
    bindEvents() {
        if (!this.button) return;

        // Toggle edit mode on button click
        this.button.addEventListener('click', () => {
            this.toggleEditMode();
        });

        // Optional: Hide button on scroll (uncomment if needed)
        // this.bindScrollEvents();

        // Listen for escape key to exit edit mode
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isEditMode) {
                this.disableEditMode();
            }
        });

        // Listen for edit mode changes from other components
        document.addEventListener('editModeChanged', (e) => {
            this.isEditMode = e.detail.enabled;
            this.updateButtonState();
        });
    }

    /**
     * Toggle edit mode
     */
    toggleEditMode() {
        // Kiểm tra quyền admin trước khi cho phép chỉnh sửa
        if (!this.isAdmin) {
            this.showNotification('Bạn không có quyền chỉnh sửa. Chỉ admin mới có thể chỉnh sửa nội dung.', 'error');
            return;
        }

        if (this.isEditMode) {
            this.disableEditMode();
        } else {
            this.enableEditMode();
        }
    }

    /**
     * Enable edit mode
     */
    enableEditMode() {
        // Kiểm tra quyền admin trước khi bật chế độ chỉnh sửa
        if (!this.isAdmin) {
            this.showNotification('Bạn không có quyền chỉnh sửa. Chỉ admin mới có thể chỉnh sửa nội dung.', 'error');
            return;
        }

        this.isEditMode = true;
        this.updateButtonState();

        // Dispatch event to other components
        const event = new CustomEvent('editModeToggle', {
            detail: { enabled: true }
        });
        document.dispatchEvent(event);

        // Show notification
        this.showNotification('Đã bật chế độ chỉnh sửa. Nhấp vào các thành phần để chỉnh sửa.', 'info');
    }

    /**
     * Disable edit mode
     */
    disableEditMode() {
        this.isEditMode = false;
        this.updateButtonState();

        // Dispatch event to other components
        const event = new CustomEvent('editModeToggle', {
            detail: { enabled: false }
        });
        document.dispatchEvent(event);

        // Show notification and reload page after a short delay
        this.showNotification('Đã tắt chế độ chỉnh sửa. Đang tải lại trang...', 'info');

        // Reload page after 1 second to show updated content
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }

    /**
     * Update button visual state
     */
    updateButtonState() {
        if (!this.button) return;

        const indicator = document.getElementById('editModeIndicator');

        if (this.isEditMode) {
            this.button.classList.add('active');
            this.button.innerHTML = '<i class="fas fa-save"></i>';

            // Hide indicator - we don't want the red X and text
            if (indicator) {
                indicator.style.display = 'none';
            }
        } else {
            this.button.classList.remove('active');
            this.button.innerHTML = '<i class="fas fa-edit"></i>';

            if (indicator) {
                indicator.style.display = 'none';
            }
        }

        // Update title based on current state and admin status
        this.updateButtonTitle();

        // Add/remove disabled class based on admin status
        if (!this.isAdmin) {
            this.button.classList.add('disabled');
        } else {
            this.button.classList.remove('disabled');
        }
    }

    /**
     * Update button title based on admin status
     */
    updateButtonTitle() {
        if (!this.button) return;

        if (!this.isAdmin) {
            this.button.title = 'Chỉ admin mới có thể chỉnh sửa nội dung';
        } else if (this.isEditMode) {
            this.button.title = 'Lưu và thoát chế độ chỉnh sửa';
        } else {
            this.button.title = 'Bật chế độ chỉnh sửa';
        }
    }

    /**
     * Bind scroll events to hide/show button
     */
    bindScrollEvents() {
        let lastScrollTop = 0;
        let scrollTimeout;

        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const container = document.querySelector('.floating-edit-container');

            if (!container) return;

            // Clear timeout
            clearTimeout(scrollTimeout);

            // Hide button when scrolling down, show when scrolling up
            if (scrollTop > lastScrollTop && scrollTop > 100) {
                container.classList.add('hidden');
            } else {
                container.classList.remove('hidden');
            }

            lastScrollTop = scrollTop;

            // Show button after scroll stops
            scrollTimeout = setTimeout(() => {
                container.classList.remove('hidden');
            }, 1000);
        });
    }

    /**
     * Show notification using existing NTS system
     */
    showNotification(message, type = 'info') {
        // Use existing NTS notification system
        switch (type) {
            case 'success':
                NTS.thanhcong(message);
                break;
            case 'error':
                NTS.loi(message);
                break;
            case 'warning':
                NTS.canhbao(message);
                break;
            case 'info':
            default:
                NTS.thongbao({ type: 'info', message: message });
                break;
        }
    }

    /**
     * Destroy the floating button
     */
    destroy() {
        const container = document.querySelector('.floating-edit-container');
        if (container) {
            container.remove();
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.floatingEditButton = new FloatingEditButton();
});
