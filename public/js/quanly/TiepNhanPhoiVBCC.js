var tempthem = "them";
var ChuaCoThongTin = "";
const today = new Date();
const dd = String(today.getDate()).padStart(2, '0');
const mm = String(today.getMonth() + 1).padStart(2, '0');
const yyyy = today.getFullYear();
const defaultDate = `${dd}/${mm}/${yyyy}`;


function setNgayTrongThang(inputTuNgayId, inputDenNgayId) {
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth(); // 0 = January
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);

  // Định dạng dd/mm/yyyy
  const format = (d) => {
    const dd = String(d.getDate()).padStart(2, '0');
    const mm = String(d.getMonth() + 1).padStart(2, '0');
    const yyyy = d.getFullYear();
    return `${dd}/${mm}/${yyyy}`;
  };

  // Gán giá trị cho input
  document.getElementById(inputTuNgayId).value = format(firstDay);
  document.getElementById(inputDenNgayId).value = format(lastDay);
}

var NhanVienID='';
var ChucVuID ='';
//#region logic thao tác click
$(async function () {
    setNgayTrongThang("TuNgay_Loc", "DenNgay_Loc")
    var resultNV = await NTS.getAjaxAPIAsync("GET", Laravel.layouts.getCurrentUserInfo,{});
    console.log(resultNV.data.nhanVien);
    
    NhanVienID=resultNV.data.nhanVien.id
    ChucVuID=resultNV.data.nhanVien.chucVuID
    $(document).on('keydown', function (e) {
        var Showmodal = $('.modal.show').attr('id')
        
        if(Showmodal == 'mdThemMoi' || Showmodal == undefined){
            switch (e.keyCode) {
                case 113: //f2
                     $('#btnThemMoi').trigger('click');
                    break;
                case 115: //f4
                    if ($('#mdThemMoi').hasClass('show')) {
                        $('#mdThemMoi').modal('hide');
                    }
                    break;
                case 120: //f9
                    if ($('#mdThemMoi').hasClass('show')) {
                        $('#btnLuuVaDong').trigger('click');
                    }
                    break;
            }
        }
        if(Showmodal == 'mdQPheDuyet'){
            switch (e.keyCode) {
                case 115: //f4
                    if ($('#mdQPheDuyet').hasClass('show')) {
                        $('#mdQPheDuyet').modal('hide');
                    }
                    break;
                case 120: //f9
                    if ($('#mdQPheDuyet').hasClass('show')) {
                        $('#btnBanHanh').trigger('click');
                    }
                    break;
            }
        }
        
    });
    $("#TrangThai_Loc").select2({
        width: "100%"  // ✅ đúng cú pháp
    });
    $("#selectLoaiChungTu").select2({
        width: "100%"  // ✅ đúng cú pháp
    });
    LoadDataComBo();
    LoadDataTable();
    


});
$(document).on("change", "#SoLuongNhan", function () {
    if ($("#SoLuongHopLe").value() == "") {
        $("#SoLuongHopLe").value($("#SoLuongNhan").value());
        $("#SoLuongLoi").value("0");
    }
});
$(document).on("click", "#TimKiemNangCao", function () {
    if ($("#KhungTimKiem").css("display") == "block") {
        $("#KhungTimKiem").slideUp(200);
    } else {
        $("#KhungTimKiem").slideDown(200);
    }
    return false;
});
$(document).on("click", "#DongTimKiem", function () {
    $("#KhungTimKiem").slideUp(200);
    return false;
});
$(document).on("click", "#TimKiem", async function () {
    $("#KhungTimKiem").slideUp(200);
    await LoadDataTable();
    return false;
});
$(document).on("click", "#btn-layout-1", async function () {
    $("#grid-layout").fadeIn(200);
    $("#list-layout").hide();
    $("#list-layout").removeClass("show");
    $("#grid-layout").addClass("show");
    $("#DivDanhSach").show();
    $("#DivLuoi").hide();
    table.setFilter(matchAny, { value: $('#SearchKey').value() });
});
$(document).on("click", "#btn-layout-2", async function () {
    $("#grid-layout").hide();
    $("#list-layout").fadeIn(200);
    $("#list-layout").addClass("show");
    $("#grid-layout").removeClass("show");
    $("#DivDanhSach").hide();
    $("#DivLuoi").show();
    GridMainLuoi.setFilter(matchAny, { value: $('#SearchKey').value() });
});
$(document).on("change", "#CbSapXep", function () {
    LoadDataTable();
});
function LoadDataComBo() {
    NTS.loadDataComboAsync({
        name: "#NhanVienID_Nhap, #NhanVienID_PheDuyet",
        type: "GET",
        ajaxUrl: window.Laravel.local.comboNhanVien,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText:2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#CapHocID",
        type: "POST",
        ajaxUrl: window.Laravel.local.getcaphoc,
        ajaxParam: {},
        columns: 1,
        indexValue: 2,
        indexText: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#CapHocID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.local.getcaphoc,
        ajaxParam: {},
        columns: 1,
        indexValue: 2,
        indexText: 1,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#LoaiPhoiVanBangChungChiID",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 1,
        indexValue: 2,
        indexText: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#MauVanBangChungChiID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getMauVanBangChungChiByLoai,
        ajaxParam: { LoaiPhoiVanBangChungChiID: "" },
        columns: 1,
        indexValue: 2,
        indexText: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#LoaiPhoiVanBangChungChiID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 1,
        indexValue: 2,
        indexText: 1,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#NamTotNghiep",
        type: "GET",
        ajaxUrl: window.Laravel.layouts.getDSNienDo,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 0,
        indexText1: 0,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#DonViID_Cap, #DonViID_Nhap,#DonViNhanID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListDonVi,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
      
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuID,#ChucVuID_PheDuyet",
        type: "GET",
        ajaxUrl: window.Laravel.local.listChucVuUrl,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });



}
$(document).on("change", "#LoaiPhoiVanBangChungChiID", function () {
    NTS.loadDataComboAsync({
        name: "#MauVanBangChungChiID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getMauVanBangChungChiByLoai,
        ajaxParam: { LoaiPhoiVanBangChungChiID: $("#LoaiPhoiVanBangChungChiID").value() },
        columns: 1,
        indexValue: 2,
        indexText: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });



});
$(document).on("change", "#DonViID_Nhap", function () {
      NTS.loadDataComboAsync({
        name: "#DonViID_YeuCau",
        type: "GET",
        ajaxUrl: window.Laravel.local.getAlldonxincapphoibang,
        ajaxParam: { DonViID_Nhap: $("#DonViID_Nhap").value() },
        columns: 1,
        indexValue: 0,
        indexText: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
});
async function LoadBieuDo() {
    $('#XepLoaiChart').html(``);
    var TongPhoi;
    var TongPhoiDaSuDung;
    var TongPhoiHuyBo;

    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getAllthongkevanbangdasudung",
        {
        }
    );
    if(result.Result.length > 0){
    for(var i = 0; i < result.Result.length; i++){
        if(result.Result[i].NoiDung =='Phôi đã sử dụng'){
            TongPhoiDaSuDung=result.Result[i].SoLuong ;
        }
        if(result.Result[i].NoiDung =='Tổng phôi'){
            TongPhoi=result.Result[i].SoLuong ;
        }
        if(result.Result[i].NoiDung =='Phôi hủy bỏ'){
             TongPhoiHuyBo=result.Result[i].SoLuong ;
        }
    }
}
$('#txtTongPhoi').text(TongPhoi)
$('#txtPhoiDaSuDung').text(TongPhoiDaSuDung)
$('#txtPhoiChuaSuDung').text(TongPhoi-TongPhoiDaSuDung)
$('#txtPhoiHuyBo').text(TongPhoiHuyBo)

// Lấy số liệu từ DOM và ép kiểu số nguyên
var daSuDung = parseInt($('#txtPhoiDaSuDung').text()) || 0;
var huyBo = parseInt($('#txtPhoiHuyBo').text()) || 0;
var tong = parseInt($('#txtTongPhoi').text()) || 0;

// Tính chưa sử dụng
var chuaSuDung = tong - daSuDung;

// Đảm bảo không âm
if (chuaSuDung < 0) chuaSuDung = 0;

// Gán dữ liệu vào series
var options = {
  series: [daSuDung, huyBo, chuaSuDung],
  chart: {
    type: 'donut',
    height: 150
  },
  labels: ['Phôi đã sử dụng', 'Phôi bị huỷ bỏ', 'Phôi chưa sử dụng'],
  colors: ['#7BA829', '#E53935', '#FB8C00'],
  stroke: {
    width: 5
  },
  legend: {
    position: 'right',
    fontSize: '13px',
    fontFamily: 'Arial'
  },
  tooltip: {
    enabled: false
  },
  dataLabels: {
    enabled: true,
    formatter: function (val) {
      return Math.round(val) + "%";
    },
    style: {
      fontSize: '12px',
      fontWeight: 'bold',
      colors: ['#ffffff'],
      fontFamily: 'Arial'
    }
  },
  plotOptions: {
    pie: {
      donut: {
        size: '70%',
        labels: {
          show: false
        }
      }
    }
  }
};

var chart = new ApexCharts(document.querySelector("#XepLoaiChart"), options);
chart.render();

}

//#region Thêm xóa sửa

$(document).on("click", "#btnThemMoi", function () {
    ThemDuLieu();
});
async function ThemDuLieu() {
    if (!QuyenThem()) {
        return;
    }
    $('#tbodyKetQuaLoaiVBCC').html(``)
    resetForm("#mdThemMoi");
    uploadedFileUrls = []; // reset uploaded files
    $("#list-file").empty();


    $("#mdThemMoi").modal("show");
    $("#tieuDeModal").text("Thêm mới thông tin nhập kho/cấp phát phôi bằng");
      $("#mdThemMoi").find("input, textarea").val('');
        $("#NhanVienID_Nhap").value(NhanVienID);
      $("#ChucVuID").value(ChucVuID);
    $("#NgayNhap").value(defaultDate);
    try {
        var result = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.local.maTuTangUrl,
            {}
        );
        debugger
        $("#SoHieuTu").value(result.SoChungTu);
        $("#SoHieuDen").value(addToCode(result.SoChungTu,1));
    } catch { }
        try {
        var result = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.local.soPhieuTuTangUrl,
            {}
        );
        $("#MaPhieu").value(result.SoChungTu);
    } catch { }
    $('#SoLuongNhap').val(1)

   
    tempthem = "them";
}
$(document).on('change', "#SoLuongNhap", function () {
    const fromCode = $("#SoHieuTu").val();
    let amount = parseInt($('#SoLuongNhap').val());

    if (isNaN(amount)) amount = 0;

    const newCode = addToCode(fromCode, amount);
    $("#SoHieuDen").val(newCode); // ✅ Corrected: use .val() not .value()
});

function addToCode(soHieuTu, soLuong) {
    // Tách phần chữ và phần số
    const match = soHieuTu.match(/^([A-Z]+)(\d+)$/);
    if (!match) return null;

    const prefix = match[1]; // 'VB'
    const so = match[2];     // '000001'

    const chieuDaiSo = so.length; // 6
    const soMoi = parseInt(so, 10) + soLuong;

    // Định dạng lại với số 0 ở đầu cho đúng độ dài ban đầu
    const soMoiStr = String(soMoi).padStart(chieuDaiSo, '0');

    return prefix + soMoiStr;
}

$("#btnLuuVaDong").on("click", async function () {
    const validate = new NTSValidate("#mdThemMoi");
    if (!validate.trim().check()) return false;


    const tbody = document.getElementById("tbodyKetQuaLoaiVBCC");
    let DieuKien = [];
    tbody.querySelectorAll("tr").forEach((row) => {
        let rowData = {};
        const tds = row.querySelectorAll("td");
        rowData["LoaiPhoiVBCCID"] = tds[1].querySelector("select") ? tds[1].querySelector("select").value : tds[1].textContent;
        rowData["DonViTinhID"] = tds[2].querySelector("select") ? tds[2].querySelector("select").value : tds[2].textContent;
        rowData["SoLuongPhoi"] = tds[3].querySelector("input") ? tds[3].querySelector("input").value : tds[3].textContent;
        rowData["TenLoaiPhoiVBCC"] = tds[1].querySelector("select")
        ? (tds[1].querySelector("select").options[tds[1].querySelector("select").selectedIndex].text.trim() === "-Chọn-" ? '' : tds[1].querySelector("select").options[tds[1].querySelector("select").selectedIndex].text.trim())
        : tds[1].textContent.trim();
        rowData["SoHieuTu"] = tds[4].querySelector("input") ? tds[4].querySelector("input").value : tds[4].textContent;
        rowData["SoHieuDen"] = tds[5].querySelector("input") ? tds[5].querySelector("input").value : tds[5].textContent;
        DieuKien.push(rowData);
    });
    const payload = {
        TiepNhanPhoiVBCCID: $("#TiepNhanPhoiVBCCID").value(),
        MaPhieu: $("#MaPhieu").value(),
        NgayNhap: $("#NgayNhap").value(),
        CapHocID: $("#CapHocID").value(),
        NhanVienID_Nhap: $("#NhanVienID_Nhap").value(),
        ChucVuID: $("#ChucVuID").value(),
        LoaiPhoiVanBangChungChiID: DieuKien,
        DonViID_Cap: $("#DonViID_Cap").value(),
        DonViID_Nhap: $("#DonViID_Nhap").value(),
        DonViID_YeuCau: $("#DonViID_YeuCau").value(),
        SoLuongNhap: tinhTong(),
        SoHieuTu: $("#SoHieuTu").value(),
        SoHieuDen: $("#SoHieuDen").value(),
        GhiChu: $("#GhiChu").value(),
        txtDuongDanFileVB: $("#txtDuongDanFileVB").value()
    };
    var met = "POST";
    if (tempthem == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }
    var result = await NTS.getAjaxAPIAsync(
        met,
        window.location.pathname + "/luuthongtin",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdThemMoi").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});
async function SuaThongTin(id) {
    if (!QuyenSua()) { return; }
    resetForm("#mdThemMoi");
    $("#tieuDeModal").text("Cập nhật thông tin nhập kho/cấp phát phôi bằng");
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        {
            id: id,
        }
    );
    if (!result.Err) {
        let data = result.result[0];
        $("#TiepNhanPhoiVBCCID").val(data.id);
        $("#MaPhieu").value(data.MaPhieu)
        $("#NgayNhap").value(data.NgayNhapConVer)
        $("#NhanVienID_Nhap").value(data.NhanVienID_Nhap)
        $("#ChucVuID").value(data.ChucVuID)
        var LoaiPhoiVanBangChungChiID= data.LoaiPhoiVanBangChungChiID;
        $('#tbodyKetQuaLoaiVBCC').html(``);
        for(var i = 0; i < LoaiPhoiVanBangChungChiID.length; i++){
            var guid = crypto.randomUUID();
            var htmlTr=`<tr class="nts-table-tr" id="tr`+guid+`">
                            <td class="nts-table-td " colspan="0" rowspan="1">
                                <label class="nts-table-tile-font-size XoaLoaiVBCC"><i class="fa fa-minus-circle" style="color: #fd0909;font-size: 18px "></i></label>
                            </td>
                            <td class="nts-table-td " colspan="0" rowspan="1">
                                <select class="form-control selectLoaiChungTu" data="`+guid+`" tabindex="0" id="selectLoaiChungTu`+guid+`" >
                                </select>
                            </td>
                            <td class="nts-table-td " colspan="0" rowspan="1">
                                <select class="form-control selectdonvitinh" data="`+guid+`" tabindex="0" id="selectdonvitinh`+guid+`" >
                                </select>
                            </td>
                            <td class="nts-table-td " colspan="0" rowspan="1">
                                <input type="number" class="form-control inputsoluong" id="inputsoluong`+guid+`" data="`+guid+`" style="text-align:center" autocomplete="off" value="0">
                            </td>
                             <td class="nts-table-td " colspan="0" rowspan="1">
                                <input type="text" class="form-control inputsohieutu" id="inputsohieutu`+guid+`" data="`+guid+`" style="text-align:center" autocomplete="off" value="0">
                            </td>
                             <td class="nts-table-td " colspan="0" rowspan="1">
                                <input type="number" class="form-control inputsohieuden" id="inputsohieuden`+guid+`" data="`+guid+`" style="text-align:center" autocomplete="off" value="0">
                            </td>
                        </tr>  `;
            $('#tbodyKetQuaLoaiVBCC').append(htmlTr);
            await NTS.loadDataComboAsync({
                name: "#selectLoaiChungTu"+guid,
                type: "POST",
                ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
                ajaxParam: {},
                columns: 1,
                indexValue: 2,
                indexText: 1,
                textShowTatCa: "-Chọn-",
                showTatCa: !0,
            }).then(async ()=>{
                 if (LoaiPhoiVanBangChungChiID[i].LoaiPhoiVBCCID) {
                    $('#selectLoaiChungTu'+guid).value(LoaiPhoiVanBangChungChiID[i].LoaiPhoiVBCCID)
                }
            });
           await NTS.loadDataComboAsync({
                name: "#selectdonvitinh"+guid,
                type: "GET",
                ajaxUrl: window.Laravel.local.getListDonViTinh,
                ajaxParam: {},
                columns: 1,
                indexValue: 0,
                indexText: 2,
                textShowTatCa: "-Chọn-",
                showTatCa: !0,
            }).then(async ()=>{
                  if (LoaiPhoiVanBangChungChiID[i].DonViTinhID) {
                        $('#selectdonvitinh'+guid).value(LoaiPhoiVanBangChungChiID[i].DonViTinhID)
                    }
            });
            if (LoaiPhoiVanBangChungChiID[i].SoLuongPhoi) {
                $('#inputsoluong'+guid).value(LoaiPhoiVanBangChungChiID[i].SoLuongPhoi)
            }
              if (LoaiPhoiVanBangChungChiID[i].SoHieuTu) {
                $('#inputsohieutu'+guid).value(LoaiPhoiVanBangChungChiID[i].SoHieuTu)
            }
              if (LoaiPhoiVanBangChungChiID[i].SoHieuDen) {
                $('#inputsohieuden'+guid).value(LoaiPhoiVanBangChungChiID[i].SoHieuDen)
            }
        }


        $("#DonViID_Cap").value(data.DonViID_Cap)
        $("#DonViID_Nhap").value(data.DonViID_Nhap)
        $("#DonViID_YeuCau").value(data.DonViID_YeuCau)
        $("#SoLuongNhap").value(data.SoLuongNhap)
        $("#SoHieuTu").value(data.SoHieuTu)
        $("#SoHieuDen").value(data.SoHieuDen)
        $("#GhiChu").value(data.GhiChu)
        

        uploadedFileUrls = data.DinhKem
            ? data.DinhKem.split("|").filter((u) => u)
            : [];

        $("#txtDuongDanFileVB").value(data.DinhKem);
        $("#list-file").empty();

        uploadedFileUrls.forEach((url) => {
            renderAttachment(url);
        });
        tempthem = "sua";
        $("#mdThemMoi").modal("show");
    }
    return false;
}
async function XemThongTin(id) {
    if (!QuyenSua()) { return; }
    $("#tieuDeModalXem").text("Xem thông tin tiếp nhận phôi bằng");
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        {
            id: id,
        }
    );
    if (!result.Err) {
        let data = result.Result;
        $("#lblSoPhieu").html(" Số phiếu:<b> " + data.SoPhieu + "</b>");
        $("#lblNgayTiepNhan").html(" Ngày tiếp nhận:<b> " + data.txtNgayTiepNhan + "</b>");
        $("#lblNhanVienID_TiepNhan").html("Nhân viên tiếp nhận:<b> " + data.TenNhanVien + "</b>");
        $("#lblNguoiGiao").html("Người giao:<b> " + data.NguoiGiao + "</b>");
        $("#lblDonViGiao").html("Đơn vị giao:<b> " + data.DonViGiao + "</b>");
        $("#lblSoVanBanGiao").html(" Số văn bản giao:<b> " + data.SoVanBanGiao + "</b>");
        $("#lblCapHocID").html(" Cấp học:<b> " + data.TenCapHoc + "</b>");
        $("#lblLoaiPhoiVanBangChungChiID").html("Loại phôi văn bằng chứng chỉ:<b> " + data.TenLoaiPhoiVanBangChungChi + "</b>");
        $("#lblNamTotNghiep").html("Năm tốt nghiệp:<b> " + data.NamTotNghiep + "</b>");
        $("#lblSoSeri").html("Số seri:<b> " + data.SoSeriBatDau + " - " + data.SoSeriKetThuc + "</b>");
        $("#lblSoLuongNhan").html("Số lượng nhận:<b> " + data.SoLuongNhan + "</b>");
        $("#lblSoLuongHopLe").html("Số lượng hợp lệ:<b> " + data.SoLuongHopLe + "</b>");
        $("#lblSoLuongLoi").html("Số lượng lỗi:<b> " + data.SoLuongLoi + "</b>");
        $("#lblGhiChu").html("Ghi chú: <b>" + data.GhiChu + "</b>");
        //$("#txtDuongDanFileVB").text(data.txtDuongDanFileVB || "");
        $("#mdXemThongTin").modal("show");
    }
    return false;
}
async function XoaThongTin(id) {
    if (!QuyenXoa()) { return; }
    var result_ktxoa = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.layouts.KiemTraXoa,
        {
            ma: id,
            model: "QuanLy\\TiepNhanPhoiVBCC",
        }
    );

    if (!result_ktxoa.Err) {
        CanhBaoXoa(async () => {
            var result = await NTS.getAjaxAPIAsync(
                "DELETE",
                window.location.pathname + "/xoa",
                { ma: id }
            );
            if (!result.Err) {
                LoadDataTable();
                NTS.thanhcong(result.Msg);
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
    } else {
        NTS.canhbao(result_ktxoa.Msg);
        return false;
    }
}
//#region Lưới 1
function htmlDuLieu(cell, formatterParams, onRendered) {
    let anhDaiDien = "";
    anhDaiDien =
        `<img src="${window.Laravel.local.imgLuoi}" alt="` +
        cell.getData().SoPhieu +
        `" class="img-thumbnail rounded lazy">`;
    return `<div class="list-item col-md-12" style="padding: 0px;">
                        <div class="card card-luoi shadow-sm mb-2 ">
                            <div id="card_${cell.getData().id
        }" class="card-body profile-user-box">
                                <div class="row">
                                    <div class="col-sm-2 text-center" style=" width: 18%; " >
                                        <div class="profile-picture" style=" height: 125px; ">
                                            ${anhDaiDien}
                                        </div>
                                            <span style="
                                            display: block;
                                            margin-top: 10px;
                                            font-weight: bold;
                                            font-size: 1rem;
                                            text-align: center;
                                            padding: 6px 0;
                                            background-color: ${cell.getData().MauSac};
                                            color: white;
                                            border-radius: 6px;

                                            ">
                                             ${cell.getData().TenTrangThai}
                                        </span>
                                    </div>
                                    <div class="col-sm-10" style=" width: 82%; ">

                                        <div class="row">
                                            <div class="col-md-3">
                                                <p class="fs-big my-1">Mã phiếu: <b>${cell.getData()
            .MaPhieu ||
        ChuaCoThongTin
        }</b></p>
                                            </div>
                                            <div class="col-md-3">
                                                <p class="fs-big my-1">Ngày nhập: <b>${cell.getData().NgayNhapConVer ||
        ChuaCoThongTin
        }</b></p>
                                            </div>
                                            <div class="col-md-3">
                                                <p class="fs-big my-1">Người nhập: <b>${cell.getData().TenNhanVien ||
        ChuaCoThongTin
        }</b></p>
                                            </div>
                                                 <div class="col-md-3">
                                                <p class="fs-big my-1">Chức vụ: <b>${cell.getData().TenChucVu ||
        ChuaCoThongTin
        }</b></p>
                                            </div>
                                        </div>
                                         <hr style="border: 1px solid var(--main-color);    margin: 0.5rem 0;"/>
                                        <div class="row">

                                                  <div class="col-md-6">
                                                <p class="fs-big my-1">Đơn vị cấp: <b>${cell.getData().TenDonViCap ||
        ChuaCoThongTin
        }</b></p>
                                            </div>

                                                 <div class="col-md-3" style="display: none;">
                                                <p class="fs-big my-1">Cấp học: <b>${cell.getData().TenCapHoc ||
        ChuaCoThongTin
        }</b></p>
                                            </div>

                                                 <div class="col-md-6" style="display: none;">
                                                <p class="fs-big my-1">Số lượng: <b>${cell.getData().SoLuongNhap ||
        ChuaCoThongTin
        }</b></p>
                                            </div>


 <div class="col-md-6">
                                                <p class="fs-big my-1">Đơn vị nhận: <b>${cell.getData().TenDonViNhap ||
        ChuaCoThongTin
        }</b></p>
                                            </div>
                                        </div>

                                        <div class="row" style="display: none;">

                                       <div class="col-md-3">
                                                <p class="fs-big my-1">Số hiệu: <b>${cell.getData().SoHieuTu+' - '+cell.getData().SoHieuDen ||
        ChuaCoThongTin
        }</b></p>
                                            </div>

                                        </div>
                                        <div class="row">

                                                <div class="col-md-6">
                                                <p class="fs-big my-1">Ghi chú: <b>${cell.getData().GhiChu ||
        ChuaCoThongTin
        }</b></p>
                                            </div>


                                           <div class="col-md-3">
                                                <p class="fs-big my-1">Đính kèm: <b> <span id="lblDinhKem_View"><a href="#" data="${cell.getData().DinhKem}" onclick="XemDinhKem_us('${cell.getData().DinhKem  ||
        ChuaCoThongTin
        }')" data="">Xem đính kèm</a></span></b></p>
                                            </div>
                                        </div>

                                        <hr style="border: 1px solid var(--main-color);    margin: 0.5rem 0;"/>


                                        <div class="row">
                                            <div class="col-md-3">
                                                <p class="fs-big my-1">Ngày phê duyệt: <b>${cell.getData().NgayPheDuyetConVer ||
        ChuaCoThongTin
        }</b></p>
                                            </div>
                                             <div class="col-md-3">
                                                <p class="fs-big my-1">Người phê duyệt: <b>${cell.getData().TenNhanVienPheDuyet ||
        ChuaCoThongTin
        }</b></p>
                                            </div>
                                             <div class="col-md-3">
                                                <p class="fs-big my-1">Chức vụ: <b>${cell.getData().TenChucVuPheDuyet ||
        ChuaCoThongTin
        }</b></p>
                                            </div>
                                        </div>

                                          <div class="row">
                                            <div class="col-md-12">
                                                <p class="fs-big my-1">Nội dung phê duyệt: <b>${cell.getData().NoiDung_PheDuyet ||
        ChuaCoThongTin
        }</b></p>
                                            </div>
                                        </div>
     <div class="dropdown text-end" style="position: absolute; top: 10px; right: 10px">
                                            <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                                            </button>
                                            <div class="dropdown-menu dropdown-menu-end w-auto" >
                                                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '32' ? 'display: none;' :'' }" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="SuaThongTin('${cell.getData().id}'); return false;"><i class=" text-primary fa fa-pencil-square-o iconsize-item"></i>&ensp; Chỉnh sửa phiếu nhập kho</a>
                                                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '32' ? 'display: none;' :'' }" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="PheDuyetThongTin('${cell.getData().id}','${cell.getData().TenDonViNhap}','${cell.getData().MaPhieu}','${cell.getData().NgayNhapConVer}'); return false;"><i class="fas fa-check-circle text-success" aria-hidden="true"></i>&ensp; Phê duyệt phiếu nhập kho</a>
                                                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '0' || cell.getData().TrangThai == '33' ? 'display: none;' :'' }" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="ThuHoiThongTin('${cell.getData().id}'); return false;"><i  class="fas fa-rotate-left text-primary" aria-hidden="true"></i>&ensp; Thu hồi phiếu nhập kho</a>
                                                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '32' ? 'display: none;' :'' }"" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XoaThongTin('${cell.getData().id }')"><i class="fa fa-trash-o iconsize-item text-danger"></i>&ensp; Xóa thông tin phiếu nhập kho</a>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>`;
}
var table = new Tabulator("#GridMainDS", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "600",
    HeaderVertAlign: "center",
    headerVisible: false,
    columns: [
        {
            title: "Thông tin",
            field: "",
            formatter: htmlDuLieu,
            visible: true,
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

async function LoadDataTable() {
    table.clearData();
    GridMainLuoi.clearData();

    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
        {
            SearchKey: $("#SearchKey").value(),
            TuNgay_Loc: $("#TuNgay_Loc").value(),
            DenNgay_Loc: $("#DenNgay_Loc").value(),
            CapHocID_Loc: $("#CapHocID_Loc").value(),
            LoaiPhoiVanBangChungChiID_Loc: $("#LoaiPhoiVanBangChungChiID_Loc").value(),
            DonViNhanID_Loc: $("#DonViNhanID_Loc").value(),
            TrangThai_Loc: $("#TrangThai_Loc").val(),
            CbSapXep: $("#CbSapXep").value(),
        }
    );
    if (!result.Err) {
      LoadBieuDo()
 var group = {};


result.result.forEach(function (item) {
    if (Array.isArray(item.LoaiPhoiVanBangChungChiID)) {
        item.LoaiPhoiVanBangChungChiID.forEach(function (entry) {
            let ten = entry.TenLoaiPhoiVBCC?.trim();
            let soLuong = parseFloat(entry.SoLuongPhoi) || 0;

            // Bỏ qua nếu không có tên hoặc là "-Chọn-"
            if (!ten || ten === "-Chọn-") return;

            group[ten] = (group[ten] || 0) + soLuong;
        });
    }
});
  // Xóa nội dung cũ (nếu có)
  $('#LisdSoLuongPhoi').html('');
  // Duyệt và gắn từng dòng vào
  $.each(group, function (ten, soluong) {
    var color = '#7AA802'; // màu tùy loại
    var icon = `<svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17"
                                        fill="none">
                                        <path
                                            d="M2.73756 2.05261C1.97809 2.05261 1.36914 2.66156 1.36914 3.42103V10.2631C1.36914 10.6261 1.51331 10.9741 1.76994 11.2308C2.02657 11.4874 2.37463 11.6316 2.73756 11.6316H8.21125V15.0526L10.2639 13L12.3165 15.0526V11.6316H13.6849C14.0479 11.6316 14.3959 11.4874 14.6525 11.2308C14.9092 10.9741 15.0534 10.6261 15.0534 10.2631V3.42103C15.0534 3.05811 14.9092 2.71004 14.6525 2.45341C14.3959 2.19678 14.0479 2.05261 13.6849 2.05261H2.73756ZM8.21125 3.42103L10.2639 4.78945L12.3165 3.42103V5.81577L14.3691 6.84209L12.3165 7.8684V10.2631L10.2639 8.89472L8.21125 10.2631V7.8684L6.15861 6.84209L8.21125 5.81577V3.42103ZM2.73756 3.42103H6.15861V4.78945H2.73756V3.42103ZM2.73756 6.15788H4.79019V7.5263H2.73756V6.15788ZM2.73756 8.89472H6.15861V10.2631H2.73756V8.89472Z"
                                            fill="#7AA802" />
                                    </svg>`; // hoặc SVG nếu muốn

    var html = `
      <h3 style="color: ${color}" class="card-title">
        ${icon} ${ten}: <b><u><b>${soluong}</b></u></b>
      </h3>
    `;

    $('#LisdSoLuongPhoi').append(html);
  });
$('#txtTongHS').text(result.result.length)
        table.setData(result.result);
        GridMainLuoi.setData(result.result);
    } else {
        table.setData(null);
        GridMainLuoi.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}
//#region Lưới 2
function actionDropdownFormatter(cell) {
    var ID = cell.getData().id;
    var button = document.createElement("button");
    button.className = "btn btn-sm btn-white dropdown-toggle-hide-arrow";
    button.innerHTML = `<i class="fa fa-ellipsis-h" style="color: var(--primary)"></i>`;
    button.style.boxShadow = "none";

    button.onclick = function (e) {
        e.stopPropagation(); // Không lan click ra ngoài

        // Đóng dropdown cũ nếu có
        document.querySelectorAll('.custom-dropdown-menu').forEach(el => el.remove());

        // Tạo dropdown mới
        const dropdown = document.createElement("div");
        dropdown.className = "custom-dropdown-menu dropdown-menu dropdown-menu-end show";
        dropdown.style.position = "absolute";
        dropdown.style.zIndex = 9999;
        dropdown.style.minWidth = "200px";
        dropdown.innerHTML = `
                                                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '32' ? 'display: none;' :'' }" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="SuaThongTin('${cell.getData().id}'); return false;"><i class=" text-primary fa fa-pencil-square-o iconsize-item"></i>&ensp; Chỉnh sửa phiếu nhập kho</a>
                                                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '32' ? 'display: none;' :'' }" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="PheDuyetThongTin('${cell.getData().id}','${cell.getData().TenDonViNhap}'); return false;"><i class="fas fa-check-circle text-success" aria-hidden="true"></i>&ensp; Phê duyệt phiếu nhập kho</a>
                                                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '0' || cell.getData().TrangThai == '33' ? 'display: none;' :'' }" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="ThuHoiThongTin('${cell.getData().id}'); return false;"><i  class="fas fa-rotate-left text-primary" aria-hidden="true"></i>&ensp; Thu hồi phiếu nhập kho</a>
                                                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '32' ? 'display: none;' :'' }"" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XoaThongTin('${cell.getData().id }')"><i class="fa fa-trash-o iconsize-item text-danger"></i>&ensp; Xóa thông tin phiếu nhập kho</a>
                                    
               `;

        // Tính vị trí button
        const rect = button.getBoundingClientRect();
        dropdown.style.left = `${rect.left + window.scrollX}px`;
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;

        // Gắn ra body
        document.body.appendChild(dropdown);
    };

    return button;
}
var GridMainLuoi = new Tabulator("#GridMainLuoi", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: actionDropdownFormatter,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false,
        },
        {
            title: "Mã nhập kho",
            field: "MaPhieu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Ngày nhập",
            field: "NgayNhapConVer",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Người nhập",
            field: "TenNhanVien",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Chức vụ",
            field: "TenChucVu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Cấp học",
            field: "TenCapHoc",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Số lượng",
            field: "SoLuongNhap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Loại phôi",
            field: "TenLoaiPhoiVanBangChungChi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 200,
        },
        {
            title: "Đơn vị nhận",
            field: "TenDonViNhap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Trạng thái",
            field: "TrangThai",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Ghi chú",
            field: "GhiChu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        }

    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
//#region Đính kèm
let uploadedFileUrls = [];

$("#btnChonTepVB_us").on("click", function () {
    $("#fileVB_us").trigger("click");
});

$("#fileVB_us").on("change", async function () {
    const files = Array.from(this.files);
    $("#list-file").empty();

    await NTS.getAjaxAPIAsync("POST", "/api/dungchung/files/clearTemp", {
        chucnang: "TiepNhanPhoiVBCC"
    });
    for (const file of files) {
        const form = new FormData();
        form.append("file", file);
        form.append("chucnang", "TiepNhanPhoiVBCC");
        try {
            const res = await NTS.getAjaxAPIAsync("POST",
                "/api/dungchung/files/uploadFileTemp",
                form,
            );
            if (!res.success) {
                NTS.loi(`Tải lên thất bại: ${file.name}`);
                continue;
            }
            // collect the returned URLs array (or wrap single URL)
            const url = Array.isArray(res.url) ? res.url[0] : res.url;
            uploadedFileUrls.push(url);
        } catch (err) {
            console.error("Upload error", err);
            NTS.loi(`Lỗi upload ${file.name}`);
        }
    }

    // 2) Render previews by iterating uploadedFileUrls
    uploadedFileUrls.forEach((url) => {
        renderAttachment(url);
    });

    const ttList = [].slice.call(
        document.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    ttList.forEach((el) => new bootstrap.Tooltip(el));

    $("#txtDuongDanFileVB").val(uploadedFileUrls.join("|"));
});

$("#btnXoaHetTepVB_us").on("click", async function () {
    CanhBaoXoa(async () => {
        $("#fileVB_us").val("");
        $("#list-file").empty();
        $("#txtDuongDanFileVB").val("");

        try {
            // call delete-multiple with our URL array
            if (uploadedFileUrls.length !== 0) {
                const res = await NTS.getAjaxAPIAsync("DELETE",
                    "/api/dungchung/files/delete-multiple",
                    { urls: uploadedFileUrls }

                );
                if (!res.success) {
                    return NTS.loi("Xóa tất cả thất bại: " + (res.message || ""));
                }
                if (res.loi) {
                    return NTS.loi("Xóa tất cả thất bại: " + (res.message || ""));
                }

                NTS.thanhcong("Xoá tất cả đính kèm thành công");
            }
        } catch (err) {
            console.error("Delete all error", err);
            NTS.loi("Có lỗi khi xóa tất cả đính kèm");
        } finally {
            uploadedFileUrls = []; // reset URL store
        }
    });
});
function renderAttachment(url) {
    const filename = url.split("/").pop();

    // 2) get the extension (e.g. "jpg")
    const ext = filename.split(".").pop().toLowerCase();

    // 3) decide if it’s an image
    const imageExts = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
    const isImage = imageExts.includes(ext);

    const $item = $(`
            <div class="file-preview position-relative d-inline-block text-center me-2 mb-2">
            <!-- delete button -->
                <button type="button" class="btn-close position-absolute top-0 end-0 m-1" aria-label="Delete"></button>
            </div>
              `);

    // image vs icon
    let $thumb;
    if (isImage) {
        $thumb = $(`
            <img class="img-thumbnail"
                 style="width:100px;height:100px;object-fit:cover;"
                 src="${url}">
          `);
    } else {
        $thumb = $(`
            <div class="file-icon bg-secondary text-white rounded
                        d-flex align-items-center justify-content-center mb-1"
                 style="width:100px;height:100px;font-size:2rem;">
              <i class="fa fa-file"></i>
            </div>
          `);
    }

    // 1) native tooltip
    $thumb.attr("title", filename);

    // 2) (optional) Bootstrap tooltip
    $thumb.attr("data-bs-toggle", "tooltip").attr("data-bs-placement", "top");

    // assemble
    $item.append($thumb);
    $item.append(
        $(
            '<a target="_blank" class="d-block small text-truncate" style="max-width:100px;"></a>'
        )
            .attr("href", url)
            .text(filename)
    );
    $("#list-file").append($item);

    $item.find(".btn-close").on("click", async () => {
        try {
            //call delete-multiple with a JSON array of this single URL
            const res = await NTS.getAjaxAPIAsync("DELETE",
                "/api/dungchung/files/delete-multiple",
                { urls: [url] }
            );
            if (!res.success) {
                return NTS.loi("Xóa file thất bại: " + filename);
            }
            // remove from uploadedFileUrls
            uploadedFileUrls = uploadedFileUrls.filter((u) => u !== url);
            // update hidden field
            // remove preview from DOM
            $item.remove();
            $("#txtDuongDanFileVB").val(uploadedFileUrls.join("|"));
        } catch (err) {
            console.error("Delete error", err);
            NTS.loi("Lỗi khi xóa " + filename);
        }
    });
}
//#endregion
function PheDuyetThongTin(id,DonViNhan,Maphieu,NgayNhap){
    $("#NgayPheDuyet").val(defaultDate )
    $("#TiepNhanPhoiVBCCID").val(id)
    $('#SoDeNghiPD').text(Maphieu)
    $('#NgayDeNghiPD').text(NgayNhap)
    $('#DonViNhan').text(DonViNhan)
    $('#DonViDeNghiPD').text(DonViNhan)
    $("#TiepNhanPhoiVBCCID").val(id)
    $("#NhanVienID_PheDuyet").value(NhanVienID);
    $("#ChucVuID_PheDuyet").value(ChucVuID);
    $("#NoiDung_PheDuyet").text(`Phê duyệt tiếp nhận phôi văn bằng, chứng chỉ số: ${Maphieu} ngày lập ${NgayNhap} của ${DonViNhan}.`);
    $('#mdQPheDuyet').modal('show')
}
async function ThuHoiThongTin(id){
    $("#TiepNhanPhoiVBCCID").val(id)
    CanhBaoThuHoiDuyet(async () => {
            const payload = {
                TiepNhanPhoiVBCCID:  $("#TiepNhanPhoiVBCCID").val(),
                NhanVienID_PheDuyet:'',
                ChucVuID_PheDuyet:'',
                NoiDung_PheDuyet:'',
                NgayPheDuyet:'',
                TrangThai:"33" // thu hồi
            };

            var result = await NTS.getAjaxAPIAsync(
                "post",
                window.location.pathname + "/PheDuyet",
                payload
            );

            if (!result.Err) {
                LoadDataTable();
            NTS.thanhcong(result.Msg);
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
}
$("#btnBanHanh").on("click", async function () {
     const validate = new NTSValidate("#mdQPheDuyet");
    if (!validate.trim().check()) return false;
    const payload = {
        TiepNhanPhoiVBCCID:  $("#TiepNhanPhoiVBCCID").val(),
        NhanVienID_PheDuyet:$("#NhanVienID_PheDuyet").value(),
        ChucVuID_PheDuyet:$("#ChucVuID_PheDuyet").value(),
        NoiDung_PheDuyet:$("#NoiDung_PheDuyet").text(),
        NgayPheDuyet:$("#NgayPheDuyet").value(),
        TrangThai:"32" // phê duyệt
    };

    var result = await NTS.getAjaxAPIAsync(
        "post",
        window.location.pathname + "/PheDuyet",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdQPheDuyet").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});



$(document).on('keyup', '#SearchKey', function (e) {
    if (e.keyCode == '13') {
        GridMainLuoi.setFilter(matchAny, { value: $(this).val() });
        table.setFilter(matchAny, { value: $(this).val() });
    }
});
$(document).on("click", "#btnAnHienTQ", function () {
    var divThongKe = document.getElementById("DivThongKe");
    var textAnHien = document.getElementById("textAnHien");
    var iconAnHien = document.getElementById("iconAnHien");
    if (iconAnHien.className == "fa fa-eye-slash") {
        divThongKe.classList.add("d-none");
        iconAnHien.className = "fa fa-eye";
        textAnHien.textContent = " Hiện trang tổng quan";
    } else {
        divThongKe.classList.remove("d-none");
        iconAnHien.className = "fa fa-eye-slash";
        textAnHien.textContent = " Ẩn trang tổng quan";
    }
});


function ThemDieuKienXepLoai(){
    var guid = crypto.randomUUID();
    var htmlTr=`<tr class="nts-table-tr" id="tr`+guid+`">
                    <td class="nts-table-td " colspan="0" rowspan="1">
                        <label class="nts-table-tile-font-size XoaLoaiVBCC"><i class="fa fa-minus-circle" style="color: #fd0909;font-size: 18px "></i></label>
                    </td>
                    <td class="nts-table-td " colspan="0" rowspan="1">
                        <select class="form-control selectLoaiChungTu" data="`+guid+`" tabindex="0" id="selectLoaiChungTu`+guid+`" >
                        </select>
                    </td>
                    <td class="nts-table-td " colspan="0" rowspan="1">
                        <select class="form-control selectdonvitinh" data="`+guid+`" tabindex="0" id="selectdonvitinh`+guid+`" >
                        </select>
                    </td>
                    <td class="nts-table-td " colspan="0" rowspan="1">
                        <input type="number" class="form-control inputsoluong" data="`+guid+`" style="text-align:center" autocomplete="off" value="0">
                    </td>

                     <td class="nts-table-td " colspan="0" rowspan="1">
                        <input type="text" class="form-control inputsohieutu" data="`+guid+`" style="text-align:center" autocomplete="off" >
                    </td>

                     <td class="nts-table-td " colspan="0" rowspan="1">
                        <input type="text" class="form-control inputsohieuden" data="`+guid+`" style="text-align:center" autocomplete="off">
                    </td>
                </tr>  `;

    $('#tbodyKetQuaLoaiVBCC').append(htmlTr);
    NTS.loadDataComboAsync({
        name: "#selectLoaiChungTu"+guid,
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 1,
        indexValue: 2,
        indexText: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#selectdonvitinh"+guid,
        type: "GET",
        ajaxUrl: window.Laravel.local.getListDonViTinh,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
}
$(document).on("click", ".XoaLoaiVBCC", function () {
    $(this).parent().parent().remove();
});


function tinhTong() {
    let tong = 0;
    const inputs = document.querySelectorAll('.inputsoluong');
    inputs.forEach(function(input) {
        let giatri = parseFloat(input.value);
        if (!isNaN(giatri)) {
            tong += giatri;
        }
    });
    return tong;
}