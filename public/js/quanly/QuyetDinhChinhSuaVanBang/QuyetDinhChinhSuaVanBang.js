var ChuaCoThongTin = "";
var tempthem = "them";
var enumModeThemHS = {
    ThemNhieu: 1
};
var table;
var tabGridHocSinh;
var selectedId;
var modeThemHS = 0;

$( function() {
    loadCombo();
    LoadDataTable();
});

 $(document).on('keydown', function (e) {
        switch (e.keyCode) {
            case 113: //f2
                if (!$('#mdThemMoi').hasClass('show')) {
                    $('#btnThemMoi').trigger('click');
                }

                break;
            case 115: //f4
                if ($('#mdThemMoi').hasClass('show')) {
                    $('#mdThemMoi').modal('hide');
                }
                else if (!$('#mdQuyetDinhTotNghiep').hasClass('show')) {
                    $('#mdQuyetDinhTotNghiep').modal('hide');
                }
                else if (!$('#mdThuHoiQD').hasClass('show')) {
                    $('#mdThuHoiQD').modal('hide');
                }else if (!$('#mdXemDinhKem_us').hasClass('show')) {
                    $('#mdXemDinhKem_us').modal('hide');
                }
                break;
            case 120: //f9
                if ($('#mdThemMoi').hasClass('show')) {
                    $('#btnLuuVaDong').trigger('click');
                }
                else if ($('#mdQuyetDinhTotNghiep').hasClass('show')) {
                    $('#btnBanHanhQD').trigger('click');
                }
                else if ($('#mdThuHoiQD').hasClass('show')) {
                    $('#btnThuHoiQD').trigger('click');
                }
                break;
        }
    });


function loadCombo(){

     NTS.loadDataCombo({
        name: "#LoaiPhoiID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.quyetdinhchinhsuavanbang.GetComBoLoaiPhoi,
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "--Tất cả--",
        showTatCa: !0,
    });

     NTS.loadDataCombo({
        name: "#TrangThaiID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.quyetdinhchinhsuavanbang.GetComBoTrangThai,
        columns: 2,
        indexValue: 0,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "--Tất cả--",
        showTatCa: !0,
    });

    NTS.loadDataCombo({
        name: "#NguoiKyID,#NguoiBanHanhID,#NguoiThuHoiBanHanhID",
        type: "GET",
        ajaxUrl: window.Laravel.quyetdinhchinhsuavanbang.GetComBoNhanVien,
        columns: 2,
        indexValue: 0,
        indexText: 1,
        indexText1: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });

    NTS.loadDataCombo({
        name: "#ChucVuID,#ChucVuID_NBH,#ChucVuID_NTH",
        type: "POST",
        ajaxUrl: window.Laravel.quyetdinhchinhsuavanbang.GetComBoChucVu,
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
}

$(document).on("keyup", "#SearchKey", async function (e) {
    if (e.keyCode == "13") {
        await LoadDataTable();
        $("#KhungTimKiem").slideUp(200);
        return false;
    }
});

$(document).on("click", "#TimKiemNangCao", function () {
    if ($("#KhungTimKiem").css("display") == "block") {
        $("#KhungTimKiem").slideUp(200);
    } else {
        $("#KhungTimKiem").slideDown(200);
    }
    return false;
});

$(document).on("click", "#DongTimKiem", function () {
    $("#KhungTimKiem").slideUp(200);
    return false;
});
$(document).on("click", "#TimKiem", async function () {
    $("#KhungTimKiem").slideUp(200);
    await LoadDataTable();
    return false;
});

async function LoadDataTable() {
    GridMainDS.clearData();
    GridMainLuoi.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
        {
            SearchKey: $("#SearchKey").value(),
            TuNgay_Loc: $("#TuNgay_Loc").value(),
            DenNgay_Loc: $("#DenNgay_Loc").value(),
            TrangThaiID_Loc: $("#TrangThaiID_Loc").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            CbSapXep: $("#CbSapXep").value(),
        }
    );
    debugger
    if (!result.Err) {
        GridMainDS.setData(result.result);
       GridMainLuoi.setData(result.result);
       $('#lblSoLuongKetQua').text(result.result.length);
    } else {
        GridMainDS.setData(null);
       $('#lblSoLuongKetQua').text("...");
        GridMainLuoi.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}
async function LoadDataTable_Tabu() {
    GridMainLuoi.clearData();

    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
        {
            SearchKey: $("#SearchKey").value(),
            TuNgay_Loc: $("#TuNgay_Loc").value(),
            DenNgay_Loc: $("#DenNgay_Loc").value(),
            TrangThaiID_Loc: $("#TrangThaiID_Loc").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            CbSapXep: $("#CbSapXep").value(),
        }
    );

    if (!result.Err) {
        const data = result.result;
        GridMainLuoi.setData(data);
        $('#lblSoLuongKetQua').text(data.length);
    } else {
        GridMainLuoi.setData([]);
       $('#lblSoLuongKetQua').text("...");
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

function htmlDuLieu(cell, formatterParams, onRendered) {
    let anhDaiDien = "";
    let url_anhDaiDien = "img/anhhoso.png";

    const data = cell.getData();
    const baseAsset = ''; // bỏ dấu "/" cuối nếu có
    let path = data.AnhDaiDien?.replaceAll("~", "").replaceAll("*", "") || "";
    path = path.replace(/^\//, ""); // bỏ dấu "/" đầu nếu có

    if (!path) {
        url_anhDaiDien = window.Laravel.quyetdinhchinhsuavanbang.imgLuoi;
    } else {
        url_anhDaiDien = `${baseAsset}/${path}`; // DÙNG path đã xử lý
    }

    anhDaiDien = `<img src="${url_anhDaiDien}" alt="${data.Hovaten}" class="img-thumbnail rounded lazy">`;

    return `<div class="list-item col-md-12" style="padding: 0px;">
    <div class="card card-luoi shadow-sm mb-2">
        <div class="card-body profile-user-box" style="position: relative;">
            <div class="row">
                <!-- Phần ảnh đại diện -->
                <div class="col-12 col-xs-6 col-sm-2 center" style="width:14%;display: flex;flex-direction: column;align-items: center;justify-content: space-around;">
                    <div class="profile-picture" style="width:137px; height:118px;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="138" height="119" viewBox="0 0 138 119" fill="none">
                            <path d="M20.1831 109.942C47.0958 121.853 90.728 121.857 117.641 109.942C144.553 98.0269 144.55 78.7161 117.641 66.8047C90.7319 54.8932 47.0958 54.8932 20.1831 66.8047C-6.72965 78.7161 -6.72574 98.0305 20.1831 109.942Z" fill="#F5F5F5"/>
                            <path d="M117.547 101.139C122.504 101.139 126.522 99.3605 126.522 97.167C126.522 94.9735 122.504 93.1953 117.547 93.1953C112.59 93.1953 108.572 94.9735 108.572 97.167C108.572 99.3605 112.59 101.139 117.547 101.139Z" fill="#EBEBEB"/>
                            <path d="M18.1406 97.737L21.3158 96.0359C23.0886 94.6923 24.4379 92.9292 25.2213 90.9325C26.2016 88.4226 26.0649 86.5147 25.3619 85.5572C24.4988 84.3784 22.2609 84.5851 22.1711 87.066C21.9836 91.9554 20.4488 95.3467 18.1406 97.737Z" fill="#BA68C8"/>
                            <path opacity="0.2" d="M18.1406 97.737L21.3158 96.0359C23.0886 94.6923 24.4379 92.9292 25.2213 90.9325C26.2016 88.4226 26.0649 86.5147 25.3619 85.5572C24.4988 84.3784 22.2609 84.5851 22.1711 87.066C21.9836 91.9554 20.4488 95.3467 18.1406 97.737Z" fill="black"/>
                            <path d="M18.1401 97.9178C18.0988 97.9173 18.0587 97.9047 18.0256 97.8817C17.9925 97.8586 17.9681 97.8265 17.9559 97.7898C17.9437 97.7531 17.9444 97.7137 17.9579 97.6774C17.9714 97.6411 17.9969 97.6097 18.0308 97.5877C25.3731 92.8725 23.9515 86.2965 23.9359 86.2312C23.9305 86.2074 23.9302 86.1828 23.9351 86.1589C23.9399 86.135 23.9498 86.1122 23.9641 86.0918C23.9784 86.0714 23.9969 86.0539 24.0186 86.0401C24.0402 86.0264 24.0645 86.0168 24.0902 86.0118C24.1158 86.0068 24.1423 86.0065 24.168 86.011C24.1938 86.0155 24.2183 86.0246 24.2403 86.0379C24.2622 86.0513 24.2811 86.0684 24.2959 86.0885C24.3107 86.1086 24.3211 86.1312 24.3264 86.155C24.3264 86.224 25.8262 93.0248 18.2651 97.8851C18.2284 97.9084 18.1845 97.9199 18.1401 97.9178Z" fill="white"/>
                            <path d="M11.7783 101.15C11.7783 101.15 10.673 100.316 10.9386 99.1843C11.2042 98.0526 11.9189 97.2401 10.6965 95.782C9.47406 94.3239 9.13428 92.3798 10.5949 91.3279C11.708 90.5118 13.4459 91.6653 14.0708 89.5144C14.5317 87.9402 14.3754 84.4546 16.5352 83.2939C18.6949 82.1332 20.0501 83.8815 19.7103 86.2464C19.4135 88.361 18.9917 90.3958 19.3198 90.9616C19.9525 92.0497 21.0343 91.901 20.9835 93.8887C20.9406 95.7385 19.0542 97.2692 19.0542 97.2692L11.7783 101.15Z" fill="#BA68C8"/>
                            <path d="M15.4065 99.3765C15.3587 99.376 15.3127 99.3597 15.2769 99.3304C15.2411 99.3011 15.2177 99.2608 15.2112 99.2169C14.6527 94.9188 15.9923 87.4977 17.0858 85.2924C17.095 85.268 17.1098 85.2456 17.1291 85.2269C17.1483 85.2082 17.1717 85.1935 17.1976 85.1839C17.2235 85.1742 17.2514 85.1699 17.2792 85.1711C17.3071 85.1723 17.3344 85.179 17.3592 85.1909C17.3825 85.2006 17.4035 85.2146 17.4209 85.232C17.4383 85.2494 17.4517 85.2699 17.4605 85.2922C17.4692 85.3145 17.473 85.3382 17.4717 85.3619C17.4705 85.3856 17.4641 85.4089 17.4529 85.4302C16.3711 87.6065 15.0784 95.0494 15.6134 99.1589C15.6189 99.2065 15.604 99.2541 15.5718 99.2915C15.5396 99.3289 15.4928 99.3529 15.4416 99.3584L15.4065 99.3765Z" fill="white"/>
                            <path d="M15.3003 96.028C15.2704 96.0294 15.2407 96.0231 15.2143 96.0099C13.8591 95.3969 12.9999 93.2097 12.9648 93.1082C12.9552 93.0856 12.9506 93.0615 12.9512 93.0373C12.9519 93.013 12.9577 92.9892 12.9685 92.9671C12.9792 92.945 12.9946 92.9251 13.0138 92.9086C13.033 92.8921 13.0555 92.8794 13.08 92.8712C13.1046 92.8629 13.1307 92.8593 13.1567 92.8606C13.1828 92.8619 13.2083 92.868 13.2318 92.8786C13.2553 92.8892 13.2763 92.9041 13.2934 92.9223C13.3106 92.9406 13.3237 92.9618 13.3319 92.9849C13.3319 93.0066 14.1716 95.1249 15.3862 95.6726C15.426 95.6905 15.4581 95.7204 15.4772 95.7575C15.4963 95.7946 15.5013 95.8366 15.4913 95.8767C15.4813 95.9167 15.457 95.9525 15.4223 95.978C15.3875 96.0035 15.3445 96.0173 15.3003 96.0171V96.028Z" fill="white"/>
                            <path d="M15.2781 97.4796C15.239 97.4796 15.2008 97.4685 15.1686 97.4477C15.1365 97.427 15.112 97.3976 15.0984 97.3635C15.079 97.3192 15.0792 97.2696 15.0989 97.2255C15.1187 97.1813 15.1564 97.1462 15.2039 97.1277C17.4847 96.279 18.9063 93.7581 18.9219 93.7291C18.9457 93.6863 18.9869 93.6541 19.0363 93.6394C19.0858 93.6248 19.1394 93.629 19.1855 93.6511C19.2316 93.6733 19.2664 93.7115 19.2821 93.7574C19.2979 93.8033 19.2933 93.8532 19.2695 93.896C19.207 94.0048 17.7659 96.5655 15.364 97.4651C15.3372 97.4768 15.3076 97.4819 15.2781 97.4796Z" fill="white"/>
                            <path d="M95.0004 77.9648C95.0004 77.9648 93.2937 75.3968 93.1258 72.6076C92.9579 69.8183 93.6179 68.6577 94.524 68.324C95.43 67.9903 95.5003 69.0167 95.9651 69.6225C96.4298 70.2282 97.3789 69.8836 97.7304 67.9939C98.0819 66.1042 99.3629 62.9159 102.026 62.2957C104.956 61.6211 103.386 64.8129 102.647 66.057C101.909 67.3011 101.339 68.9986 101.823 69.4593C102.308 69.9199 103.061 69.0675 103.776 69.8582C104.647 70.8122 101.94 74.2471 100.386 75.0777L95.0004 77.9648Z" fill="#BA68C8"/>
                            <path d="M97.34 76.8883C97.3143 76.8874 97.2891 76.8818 97.2658 76.8718C97.2425 76.8618 97.2215 76.8477 97.2041 76.8301C97.1867 76.8126 97.1732 76.7921 97.1643 76.7697C97.1555 76.7473 97.1515 76.7236 97.1525 76.6997C97.417 72.2128 98.9236 67.8653 101.527 64.0774C101.541 64.0576 101.56 64.0409 101.582 64.0281C101.604 64.0154 101.629 64.0069 101.654 64.0031C101.68 63.9994 101.706 64.0005 101.731 64.0064C101.756 64.0123 101.779 64.0229 101.8 64.0375C101.841 64.0657 101.868 64.1077 101.876 64.1545C101.884 64.2012 101.872 64.2491 101.843 64.2877C99.2865 68.0202 97.807 72.3008 97.547 76.7179C97.5455 76.7417 97.5389 76.765 97.5277 76.7865C97.5165 76.808 97.5008 76.8272 97.4816 76.843C97.4623 76.8589 97.4399 76.871 97.4156 76.8788C97.3913 76.8866 97.3656 76.8898 97.34 76.8883Z" fill="white"/>
                            <path d="M97.5616 74.744C97.529 74.7438 97.4968 74.7363 97.4679 74.7223C96.3743 74.1601 95.769 72.4916 95.7456 72.4227C95.7369 72.4001 95.7332 72.3761 95.7347 72.3521C95.7361 72.3282 95.7427 72.3047 95.7539 72.2831C95.7652 72.2615 95.7809 72.2422 95.8003 72.2264C95.8196 72.2105 95.8422 72.1983 95.8666 72.1905C95.9158 72.1757 95.9694 72.1795 96.0155 72.2013C96.0616 72.2231 96.0966 72.2609 96.1127 72.3066C96.1127 72.3066 96.6985 73.9134 97.6749 74.4031C97.7191 74.4274 97.7515 74.4666 97.7654 74.5126C97.7792 74.5585 97.7734 74.6077 97.7491 74.6497C97.7314 74.6809 97.7042 74.7066 97.6708 74.7234C97.6374 74.7402 97.5994 74.7474 97.5616 74.744Z" fill="white"/>
                            <path d="M97.7542 73.6457C97.7109 73.6453 97.669 73.6315 97.6351 73.6066C97.6012 73.5816 97.5771 73.5469 97.5667 73.5079C97.5603 73.485 97.5589 73.4612 97.5625 73.4378C97.5661 73.4144 97.5747 73.3919 97.5878 73.3717C97.6009 73.3514 97.6182 73.3338 97.6387 73.3198C97.6593 73.3058 97.6826 73.2958 97.7073 73.2903C100.527 72.6265 101.503 71.2555 101.511 71.2409C101.525 71.2207 101.543 71.2032 101.564 71.1894C101.585 71.1756 101.609 71.1659 101.634 71.1606C101.66 71.1554 101.686 71.1548 101.711 71.159C101.737 71.1631 101.761 71.1719 101.783 71.1847C101.805 71.1976 101.823 71.2143 101.838 71.2339C101.853 71.2536 101.864 71.2757 101.869 71.2991C101.875 71.3225 101.875 71.3467 101.871 71.3704C101.867 71.394 101.857 71.4166 101.843 71.4368C101.8 71.4985 100.785 72.9384 97.805 73.6421L97.7542 73.6457Z" fill="white"/>
                            <path d="M52.5079 72.3287C52.5079 72.3287 48.7625 71.4799 48.5672 64.9656C48.3603 57.9363 49.485 49.7317 45.0523 49.068C40.6196 48.4042 38.1591 53.4205 38.2138 59.2783C38.2646 64.407 40.4907 75.5423 52.4923 77.7258L52.5079 72.3287Z" fill="#BA68C8"/>
                            <path opacity="0.2" d="M52.5079 72.3287C52.5079 72.3287 48.7625 71.4799 48.5672 64.9656C48.3603 57.9363 49.485 49.7317 45.0523 49.068C40.6196 48.4042 38.1591 53.4205 38.2138 59.2783C38.2646 64.407 40.4907 75.5423 52.4923 77.7258L52.5079 72.3287Z" fill="black"/>
                            <path d="M52.5082 77.058H52.473C49.7744 76.601 47.5756 75.2191 45.9392 72.9558C41.0885 66.2347 42.6156 53.6849 43.1194 51.4941C43.1307 51.4475 43.161 51.4066 43.2039 51.3803C43.2469 51.3539 43.2992 51.344 43.3498 51.3527C43.4004 51.3639 43.4444 51.3928 43.4727 51.4333C43.501 51.4738 43.5116 51.5228 43.5021 51.5703C43.0061 53.7466 41.4869 66.1368 46.2633 72.7526C46.9586 73.7786 47.8758 74.6592 48.9569 75.3385C50.038 76.0179 51.2593 76.4811 52.5433 76.6989C52.5688 76.7033 52.5931 76.7123 52.6148 76.7255C52.6365 76.7386 52.6552 76.7556 52.6697 76.7756C52.6843 76.7955 52.6944 76.8179 52.6995 76.8415C52.7046 76.865 52.7047 76.8893 52.6996 76.9129C52.6913 76.9542 52.6675 76.9915 52.6325 77.018C52.5974 77.0446 52.5534 77.0587 52.5082 77.058Z" fill="white"/>
                            <path d="M52.5091 93.7754C48.6387 94.2614 42.3548 94.2542 38.7266 91.5991C35.0984 88.9441 35.856 86.659 37.2503 85.705C38.6446 84.7511 39.8553 83.663 37.4573 81.5447C35.0593 79.4265 31.2085 76.0243 29.9587 70.9209C28.709 65.8176 29.3261 62.6257 31.7787 61.563C34.1572 60.5329 34.7859 62.1723 36.2427 65.5528C37.6994 68.9332 40.9761 66.0352 43.7803 67.0907C46.5844 68.1462 46.4712 70.2354 46.6469 72.0308C46.8227 73.8262 47.7483 74.3232 49.3378 74.3232C50.9273 74.3232 52.5091 75.1719 52.5091 76.8621V93.7754Z" fill="#BA68C8"/>
                            <path d="M52.5088 87.849C52.4921 87.8525 52.4747 87.8525 52.458 87.849C52.2901 87.8055 48.287 86.7246 42.4561 81.7519C36.2463 76.4563 33.3172 68.5129 33.286 68.4331C33.271 68.3874 33.2756 68.3381 33.2989 68.2955C33.3222 68.2529 33.3624 68.2203 33.4109 68.2046C33.4596 68.1895 33.5127 68.1929 33.5588 68.2139C33.6048 68.235 33.6401 68.272 33.657 68.317C33.6843 68.3968 36.5861 76.2532 42.7216 81.4835C48.4705 86.3909 52.5205 87.49 52.5635 87.5008C52.5881 87.5075 52.6111 87.5186 52.631 87.5335C52.651 87.5484 52.6677 87.5669 52.68 87.5877C52.6923 87.6086 52.7001 87.6316 52.7029 87.6552C52.7057 87.6789 52.7034 87.7029 52.6963 87.7257C52.6836 87.7623 52.6585 87.7942 52.6247 87.8164C52.5909 87.8387 52.5502 87.8501 52.5088 87.849Z" fill="white"/>
                            <path d="M44.0837 83.0138C44.0444 83.0143 44.006 83.0034 43.9737 82.9826C43.9415 82.9618 43.9171 82.9321 43.904 82.8977C42.6504 79.909 42.3653 73.5144 42.3418 73.2423C42.3407 73.1945 42.3597 73.1481 42.3946 73.1129C42.4296 73.0777 42.4779 73.0565 42.5293 73.0537C42.5803 73.0544 42.6292 73.0725 42.6667 73.1047C42.7042 73.1368 42.7276 73.1806 42.7324 73.2278C42.7324 73.2931 43.0253 79.8328 44.2555 82.7671C44.2649 82.7895 44.2695 82.8134 44.2689 82.8374C44.2682 82.8614 44.2625 82.8851 44.252 82.907C44.2414 82.9289 44.2263 82.9487 44.2075 82.9652C44.1886 82.9816 44.1665 82.9944 44.1423 83.0029C44.1234 83.0092 44.1037 83.0128 44.0837 83.0138Z" fill="white"/>
                            <path d="M44.0539 87.3127C43.6938 87.3154 43.3339 87.2984 42.976 87.262C42.9503 87.2591 42.9255 87.2516 42.903 87.2398C42.8805 87.2281 42.8607 87.2123 42.8448 87.1935C42.8288 87.1746 42.817 87.153 42.81 87.1299C42.8031 87.1068 42.8011 87.0827 42.8041 87.0588C42.8072 87.035 42.8153 87.012 42.828 86.9911C42.8406 86.9702 42.8576 86.9518 42.8779 86.937C42.8982 86.9222 42.9215 86.9112 42.9463 86.9047C42.9712 86.8983 42.9972 86.8964 43.0228 86.8992C43.058 86.8992 46.694 87.2837 48.3617 85.7059C48.379 85.6868 48.4004 85.6713 48.4246 85.6603C48.4487 85.6492 48.4751 85.6429 48.5019 85.6418C48.5288 85.6407 48.5557 85.6447 48.5808 85.6536C48.6059 85.6626 48.6288 85.6763 48.6479 85.6938C48.6671 85.7114 48.6821 85.7324 48.6921 85.7556C48.7021 85.7788 48.7068 85.8037 48.706 85.8286C48.7052 85.8536 48.6988 85.8782 48.6872 85.9007C48.6757 85.9233 48.6592 85.9434 48.6389 85.9598C47.4321 87.1169 45.3271 87.3127 44.0539 87.3127Z" fill="white"/>
                            <path d="M41.2412 18.6285L40.8194 16.3688L38.6479 17.6963C38.4495 17.3336 38.1683 17.0155 37.8238 16.7641L38.8627 14.1526L36.91 13.75L35.4806 16.289C34.9634 16.37 34.462 16.5216 33.9926 16.7387L33.3716 14.8236L31.0517 16.5175L31.204 18.7663C30.7316 19.2578 30.3032 19.7842 29.923 20.3405L28.0094 20.2498L26.6659 23.0463L28.3101 23.6992C28.1384 24.2776 28.0298 24.8706 27.9859 25.4692L25.8965 27.2538L26.3144 29.5171L28.4897 28.1895C28.687 28.5519 28.9685 28.8691 29.3138 29.1181L28.2945 31.7332L30.2472 32.1322L32.2937 30.2824C32.7662 30.2135 32.6491 29.3865 33.1646 29.1507L33.7856 31.0586L36.1015 29.3684L36.0429 28.4833C36.5116 27.9973 38.2847 26.9273 38.6674 26.3687L39.1478 25.6433L40.4913 22.8468L39.8664 21.7949L39.1712 20.4239L41.2412 18.6285ZM34.1527 26.0677C32.3718 27.3662 31.8094 27.6527 31.5087 25.9153C31.2079 24.178 32.2273 21.5374 34.0082 20.2353C35.7891 18.9331 36.4686 18.8425 36.7889 20.5762C37.1091 22.31 35.9375 24.7837 34.1527 26.0677Z" fill="#E0E0E0"/>
                            <path d="M36.9255 13.75L39.4602 15.1065L38.0308 17.6383L35.4961 16.2817L36.9255 13.75Z" fill="#F5F5F5"/>
                            <path d="M43.3546 17.7257L41.1831 19.0532L38.6484 17.6967L40.8199 16.3691L43.3546 17.7257Z" fill="#F5F5F5"/>
                            <path d="M43.007 24.1933L41.3628 23.544L38.8281 22.1875L40.4723 22.8368L43.007 24.1933Z" fill="#E0E0E0"/>
                            <path d="M28.291 31.7333L30.8257 33.0862L31.8645 30.4747L29.3299 29.1182L28.291 31.7333Z" fill="#E0E0E0"/>
                            <path d="M36.2982 32.4158L33.7675 31.0592L33.1465 29.1514L35.6811 30.5079L36.2982 32.4158Z" fill="#E0E0E0"/>
                            <path d="M43.7724 19.9859L43.3545 17.7262L41.1831 19.0537C40.9832 18.6909 40.7007 18.3728 40.3551 18.1216L41.394 15.51L39.4607 15.1074L38.0274 17.6464C37.5103 17.7274 37.0089 17.879 36.5394 18.0962L35.9224 16.1883L33.6025 17.8822L33.7548 20.131C33.2819 20.6259 32.8535 21.156 32.4738 21.716L30.5562 21.6253L29.2127 24.4219L30.857 25.0747C30.6869 25.6547 30.5783 26.2488 30.5328 26.8484L28.4473 28.6075L28.8652 30.8709L31.0366 29.5433C31.2365 29.9051 31.5191 30.222 31.8646 30.4719L30.8257 33.0834L32.7785 33.486L34.2079 30.947C34.727 30.8669 35.23 30.714 35.6998 30.4936L36.3168 32.4015L38.6367 30.7113L38.4844 28.4625C38.9568 27.971 39.3852 27.4445 39.7654 26.8883L41.683 26.9753L43.0265 24.1788L41.3588 23.5296C41.5317 22.95 41.6416 22.3558 41.6869 21.7559L43.7724 19.9859ZM36.6918 27.4396C34.9148 28.7381 33.212 28.3863 32.8878 26.6525C32.5637 24.9188 33.747 22.456 35.5279 21.1575C37.3088 19.8589 39.0077 20.2144 39.3319 21.9482C39.656 23.6819 38.4727 26.1411 36.6918 27.4396Z" fill="#EBEBEB"/>
                            <path d="M35.9229 16.1808L33.3882 14.8242L31.0684 16.5181L33.603 17.8746L35.9229 16.1808Z" fill="#F5F5F5"/>
                            <path d="M31.0684 16.5176L31.2207 18.7664L33.7553 20.1229L33.603 17.8741L31.0684 16.5176Z" fill="#E0E0E0"/>
                            <path d="M32.4737 21.6975L29.9391 20.341L28.0254 20.2539L30.5561 21.6104L32.4737 21.6975Z" fill="#F5F5F5"/>
                            <path d="M28.0251 20.2539L26.6816 23.0468L29.2124 24.4033L30.5559 21.6104L28.0251 20.2539Z" fill="#E0E0E0"/>
                            <path d="M30.5323 26.8263L28.0015 25.4697L25.9121 27.2543L28.4429 28.6108L30.5323 26.8263Z" fill="#F5F5F5"/>
                            <path d="M28.8647 30.8738L26.33 29.5172L25.9121 27.2539L28.4429 28.6104L28.8647 30.8738Z" fill="#E0E0E0"/>
                            <path d="M39.4604 15.1065L36.9258 13.75L38.8629 14.1526L41.3937 15.5092L39.4604 15.1065Z" fill="#F5F5F5"/>
                            <path d="M29.8925 8.37138V4.99454L26.9986 6.20599C26.8453 5.63307 26.5806 5.09099 26.2175 4.60644L28.0413 1.19332L25.8152 0L23.5422 3.17373C22.9088 3.12233 22.2706 3.18013 21.6598 3.3442L21.3395 0.409866L18.1917 2.1001L17.8753 5.3645C17.1903 5.92827 16.5603 6.5473 15.9929 7.21433L13.7199 6.48891L11.4938 10.0725L13.3137 11.5233C12.9775 12.3131 12.7161 13.1287 12.5326 13.9608L9.63867 15.8505V19.231L12.5326 18.0195C12.6859 18.5924 12.9506 19.1345 13.3137 19.6191L11.5016 23.0358L13.7277 24.2291L16.0007 21.0554C16.6341 21.1066 17.2723 21.0488 17.8832 20.8849L18.1995 23.8193L21.3473 22.129L21.6676 18.8646C22.6501 18.5317 23.5204 17.9636 24.1905 17.2179L25.823 17.7511L28.0492 14.1711L26.6822 13.083C26.979 12.1756 27.0889 11.2244 27.0064 10.2792L29.8925 8.37138ZM19.7656 16.8081C17.352 18.1029 15.8601 17.1708 15.8601 14.5919C15.8601 12.013 17.8558 8.82477 20.2733 7.52989C22.6908 6.23501 24.1515 7.16718 24.1515 9.76782C24.1515 12.3685 22.1831 15.5095 19.7656 16.8081Z" fill="#E0E0E0"/>
                            <path d="M28.3467 1.35292L25.816 0L23.543 3.17373L26.0737 4.53027L28.3467 1.35292Z" fill="#F5F5F5"/>
                            <path d="M32.4267 6.35068L29.892 4.99414L26.998 6.2056L29.5327 7.56214L32.4267 6.35068Z" fill="#F5F5F5"/>
                            <path d="M20.7221 25.1756L18.1913 23.8191L17.875 20.8848L20.4058 22.2413L20.7221 25.1756Z" fill="#E0E0E0"/>
                            <path d="M11.4902 23.0362L14.0249 24.3927L15.8488 20.9796L13.3141 19.623L11.4902 23.0362Z" fill="#E0E0E0"/>
                            <path d="M32.4277 9.74624V6.3694L29.5337 7.56997C29.3821 6.99654 29.1172 6.45418 28.7526 5.97042L30.5765 2.5573L28.3504 1.36035L26.0774 4.53771C25.4439 4.48452 24.8054 4.54235 24.1949 4.70818L23.8747 1.76659L20.7269 3.45683L20.4066 6.72123C19.7225 7.28594 19.0927 7.90489 18.5242 8.57106L16.2512 7.84564L14.025 11.4292L15.8489 12.8801C15.5108 13.6693 15.2494 14.485 15.0678 15.3175L12.1738 17.2072V20.5877L15.0678 19.3762C15.2194 19.9497 15.4842 20.4921 15.8489 20.9758L14.025 24.3889L16.2512 25.5822L18.5242 22.4085C19.1576 22.4599 19.7958 22.4021 20.4066 22.238L20.7269 25.1724L23.8747 23.4821L24.191 20.2177C24.8761 19.654 25.506 19.0349 26.0735 18.3679L28.3465 19.0933L30.5726 15.5134L28.7487 14.0625C29.0867 13.2732 29.3481 12.4575 29.5298 11.6251L32.4277 9.74624ZM22.3008 18.1829C19.8833 19.4778 17.9227 18.4259 17.9227 15.8362C17.9227 13.2464 19.8755 10.0944 22.3008 8.79594C24.7261 7.49743 26.6749 8.55292 26.6749 11.1427C26.6749 13.7324 24.7144 16.8663 22.3008 18.1648V18.1829Z" fill="#EBEBEB"/>
                            <path d="M15.9937 7.22138L18.5245 8.57792L16.2515 7.84161L13.7207 6.48145L15.9937 7.22138Z" fill="#F0F0F0"/>
                            <path d="M13.7189 6.48551L11.4922 10.0703L14.026 11.4278L16.2527 7.84304L13.7189 6.48551Z" fill="#F5F5F5"/>
                            <path d="M11.4902 10.0654L14.0249 11.422L15.8488 12.8801L13.3141 11.5272L11.4902 10.0654Z" fill="#E0E0E0"/>
                            <path d="M12.5326 13.9648L15.0673 15.3214L12.1733 17.2111L9.63867 15.8546L12.5326 13.9648Z" fill="#F5F5F5"/>
                            <path d="M9.63867 15.8545L12.1733 17.211V20.5915L9.63867 19.235V15.8545Z" fill="#E0E0E0"/>
                            <path d="M23.8739 1.7667L21.3392 0.410156L18.1914 2.10039L20.7222 3.45693L23.8739 1.7667Z" fill="#F5F5F5"/>
                            <path d="M56.3676 98.6283L93.0792 78.9403C93.16 78.9139 93.2301 78.8648 93.2797 78.7999C93.3292 78.7349 93.3558 78.6572 93.3558 78.5776C93.3558 78.498 93.3292 78.4203 93.2797 78.3554C93.2301 78.2904 93.16 78.2413 93.0792 78.2149L89.6853 76.4014C89.4751 76.3031 89.243 76.252 89.0077 76.252C88.7724 76.252 88.5403 76.3031 88.3301 76.4014L51.6068 96.0712C51.5259 96.0976 51.4558 96.1467 51.4063 96.2117C51.3567 96.2766 51.3301 96.3543 51.3301 96.4339C51.3301 96.5135 51.3567 96.5912 51.4063 96.6562C51.4558 96.7211 51.5259 96.7702 51.6068 96.7966L55.0123 98.6102C55.221 98.7112 55.4523 98.7655 55.6876 98.7687C55.923 98.7718 56.1558 98.7238 56.3676 98.6283Z" fill="#E0E0E0"/>
                            <path d="M92.0714 2.92719L58.9684 20.6783C58.4763 20.9765 58.0668 21.3783 57.773 21.8509C57.4792 22.3235 57.3094 22.8537 57.2773 23.3986V96.329C57.2773 97.3301 58.035 97.7364 58.9684 97.2358L92.0714 79.4847C92.5641 79.1871 92.9741 78.7856 93.268 78.3128C93.5619 77.8401 93.7313 77.3095 93.7624 76.7644V3.83397C93.7624 2.83289 93.0087 2.42665 92.0714 2.92719Z" fill="#455A64"/>
                            <path d="M58.9695 20.6784L92.0725 2.9273C92.9083 2.47754 93.5995 2.75683 93.7401 3.53666C93.662 3.04636 93.4731 2.57697 93.1856 2.15937C92.8982 1.74177 92.5188 1.38541 92.0725 1.11374L90.6978 0.388319C90.1737 0.141385 89.5943 0.0126953 89.0067 0.0126953C88.419 0.0126953 87.8397 0.141385 87.3156 0.388319L54.2088 18.1249C53.7101 18.4153 53.2989 18.8184 53.0137 19.2965L57.7745 21.8355C58.0596 21.3611 58.4711 20.9627 58.9695 20.6784Z" fill="#37474F"/>
                            <path d="M52.5098 93.7752C52.5418 94.3201 52.7116 94.8504 53.0054 95.323C53.2992 95.7956 53.7088 96.1973 54.2008 96.4956L55.5795 97.221C56.0566 97.4427 56.5788 97.5683 57.1119 97.5895C57.6449 97.6108 58.1768 97.5272 58.6726 97.3443C57.8915 97.6272 57.2706 97.2101 57.2706 96.3142V23.3983C57.2901 22.8511 57.4609 22.3177 57.7666 21.8495L53.0058 19.3105C52.7044 19.7752 52.5338 20.3028 52.5098 20.8448V93.7752Z" fill="#263238"/>
                            <path d="M59.8787 30.3955L91.1734 13.6201C91.7358 13.3227 92.1888 13.5657 92.1888 14.1641V68.1429C92.1701 68.47 92.0683 68.7884 91.8918 69.0721C91.7154 69.3557 91.4692 69.5966 91.1734 69.7751L59.8904 86.5505C59.328 86.8515 58.875 86.6085 58.875 86.0064V32.0277C58.8928 31.7016 58.9929 31.3839 59.1672 31.1004C59.3416 30.8169 59.5853 30.5754 59.8787 30.3955Z" fill="white"/>
                            <path d="M75.5194 14.5666C75.3467 14.6719 75.203 14.8134 75.0999 14.9798C74.9968 15.1461 74.9371 15.3325 74.9258 15.5242C74.9258 15.8869 75.1914 16.0211 75.5194 15.8433C75.6928 15.7387 75.837 15.5973 75.9402 15.4309C76.0434 15.2644 76.1026 15.0776 76.1131 14.8858C76.1131 14.534 75.8475 14.3925 75.5194 14.5666Z" fill="#37474F"/>
                            <path d="M75.521 80.7434C74.8383 81.1582 74.2704 81.7166 73.8632 82.3732C73.4561 83.0298 73.2213 83.7663 73.1777 84.5229C73.1777 85.9121 74.2283 86.4779 75.521 85.7815C76.2036 85.3665 76.7713 84.8081 77.1784 84.1515C77.5855 83.4949 77.8205 82.7586 77.8643 82.002C77.8721 80.6129 76.8177 80.047 75.521 80.7434Z" fill="#37474F"/>
                            <path d="M69.9002 20.1817C69.5569 20.3933 69.2721 20.6769 69.0685 21.0095C68.865 21.3422 68.7485 21.7146 68.7285 22.0968C68.7285 22.8005 69.2597 23.087 69.9002 22.7352C70.247 22.5256 70.5357 22.2429 70.7427 21.9101C70.9497 21.5773 71.0691 21.2038 71.0913 20.82C71.0913 20.1164 70.5602 19.8298 69.9002 20.1817Z" fill="#37474F"/>
                            <path d="M72.8184 19.9423C72.8184 20.4138 73.2089 20.5915 73.6659 20.3412L81.4768 16.1447C81.7188 16.0043 81.9217 15.8129 82.0689 15.586C82.2162 15.3591 82.3037 15.1031 82.3243 14.8389C82.3243 14.371 81.9338 14.1933 81.4768 14.4436L73.6659 18.6401C73.4244 18.7801 73.2218 18.9709 73.0746 19.1972C72.9274 19.4234 72.8396 19.6787 72.8184 19.9423Z" fill="#37474F"/>
                            <path d="M55.1483 32.7314C55.4254 32.8991 55.6561 33.125 55.8217 33.391C55.9872 33.6569 56.083 33.9553 56.1012 34.2621C56.1012 34.8279 55.6755 35.0564 55.1483 34.7735C54.872 34.6052 54.6423 34.379 54.4774 34.1132C54.3126 33.8473 54.2173 33.5492 54.1992 33.2428C54.1992 32.6806 54.6249 32.4521 55.1483 32.7314Z" fill="#37474F"/>
                            <path d="M55.1483 39.5432C55.4254 39.7109 55.6561 39.9368 55.8217 40.2028C55.9872 40.4687 56.083 40.7671 56.1012 41.0739C56.1012 41.6361 55.6755 41.8646 55.1483 41.5853C54.872 41.417 54.6423 41.1908 54.4774 40.925C54.3126 40.6591 54.2173 40.361 54.1992 40.0547C54.1992 39.4888 54.6249 39.2603 55.1483 39.5432Z" fill="#37474F"/>
                            <path d="M54.8514 32.8695C55.1285 33.0372 55.3592 33.2631 55.5248 33.5291C55.6904 33.795 55.7861 34.0934 55.8043 34.4002C55.8043 34.9624 55.3786 35.1909 54.8514 34.9116C54.5742 34.7439 54.3435 34.518 54.1779 34.252C54.0124 33.9861 53.9166 33.6877 53.8984 33.3809C53.9023 32.8151 54.328 32.5975 54.8514 32.8695Z" fill="#455A64"/>
                            <path d="M54.8514 39.6777C55.129 39.8457 55.3601 40.0723 55.5257 40.3389C55.6913 40.6055 55.7867 40.9046 55.8043 41.212C55.8043 41.7742 55.3786 42.0027 54.8514 41.7198C54.5736 41.5528 54.3424 41.3269 54.1767 41.0609C54.0111 40.7948 53.9157 40.4961 53.8984 40.1891C53.9023 39.6269 54.328 39.3984 54.8514 39.6777Z" fill="#455A64"/>
                            <path d="M98.5228 15.8724C98.5228 15.2703 98.0698 15.0273 97.5074 15.3284L93.762 17.3342L66.2244 32.1038C65.9286 32.2823 65.6825 32.5232 65.506 32.8068C65.3295 33.0905 65.2278 33.4089 65.209 33.736V87.7002C65.209 88.3023 65.662 88.5453 66.2244 88.2443L93.7737 73.471L97.5074 71.4689C97.8032 71.2904 98.0493 71.0495 98.2258 70.7658C98.4023 70.4822 98.5041 70.1638 98.5228 69.8367V15.8724Z" fill="white"/>
                            <path d="M98.9148 15.8725C98.9394 15.6906 98.9057 15.506 98.8179 15.3418C98.7301 15.1776 98.5921 15.0411 98.4212 14.9494C98.2503 14.8576 98.0541 14.8147 97.8571 14.826C97.6601 14.8373 97.471 14.9023 97.3136 15.0128L93.74 16.9316L66.0306 31.8028C65.6738 32.0119 65.3772 32.2985 65.1663 32.6378C64.9554 32.9771 64.8365 33.359 64.8199 33.7505V87.7002C64.8042 87.8813 64.8417 88.063 64.9283 88.2258C65.015 88.3887 65.1476 88.5267 65.312 88.6252C65.4518 88.7006 65.6111 88.7395 65.7728 88.7376C66.0018 88.7309 66.225 88.6698 66.4211 88.5599L94.1501 73.6887L97.7158 71.7772C98.0705 71.566 98.3656 71.279 98.5762 70.9401C98.7869 70.6012 98.907 70.2203 98.9265 69.8294L98.9148 15.8725ZM93.7478 73.0684L93.4276 73.2425L66.0462 87.9251C66.0002 87.958 65.9469 87.9809 65.8901 87.9921C65.8334 88.0034 65.7746 88.0028 65.7182 87.9904C65.6674 87.9614 65.6166 87.8598 65.6166 87.693V33.736C65.6368 33.4727 65.7212 33.217 65.8632 32.9883C66.0053 32.7596 66.2014 32.5638 66.4368 32.4157L93.7908 17.7259L97.6963 15.6258C97.7423 15.593 97.7957 15.5701 97.8524 15.5588C97.9092 15.5475 97.9679 15.5481 98.0244 15.5605C98.0751 15.5896 98.1259 15.6911 98.1259 15.858V69.8367C98.1057 70.1001 98.0214 70.3557 97.8793 70.5844C97.7372 70.8131 97.5411 71.0089 97.3058 71.157L93.74 73.0684H93.7478Z" fill="#BA68C8"/>
                            <path d="M81.866 28.4114C85.3809 26.5325 88.2124 28.0487 88.2124 31.8172C88.2124 35.5858 85.3692 40.1596 81.866 42.0239C78.3627 43.8883 75.5195 42.3867 75.5195 38.6217C75.5195 34.8568 78.3627 30.2939 81.866 28.4114Z" fill="#BA68C8"/>
                            <path d="M81.8668 31.3709C83.0033 30.7615 83.925 31.2548 83.925 32.459C83.8864 33.1224 83.6799 33.768 83.3223 34.3434C82.9647 34.9187 82.466 35.4077 81.8668 35.7706C80.7303 36.3799 79.8086 35.8866 79.8086 34.6824C79.8472 34.019 80.0537 33.3735 80.4113 32.7981C80.7689 32.2227 81.2676 31.7337 81.8668 31.3709Z" fill="#BA68C8"/>
                            <path d="M85.8774 37.8711C85.8774 35.8689 84.3659 35.0564 82.4952 36.0575L81.3236 36.685C79.4567 37.6861 77.9414 40.1235 77.9414 42.1257V42.6661C79.0232 42.9962 80.3823 42.8185 81.8469 42.0241C83.4693 41.0947 84.8413 39.8329 85.8578 38.3353L85.8774 37.8711Z" fill="#BA68C8"/>
                            <path opacity="0.7" d="M81.8668 31.3709C83.0033 30.7615 83.925 31.2548 83.925 32.459C83.8864 33.1224 83.6799 33.768 83.3223 34.3434C82.9647 34.9187 82.466 35.4077 81.8668 35.7706C80.7303 36.3799 79.8086 35.8866 79.8086 34.6824C79.8472 34.019 80.0537 33.3735 80.4113 32.7981C80.7689 32.2227 81.2676 31.7337 81.8668 31.3709Z" fill="white"/>
                            <path opacity="0.7" d="M85.8774 37.8711C85.8774 35.8689 84.3659 35.0564 82.4952 36.0575L81.3236 36.685C79.4567 37.6861 77.9414 40.1235 77.9414 42.1257V42.6661C79.0232 42.9962 80.3823 42.8185 81.8469 42.0241C83.4693 41.0947 84.8413 39.8329 85.8578 38.3353L85.8774 37.8711Z" fill="white"/>
                            <path d="M69.5878 50.3124L77.7541 45.9344C78.1447 45.7349 78.4298 45.8945 78.4298 46.2971V46.5474C78.4174 46.7654 78.3498 46.9776 78.2324 47.1667C78.115 47.3558 77.9511 47.5164 77.7541 47.6355L69.5878 52.0135C69.2167 52.213 68.9121 52.0497 68.9121 51.6508V51.4005C68.9248 51.1826 68.9926 50.9705 69.1099 50.7814C69.2273 50.5924 69.391 50.4317 69.5878 50.3124Z" fill="#BA68C8"/>
                            <path opacity="0.1" d="M69.5878 50.3124L77.7541 45.9344C78.1447 45.7349 78.4298 45.8945 78.4298 46.2971V46.5474C78.4174 46.7654 78.3498 46.9776 78.2324 47.1667C78.115 47.3558 77.9511 47.5164 77.7541 47.6355L69.5878 52.0135C69.2167 52.213 68.9121 52.0497 68.9121 51.6508V51.4005C68.9248 51.1826 68.9926 50.9705 69.1099 50.7814C69.2273 50.5924 69.391 50.4317 69.5878 50.3124Z" fill="black"/>
                            <path d="M69.2519 55.5969L93.958 42.3507C94.1416 42.2491 94.2939 42.3289 94.2939 42.532V45.2088C94.287 45.3175 94.2531 45.4231 94.1948 45.5175C94.1366 45.6119 94.0555 45.6925 93.958 45.7529L69.2519 58.9991C69.0644 59.1007 68.9121 59.0173 68.9121 58.8178V56.141C68.9188 56.0319 68.9531 55.9258 69.0121 55.8313C69.0711 55.7368 69.1533 55.6565 69.2519 55.5969Z" fill="#BA68C8"/>
                            <path opacity="0.2" d="M69.2519 55.5969L93.958 42.3507C94.1416 42.2491 94.2939 42.3289 94.2939 42.532V45.2088C94.287 45.3175 94.2531 45.4231 94.1948 45.5175C94.1366 45.6119 94.0555 45.6925 93.958 45.7529L69.2519 58.9991C69.0644 59.1007 68.9121 59.0173 68.9121 58.8178V56.141C68.9188 56.0319 68.9531 55.9258 69.0121 55.8313C69.0711 55.7368 69.1533 55.6565 69.2519 55.5969Z" fill="white"/>
                            <path d="M69.5878 62.2199L77.7541 57.842C78.1447 57.6425 78.4298 57.8057 78.4298 58.2047V58.455C78.4177 58.673 78.3502 58.8853 78.2328 59.0744C78.1153 59.2636 77.9513 59.4242 77.7541 59.5431L69.5878 63.921C69.2167 64.1241 68.9121 63.9609 68.9121 63.5583V63.308C68.9254 63.0903 68.9934 62.8784 69.1108 62.6894C69.2281 62.5005 69.3915 62.3397 69.5878 62.2199Z" fill="#BA68C8"/>
                            <path opacity="0.1" d="M69.5878 62.2199L77.7541 57.842C78.1447 57.6425 78.4298 57.8057 78.4298 58.2047V58.455C78.4177 58.673 78.3502 58.8853 78.2328 59.0744C78.1153 59.2636 77.9513 59.4242 77.7541 59.5431L69.5878 63.921C69.2167 64.1241 68.9121 63.9609 68.9121 63.5583V63.308C68.9254 63.0903 68.9934 62.8784 69.1108 62.6894C69.2281 62.5005 69.3915 62.3397 69.5878 62.2199Z" fill="black"/>
                            <path d="M69.2519 67.5044L93.958 54.2582C94.1416 54.1567 94.2939 54.2401 94.2939 54.4396V57.1164C94.287 57.225 94.2531 57.3307 94.1948 57.4251C94.1366 57.5195 94.0555 57.6001 93.958 57.6604L69.2519 70.9067C69.0644 71.0082 68.9121 70.9284 68.9121 70.7253V68.0485C68.9188 67.9394 68.9531 67.8333 69.0121 67.7388C69.0711 67.6443 69.1533 67.564 69.2519 67.5044Z" fill="#BA68C8"/>
                            <path opacity="0.2" d="M69.2519 67.5044L93.958 54.2582C94.1416 54.1567 94.2939 54.2401 94.2939 54.4396V57.1164C94.287 57.225 94.2531 57.3307 94.1948 57.4251C94.1366 57.5195 94.0555 57.6001 93.958 57.6604L69.2519 70.9067C69.0644 71.0082 68.9121 70.9284 68.9121 70.7253V68.0485C68.9188 67.9394 68.9531 67.8333 69.0121 67.7388C69.0711 67.6443 69.1533 67.564 69.2519 67.5044Z" fill="white"/>
                            <path d="M79.1053 69.024L88.8691 63.7973C89.2596 63.5942 89.5486 63.7574 89.5486 64.1601V66.9602C89.5358 67.1785 89.4676 67.3909 89.3495 67.58C89.2314 67.7691 89.0667 67.9296 88.8691 68.0483L79.1053 73.2786C78.7148 73.4781 78.4297 73.3149 78.4297 72.9159V70.1122C78.4433 69.8944 78.5114 69.6827 78.6287 69.4938C78.746 69.3049 78.9093 69.144 79.1053 69.024Z" fill="#BA68C8"/>
                            <path opacity="0.1" d="M79.1053 69.024L88.8691 63.7973C89.2596 63.5942 89.5486 63.7574 89.5486 64.1601V66.9602C89.5358 67.1785 89.4676 67.3909 89.3495 67.58C89.2314 67.7691 89.0667 67.9296 88.8691 68.0483L79.1053 73.2786C78.7148 73.4781 78.4297 73.3149 78.4297 72.9159V70.1122C78.4433 69.8944 78.5114 69.6827 78.6287 69.4938C78.746 69.3049 78.9093 69.144 79.1053 69.024Z" fill="black"/>
                            <path d="M102.226 57.2073C102.226 57.2073 97.4301 51.0629 97.1489 50.3157C96.9263 49.7644 97.1489 48.9447 97.2192 48.5638C97.2895 48.183 97.352 47.3342 97.4223 47.113C97.5394 46.6995 96.9653 46.7829 96.6412 47.1601C96.317 47.5374 96.2077 48.2918 96.0397 48.4151C95.8718 48.5384 94.5947 46.8083 93.9269 46.0103C93.259 45.2124 92.8411 44.7263 92.4311 45.0238C92.1382 45.2378 92.3569 45.702 92.8411 46.3839C93.124 46.7511 93.3847 47.1326 93.6222 47.5265C93.6964 47.6752 93.2981 47.6861 93.0833 47.9944C92.9154 48.2338 93.0833 48.6219 92.849 48.8069C92.7047 48.9088 92.6059 49.0564 92.5714 49.2216C92.5368 49.3868 92.5689 49.5581 92.6615 49.7027C93.0962 50.3368 93.6569 50.8882 94.3135 51.3277C95.0321 51.7883 95.3211 51.9516 95.7468 52.579C96.1725 53.2065 99.9921 59.6157 100.25 60.0001C100.508 60.3846 101.004 61.1681 101.757 61.1898C102.511 61.2116 108.682 56.2787 108.998 56.0321C110.014 55.245 110.338 55.1325 110.51 55.8398C110.569 56.0913 110.613 56.3455 110.642 56.6015C110.763 57.501 111.423 60.7147 111.423 60.7147C111.423 60.7147 114.548 62.1184 117.114 61.4401C119.68 60.7618 120.547 59.7499 120.547 59.7499L119.262 57.8057C119.609 57.0041 121.215 53.7542 121.734 51.48L113.689 50.2722C113.525 50.2396 113.259 50.1779 112.908 50.0945C112.466 49.9857 111.345 49.6266 110.498 50.3157C107.733 52.5537 102.234 57.2073 102.234 57.2073" fill="#FFA8A7"/>
                            <path d="M118.176 94.9076L116.734 96.7684C117.389 97.0524 118.105 97.195 118.828 97.1855C120.12 97.229 120.476 96.8228 120.523 96.743C120.741 96.3077 120.402 95.5243 120.308 94.8496L118.176 94.9076Z" fill="#37474F"/>
                            <path d="M116.816 95.8761C116.816 95.8761 115.066 95.7709 113.86 95.6331C112.653 95.4953 111.696 96.0393 111.977 96.5C112.258 96.9606 112.926 97.3451 113.965 97.6679C114.852 97.9472 115.918 98.1467 116.84 98.3933C117.761 98.64 118.402 98.9628 118.937 98.7887C119.534 98.5964 119.792 98.3752 119.741 97.969C119.611 97.3911 119.449 96.8197 119.257 96.257L116.816 95.8761Z" fill="#37474F"/>
                            <path d="M120.523 93.0393C120.523 93.0393 120.304 94.7296 120.289 95.0923C120.273 95.455 118.512 95.5711 118.176 94.9073L118.121 92.8398L120.523 93.0393Z" fill="#F28F8F"/>
                            <path d="M118.751 94.0771L119.379 96.6451C119.379 96.6451 119.438 96.9571 118.001 97.1566C116.564 97.3561 114.306 96.4565 113.92 96.2752C113.533 96.0938 113.49 95.7275 114.904 95.8C116.318 95.8726 116.817 95.8762 116.817 95.8762L116.357 94.7409L118.751 94.0771Z" fill="#FFA8A7"/>
                            <path d="M121.711 51.4577L122.706 51.2002C122.706 51.2002 124.788 57.3191 125.019 57.9104C125.249 58.5016 125.511 59.1254 125.062 59.8545C124.526 60.7214 121.711 64.207 121.711 64.207L119.922 62.5821L121.953 59.1291L120.156 55.7559L121.711 51.4577Z" fill="#F28F8F"/>
                            <path d="M111.482 43.5112C111.248 43.1041 111.13 42.6487 111.137 42.1876C111.145 41.7264 111.278 41.2746 111.524 40.8743C112.516 39.1659 114.727 38.3353 117.359 38.6508C118.578 38.7995 120.015 39.739 120.8 41.1427C121.667 42.6987 121.749 45.8325 121.972 47.0186L111.482 43.5112Z" fill="#37474F"/>
                            <path d="M111.411 44.1826C111.411 44.1826 110.786 45.2889 110.896 45.3904C111.058 45.4783 111.235 45.5397 111.419 45.5718L111.411 44.1826Z" fill="#FFA8A7"/>
                            <path d="M113.309 40.6855C112.63 41.0954 111.458 41.8498 111.407 44.3344C111.353 46.8733 111.743 47.7112 112.15 48.005C112.423 48.2045 114.028 48.1029 114.715 47.9542C115.403 47.8055 117.383 46.8951 118.098 45.5132C118.937 43.8882 118.765 41.5234 117.52 40.7979C116.885 40.4497 116.167 40.2567 115.43 40.237C114.694 40.2174 113.964 40.3717 113.309 40.6855Z" fill="#FFA8A7"/>
                            <path d="M120.523 59.7354C121.726 61.4764 122.866 62.9997 123.116 64.9656C123.631 69.0969 121.944 73.8194 121.581 76.8771C122.264 77.7468 122.658 78.7833 122.714 79.8586C122.714 81.1244 120.523 93.0395 120.523 93.0395C120.523 93.0395 118.929 93.4893 118.121 92.84L116.617 70.9214L117.398 68.2518L120.523 59.7354Z" fill="#BA68C8"/>
                            <path opacity="0.2" d="M120.523 59.7354C121.726 61.4764 122.866 62.9997 123.116 64.9656C123.631 69.0969 121.944 73.8194 121.581 76.8771C122.264 77.7468 122.658 78.7833 122.714 79.8586C122.714 81.1244 120.523 93.0395 120.523 93.0395C120.523 93.0395 118.929 93.4893 118.121 92.84L116.617 70.9214L117.398 68.2518L120.523 59.7354Z" fill="black"/>
                            <path d="M116.617 70.9216L117.398 68.252C118.894 67.8864 120.336 67.3542 121.694 66.667C121.694 66.667 120.882 68.0417 118.547 68.7961L117.34 72.2237L117.238 80.0764L116.617 70.9216Z" fill="#BA68C8"/>
                            <path opacity="0.3" d="M116.617 70.9216L117.398 68.252C118.894 67.8864 120.336 67.3542 121.694 66.667C121.694 66.667 120.882 68.0417 118.547 68.7961L117.34 72.2237L117.238 80.0764L116.617 70.9216Z" fill="black"/>
                            <path d="M120.523 59.7354C121.168 60.7147 122.707 62.3831 122.379 63.99C121.988 65.9341 120.855 67.3015 117.411 68.2518L114.177 79.1332C114.177 79.1332 115.86 80.584 116.376 81.9877C116.891 83.3914 118.75 94.0841 118.75 94.0841C118.75 94.0841 118.172 94.9583 116.356 94.7479C116.356 94.7479 110.026 81.3276 109.58 80.4534C109.04 79.5682 108.851 78.535 109.045 77.5336C109.244 76.1263 111.389 60.7219 111.389 60.7219L120.523 59.7354Z" fill="#BA68C8"/>
                            <path opacity="0.2" d="M120.523 59.7354C121.168 60.7147 122.707 62.3831 122.379 63.99C121.988 65.9341 120.855 67.3015 117.411 68.2518L114.177 79.1332C114.177 79.1332 115.86 80.584 116.376 81.9877C116.891 83.3914 118.75 94.0841 118.75 94.0841C118.75 94.0841 118.172 94.9583 116.356 94.7479C116.356 94.7479 110.026 81.3276 109.58 80.4534C109.04 79.5682 108.851 78.535 109.045 77.5336C109.244 76.1263 111.389 60.7219 111.389 60.7219L120.523 59.7354Z" fill="black"/>
                            <path d="M121.711 51.4578L113.665 50.25C113.501 50.2174 113.236 50.1557 112.884 50.0723C112.884 50.0723 112.541 54.7585 110.475 55.8321C110.525 56.057 110.568 56.3073 110.607 56.5938C110.728 57.4933 111.388 60.707 111.388 60.707C111.388 60.707 114.513 62.1107 117.079 61.4324C119.645 60.7541 120.512 59.7422 120.512 59.7422L119.227 57.798C119.586 56.9892 121.203 53.7393 121.711 51.4578Z" fill="#BA68C8"/>
                            <path opacity="0.7" d="M121.711 51.4578L113.665 50.25C113.501 50.2174 113.236 50.1557 112.884 50.0723C112.884 50.0723 112.541 54.7585 110.475 55.8321C110.525 56.057 110.568 56.3073 110.607 56.5938C110.728 57.4933 111.388 60.707 111.388 60.707C111.388 60.707 114.513 62.1107 117.079 61.4324C119.645 60.7541 120.512 59.7422 120.512 59.7422L119.227 57.798C119.586 56.9892 121.203 53.7393 121.711 51.4578Z" fill="white"/>
                            <path d="M119.589 57.0254C118.521 57.6212 117.401 58.1344 116.242 58.5597C117.294 58.5402 118.324 58.2758 119.238 57.7907L119.589 57.0254Z" fill="#BA68C8"/>
                            <path d="M113.111 40.8092C113.111 40.8092 112.721 43.1669 113.643 44.5524C113.643 44.5524 113.986 43.613 114.842 43.7907C115.697 43.9684 115.744 45.0167 115.142 45.6043C114.989 45.7691 114.79 45.8906 114.566 45.9547C114.342 46.0189 114.103 46.0231 113.877 45.967C113.877 45.967 113.74 47.6717 113.666 49.1008C113.592 50.5299 113.186 53.6456 114.916 54.9042C116.646 56.1628 120.989 56.6053 123.024 53.9648C124.156 52.4958 122.653 49.8988 121.954 46.9862C121.309 44.3094 120.946 40.3413 117.888 39.703C114.83 39.0646 113.111 40.8092 113.111 40.8092Z" fill="#37474F"/>
                            <path d="M124.331 8.59639L123.687 8.95911C123.741 8.74616 123.771 8.52858 123.777 8.30985C123.777 7.09477 122.859 6.60511 121.726 7.22172C121.382 7.41481 121.074 7.65998 120.816 7.94714C120.816 6.31494 119.582 5.65117 118.059 6.46728C117.256 6.95341 116.588 7.60841 116.109 8.37914C115.63 9.14986 115.354 10.0146 115.302 10.9032C115.302 10.9867 115.302 11.0665 115.302 11.1463C114.909 11.1004 114.512 11.1852 114.181 11.3856C113.584 11.7468 113.087 12.2336 112.731 12.8064C112.375 13.3793 112.169 14.0221 112.13 14.6827C112.13 14.8363 112.147 14.9894 112.181 15.1397L111.838 15.3247C110.904 15.8252 110.146 16.8009 110.146 17.501C110.146 18.201 110.904 18.3678 111.838 17.8637L124.335 11.1716C125.269 10.6711 126.026 9.69541 126.026 8.99538C126.026 8.29534 125.265 8.09222 124.331 8.59639Z" fill="#EBEBEB"/>
                        </svg>
                    </div>
                    <div style="display: flex;justify-content: center; align-item:center;">
                        <button type="button" class="btn btn-primary px-5" style="background: ${data.MauSacTT || '#F76707'} !important;">
                            <span>${data.TenTrangThai || ChuaCoThongTin}</span>
                        </button>
                    </div>
                </div>

                <!-- Phần thông tin chính -->
                <div class="col-md-10" style="width:85.333333%;">
                    <div class="row">
                        <div class="col-md-3">
                            <p class="fs-big my-1">Số quyết định: <b>${data.SoQuyetDinh || ChuaCoThongTin}</b></p>
                        </div>
                        <div class="col-md-3">
                            <p class="fs-big my-1">Ngày ký: <b>${data.txtNgayKy || ChuaCoThongTin}</b></p>
                        </div>
                        <div class="col-md-3">
                            <p class="fs-big my-1">Người ký: <b>${data.TenNguoiKy || ChuaCoThongTin}</b></p>
                        </div>
                        <div class="col-md-3">
                            <p class="fs-big my-1">Chức vụ: <b>${data.TenChucVu || ChuaCoThongTin}</b></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-9">
                            <p class="fs-big my-1">Cơ quan ban hành: <b>${data.CoQuanBanHanh || ChuaCoThongTin}</b></p>
                        </div>
                    <div class="col-md-3">
                         <p class="fs-big my-1">Đính kèm: <b>${
                                typeof data.DinhKem === 'string' && data.DinhKem.trim() !== ''
                                    ? `<a href="#" onclick="XemDinhKem_us('${data.DinhKem.replace(/'/g, "\\'")}')" class="mr-2" data="">
                                        <i class="fa fa-paperclip"></i> Xem đính kèm
                                    </a>`
                                    : 'Chưa có đính kèm'
                            }</b>
                        </p>
                    </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <p class="fs-big my-1">Lý do chỉnh sửa: <b>${data.LyDoChinhSua || ChuaCoThongTin}</b></p>
                        </div>
                    </div>
                    <hr class="hr_trenluoi">

                    <!-- Phần thông tin học sinh -->
                    <div class="row">
                        <div class="col-md-3">
                            <p class="fs-big my-1">Học sinh: <b>${data.TenHocSinh || ChuaCoThongTin}</b></p>
                        </div>
                        <div class="col-md-3">
                            <p class="fs-big my-1">Ngày sinh: <b>${data.txtNgaySinh || ChuaCoThongTin}</b></p>
                        </div>
                        <div class="col-md-3">
                            <p class="fs-big my-1">Trường: <b>${data.TenTruong || ChuaCoThongTin}</b></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <p class="fs-big my-1"><a href="#" style="text-decoration: underline;">Số hiệu cũ: <b>${data.SoHieu || ChuaCoThongTin}</b></a></p>
                        </div>
                        <div class="col-md-3">
                            <p class="fs-big my-1">Số vào sổ cũ: <b>${data.SoVaoSo || ChuaCoThongTin}</b></p>
                        </div>
                    </div>
                    <hr class="hr_trenluoi">

                    <div class="row" style="margin-top:4px;">
                        <div class="col-md-3">
                            <p class="fs-big my-1">Ngày ban hành: <b>${data.txtNgayBanHanh || ChuaCoThongTin}</b></p>
                        </div>
                        <div class="col-md-3">
                            <p class="fs-big my-1">Người ban hành: <b>${data.TenNguoiBanHanh || ChuaCoThongTin}</b></p>
                        </div>
                        <div class="col-md-3">
                            <p class="fs-big my-1">Chức vụ: <b>${data.TenChucVuNBH || ChuaCoThongTin}</b></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <p class="fs-big my-1">Nội dung ban hành: <b>${data.NoiDungBanHanh || ChuaCoThongTin}</b></p>
                        </div>
                    </div>


                </div>
            </div>


            <!-- Phần nút dropdown -->
            <div class="col-sm-2 text-end" >
                <div class="dropdown" style="position: absolute; top: 8px; right: 9px">
                    <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end w-auto" style="font-weight:400;" >
                        <a style="padding: 8px 12px" class="dropdown-item textsize-item btnXemQD" href="javascript:void(0);" data='${cell.getData().id}' onclick="XemTTQD('${cell.getData().id}'); return false;">
                            ${icons["xem"]}
                            &ensp;Xem chi tiết quyết định
                        </a>
                          ${(cell.getData().TrangThai === '22' || cell.getData().TrangThai === '21')
                            ? `
                                <a style="padding: 8px 12px" class="dropdown-item textsize-item btnSuaQD" href="javascript:void(0);" onclick="SuaThongTin('${cell.getData().id}'); return false;" data='${cell.getData().id}'>
                                    <i class="fa fa-pencil nts-iconTT-sua nts-iconThaoTacs" aria-hidden="true"></i>
                                    &ensp;Chỉnh sửa quyết định
                                </a>
                            `
                            : ``}

                       ${(cell.getData().TrangThai === '22' || cell.getData().TrangThai === '21')
                            ? `
                                <a style="padding: 8px 12px" class="dropdown-item textsize-item btnBanHanhQD" href="javascript:void(0);" onclick="BanHanhQD('${cell.getData().id}'); return false;" data='${cell.getData().id}'>
                                    ${icons["banhanh"]}
                                    &ensp;Ban hành quyết định
                                </a>
                            `
                            : `
                                <a style="padding: 8px 12px" class="dropdown-item textsize-item btnBanHanhQD" href="javascript:void(0);" onclick="ThuHoiQD('${cell.getData().id}'); return false;" data='${cell.getData().id}'>
                                    ${icons["thuhoiBH"]}
                                    &ensp;Thu hồi quyết định
                                </a>
                            `}

                             ${(cell.getData().TrangThai === '22' || cell.getData().TrangThai === '21')
                            ? `
                               <a style="padding: 8px 12px" class="dropdown-item textsize-item btnXoaQD" href="javascript:void(0);" onclick="XoaThongTin('${cell.getData().id}'); return false;" data='${cell.getData().id}'>
                                    ${icons["xoa"]}
                                   &ensp;Xóa quyết định
                                </a>
                            `
                            : ``}

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>`;
}
//Lưới danh sách tra cứu

var GridMainDS = new Tabulator("#GridMainDS", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "600",
    HeaderVertAlign: "center",
    headerVisible: false,
    columns: [
        { title: "Thông tin", field: "ThongTinHocSinh",formatter: htmlDuLieu, visible: true,minWidth: 250,},
        { title: "MaDoiTuong", field: "MaDoiTuong", visible: false, width: 100, headerSort: true, },
        { title: "TenHocSinh", field: "TenHocSinh", visible: false, width: 250, headerSort: true, },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

var btnThaoTac = function (cell) {
    return `<div class="show-or-hide col-md-12" style="padding-right: 0px; padding-left: 0px; " >
            <a class='text-success btnXemGridMainLuoi nts-btn-xem' style='margin:2px' title="Xem thông tin học sinh" data='${cell.getData().TenLopHoc}' data2='${cell.getData().TenLopHoc}' data3='${cell.getData().TenLopHoc}'><i class='fa fa-eye'></i></a>
            </div>`;
}

function actionDropdownFormatter(cell) {
    var ID = cell.getData().id;
    var button = document.createElement("button");
    button.className = "btn btn-sm btn-white dropdown-toggle-hide-arrow";
    button.innerHTML = `<i class="fa fa-ellipsis-h" style="color: var(--primary)"></i>`;
    button.style.boxShadow = "none";

    button.onclick = function (e) {
        e.stopPropagation(); // Không lan click ra ngoài

        // Đóng dropdown cũ nếu có
        document
            .querySelectorAll(".custom-dropdown-menu")
            .forEach((el) => el.remove());

        // Tạo dropdown mới
        const dropdown = document.createElement("div");
        dropdown.className =
            "custom-dropdown-menu dropdown-menu dropdown-menu-end show";
        dropdown.style.position = "absolute";
        dropdown.style.zIndex = 9999;
        dropdown.style.minWidth = "200px";
        dropdown.innerHTML =
            `<a style="padding: 8px 12px" class="dropdown-item textsize-item btnXemQD" href="javascript:void(0);" data='${cell.getData().id}' onclick="XemTTQD('${cell.getData().id}'); return false;">
                            ${icons["xem"]}
                            &ensp;Xem chi tiết quyết định
                        </a>
                          ${(cell.getData().TrangThai === '22' || cell.getData().TrangThai === '21')
                            ? `
                                <a style="padding: 8px 12px" class="dropdown-item textsize-item btnSuaQD" href="javascript:void(0);" onclick="SuaThongTin('${cell.getData().id}'); return false;" data='${cell.getData().id}'>
                                    <i class="fa fa-pencil nts-iconTT-sua nts-iconThaoTacs" aria-hidden="true"></i>
                                    &ensp;Chỉnh sửa quyết định
                                </a>
                            `
                            : ``}

                       ${(cell.getData().TrangThai === '22' || cell.getData().TrangThai === '21')
                            ? `
                                <a style="padding: 8px 12px" class="dropdown-item textsize-item btnBanHanhQD" href="javascript:void(0);" onclick="BanHanhQD('${cell.getData().id}'); return false;" data='${cell.getData().id}'>
                                    ${icons["banhanh"]}
                                    &ensp;Ban hành quyết định
                                </a>
                            `
                            : `
                                <a style="padding: 8px 12px" class="dropdown-item textsize-item btnBanHanhQD" href="javascript:void(0);" onclick="ThuHoiQD('${cell.getData().id}'); return false;" data='${cell.getData().id}'>
                                    ${icons["thuhoiBH"]}
                                    &ensp;Thu hồi quyết định
                                </a>
                            `}

                             ${(cell.getData().TrangThai === '22' || cell.getData().TrangThai === '21')
                            ? `
                               <a style="padding: 8px 12px" class="dropdown-item textsize-item btnXoaQD" href="javascript:void(0);" onclick="XoaThongTin('${cell.getData().id}'); return false;" data='${cell.getData().id}'>
                                    ${icons["xoa"]}
                                   &ensp;Xóa quyết định
                                </a>
                            `
                            : ``}
`;

        // Tính vị trí button
        const rect = button.getBoundingClientRect();
        dropdown.style.left = `${rect.left + window.scrollX}px`;
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;

        // Gắn ra body
        document.body.appendChild(dropdown);
    };

    return button;
}

var GridMainLuoi = new Tabulator("#GridMainLuoi", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    locale: true,
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
    paginationCounter: "rows",
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: actionDropdownFormatter,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false,
        },
        { title: "Số quyết định", headerHozAlign: "center", field: "SoQuyetDinh", formatter: 'textarea', hozAlign: "left", visible: true, width: 150, vertAlign: "middle", headerSort: true, },
        { title: "Ngày ký", headerHozAlign: "center", field: "txtNgayKy", formatter: 'textarea', hozAlign: "center", visible: true, width: 120, vertAlign: "middle", headerSort: true, },
        { title: "Người ký", headerHozAlign: "center", field: "TenNguoiKy", formatter: 'textarea', hozAlign: "left", visible: true, width: 200, vertAlign: "middle", headerSort: true, },
        { title: "Chức vụ người ký", headerHozAlign: "center", field: "TenChucVu", formatter: 'textarea', hozAlign: "left", visible: true, width: 150, vertAlign: "middle", headerSort: true, },
        { title: "Cơ quan ban hành", headerHozAlign: "center", field: "CoQuanBanHanh", formatter: 'textarea', hozAlign: "left", visible: true, width: 250, vertAlign: "middle", headerSort: true, },
        { title: "Lý do chỉnh sửa", headerHozAlign: "center", field: "LyDoChinhSua", formatter: 'textarea', hozAlign: "left", visible: true, width: 250, vertAlign: "middle", headerSort: true, },
        { title: "Tên học sinh", headerHozAlign: "center", field: "TenHocSinh", formatter: 'textarea', hozAlign: "left", visible: true, width: 250, vertAlign: "middle", headerSort: true, },
        { title: "Ngày sinh", headerHozAlign: "center", field: "txtNgaySinh", formatter: 'textarea', hozAlign: "center", visible: true, width: 120, vertAlign: "middle", headerSort: true, },
        { title: "Trường", headerHozAlign: "center", field: "TenTruong", formatter: 'textarea', hozAlign: "left", visible: true, width: 200, vertAlign: "middle", headerSort: true, },
        { title: "Số hiệu cũ", headerHozAlign: "center", field: "SoHieu", formatter: 'textarea', hozAlign: "left", visible: true, width: 250, vertAlign: "middle", headerSort: true, },
        { title: "Số vào sổ cũ", headerHozAlign: "center", field: "SoVaoSo", formatter: 'textarea', hozAlign: "left", visible: true, width: 250, vertAlign: "middle", headerSort: true, },
        { title: "Ngày ban hành", headerHozAlign: "center", field: "txtNgayBanHanh", formatter: 'textarea', hozAlign: "center", visible: true, width: 120, vertAlign: "middle", headerSort: true, },
        { title: "Người ban hành", headerHozAlign: "center", field: "TenNguoiBanHanh", formatter: 'textarea', hozAlign: "left", visible: true, width: 200, vertAlign: "middle", headerSort: true, },
        { title: "Chức vụ người ban hành", headerHozAlign: "center", field: "TenChucVuNBH", formatter: 'textarea', hozAlign: "left", visible: true, minWidth: 350, vertAlign: "middle", headerSort: true, },
        { title: "Nội dung ban hành", headerHozAlign: "center", field: "NoiDungBanHanh", formatter: 'textarea', hozAlign: "left", visible: true, minWidth: 350, vertAlign: "middle", headerSort: true, },
        { title: "Trạng thái", headerHozAlign: "center", field: "TenTrangThai", formatter: 'textarea', hozAlign: "left", visible: true, width: 200, vertAlign: "middle", headerSort: true, },
        { title: "QuyetDinhChinhSuaVanBangID", field: "id", visible: false },
    ],
});

function SapXepLuoi() {
    var kiemTraSapXep = $('#BtnSapXepTangGiam i').attr('class').search('fa-sort-alpha-asc');
    if (kiemTraSapXep != -1) { // tìm thấy
        $('#BtnSapXepTangGiam').html('<i class="fa fa-sort-alpha-desc" aria-hidden="true"></i>');
    }
    else {
        $('#BtnSapXepTangGiam').html('<i class="fa fa-sort-alpha-asc" aria-hidden="true"></i>');
    }
    if ($('#btn-layout-1').value() == true) { // Lưới thường
        GridMainLuoi.clearSort();
        GridMainLuoi.setSort([
            { column: $('#CbSapXep').value(), dir: (kiemTraSapXep != -1 ? "desc" : "asc") }, //sort by this first
        ]);
    } else { // Lưới danh sách
        GridMainDS.clearSort();
        GridMainDS.setSort([
            { column: $('#CbSapXep').value(), dir: (kiemTraSapXep != -1 ? "desc" : "asc") }, //sort by this first
        ]);
    }
}

$(document).on('change', '#CbSapXep', function () {
    SapXepLuoi();
});

$(document).on('click', '#BtnSapXepTangGiam', function () {
    SapXepLuoi();
});


$(document).on('click', '#btn-layout-1',async function () {
    var DivLuoi = document.getElementById("DivLuoi");
    var DivDanhSach = document.getElementById("DivDanhSach");
    DivLuoi.style.display = "unset";
    DivDanhSach.style.display = "none";
    await LoadDataTable_Tabu();
    GridMainLuoi.redraw(!0);
});
$(document).on('click', '#btn-layout-2',async function () {
    var DivLuoi = document.getElementById("DivLuoi");
    var DivDanhSach = document.getElementById("DivDanhSach");
    DivLuoi.style.display = "none";
    DivDanhSach.style.display = "unset";
    await LoadDataTable();
    GridMainDS.redraw(!0);
});


$("#btnThemMoi").on("click", showCreateModal);

async function showCreateModal() {
    if (!(await QuyenThem())) {
        return false;
    }
    tempAction = "them";
            selectedId = null;

    resetForm("#mdThemMoi");
    $("#btnChonHocSinh").prop("disabled", false);
    $("#trangThai").prop("checked", true);
    $("#tieuDeModal").text("Thêm mới quyết định chỉnh sửa văn bằng");
    $("#QuyetDinhChinhSuaVanBangID").val("");
    $("#txtDuongDanFileVB").value("");
    $("#list-file").empty();
    tempthem = "them";
    $("#mdThemMoi").modal("show");
}


$(document).on("click", "#btnChonHocSinh", function () {
    $('#mdChonHocSinh').modal("show");
    loadTruongHoc('');
    if (gridChonHocSinh && typeof gridChonHocSinh.destroy === "function") {
        gridChonHocSinh.destroy();
    }

    gridChonHocSinh = initGridChonHocSinh(modeThemHS);
});


//#region Hàm chọn học sinh
 $(document).on("click", "#btnChonVaDong", async function () {
        let selectedRows = gridChonHocSinh.getSelectedData();

        if (selectedRows.length === 0) {
            NTS.canhbao("Vui lòng chọn ít nhất một học sinh.");
            return;
        }

        $('#TenHocSinh').value(selectedRows[0].MaDoiTuong + ": " + selectedRows[0].Hovaten + " - " + selectedRows[0].ten_Truong);
        $('#HocSinhID').val(selectedRows[0].id);
        $("#mdChonHocSinh").modal("hide");
    });
//#endregion

//#region Đính kèm
let uploadedFileUrls = [];

$("#btnChonTepVB").on("click", function () {
    $("#fileVB").trigger("click");
});

$("#fileVB").on("change", async function () {
    const files = Array.from(this.files);
    $("#list-file").empty();
    await NTS.getAjaxAPIAsync("POST", "/api/dungchung/files/clearTemp", {
        chucnang: "QuyetDinhChinhSuaVanBang",
    });
    for (const file of files) {
        const form = new FormData();
        form.append("file", file);
        form.append("chucnang", "QuyetDinhChinhSuaVanBang");
        try {
            const res = await NTS.getAjaxAPIAsync("POST",
                "/api/dungchung/files/uploadFileTemp",
                form,
            );
            if (!res.success) {
                NTS.loi(`Tải lên thất bại: ${file.name}`);
                continue;
            }
            // collect the returned URLs array (or wrap single URL)
            const url = Array.isArray(res.url) ? res.url[0] : res.url;
            uploadedFileUrls.push(url);
        } catch (err) {
            console.error("Upload error", err);
            NTS.loi(`Lỗi upload ${file.name}`);
        }
    }

    // 2) Render previews by iterating uploadedFileUrls
    uploadedFileUrls.forEach((url) => {
        renderAttachment(url);
    });

    const ttList = [].slice.call(
        document.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    ttList.forEach((el) => new bootstrap.Tooltip(el));

    $("#txtDuongDanFileVB").val(uploadedFileUrls.join("|"));
});

$("#btnXoaHetTepVB").on("click", async function () {
    CanhBaoXoa(async () => {
        $("#fileVB").val("");
        $("#list-file").empty();
        $("#txtDuongDanFileVB").val("");

        try {
            // call delete-multiple with our URL array
            if (uploadedFileUrls.length !== 0) {
                const res = await NTS.getAjaxAPIAsync("DELETE",
                    "/api/dungchung/files/delete-multiple",
                    { urls: uploadedFileUrls }

                );
                if (!res.success) {
                    return NTS.loi("Xóa tất cả thất bại: " + (res.message || ""));
                }
                if (res.loi) {
                    return NTS.loi("Xóa tất cả thất bại: " + (res.message || ""));
                }

                NTS.thanhcong("Xoá tất cả đính kèm thành công");
            }
        } catch (err) {
            console.error("Delete all error", err);
            NTS.loi("Có lỗi khi xóa tất cả đính kèm");
        } finally {
            uploadedFileUrls = []; // reset URL store
        }
    });
});

function renderAttachment(url) {
    const filename = url.split("/").pop();

    // 2) get the extension (e.g. "jpg")
    const ext = filename.split(".").pop().toLowerCase();

    // 3) decide if it’s an image
    const imageExts = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
    const isImage = imageExts.includes(ext);

    const $item = $(`
            <div class="file-preview position-relative d-inline-block text-center me-2 mb-2">
            <!-- delete button -->
                <button type="button" class="btn-close position-absolute top-0 end-0 m-1" aria-label="Delete"></button>
            </div>
              `);

    // image vs icon
    let $thumb;
    if (isImage) {
        $thumb = $(`
            <img class="img-thumbnail"
                 style="width:100px;height:100px;object-fit:cover;"
                 src="${url}">
          `);
    } else {
        $thumb = $(`
            <div class="file-icon bg-secondary text-white rounded
                        d-flex align-items-center justify-content-center mb-1"
                 style="width:100px;height:100px;font-size:2rem;">
              <i class="fa fa-file"></i>
            </div>
          `);
    }

    // 1) native tooltip
    $thumb.attr("title", filename);

    // 2) (optional) Bootstrap tooltip
    $thumb.attr("data-bs-toggle", "tooltip").attr("data-bs-placement", "top");

    // assemble
    $item.append($thumb);
    $item.append(
        $(
            '<a target="_blank" class="d-block small text-truncate" style="max-width:100px;"></a>'
        )
            .attr("href", url)
            .text(filename)
    );
    $("#list-file").append($item);

    $item.find(".btn-close").on("click", async () => {
        try {
            //call delete-multiple with a JSON array of this single URL
            const res = await NTS.getAjaxAPIAsync("DELETE",
                "/api/dungchung/files/delete-multiple",
                { urls: [url] }
            );
            if (!res.success) {
                return NTS.loi("Xóa file thất bại: " + filename);
            }
            // remove from uploadedFileUrls
            uploadedFileUrls = uploadedFileUrls.filter((u) => u !== url);
            // update hidden field
            // remove preview from DOM
            $item.remove();
            $("#txtDuongDanFileVB").val(uploadedFileUrls.join("|"));
        } catch (err) {
            console.error("Delete error", err);
            NTS.loi("Lỗi khi xóa " + filename);
        }
    });
}
//#endregion

//region Luu thong tin quyet dinh
    $("#btnLuuVaDong").on("click", async function () {
        const validate = new NTSValidate("#mdThemMoi");
        if (!validate.trim().check()) return false;

        const payload = {
            loai: tempthem,
            SoQuyetDinh: $("#SoQuyetDinh").value(),
            NgayKy: $("#NgayKy").value(),
            NguoiKyID: $("#NguoiKyID").value(),
            ChucVuID: $("#ChucVuID").value(),
            HocSinhID: $("#HocSinhID").value(),
            SoHieu: $("#SoHieu").value(),
            NgayCap: $("#NgayCap").value(),
            SoVaoSo: $("#SoVaoSo").value(),
            NoiDungChinhSua: $("#NoiDungChinhSua").value(),
            LyDoChinhSua: $("#LyDoChinhSua").value(),
            CoQuanBanHanh: $("#CoQuanBanHanh").value(),
            TrichYeu: $("#TrichYeu").value(),
            txtDuongDanFileVB: $("#txtDuongDanFileVB").value(),
            QuyetDinhChinhSuaVanBangID: $("#QuyetDinhChinhSuaVanBangID").value(),
            DonYeuCauCSVBCCID: $("#DonYeuCauCSVBCCID").value(),
            TrangThai: "22", // Chưa ban hành
        };
        var met = "POST";
        if (tempthem == "them") {
            met = "POST";
        } else {
            met = "PUT";
        }
        var result = await NTS.getAjaxAPIAsync(
            met,
            window.location.pathname + "/luuthongtin",
            payload
        );
        debugger;
        if (!result.Err) {
            LoadDataTable();
            NTS.thanhcong(result.Msg);
            $("#mdThemMoi").modal("hide");
        } else {
            result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            return false;
        }
    });
//endregion

//region Chỉnh sửa quyết định
async function SuaThongTin(id) {
    if (!QuyenSua()) { return; }
    resetForm("#mdThemMoi");
    $("#tieuDeModal").text("Cập nhật quyết định chỉnh văn bằng");
    $("#btnChonHocSinh").prop("disabled", false);

    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        {
            id: id,
        }
    );
    if (!result.Err) {
        let data = result.Result;
        $("#SoQuyetDinh").value(data.SoQuyetDinh);
        $("#NgayKy").value(data.txtNgayKy);
        $("#NguoiKyID").value(data.NguoiKyID);
        $("#ChucVuID").value(data.ChucVuID_NK);
        $("#HocSinhID").val(data.HocSinhID);
        $("#TenHocSinh").value(data.MaDoiTuong + ": " + data.TenHocSinh + " - " + data.TenTruong);
        $("#SoHieu").value(data.SoHieu);
        $("#NgayCap").value(data.txtNgayCap);
        $("#SoVaoSo").value(data.SoVaoSo);
        $("#NoiDungChinhSua").value(data.NoiDungChinhSua);
        $("#LyDoChinhSua").value(data.LyDoChinhSua);
        $("#CoQuanBanHanh").value(data.CoQuanBanHanh);
        $("#TrichYeu").value(data.TrichYeu);
        $("#QuyetDinhChinhSuaVanBangID").value(id);
        $("#DonYeuCauCSVBCCID").value(data.DonYeuCauCSVBCCID);
        uploadedFileUrls = data.DinhKem
            ? data.DinhKem.split("|").filter((u) => u)
            : [];

        $("#txtDuongDanFileVB").value(data.DinhKem);
        $("#list-file").empty();

        uploadedFileUrls.forEach((url) => {
            renderAttachment(url);
        });
        tempthem = "sua";

        $("#mdThemMoi").modal("show");
    }
    return false;
}
//endregion

//region Xóa quyết định
async function XoaThongTin(id) {
    if (!QuyenXoa()) { return; }
    var result_ktxoa = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.layouts.KiemTraXoa,
        {
            ma: id,
            model: "QuanLy\\QuyetDinhChinhSuaVanBang",
        }
    );

    if (!result_ktxoa.Err) {
        CanhBaoXoa(async () => {
            var result = await NTS.getAjaxAPIAsync(
                "DELETE",
                window.location.pathname + "/xoa",
                { ma: id }
            );
            debugger
            if (!result.Err) {
                LoadDataTable();
                NTS.thanhcong(result.Msg);
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
    } else {
        NTS.canhbao(result_ktxoa.Msg);
        return false;
    }
}
//endregion

//region Ban hành quyết định
function BanHanhQD(id) {
    resetForm("#mdQuyetDinhTotNghiep");
    $("#mdQuyetDinhTotNghiep").modal("show");
    $("#tieuDe_mdQuyetDinhTotNghiep").text("Ban hành quyết định chỉnh sửa văn bằng");
    $("#QuyetDinhChinhSuaVanBangID").value(id);
    return false;
}


$("#btnBanHanhQD").on("click", async function () {
    const validate = new NTSValidate("#mdQuyetDinhTotNghiep");
    if (!validate.trim().check()) return false;

    const payload = {
        NguoiBanHanhID: $("#NguoiBanHanhID").val(), // Sử dụng .val()
        NgayBanHanh: $("#NgayBanHanh").val(),
        ChucVuID_NBH: $("#ChucVuID_NBH").val(),
        NoiDungBanHanh: $("#NoiDungBanHanh").value(),
        QuyetDinhChinhSuaVanBangID: $("#QuyetDinhChinhSuaVanBangID").val(),
        TrangThai: "20",
    };

    try {
        var result = await NTS.getAjaxAPIAsync(
            "PUT",
            window.Laravel.quyetdinhchinhsuavanbang.BanHanhQD,
            payload
        );
        if (!result.Err) {
            LoadDataTable();
            NTS.thanhcong(result.Msg);
            $("#mdQuyetDinhTotNghiep").modal("hide");
        } else {
            result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            return false;
        }
    } catch (error) {
        NTS.loi("Lỗi hệ thống: " + error.message);
        return false;
    }
});

//endregion


//region thu hồi quyết định
function ThuHoiQD(id) {
    resetForm("#mdThuHoiQD");
    $("#mdThuHoiQD").modal("show");
    $("#tieuDe_mdThuHoiQD").text("Thu hồi quyết định chỉnh sửa văn bằng");
    $("#QuyetDinhChinhSuaVanBangID").value(id);
    return false;
}


$("#btnThuHoiQD").on("click", async function () {
    const validate = new NTSValidate("#mdThuHoiQD");
    if (!validate.trim().check()) return false;

    const payload = {
        NguoiThuHoiBanHanhID: $("#NguoiThuHoiBanHanhID").val(), // Sử dụng .val()
        NgayBNgayThuHoiBanHanhanHanh: $("#NgayThuHoiBanHanh").val(),
        ChucVuID_NTH: $("#ChucVuID_NTH").val(),
        NoiDungThuHoiBanHanh: $("#NoiDungThuHoiBanHanh").value(),
        QuyetDinhChinhSuaVanBangID: $("#QuyetDinhChinhSuaVanBangID").val(),
        TrangThai: "21",
    };

    try {
        var result = await NTS.getAjaxAPIAsync(
            "PUT",
            window.Laravel.quyetdinhchinhsuavanbang.ThuHoiQD,
            payload
        );
        if (!result.Err) {
            LoadDataTable();
            NTS.thanhcong(result.Msg);
            $("#mdThuHoiQD").modal("hide");
        } else {
            result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            return false;
        }
    } catch (error) {
        NTS.loi("Lỗi hệ thống: " + error.message);
        return false;
    }
});

//endregion

//region Xem thông tin quyết định
async function XemTTQD(id) {
    selectedId = id;
    $("#mdXemThongTin").modal("show");
    $("#tieuDeModalXemTTQD").text("Xem chi tiết quyết định chỉnh sửa văn bằng");
    $("#QuyetDinhChinhSuaVanBangID").val(id); // Sử dụng .val() thay vì .value cho jQuery

    // Xóa giá trị cũ
    $('#txtSoQuyetDinh').text('');
    $('#txtNgayKy').text('');
    $('#txtNguoiKy').text('');
    $('#txtChuVuNguoiKy').text('');
    $('#txtCoQuanBanHanh').text('');
    $('#txtLyDoChinhSua').text('');
    $('#txtTrichYeu').text('');
    $('#txtHocSinh').text('');
    $('#txtNgaySinh').text('');
    $('#txtTruong').text('');
    $('#txtSoHieuCu').text('');
    $('#txtSoVaoSoCu').text('');
    $('#txtNgayBanHanh').text('');
    $('#txtNguoiBanHanh').text('');
    $('#txtChucVuNguoiBanHanh').text('');
    $('#txtNoiDungBanHanh').text('');
    $('#txtDinhKem').text('');

    // Xóa danh sách file cũ
    $('#fileAttachmentList').empty();

    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        {
            id: id,
        }
    );
    if (!result.Err) {
        let data = result.Result;
        $('#txtSoQuyetDinh').text(data.SoQuyetDinh);
        $('#txtNgayKy').text(data.txtNgayKy);
        $('#txtNguoiKy').text(data.TenNguoiKy);
        $('#txtChuVuNguoiKy').text(data.TenChucVu_NK);
        $('#txtCoQuanBanHanh').text(data.TenCoQuanBanHanh); // Giả định trường này
        $('#txtLyDoChinhSua').text(data.LyDoChinhSua);
        $('#txtTrichYeu').text(data.TrichYeu);
        $('#txtHocSinh').text(data.MaDoiTuong + ": " + data.TenHocSinh + " - " + data.TenTruong);
        $('#txtNgaySinh').text(data.txtNgaySinh);
        $('#txtTruong').text(data.TenTruong);
        $('#txtSoHieuCu').text(data.SoHieu);
        $('#txtSoVaoSoCu').text(data.SoVaoSo);
        $('#txtNgayBanHanh').text(data.txtNgayBanHanh);
        $('#txtNguoiBanHanh').text(data.TenNguoiBanHanh);
        $('#txtChucVuNguoiBanHanh').text(data.TenChucVu_NBH);
        $('#txtNoiDungBanHanh').text(data.NoiDungBanHanh);

        const dinhKem = (data.DinhKem || '').trim();

        const fileList = $('#fileAttachmentList');
        fileList.empty(); // Xoá nội dung cũ

        if (dinhKem !== '') {
            fileList.append(`
                <div class="list-group-item">
                    Đính kèm:
                    <a href="#" onclick="XemDinhKem_us('${dinhKem.replace(/'/g, "\\'")}')">
                        <i class="fa fa-paperclip me-1"></i> Xem đính kèm
                    </a>
                </div>
            `);
        } else {
            fileList.append('<div class="list-group-item">Chưa có đính kèm</div>');
        }

    }
    return false;
}
//endregion

//region Lấy danh sách Đơn YCCSVBCC
function htmlDuLieuDonYCCSVBCC(cell) {
    const data = cell.getData();

    const fileName = data.TenTepDinhKem || "Không rõ";
    const fileLink = data.LinkDinhKem || "#";
    const dinhKemHTML = data.LinkDinhKem
        ? `<a href="${fileLink}" target="_blank">${fileName}</a>`
        : "Không có";

    let labelNgay, labelNguoi, labelChucVu, labelNoiDung;
    let txtNgay = data.txtNgayTiepNhanXuLy || "";
    let tenNguoi = data.TenNguoiTiepNhanXuLy || "";
    let chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
    let noiDung = data.NoiDungTiepNhanXuLy || "";
    switch (parseInt(data.TrangThaiXuLyID)) {
        case 40:
            labelNgay = "Ngày gửi";
            labelNguoi = "Người gửi";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung gửi";

            txtNgay = data.txtNgayXuLy || "";
            tenNguoi = data.TenNguoiXuLy || "";
            chucVu = data.TenChucVuNguoiXuLy || "";
            noiDung = data.NoiDungXuLy || "";
            break;
        case 41:
            labelNgay = "Ngày yêu cầu";
            labelNguoi = "Người yêu cầu";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung yêu cầu";

            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        case 42:
            labelNgay = "Ngày từ chối";
            labelNguoi = "Người từ chối";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung từ chối";


            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        case 32:
            labelNgay = "Ngày phê duyệt";
            labelNguoi = "Người phê duyệt";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung phê duyệt";


            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        default:
            labelNgay = "Ngày yêu cầu";
            labelNguoi = "Người yêu cầu";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung yêu cầu";

            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;

    }
    return `<div class="list-item col-md-12" style="padding: 0px;">
        <div class="card card-luoi shadow-sm mb-2">
            <div id="card_${data.id}" class="card-body profile-user-box">
                <div class="row">
                    <div class="col-sm-2 text-center" style="width:12%; flex-direction: column;
                        display: flex;
                        align-items: center;
                        justify-content: space-around;">
                        <div class="profile-picture" style="height: 127px;
                        width: 127px;
                        object-fit: contain;
                        object-position: center;">
                            <img src="${window.Laravel.quyetdinhchinhsuavanbang.linkAnhDonXin}" alt="ảnh đơn xin" class="img-thumbnail rounded lazy mb-2" style="background: white !important;
                            border: none !important;
                            box-shadow: unset !important;">
                        </div>
                        <div style="display: flex; justify-content: center;">
                            <span style="
                                display: block;
                                margin-top: 10px;
                                font-weight: 600;
                                font-size: 12px;
                                text-align: center;
                                padding: 6px 30px;
                                background-color: ${data.MauSacTrangThaiXuLy};
                                color: white;
                                border-radius: 6px;
                                width: 95%;
                                ">
                                ${data.TenTrangThaiXuLy}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-10" style="width:88%;">
                        <div class="row">
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-4">
                                        <p class="fs-big my-0">Ngày lập: <b>${data.txtNgayLap || ""}</b></p>
                                    </div>
                                    <div class="col-md-4">
                                        <p class="fs-big my-0">Người lập: <b>${data.TenNguoiLap || ""}</b></p>
                                    </div>
                                    <div class="col-md-4">
                                        <p class="fs-big my-0">Chức vụ: <b>${data.TenChucVuNguoiLap || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="dropdown text-end" style="position: absolute; top: 10px; right: 10px;">
                                    <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-end w-auto">
                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item" style="font-size: 25px;"
                                        href="javascript:void(0);" onclick="LuuThongTinQD_DonYeuCau('${data.id}'); return false;">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 16 18" fill="none">
                                                <path d="M11 9H9V7C9 6.73478 8.89464 6.48043 8.70711 6.29289C8.51957 6.10536 8.26522 6 8 6C7.73478 6 7.48043 6.10536 7.29289 6.29289C7.10536 6.48043 7 6.73478 7 7V9H5C4.73478 9 4.48043 9.10536 4.29289 9.29289C4.10536 9.48043 4 9.73478 4 10C4 10.2652 4.10536 10.5196 4.29289 10.7071C4.48043 10.8946 4.73478 11 5 11H7V13C7 13.2652 7.10536 13.5196 7.29289 13.7071C7.48043 13.8946 7.73478 14 8 14C8.26522 14 8.51957 13.8946 8.70711 13.7071C8.89464 13.5196 9 13.2652 9 13V11H11C11.2652 11 11.5196 10.8946 11.7071 10.7071C11.8946 10.5196 12 10.2652 12 10C12 9.73478 11.8946 9.48043 11.7071 9.29289C11.5196 9.10536 11.2652 9 11 9ZM15.707 4.293L11.707 0.293C11.5195 0.105451 11.2652 5.66374e-05 11 0H3C1.346 0 0 1.346 0 3V15C0 16.654 1.346 18 3 18H13C14.654 18 16 16.654 16 15V5C15.9999 4.73481 15.8946 4.48049 15.707 4.293ZM13.586 5H12.5C11.673 5 11 4.327 11 3.5V2.414L13.586 5ZM13 16H3C2.73478 16 2.48043 15.8946 2.29289 15.7071C2.10536 15.5196 2 15.2652 2 15V3C2 2.73478 2.10536 2.48043 2.29289 2.29289C2.48043 2.10536 2.73478 2 3 2H10V3.5C10 4.879 11.121 6 12.5 6H14V15C14 15.2652 13.8946 15.5196 13.7071 15.7071C13.5196 15.8946 13.2652 16 13 16Z" fill="#146654"/>
                                            </svg>&ensp; Quyết định điều chỉnh văn bằng
                                        </a>
                                         <a style="padding: 5px 12px" class="dropdown-item textsize-item btnxemNhatKtDonYC" style="font-size: 25px;"
                                        href="javascript:void(0);" onclick="getNhatKyThaoTac('NhatKy_XemChiTietDonYC','yeu_cau_c_s_n_d_v_b_c_c_s', '${data.id}');" >
                                            <i class="fa fa-history" aria-hidden="true" style="color: #F76707;font-size: 26px;"></i>&ensp; Xem nhật ký thao tác
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-4">
                                        <p class="fs-big my-0">Đơn vị yêu cầu: <b>${data.TenDonViGui || ""}</b></p>
                                    </div>
                                    <div class="col-md-4">
                                        <p class="fs-big my-0">Học sinh: <b>${data.TenHocSinh || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-4">
                                        <p class="fs-big my-0">Lý do chỉnh sửa: <b>${data.LyDoDieuChinh || ""}</b></p>
                                    </div>
                                    <div class="col-md-4">
                                        <p class="fs-big my-0">Số hiệu: <b>${data.SoHieu || ""}</b></p>
                                    </div>
                                     <div class="col-md-4">
                                        <p class="fs-big my-0">Số vào sổ: <b>${data.SoVaoSo || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                                <div class="col-md-11">
                                    <div class="row mb-12">
                                        <div class="col-md-8">
                                            <p class="fs-big my-0">Đơn vị tiếp nhận: <b>${data.TenDonViNhan || ""}</b></p>
                                        </div>
                                        <div class="col-md-4">
                                        <p class="fs-big my-1">Đính kèm: <b>${
                                                typeof data.DinhKem === 'string' && data.DinhKem.trim() !== ''
                                                    ? `<a href="#" onclick="XemDinhKem_us('${data.DinhKem.replace(/'/g, "\\'")}')" class="mr-2" data="">
                                                        <i class="fa fa-paperclip"></i> Xem đính kèm
                                                    </a>`
                                                    : 'Chưa có đính kèm'
                                            }</b>
                                        </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-1"></div>
                                <div class="col-md-11">
                                    <div class="row mb-12">
                                        <div class="col-md-12">
                                            <p class="fs-big my-1">Ghi chú: <b>${data.GhiChu || ""}</b></p>
                                        </div>
                                    </div>
                                </div>
                            <div class="col-md-1"></div>
                        </div>
                        <div class="row" style="padding:0 6px;">
                            <hr style="border-top: 2px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;opacity:0.6;" />
                        </div>
                        <div class="row">
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">${labelNgay}: <b>${txtNgay}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">${labelNguoi}: <b>${tenNguoi}</b></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="fs-big my-1">${labelChucVu}: <b>${chucVu}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">${labelNoiDung}: <b>${noiDung}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>`;
}

var table_DSDonYCCSVBCC = new Tabulator("#GridDSDonYeuCau", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "600",
    HeaderVertAlign: "center",
    headerVisible: false,
    columns: [
        {
            title: "Thông tin",
            field: "",
            formatter: htmlDuLieuDonYCCSVBCC,
            visible: true,
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

async function LoadDataTable_DSDonYCCSVBCC() {
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.quyetdinhchinhsuavanbang.GetYeuCauCSNDVBCC,{}    );
    table_DSDonYCCSVBCC.clearData();
    if (!result.Err) {
        table_DSDonYCCSVBCC.setData(result.result);
        table_DSDonYCCSVBCC.redraw(1);
    } else {
        table_DSDonYCCSVBCC.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
    return false;
}

$(document).on("click", "#btnThemMoiKhongTheoDYC", async function () {
    $("#mdThemMoiKhongTheoDYC").modal("show");
    $("#DonYeuCauCSVBCCID").value("");
    $("#tieuDeModalDonYCCSVBCC").text("THÊM MỚI QUYẾT ĐỊNH CHỈNH SỬA NỘI DUNG VĂN BẰNG");
    await LoadDataTable_DSDonYCCSVBCC();
});

async function LuuThongTinQD_DonYeuCau(id) {
    var donYeuCauCSVBCCID = id;
    $("#DonYeuCauCSVBCCID").value(id);
    $("#btnChonHocSinh").prop("disabled", true);
    $('#mdThemMoi').modal('show');
    $("#mdThemMoiKhongTheoDYC").modal("hide");
    resetForm("#mdThemMoi");
    $("#tieuDeModal").text("Thêm mới quyết định chỉnh văn bằng");
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.quyetdinhchinhsuavanbang.GetDonYeuCauByID,
        {
            id: id,
        }
    );

    if (!result.Err) {
        let data = result.Result;
        $("#SoQuyetDinh").value('');
        $("#NgayKy").value();
        $("#NguoiKyID").value();
        $("#ChucVuID").value();
        $("#HocSinhID").val(data.HocSinhID);
        $("#TenHocSinh").value(data.MaDoiTuong + ": " + data.TenHocSinh + " - " + data.TenTruong);
        $("#SoHieu").value(data.SoHieu);
        $("#NgayCap").value('');
        $("#SoVaoSo").value(data.SoVaoSo);
        $("#NoiDungChinhSua").value('');
        $("#LyDoChinhSua").value(data.LyDoDieuChinh);
        $("#CoQuanBanHanh").value('');
        $("#TrichYeu").value('');
        $("#QuyetDinhChinhSuaVanBangID").value('');
        $("#DonYeuCauCSVBCCID").value(id);
        uploadedFileUrls = data.DinhKem
            ? data.DinhKem.split("|").filter((u) => u)
            : [];

        $("#txtDuongDanFileVB").value(data.DinhKem);
        $("#list-file").empty();

        uploadedFileUrls.forEach((url) => {
            renderAttachment(url);
        });
        tempthem = "them";
    }
 }

 $(document).on("click", ".btnxemNhatKtDonYC", async function () {
    $('#mdXemThongTinNhatKy_DonYeuCauCSVBCC').modal('show');
 });

//endregion

