var tempthem = "them";
var tempTrangThai = "40";
var ChuaCoThongTin = "";
///////// PHÍM TẮT /////////
var hotKey = 0; // 1 thêm\
var currentAvatarPath = "";
const today = new Date();
const dd = String(today.getDate()).padStart(2, '0');
const mm = String(today.getMonth() + 1).padStart(2, '0');
const yyyy = today.getFullYear();
const defaultDate = `${dd}/${mm}/${yyyy}`;
var selectedId;
$(function () {
    $(document).on("keydown", function (e) {
        switch (e.keyCode) {
            case 113:
                if (hotKey == 0) $("#btnThemMoi").trigger("click");
                e.preventDefault();
                break;
            case 114:
                if (hotKey == 0) $(".nav-search-input").focus();
                e.preventDefault();
                break;
            case 115:
                if (hotKey == 1) $("#mdThemMoi").modal("hide");
                e.preventDefault();
                break;
            case 120:
                if (hotKey == 1) $("#btnLuuVaDong").trigger("click");
                e.preventDefault();
                break;
        }
    });

    LoadDataComBo();
    LoadDataComBo_Loc();
    LoadDataTable();
    LoadDataComBo_GuiDonXinCapPhoiBang();
});

$(document).on("shown.bs.modal", "#mdThemMoi", function () {
    hotKey = 1;
});
$(document).on("hidden.bs.modal", "#mdThemMoi", function () {
    hotKey = 0;
});
$(document).on("click", "#btnThemMoi", function () {

    NTS.getAjaxAPIAsync("GET", window.Laravel.layouts.getCurrentUserInfo, {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NguoiLapID").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuNguoiLapID").value(nhanVien.chuc_vu.id);
                }
            }
            console.log(response);
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });
    ThemDuLieu();
});

async function ThemDuLieu() {
    if (!QuyenThem()) {
        return;
    }

    resetForm("#mdThemMoi");
    $("#tieuDeModal").text("Thêm mới phiếu đề nghị cấp phôi văn bằng, chứng chỉ");
    $("#DonYeuCauID").val("");
    tempthem = "them";
    try {
        const input = document.getElementById("NgayLap");
        const today = new Date();
        const dd = String(today.getDate()).padStart(2, '0');
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const yyyy = today.getFullYear();

        input.value = `${dd}/${mm}/${yyyy}`;
        $("#NgayLap").val(timestamp);


    } catch { }


    try {
        await initPhoiVBCCTable([]);
    } catch (error) {
        console.error("Lỗi khi khởi tạo phôi:", error);
    }
    $("#mdThemMoi").modal("show");

}

async function SuaDuLieu(id) {
    if (!QuyenSua()) {
        return false;
    }

    $("#tieuDeModal").text("Cập nhật đơn xin cấp phôi bằng");
    $("#DonYeuCauID").val(id);

    const result = await NTS.getAjaxAPIAsync("GET", window.location.pathname + "/loadDuLieuSua", { id: id });

    if (!result.Err) {
        const data = result.Result;

        $("#NgayLap").val(data.txtNgayLap || "");
        $("#NguoiLapID").value(data.NguoiLapID || "");
        $("#ChucVuNguoiLapID").value(data.ChucVuNguoiLapID || "");
        $("#DonViNhanID").value(data.DonViNhanID || "");
        $("#LyDoXinCap").val(data.LyDoXinCap || "");
        $("#GhiChu").val(data.GhiChu || "");
        $("#SoPhieu").val(data.SoPhieu || "");
        $("#NguoiLienHe").val(data.NguoiLienHe || "");
        $("#SoDienThoai").val(data.SoDienThoai || "");
        $("#Email").val(data.Email || "");
        $("#DiaChi").value(data.DiaChi || "");
        $("#HinhThucNhanPhoiID").value(data.HinhThucNhanPhoiID || "");
        $("#NgayLap").value(data.NgayLap || "");

        if (data.DinhKem) {
            $("#dinhKemQD_txtDuongDanFileVB").val(data.DinhKem);
            $("#dinhKemQD_list-file").empty();

            var links = data.DinhKem.split('|');
            links.forEach(function (link) {
                link = link.trim();
                if (link) {
                    renderAttachment(link);
                }
            });
        }
        // Process PhoiVBCC data - ensure it's an array
        let phoiVBCCArr = [];
        if (typeof data.PhoiVBCC === "string") {
            try {
                phoiVBCCArr = JSON.parse(data.PhoiVBCC);
            } catch {
                phoiVBCCArr = [];
            }
        } else if (Array.isArray(data.PhoiVBCC)) {
            phoiVBCCArr = data.PhoiVBCC;
        }

        try {
            // Ensure combo options are loaded first
            await fetchLoaiPhoiVBCCOptions();
            await fetchDonViTinhptions();

            // Then render the table with the data
            await renderPhoiVBCCTable(phoiVBCCArr);
        } catch (error) {
            console.error("Lỗi khi khởi tạo bảng phôi VBCC:", error);
        }
        $("#mdThemMoi").modal("show");
        tempthem = "sua";
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

function GuiDonXinCapPhoiBang(id, SoPhieu, txtNgayLap, DonViGui) {
    $("#DonYeuCauID").val(id);
    $("#NgayXuLy").val(defaultDate);

    // Set default values from current user info
    NTS.getAjaxAPIAsync("GET", window.location.pathname + "/getCurrentUserInfo", {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NguoiXuLyID").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuNguoiXuLyID").value(nhanVien.chuc_vu.id);
                }
            }
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });

    $("#tieuDe_mdGuiDonXinCapPhoiBang").text("Gửi phiếu đề nghị cấp phôi văn bằng, chứng chỉ");
    $("#alertMessage").html(
        'Bạn đang thực hiện gửi phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: <b id="SoPhieu_lbl"></b> ngày lập <b id="txtNgayLap_lbl"></b> của <b id="TenDonViGui_lbl"></b>. Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>"Gửi"</b> để thực hiện thao tác gửi phiếu đề nghị đến cơ quan có thẩm quyền.'
    );

    $("#btnGuiDon").html('<i class="fa fa-paper-plane-o me-1"></i> Gửi (F9)');
    $('#SoPhieu_lbl').text(SoPhieu);
    $('#txtNgayLap_lbl').text(txtNgayLap);
    $('#TenDonViGui_lbl').text(DonViGui);
    tempTrangThai = "40";

    $('#DonViTiepNhanXuLyID').closest('.col-12').show();
    $('#DonViTiepNhanXuLyID').attr('required', true);
    $('#mdGuiDonXinCapPhoiBang').modal('show');

    const noiDungText = `Gửi phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: ${SoPhieu} ngày lập ${txtNgayLap} của ${DonViGui}.`;
    $('#NoiDungXuLy').val(noiDungText);
}

function ThuHoiDonXinCapPhoiBang(id, SoPhieu, txtNgayLap, DonViGui) {
    $("#DonYeuCauID").val(id);
    $("#NgayXuLy").val(defaultDate);

    // Set default values from current user info
    NTS.getAjaxAPIAsync("GET", window.location.pathname + "/getCurrentUserInfo", {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NguoiXuLyID").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuNguoiXuLyID").value(nhanVien.chuc_vu.id);
                }
            }
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });

    $("#tieuDe_mdGuiDonXinCapPhoiBang").text("Thu hồi gửi phiếu đề nghị cấp phôi văn bằng, chứng chỉ");
    $("#alertMessage").html('Bạn đang thực hiện thu hồi gửi phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: <b id="SoPhieu_lbl"></b> ngày lập <b id="txtNgayLap_lbl"></b> của <b id="TenDonViGui_lbl"></b>. Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>"Thu hồi"</b> để thực hiện thao tác thu hồi phiếu đề nghị đến cơ quan có thẩm quyền.');
    $("#btnGuiDon").html('<i class="fa fa-undo me-1"></i> Thu hồi (F9)');
    $('#SoPhieu_lbl').text(SoPhieu)
    $('#txtNgayLap_lbl').text(txtNgayLap)
    $('#TenDonViGui_lbl').text(DonViGui)
    tempTrangThai = "41";
    $('#DonViTiepNhanXuLyID').closest('.col-12').hide();
    $('#DonViTiepNhanXuLyID').removeAttr('required');
    $('#mdGuiDonXinCapPhoiBang').modal('show');

    const noiDungText = `Thu hồi phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: ${SoPhieu} ngày lập ${txtNgayLap} của ${DonViGui}.`;
    $('#NoiDungXuLy').val(noiDungText);
}
function XemChiTietNhatKy(id) {
    $("#DonYeuCauID").val(id);
    selectedId = id;
    $.ajax({
        url: window.location.pathname + '/loadDuLieuSua',
        method: 'GET',
        data: { id: id },
        success: function (res) {
            const configByTrangThai = {
                40: {
                    labelNgay: 'Ngày đề nghị',
                    labelNguoi: 'Người đề nghị',
                    labelChucVu: 'Chức vụ người đề nghị',
                    labelNoiDung: 'Nội dung đề nghị',

                },
                41: {
                    labelNgay: 'Ngày gửi',
                    labelNguoi: 'Người gửi',
                    labelChucVu: 'Chức vụ người gửi',
                    labelNoiDung: 'Nội dung gửi',

                },
                42: {
                    labelNgay: 'Ngày từ chối',
                    labelNguoi: 'Người từ chối',
                    labelChucVu: 'Chức vụ người từ chối',
                    labelNoiDung: 'Nội dung từ chối',

                },

                32: {
                    labelNgay: 'Ngày phê duyệt',
                    labelNguoi: 'Người phê duyệt',
                    labelChucVu: 'Chức vụ',
                    labelNoiDung: 'Nội dung phê duyệt',

                },
                default: {
                    labelNgay: 'Ngày xử lý',
                    labelNguoi: 'Người xử lý',
                    labelChucVu: 'Chức vụ',
                    labelNoiDung: 'Nội dung',

                }
            };
            if (!res.Err && res.Result) {
                var data = res.Result;
                if (window.Laravel && window.Laravel.local && window.Laravel.local.linkAnhDonXin) {
                    $('#dxcpb_imgDonXin').attr('src', window.Laravel.local.linkAnhDonXin);
                }
                $('#dxcpb_SoPhieu_ct').text(data.SoPhieu || '');
                $('#dxcpb_NgayLap_ct').text(data.txtNgayLap || '');
                $('#dxcpb_NguoiLap_ct').text(data.TenNguoiLap || '');
                $('#dxcpb_ChucVuNguoiLap_ct').text(data.TenChucVuNguoiLap || '');
                $('#dxcpb_DonViGui_ct').text(data.TenDonViGui || '');
                $('#dxcpb_NguoiLienHe_ct').text(data.NguoiLienHe || '');
                $('#dxcpb_SoDienThoai_ct').text(data.SoDienThoai || '');
                $('#dxcpb_Email_ct').text(data.Email || '');
                $('#dxcpb_DiaChi_ct').text(data.DiaChi || '');
                $('#dxcpb_HinhThucNhanPhoi_ct').text(data.TenHinhThucNhanPhoi || '');
                $('#dxcpb_DonViNhan_ct').text((data.TenDonViNhan || '') + (data.MaDonViNhan ? ` (${data.MaDonViNhan})` : ''));
                $('#dxcpb_LyDoXinCap_ct').text(data.LyDoXinCap || '');
                $('#dxcpb_TrangThai_ct')
                    .text(data.TenTrangThaiXuLy || 'Chưa rõ')
                    .css('background-color', data.MauSacTrangThaiXuLy || '#ccc');

                $('#dxcpb_CacMinhChung_ct').text(data.MinhChungKemTheo || '');
                // if (data.DinhKem && data.LinkDinhKem) {
                //     $('#dxcpb_DinhKem_ct').html(`<a href="${data.LinkDinhKem}" target="_blank">${data.TenTepDinhKem || 'Tệp đính kèm'}</a>`);
                // } else {
                //     $('#dxcpb_DinhKem_ct').html('');
                // }
                $('#txtDinhKem').html(`<p class="fs-big my-1">
                            Đính kèm: 
                            <a href="#" data="" onclick="XemDinhKem_us('`+ data.DinhKem + `')">
                                <i class="fa fa-paperclip me-1"></i> Xem đính kèm
                            </a>
                            </p> `)
                $('#dxcpb_GhiChu_ct').text(data.GhiChu || '');
                var labelNgay = 'Ngày xử lý', labelNguoi = 'Người xử lý', labelChucVu = 'Chức vụ', labelNoiDung = 'Nội dung';
                if (data.TrangThaiXuLyID == 40) {
                    labelNgay = 'Ngày đề nghị';
                    labelNguoi = 'Người đề nghị';
                    labelChucVu = 'Chức vụ người đề nghị';
                    labelNoiDung = 'Nội dung đề nghị';
                    labelDonViTiepNhan = 'Đơn vị tiếp nhận';
                } else if (data.TrangThaiXuLyID == 41) {
                    labelNgay = 'Ngày gửi';
                    labelNguoi = 'Người gửi';
                    labelChucVu = 'Chức vụ người gửi';
                    labelNoiDung = 'Nội dung gửi';
                } else if (data.TrangThaiXuLyID == 42) {
                    labelNgay = 'Ngày từ chối';
                    labelNguoi = 'Người từ chối';
                    labelChucVu = 'Chức vụ người từ chối';
                    labelNoiDung = 'Lý do từ chối';
                }

                const tbody = document.querySelector('#phoiVBCCTable_ct tbody');
                tbody.innerHTML = '';
                let count = 1;
                data.PhoiVBCC.forEach(item => {
                    const tr = document.createElement('tr');

                    const tenPhoi = item.TenLoaiPhoiVanBangChungChi;
                    const donViTinh = item.TenDonViTinh;
                    const soLuong = item.SoLuongPhoi;

                    tr.innerHTML = `
                    <td>${count}</td>
                    <td>${tenPhoi}</td>
                    <td>${donViTinh}</td>
                    <td>${soLuong}</td>
                `;

                    tbody.appendChild(tr);
                    count++;
                });

                const config = configByTrangThai[parseInt(data.TrangThaiXuLyID)] || configByTrangThai.default;

                $('#dxcpb_LabelNgay_ct').html('<b>' + (data.txtNgayXuLy || '') + '</b>');
                $('#dxcpb_LabelNguoi_ct').html('<b>' + (data.TenNguoiXuLy || '') + '</b>');
                $('#dxcpb_LabelChucVu_ct').html('<b>' + (data.TenChucVuNguoiXuLy || '') + '</b>');
                $('#dxcpb_LabelNoiDung_ct').html('<b>' + (data.NoiDungXuLy || '') + '</b>');

                $('#dxcpb_LabelNgayTiepNhanXuLy_ct').text(config.labelNgay + ': ');
                $('#dxcpb_NgayTiepNhanXuLy_ct').text(data.txtNgayTiepNhanXuLy || '');

                $('#dxcpb_LabelNguoiTiepNhanXuLy_ct').text(config.labelNguoi + ': ');
                $('#dxcpb_NguoiTiepNhanXuLy_ct').text(data.TenNguoiTiepNhanXuLy || '');

                $('#dxcpb_LabelChucVuNguoiTiepNhanXuLy_ct').text(config.labelChucVu + ': ');
                $('#dxcpb_ChucVuNguoiTiepNhanXuLy_ct').text(data.TenChucVuNguoiTiepNhanXuLy || '');

                $('#dxcpb_LabelNoiDungTiepNhan_ct').text(config.labelNoiDung + ': ');
                $('#dxcpb_NoiDungTiepNhanXuLy_ct').text(data.NoiDungTiepNhanXuLy || '');

                $('#dxcpb_DonViTiepNhanXuLyID_ct').text(data.TenDonViTiepNhanXuLy || '');

            } else {
                $('#dxcpb_imgDonXin').attr('src', window.Laravel && window.Laravel.local ? window.Laravel.local.linkAnhDonXin : '');
                $('#dxcpb_TrangThaiLabel').text('').css('background-color', '#ccc');
                $('#dxcpb_NgayLap_ct, #dxcpb_NguoiLap_ct, #dxcpb_ChucVuNguoiLap_ct, #dxcpb_DonViGui_ct, #dxcpb_LoaiPhoi_ct, #dxcpb_SoLuong_ct, #dxcpb_DonViNhan_ct, #dxcpb_LyDoXinCap_ct, #dxcpb_DinhKem_ct, #dxcpb_GhiChu_ct, #dxcpb_LabelNgay_ct, #dxcpb_LabelNguoi_ct, #dxcpb_LabelChucVu_ct, #dxcpb_LabelNoiDung_ct').text('');
            }
            $('#mdChiTietQD').modal('show');
        },
        error: function () {
            $('#dxcpb_imgDonXin').attr('src', window.Laravel && window.Laravel.local ? window.Laravel.local.linkAnhDonXin : '');
            $('#dxcpb_TrangThaiLabel').text('').css('background-color', '#ccc');
            $('#dxcpb_NgayLap_ct, #dxcpb_NguoiLap_ct, #dxcpb_ChucVuNguoiLap_ct, #dxcpb_DonViGui_ct, #dxcpb_LoaiPhoi_ct, #dxcpb_SoLuong_ct, #dxcpb_DonViNhan_ct, #dxcpb_LyDoXinCap_ct, #dxcpb_DinhKem_ct, #dxcpb_GhiChu_ct, #dxcpb_LabelNgay_ct, #dxcpb_LabelNguoi_ct, #dxcpb_LabelChucVu_ct, #dxcpb_LabelNoiDung_ct').text('');
            $('#mdChiTietQD').modal('show');
        }
    });
}

async function XoaDuLieu(ID) {
    var result_ktxoa = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.layouts.KiemTraXoa,
        {
            ma: ID,
            model: "QuanLy\\DonYeuCau",
        }
    );

    if (!result_ktxoa.Err) {
        CanhBaoXoa(async () => {
            var result = await NTS.getAjaxAPIAsync(
                "DELETE",
                window.location.pathname + "/xoa",
                { ma: ID }
            );
            if (!result.Err) {
                LoadDataTable();
                NTS.thanhcong(result.Msg);
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
    } else {
        NTS.canhbao(result_ktxoa.Msg);
        return false;
    }
}

let commonComboConfig = {
    columns: 2,
    indexValue: 0,
    indexText: 1, // assuming your result rows are [id, code, name]
    indexText1: 2, // assuming your result rows are [id, code, name]
    textShowTatCa: "-Chọn-",
    showTatCa: true,
};

function LoadDataComBo() {
    NTS.loadDataComboAsync({
        name: "#NguoiLapID",
        type: "GET",
        ajaxUrl: window.Laravel.local.comboNhanVien,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuNguoiLapID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListChucVu,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#LoaiVBCCID",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Tất cả-",
        showTatCa: true,
    });
    NTS.loadDataComboAsync({
        name: "#DonViNhanID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListDonVi,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#HinhThucNhanPhoiID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getHinhThucNhanPhoiList,
        ajaxParam: {},
        ...commonComboConfig
    });

}

function LoadDataComBo_Loc() {
    NTS.loadDataComboAsync({
        name: "#CapHocID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListCapHoc,
        ajaxParam: {},
        ...commonComboConfig

    });

    NTS.loadDataComboAsync({
        name: "#LoaiPhoiID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Tất cả-",
        showTatCa: true,
    });

    NTS.loadDataComboAsync({
        name: "#TrangThaiXuLyID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListTrangThaiCapBang,
        ajaxParam: {},
        ...commonComboConfig,
        indexValue: 1,
        textShowTatCa: "-Tất cả-"

    });
}

function LoadDataComBo_GuiDonXinCapPhoiBang() {
    NTS.loadDataComboAsync({
        name: "#NguoiXuLyID",
        type: "GET",
        ajaxUrl: window.Laravel.local.comboNhanVien,
        ajaxParam: {},
        ...commonComboConfig,
        textShowTatCa: "-Chọn-",
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuNguoiXuLyID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListChucVu,
        ajaxParam: {},
        ...commonComboConfig,
        textShowTatCa: "-Chọn-",
    });
    NTS.loadDataComboAsync({
        name: "#DonViTiepNhanXuLyID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListDonVi,
        ajaxParam: {},
        ...commonComboConfig,
        textShowTatCa: "-Chọn-",
    });
}

$(document).on("click", "#TimKiemNangCao", function () {
    if ($("#KhungTimKiem").css("display") == "block") {
        $("#KhungTimKiem").slideUp(200);
    } else {
        $("#KhungTimKiem").slideDown(200);
    }
    return false;
});
$(document).on("click", "#DongTimKiem", function () {
    $("#KhungTimKiem").slideUp(200);
    return false;
});

$(document).on("click", "#btnAnHienTQ", function () {
    var divThongKe = document.getElementById("DivThongKe");
    var textAnHien = document.getElementById("textAnHien");
    var iconAnHien = document.getElementById("iconAnHien");
    if (iconAnHien.className == "fa fa-eye-slash") {
        divThongKe.classList.add("d-none");
        iconAnHien.className = "fa fa-eye";
        textAnHien.textContent = " Hiện trang tổng quan";
    } else {
        divThongKe.classList.remove("d-none");
        iconAnHien.className = "fa fa-eye-slash";
        textAnHien.textContent = " Ẩn trang tổng quan";
    }
});

$("#btnLuuVaDong").on("click", async function () {

    let isValidPhoiTable = true;

    $("#phoiVBCCTable tbody tr").each(function () {
        const loaiPhoiSelect = $(this).find('.loaiPhoi-select');
        const donViTinhSelect = $(this).find('.donViTinh-select');
        const soLuong = $(this).find('.soLuongPhoi_num');

        if (!loaiPhoiSelect.val() || loaiPhoiSelect.val() === '') {
            NTS.canhbao("Phôi văn bằng chứng chỉ không được để trống!");
            loaiPhoiSelect.focus();
            isValidPhoiTable = false;
            return false;
        }

        if (!donViTinhSelect.val() || donViTinhSelect.val() === '') {
            NTS.canhbao("Đơn vị tính không được để trống!");
            donViTinhSelect.focus();
            isValidPhoiTable = false;
            return false;
        }

        if (!soLuong.val() || parseFloat(soLuong.val()) <= 0) {
            NTS.canhbao("Số lượng phôi chưa hợp hệ!");
            soLuong.focus();
            isValidPhoiTable = false;
            return false;
        }
    });


    if (!isValidPhoiTable) return false;

    const validate = new NTSValidate("#mdThemMoi");
    if (!validate.trim().check()) return false;

    const dinhKemQDValue = $("#dinhKemQD").val();

    const loaiPhoiArr = [];
    $("#phoiVBCCTable tbody tr").each(function () {
        const loaiPhoiID = $(this).find(".loaiPhoi-select").val();
        const donViTinhID = $(this).find(".donViTinh-select").val();
        const soLuongPhoi = $(this).find(".soLuongPhoi_num").val();
        if (loaiPhoiID || donViTinhID || soLuongPhoi) {
            loaiPhoiArr.push({
                LoaiPhoiVBCCID: loaiPhoiID,
                DonViTinhID: donViTinhID,
                SoLuongPhoi: soLuongPhoi,
            });
        }
    });
    const payload = {
        SoPhieu: $("#SoPhieu").val(),
        NgayLap: $("#NgayLap").val(),
        NguoiLapID: $("#NguoiLapID").val(),
        ChucVuNguoiLapID: $("#ChucVuNguoiLapID").val(),
        LoaiPhoiID: $("#LoaiPhoiID").val(),
        SoLuong: parseInt($("#SoLuong").val()) || 0,
        DonViNhanID: $("#DonViNhanID").val(),
        NguoiLienHe: $("#NguoiLienHe").val(),
        SoDienThoai: $("#SoDienThoai").val(),
        DiaChi: $("#DiaChi").val(),
        Email: $("#Email").val(),
        LyDoXinCap: $("#LyDoXinCap").val(),
        GhiChu: $("#GhiChu").val(),
        DonYeuCauID: $("#DonYeuCauID").value(),
        DinhKem: dinhKemQDValue,
        PhoiVBCC: loaiPhoiArr,
        HinhThucNhanPhoiID: $("#HinhThucNhanPhoiID").val(),
        MinhChungKemTheo: $("#MinhChungKemTheo").val(),

    };
    var met = "POST";
    if (tempthem == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }
    var result = await NTS.getAjaxAPIAsync(
        met,
        window.location.pathname + "/luuThongTin",
        payload
    );
    if (!result.Err) {
        NTS.thanhcong(result.Msg);
        LoadDataTable();
        $("#mdThemMoi").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});

// ==== Phôi VBCC DYNAMIC TABLE ==== //
let phoiVBCCOptions = [];
let phoiVBCCOptionsLoaded = false;
let donViTinhOptions = [];
let donViTinhOptionsLoaded = false;

$(document).on("click", "#btnAddPhoi", async function () {
    await addPhoiVBCCRow();
});

$(document).on("click", ".btnRemoveMPhoi", function () {
    $(this).closest("tr").remove();
});

async function initPhoiVBCCTable(phoiVBCCArr = []) {
    try {
        await fetchLoaiPhoiVBCCOptions();
        await fetchDonViTinhptions();
        await renderPhoiVBCCTable(phoiVBCCArr);
    } catch (error) {
        console.error("Lỗi khi khởi tạo bảng môn thi:", error);
    }
}
async function fetchLoaiPhoiVBCCOptions() {
    if (phoiVBCCOptionsLoaded && phoiVBCCOptions.length) return phoiVBCCOptions;
    try {
        const res = await NTS.getAjaxAPIAsync(
            "POST",
            Laravel.local.getloaiphoivanbangchungchi,
            {}
        );
        if (res && res.Result) {
            phoiVBCCOptions = res.Result.map((item) => ({
                id: item.id,
                text: item.name,
            }));
            phoiVBCCOptionsLoaded = true;
        }
    } catch (error) {
        console.error("Lỗi khi fetch loại phôi VBCC:", error);
    }
    return phoiVBCCOptions;
}

async function fetchDonViTinhptions() {
    if (donViTinhOptionsLoaded && donViTinhOptions.length) return donViTinhOptions;
    try {
        const res = await NTS.getAjaxAPIAsync(
            "GET",
            Laravel.local.getListDonViTinh,
            {}
        );
        if (res && res.Result) {
            donViTinhOptions = res.Result.map((item) => ({
                id: item.id,
                text: item.name,
            }));
            donViTinhOptionsLoaded = true;
        }
    } catch (error) {
        console.error("Lỗi khi fetch loại phôi VBCC:", error);
    }
    return donViTinhOptions;
}

async function renderPhoiVBCCTable(phoiVBCCArr = []) {
    const tbody = $("#phoiVBCCTable tbody");
    tbody.empty();
    if (!Array.isArray(phoiVBCCArr) || !phoiVBCCArr.length) {
        await addPhoiVBCCRow();
        return;
    }
    for (const row of phoiVBCCArr) {
        await addPhoiVBCCRow(row);
    }
}

async function addPhoiVBCCRow(row = {}) {
    const tbody = $("#phoiVBCCTable tbody");

    const loaiPhoiID = row.LoaiPhoiVBCCID || "";
    const donViTinhID = row.DonViTinhID || "";
    const soLuongPhoi = row.SoLuongPhoi || "";

    const rowIndex = tbody.children().length;
    const loaiPhoiSelectId = `loaiPhoiSelect_${rowIndex}`;
    const donViTinhSelectId = `donViTinhSelect_${rowIndex}`;

    const tr = $(`
        <tr>
            <td style="text-align:center; vertical-align:middle; padding: 0;">
                <div style="display: flex; justify-content: center; align-items: center; height: 100%;">
                    <button type="button" class="btn btn-danger btn-sm rounded-circle btnRemoveMPhoi"
                        tabindex="-1" style="width: 20px; height: 20px; padding: 0;">
                        <i class="fa fa-minus" style="font-size: 12px;"></i>
                    </button>
                </div>
            </td>
            <td><select id="${loaiPhoiSelectId}" class="form-select loaiPhoi-select" name="loaiPhoiID" required style="min-width:120px; text-align: left;"></select></td>
            <td><select id="${donViTinhSelectId}" class="form-select donViTinh-select" name="donViTinhID" required style="min-width:120px; text-align: left;"></select></td>
            <td><input type="number" class="form-control soLuongPhoi_num" name="soLuongPhoi" min="0" step="1" value="${soLuongPhoi || 1}"></td>
        </tr>
    `);
    tbody.append(tr);

    await NTS.loadDataComboAsync({
        name: `#${loaiPhoiSelectId}`,
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        ...commonComboConfig,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
    });

    await NTS.loadDataComboAsync({
        name: `#${donViTinhSelectId}`,
        type: "GET",
        ajaxUrl: window.Laravel.local.getListDonViTinh,
        ajaxParam: {},
        ...commonComboConfig
    });

    // Set selected values after combo is loaded
    if (loaiPhoiID) {
        $(`#${loaiPhoiSelectId}`).value(loaiPhoiID);
    }
    if (donViTinhID) {
        $(`#${donViTinhSelectId}`).value(donViTinhID);
    }
}

function htmlDuLieu(cell) {
    const data = cell.getData();

    const fileName = data.TenTepDinhKem || "Không rõ";
    const fileLink = data.LinkDinhKem || "#";
    const dinhKemHTML = data.LinkDinhKem
        ? `<a href="${fileLink}" target="_blank">${fileName}</a>`
        : "Không có";

    let labelNgay, labelNguoi, labelChucVu, labelNoiDung;
    let txtNgay = data.txtNgayTiepNhanXuLy || "";
    let tenNguoi = data.TenNguoiTiepNhanXuLy || "";
    let chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
    let noiDung = data.NoiDungTiepNhanXuLy || "";
    switch (parseInt(data.TrangThaiXuLyID)) {
        case 40:
            labelNgay = "Ngày gửi";
            labelNguoi = "Người gửi";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung gửi";

            txtNgay = data.txtNgayXuLy || "";
            tenNguoi = data.TenNguoiXuLy || "";
            chucVu = data.TenChucVuNguoiXuLy || "";
            noiDung = data.NoiDungXuLy || "";
            break;
        case 41:
            labelNgay = "Ngày đề nghị";
            labelNguoi = "Người đề nghị";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung đề nghị";

            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        case 42:
            labelNgay = "Ngày từ chối";
            labelNguoi = "Người từ chối";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung từ chối";


            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        case 32:
            labelNgay = "Ngày duyệt";
            labelNguoi = "Người duyệt";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung duyệt";


            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        default:
            labelNgay = "Ngày đề nghị";
            labelNguoi = "Người đề nghị";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung đề nghị";

            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;

    }
    return `<div class="list-item col-md-12" style="padding: 0px;">
        <div class="card card-luoi shadow-sm mb-2">
            <div id="card_${data.id}" class="card-body profile-user-box">
                <div class="row">
                    <div class="col-sm-2 text-center" style="width:12%;">
                        <div class="profile-picture" style=" height: 109px;">
                            <img src="${window.Laravel.local.linkAnhDonXin}" alt="ảnh đơn xin" class="img-thumbnail rounded lazy mb-2" style="background: white;
                            border: none;
                            box-shadow: unset;">
                        </div>
                        <div style="display: flex; justify-content: center;">
                            <span style="
                                display: block;
                                margin-top: 10px;
                                font-weight: bold;
                                font-size: 1rem;
                                text-align: center;
                                padding: 6px 0;
                                background-color: ${data.MauSacTrangThaiXuLy};
                                color: white;
                                border-radius: 6px;
                                width: 95%;
                                ">
                                ${data.TenTrangThaiXuLy}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-10" style="width:88%;">
                        <div class="row">
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Số phiếu: <b>${data.SoPhieu || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Ngày lập: <b>${data.txtNgayLap || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Người lập: <b>${data.TenNguoiLap || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Chức vụ: <b>${data.TenChucVuNguoiLap || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="dropdown text-end" style="position: absolute; top: 10px; right: 10px;">
                                    <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-end w-auto">

                                        <!-- Luôn hiển thị đầu tiên -->
                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                                            href="javascript:void(0);" onclick="XemChiTietNhatKy('${data.id}'); return false;">
                                            <i class="fa fa-eye" aria-hidden="true" style="color: #4265B6"></i>&ensp; Xem phiếu đề nghị
                                        </a>

                                        <!-- Các nút tùy theo trạng thái -->
                                        ${data.TrangThaiXuLyID == '41' ? `
                                                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                                                    href="javascript:void(0);" onclick="SuaDuLieu('${data.id}'); return false;">
                                                    <i class="fa fa-pencil-square-o iconsize-item" style="color: #F76707"></i>&ensp;Chỉnh sửa phiếu đề nghị
                                                </a>
                                                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                                                    href="javascript:void(0);" onclick="GuiDonXinCapPhoiBang('${data.id}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                                                    <i class="fa fa-paper-plane-o text-success" aria-hidden="true"></i>&ensp; Gửi phiếu đề nghị
                                                </a>
                                            ` : ''
        }

                                        ${data.TrangThaiXuLyID == '40' ? `
                                                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                                                    href="javascript:void(0);" onclick="ThuHoiDonXinCapPhoiBang('${data.id}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                                                    <i class="fa fa-refresh" style="color: #F76707" aria-hidden="true"></i>&ensp; Thu hồi đơn xin cấp phôi bằng
                                                </a>
                                            ` : ''
        }

                                        <!-- Luôn hiển thị in -->
                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                                            href="javascript:void(0);" onclick="InPhieuDeNghi('${data.id}')">
                                            <i class="fa fa-print iconsize-item" style="color: #2A79FF"></i>&ensp; In phiếu đề nghị
                                        </a>

                                        ${data.TrangThaiXuLyID == '41' ? `
                                                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                                                    href="javascript:void(0);" onclick="XoaDuLieu('${data.id}')">
                                                    <i class="fa fa-trash-o iconsize-item" style="color: #D63939"></i>&ensp; Xóa phiếu đề nghị
                                                </a>
                                            ` : ''
        }

                                    </div>
                                </div>
                            </div>


                        </div>
                        <div class="row">
                            <hr style="border-top: 2px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-6">
                                        <p class="fs-big my-1">Đơn vị xin cấp phôi: <b>${data.TenDonViGui || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Người liên hệ: <b>${data.NguoiLienHe || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Số điện thoại: <b>${data.SoDienThoai || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-6">
                                        <p class="fs-big my-1">Địa chỉ: <b>${data.DiaChi || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Email: <b>${data.Email || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Hình thức nhận phôi: <b>${data.TenHinhThucNhanPhoi || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div> 
                        </div>
                        <div class="row">
                            <hr style="border-top: 2px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />
                            <div class="col-md-11">
                                <div class="row mb-12">
                                    <div class="col-md-6">
                                        <p class="fs-big my-1">Đơn vị tiếp nhận: <b>${data.TenDonViTiepNhanXuLy || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                        </div>
                        <div class="row">
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">${labelNgay}: <b>${txtNgay}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">${labelNguoi}: <b>${tenNguoi}</b></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="fs-big my-1">${labelChucVu}: <b>${chucVu}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>`;
}

var table = new Tabulator("#Grid1", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "600",
    HeaderVertAlign: "center",
    headerVisible: false,
    columns: [
        {
            title: "Thông tin",
            field: "ThongTinHoGiaDinh",
            formatter: htmlDuLieu,
            visible: true,
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

async function LoadDataTable() {
    table.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getAll",
        {
            SearchKey: $("#SearchKey").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            TrangThaiXuLyID_Loc: $("#TrangThaiXuLyID_Loc").value(),
        }
    );
    if (!result.Err) {
        VeChart();
        table.setData(result.result);
    } else {
        table.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

$(document).on("click", "#TimKiem", async function () {
    $("#KhungTimKiem").slideUp(200);
    await LoadDataTable();
    await LoadDataTable2();
    return false;
});

$(document).on("keyup", "#SearchKey", async function (e) {
    if (e.keyCode == "13") {
        await LoadDataTable();
        $("#KhungTimKiem").slideUp(200);
        return false;
    }
});

$(document).on("change", "#CbSapXep", function () {
    LoadDataTable();
});

$(document).on("click", "#btn-layout-1", async function () {
    $("#grid-layout").fadeIn(200);
    $("#list-layout").hide();
    $("#list-layout").removeClass("show");
    $("#grid-layout").addClass("show");
    await LoadDataTable();
});

$(document).on("click", "#btn-layout-2", async function () {
    $("#grid-layout").hide();
    $("#list-layout").fadeIn(200);
    $("#list-layout").addClass("show");
    $("#grid-layout").removeClass("show");
    $("#DonYeuCauID").value("");
    $(".divThaoTacNhanh").hide();
    $("#txtTenTrangThaiXuLy_View").html("Trạng thái xử lý: <b>---</b>");
    $("#txtTenDonViGui_View").html("Đơn vị gửi: <b>---</b>");
    await LoadDataTable2();
});

function actionDropdownFormatter(cell) {
    const data = cell.getData();
    const ID = data.id;
    const button = document.createElement("button");
    button.className = "btn btn-sm btn-white dropdown-toggle-hide-arrow";
    button.innerHTML = `<i class="fa fa-ellipsis-h" style="color: #696cff;"></i>`;
    button.style.boxShadow = "none";

    button.onclick = function (e) {
        e.stopPropagation();
        document.querySelectorAll('.custom-dropdown-menu').forEach(el => el.remove());

        const dropdown = document.createElement("div");
        dropdown.className = "custom-dropdown-menu dropdown-menu dropdown-menu-end show";
        dropdown.style.position = "absolute";
        dropdown.style.zIndex = 9999;
        dropdown.style.minWidth = "220px";

        const TrangThai = data.TrangThaiXuLyID;
        let html = `
            <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                href="javascript:void(0);" onclick="XemChiTietNhatKy('${ID}'); return false;">
                <i class="fa fa-eye" aria-hidden="true" style="color: #4265B6"></i>&ensp; Xem phiếu đề nghị
            </a>
        `;

        if (TrangThai == '41') {
            html += `
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="SuaDuLieu('${ID}'); return false;">
                    <i class="fa fa-pencil-square-o iconsize-item" style="color: #F76707"></i>&ensp;Chỉnh sửa phiếu đề nghị
                </a>
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="GuiDonXinCapPhoiBang('${ID}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                    <i class="fa fa-check-square-o text-success" aria-hidden="true"></i>&ensp; Gửi phiếu đề nghị
                </a>
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="XoaDuLieu('${ID}')">
                    <i class="fa fa-trash-o iconsize-item" style="color: #D63939"></i>&ensp; Xóa phiếu đề nghị
                </a>
            `;
        }

        if (TrangThai == '40') {
            html += `
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="ThuHoiDonXinCapPhoiBang('${ID}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                    <i class="fa fa-refresh" style="color: #F76707" aria-hidden="true"></i>&ensp; Thu hồi đơn xin cấp phôi bằng
                </a>
            `;
        }

        html += `
            <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                href="javascript:void(0);" onclick="InPhieuDeNghi('${ID}')">
                <i class="fa fa-print iconsize-item" style="color: #2A79FF"></i>&ensp; In phiếu đề nghị
            </a>
        `;

        dropdown.innerHTML = html;

        const rect = button.getBoundingClientRect();
        dropdown.style.left = `${rect.left + window.scrollX}px`;
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;

        document.body.appendChild(dropdown);

        const closeDropdown = (event) => {
            if (!dropdown.contains(event.target) && event.target !== button) {
                dropdown.remove();
                document.removeEventListener('click', closeDropdown);
            }
        };
        document.addEventListener('click', closeDropdown);
    };

    return button;
}


function trangThaiSpanFormatter(cell) {
    const data = cell.getData();

    const mauSac = data.MauSacTrangThaiXuLy || "#6c757d";
    const tenTrangThai = data.TenTrangThaiXuLy || "Không xác định";

    const container = document.createElement("div");
    container.style.display = "flex";
    container.style.justifyContent = "center";

    const span = document.createElement("span");
    span.style.display = "block";
    span.style.marginTop = "10px";
    span.style.fontWeight = "bold";
    span.style.fontSize = "1rem";
    span.style.textAlign = "center";
    span.style.padding = "6px 0";
    span.style.backgroundColor = mauSac;
    span.style.color = "white";
    span.style.borderRadius = "6px";
    span.style.width = "95%";
    span.innerText = tenTrangThai;

    container.appendChild(span);
    return container;
}


var GridMainLuoi = new Tabulator("#GridMainLuoi", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: actionDropdownFormatter,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false
        },

        {
            title: "Số phiếu",
            field: "SoPhieu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Ngày lập",
            field: "txtNgayLap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Người lập",
            field: "TenNguoiLap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Chức vụ người lập",
            field: "TenChucVuNguoiLap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Đơn vị xin cấp phôi",
            field: "TenDonViGui",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 300,
        },
        {
            title: "Đơn vị tiếp nhận",
            field: "TenDonViNhan",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 300,
        },
        {
            title: "Người liên hệ",
            field: "NguoiLienHe",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Số điện thoại",
            field: "SoDienThoai",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Địa chỉ",
            field: "DiaChi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 300,
        },
        {
            title: "Email",
            field: "Email",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Hình thực nhận phôi",
            field: "TenHinhThucNhanPhoi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Trạng thái",
            field: "TenTrangThaiXuLy",
            formatter: "textarea",
            hozAlign: "center",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 120,
        }


    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

async function LoadDataTable2() {

    GridMainLuoi.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getAll",
        {
            SearchKey: $("#SearchKey").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            TrangThaiXuLyID_Loc: $("#TrangThaiXuLyID_Loc").value(),
            CbSapXep: $("#CbSapXep").value(),
        }
    );
    if (!result.Err) {
        GridMainLuoi.setData(result.result);
    } else {
        GridMainLuoi.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}
//#endregion
async function LoadTabThongTinDonXin() {
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        { id: $("#DonYeuCauID").val() }
    );
    if (!result.Err && result.Result != null) {
        let data = result.Result;
        if (data != undefined) {
            $(".divThaoTacNhanh").show();
            $("#dxcpb_NgayLap").text(data.txtNgayLap || ChuaCoThongTin);
            $("#dxcpb_NguoiLap").text(data.TenNguoiLap || ChuaCoThongTin);
            $("#dxcpb_ChucVuNguoiLap").text(data.TenChucVuNguoiLap || ChuaCoThongTin);
            $("#dxcpb_DonViGui").text(data.TenDonViGui || ChuaCoThongTin);
            $("#dxcpb_LoaiPhoi").text(data.TenLoaiPhoi || ChuaCoThongTin);
            $("#dxcpb_SoLuong").text(data.SoLuong || ChuaCoThongTin);
            $("#dxcpb_DonViNhan").text((data.TenDonViNhan || ChuaCoThongTin) + (data.MaDonViNhan ? ` (${data.MaDonViNhan})` : ""));
            $("#dxcpb_LyDoXinCap").text(data.LyDoXinCap || ChuaCoThongTin);
            $("#txtTenTrangThaiXuLy_View").text(data.TenTrangThaiXuLy || ChuaCoThongTin);
            $("#txtTenDonViGui_View").text(data.TenDonViGui || ChuaCoThongTin);



            if (data.DinhKem) {
                $("#dxcpb_DinhKem").html(`<a href="${data.LinkDinhKem || '#'}" target="_blank">${data.TenTepDinhKem || 'Tệp đính kèm'}</a>`);
            } else {
                $("#dxcpb_DinhKem").html(ChuaCoThongTin);
            }
            $("#dxcpb_GhiChu").text(data.GhiChu || ChuaCoThongTin);

            let labelNgay = "Ngày xử lý", labelNguoi = "Người xử lý", labelChucVu = "Chức vụ", labelNoiDung = "Nội dung";
            if (data.TrangThaiXuLyID == 40) {
                labelNgay = "Ngày gửi";
                labelNguoi = "Người gửi";
                labelChucVu = "Chức vụ người gửi";
                labelNoiDung = "Nội dung gửi";
            } else if (data.TrangThaiXuLyID == 41) {
                labelNgay = "Ngày đề nghị";
                labelNguoi = "Người đề nghị";
                labelChucVu = "Chức vụ người đề nghị";
                labelNoiDung = "Nội dung đề nghị";
            } else if (data.TrangThaiXuLyID == 42) {
                labelNgay = "Ngày từ chối";
                labelNguoi = "Người từ chối";
                labelChucVu = "Chức vụ người từ chối";
                labelNoiDung = "Lý do từ chối";
            }
            $("#dxcpb_LabelNgay").text(labelNgay);
            $("#dxcpb_LabelNguoi").text(labelNguoi);
            $("#dxcpb_LabelChucVu").text(labelChucVu);
            $("#dxcpb_LabelNoiDung").text(labelNoiDung);
            $("#dxcpb_NgayXuLy").text(data.txtNgayXuLy || ChuaCoThongTin);
            $("#dxcpb_NguoiXuLy").text(data.TenNguoiXuLy || ChuaCoThongTin);
            $("#dxcpb_ChucVuNguoiXuLy").text(data.TenChucVuNguoiXuLy || ChuaCoThongTin);
            $("#dxcpb_NoiDungXuLy").text(data.NoiDungXuLy || ChuaCoThongTin);

            const id = data.id;

            $("#btnSuaChiTiet").off("click").on("click", function () { SuaDuLieu(id); });

            if (data.TrangThaiXuLyID != 40) {
                $("#btnGuiDonChiTiet").show().off("click").on("click", function () { GuiDonXinCapPhoiBang(id); });
            } else {
                $("#btnGuiDonChiTiet").hide();
            }

            if (data.TrangThaiXuLyID != 41) {
                $("#btnThuHoiChiTiet").show().off("click").on("click", function () { ThuHoiDonXinCapPhoiBang(id); });
            } else {
                $("#btnThuHoiChiTiet").hide();
            }

            $("#btnNhatKyChiTiet").off("click").on("click", function () { XemChiTietNhatKy(id); });

            $("#btnXoaChiTiet").off("click").on("click", function () { XoaDuLieu(id); });
        }
    } else {
        $(".divThaoTacNhanh").hide();

        $("#dxcpb_NgayLap, #dxcpb_NguoiLap, #dxcpb_ChucVuNguoiLap, #dxcpb_DonViGui, #dxcpb_LoaiPhoi, #dxcpb_SoLuong, #dxcpb_DonViNhan, #dxcpb_LyDoXinCap, #dxcpb_DinhKem, #dxcpb_GhiChu, #dxcpb_LabelNgay, #dxcpb_LabelNguoi, #dxcpb_LabelChucVu, #dxcpb_LabelNoiDung, #dxcpb_NgayXuLy, #dxcpb_NguoiXuLy, #dxcpb_ChucVuNguoiXuLy, #dxcpb_NoiDungXuLy").text(ChuaCoThongTin);
        $("#dxcpb_DinhKem").html(ChuaCoThongTin);
    }
}

$("#btnGuiDon").on("click", async function () {

    const validate = new NTSValidate("#mdGuiDonXinCapPhoiBang");
    if (!validate.trim().check()) return false;

    const payload = {
        DonYeuCauID: $("#DonYeuCauID").val(),
        NguoiXuLyID: $("#NguoiXuLyID").val(),
        ChucVuNguoiXuLyID: $("#ChucVuNguoiXuLyID").val(),
        NoiDungXuLy: $("#NoiDungXuLy").val(),
        NgayXuLy: $("#NgayXuLy").val(),
        DonViTiepNhanXuLyID: tempTrangThai === "41" ? null : $("#DonViTiepNhanXuLyID").val(),
        TrangThaiXuLyID: tempTrangThai
    };


    var result = await NTS.getAjaxAPIAsync(
        "post",
        window.location.pathname + "/GuiDon",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdGuiDonXinCapPhoiBang").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});

async function VeChart() {
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.getThongKe,
        {
            SearchKey: $("#SearchKey").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            TrangThaiXuLyID_Loc: $("#TrangThaiXuLyID_Loc").value(),
        }
    );

    try {
        $("#txtTong").text(result.Result.TS);

        result.Result.ThongKeTrangThai.forEach(item => {
            $("#txtTrangThai_" + item.TrangThaiXuLyID).text(item.SoLuong);
        });
        const ThongKeLoaiPhoi = result.Result.ThongKeLoaiPhoi;
        $("#txtPhoiTHCS").text(ThongKeLoaiPhoi[0].TenLoaiPhoiVanBangChungChi);
        $("#txtPhoiTHCSCount").text(ThongKeLoaiPhoi[0].SoLuong);

        $("#txtPhoiTHPT").text(ThongKeLoaiPhoi[1].TenLoaiPhoiVanBangChungChi);
        $("#txtPhoiTHPTCount").text(ThongKeLoaiPhoi[1].SoLuong);

        const chartLabels = [];
        const chartSeries = [];
        const chartColors = [];

        result.Result.ThongKeTrangThai.forEach(item => {
            chartLabels.push(item.TenTrangThai);
            chartSeries.push(item.SoLuong);
            chartColors.push(item.MauSac || "#999999");
        });

        document.querySelector("#XepLoaiChart").innerHTML = "";

        mixedXepLoaiChart = new ApexCharts(
            document.querySelector("#XepLoaiChart"),
            {
                labels: chartLabels,
                colors: chartColors,
                series: chartSeries,
                chart: {
                    type: "donut",
                    height: 110,
                },
                dataLabels: {
                    enabled: false,
                },
                responsive: [
                    {
                        breakpoint: 480,
                        options: {
                            chart: {
                                width: 110,
                            },
                            legend: {
                                position: "bottom",
                            },
                        },
                    },
                ],
            }
        );
        mixedXepLoaiChart.render();

    } catch (err) {
        console.error("Lỗi khi vẽ biểu đồ:", err);
    }
}





