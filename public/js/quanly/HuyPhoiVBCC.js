var tempthem = "them";
var ChuaCoThongTin = "";

const today = new Date();
const dd = String(today.getDate()).padStart(2, '0');
const mm = String(today.getMonth() + 1).padStart(2, '0');
const yyyy = today.getFullYear();
const defaultDate = `${dd}/${mm}/${yyyy}`;
var NhanVienID='';
var ChucVuID ='';

function setNgayTrongThang(inputTuNgayId, inputDenNgayId) {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth(); // 0 = January
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    // Định dạng dd/mm/yyyy
    const format = (d) => {
        const dd = String(d.getDate()).padStart(2, '0');
        const mm = String(d.getMonth() + 1).padStart(2, '0');
        const yyyy = d.getFullYear();
        return `${dd}/${mm}/${yyyy}`;
    };
    // Gán giá trị cho input
    document.getElementById(inputTuNgayId).value = format(firstDay);
    document.getElementById(inputDenNgayId).value = format(lastDay);
}
//#region logic thao tác click
$(async function () {
     var resultNV = await NTS.getAjaxAPIAsync("GET", Laravel.layouts.getCurrentUserInfo,{});    
    NhanVienID=resultNV.data.nhanVien.id
    ChucVuID=resultNV.data.nhanVien.chucVuID
    setNgayTrongThang("TuNgay_Loc", "DenNgay_Loc")
    $(document).on('keydown', function (e) {
        var Showmodal = $('.modal.show').attr('id')
        if (Showmodal == 'mdThemMoi' || Showmodal == undefined) {
            switch (e.keyCode) {
                case 113: //f2
                    $('#btnThemMoi').trigger('click');
                    break;
                case 115: //f4
                    if ($('#mdThemMoi').hasClass('show')) {
                        $('#mdThemMoi').modal('hide');
                    }
                    break;
                case 120: //f9
                    if ($('#mdThemMoi').hasClass('show')) {
                        $('#btnLuuVaDong').trigger('click');
                    }
                    break;
            }
        }
        if (Showmodal == 'mdQPheDuyet') {
            switch (e.keyCode) {
                case 115: //f4
                    if ($('#mdQPheDuyet').hasClass('show')) {
                        $('#mdQPheDuyet').modal('hide');
                    }
                    break;
                case 120: //f9
                    if ($('#mdQPheDuyet').hasClass('show')) {
                        $('#btnBanHanh').trigger('click');
                    }
                    break;
            }
        }
    });
    await LoadDataComBo();
    await LoadDataTable();
});

$(document).on("click", "#TimKiemNangCao", function () {
    if ($("#KhungTimKiem").css("display") == "block") {
        $("#KhungTimKiem").slideUp(200);
    } else {
        $("#KhungTimKiem").slideDown(200);
    }
    return false;
});
$(document).on("click", "#DongTimKiem", function () {
    $("#KhungTimKiem").slideUp(200);
    return false;
});
$(document).on("click", "#TimKiem", async function () {
    $("#KhungTimKiem").slideUp(200);
    await LoadDataTable();
   
    return false;
});
$(document).on("click", "#btn-layout-1", async function () {
    $("#grid-layout").fadeIn(200);
    $("#list-layout").hide();
    $("#list-layout").removeClass("show");
    $("#grid-layout").addClass("show");
    $("#DivDanhSach").show();
    $("#DivLuoi").hide();
    table.setFilter(matchAny, { value: $('#SearchKey').value() });
});
$(document).on("click", "#btn-layout-2", async function () {
    $("#grid-layout").hide();
    $("#list-layout").fadeIn(200);
    $("#list-layout").addClass("show");
    $("#grid-layout").removeClass("show");
    $("#DivDanhSach").hide();
    $("#DivLuoi").show();
    GridMainLuoi.setFilter(matchAny, { value: $('#SearchKey').value() });
});
$(document).on("change", "#CbSapXep", function () {
    LoadDataTable();
});
async function LoadDataComBo() {
    NTS.loadDataComboAsync({
        name: "#NhanVienID_NguoiLap,#NhanVienID_PheDuyet",
        type: "GET",
        ajaxUrl: window.Laravel.local.comboNhanVien,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#CapHocID,#CapHocID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.local.getcaphoc,
        ajaxParam: {},
        columns: 1,
        indexValue: 2,
        indexText: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });

    NTS.loadDataComboAsync({
        name: "#LoaiPhoiVanBangChungChiID,#LoaiPhoiVanBangChungChiID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 1,
        indexValue: 2,
        indexText: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#DonViID_SuDung,#DonViID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListDonVi,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });

    NTS.loadDataComboAsync({
        name: "#ChucVuID_NguoiLap,#ChucVuID_PheDuyet",
        type: "GET",
        ajaxUrl: window.Laravel.local.listChucVuUrl,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });

    NTS.loadDataComboAsync({
        name: "#LyDoID,#LyDoID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListLyDo,
        ajaxParam: {},
        columns: 1,
        indexValue: 1,
        indexText: 0,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });

}
var soluongConLaiCoTheHuy = 0;
$(document).on("change", "#TiepNhanPhoiVBCCID", async function () {
    if ($("#LoaiPhoiVanBangChungChiID").value() != "") {
        var result = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.local.getDanhSachTiepNhanCoCapPhat,
            {
                id: $("#TiepNhanPhoiVBCCID").value(),
            }
        );
        soluongConLaiCoTheHuy = result.ConLai ? result.ConLai : 0;

        tempthem == "them" ? $("#SoLuongHuy").value(soluongConLaiCoTheHuy) : "";
    }
    else {
        soluongConLaiCoTheHuy = 0
    }
});


//#region Thêm xóa sửa
$(document).on("click", "#btnThemMoi", function () {
    ThemDuLieu();
});
async function ThemDuLieu() {
    if (!QuyenThem()) {
        return;
    }
    resetForm("#mdThemMoi");
    uploadedFileUrls = []; // reset uploaded files
    $("#HuyPhoiVBCC_list-file").empty();
    $("#mdThemMoi").modal("show");
    $("#tieuDeModal").text("Thêm mới thông tin thu hồi/hủy bỏ phôi văn bằng, chứng chỉ");
    $("#HuyPhoiVBCCID").value("");
    $("#mdThemMoi").find("input, textarea").val('');
    $('#SoLuongHuy').val(1)
    $("#NgayLap").value(defaultDate);
    $("#NhanVienID_NguoiLap").value(NhanVienID)
    $("#ChucVuID_NguoiLap").value(ChucVuID)
    try {
        var result = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.local.maTuTangUrl,
            {}
        );
        $("#SoHieuTu").value(addToCode(result.SoChungTu));
        $("#SoHieuDen").value(addToCode(result.SoChungTu));
    } catch { }

            try {
        var result = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.local.soPhieuTuTangUrl,
            {}
        );
        $("#SoQuyetDinh").value(result.SoChungTu);
    } catch { }
    tempthem = "them";
}
$(document).on('change', "#SoLuongHuy", function () {
    const fromCode = $("#SoHieuTu").val();
    let amount = parseInt($('#SoLuongHuy').val());

    if (isNaN(amount)) amount = 0;

    const newCode = addToCode(fromCode, amount);
    $("#SoHieuDen").val(newCode); // ✅ Corrected: use .val() not .value()
});

function addToCode(code, amountToAdd, prefix = "CC", postfix = "/2025", numLength = 6) {
    const numberPart = code.replace(prefix, "").replace(postfix, "");
    const baseNumber = parseInt(numberPart, 10);

    const addedNumber = baseNumber + (isNaN(amountToAdd) ? 0 : amountToAdd);
    const paddedNumber = addedNumber.toString().padStart(numLength, '0');

    return `${prefix}${paddedNumber}${postfix}`;
}
$("#btnLuuVaDong").on("click", async function () {
    const validate = new NTSValidate("#mdThemMoi");
    if (!validate.trim().check()) return false;
    const payload = {
        SoQuyetDinh: $("#SoQuyetDinh").value(),
        NgayLap: $("#NgayLap").value(),
        CapHocID: $("#CapHocID").value(),
        NhanVienID_NguoiLap: $("#NhanVienID_NguoiLap").value(),
        ChucVuID_NguoiLap: $("#ChucVuID_NguoiLap").value(),
        LoaiPhoiVanBangChungChiID: $("#LoaiPhoiVanBangChungChiID").value(),
        DonViID_SuDung: $("#DonViID_SuDung").value(),
        SoLuongHuy: $("#SoLuongHuy").value(),
        SoHieuTu: $("#SoHieuTu").value(),
        SoHieuDen: $("#SoHieuDen").value(),
        LyDoID: $("#LyDoID").value(),
        GhiChu: $("#GhiChu").value(),
        txtDuongDanFileVB: $("#HuyPhoiVBCC_txtDuongDanFileVB").value(),
        HuyPhoiVBCCID: $("#HuyPhoiVBCCID").value(),
    };
    var met = "POST";
    if (tempthem == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }
    var result = await NTS.getAjaxAPIAsync(
        met,
        window.location.pathname + "/luuthongtin",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdThemMoi").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});
async function SuaThongTin(id) {
    if (!QuyenSua()) { return; }
    resetForm("#mdThemMoi");
    $("#tieuDeModal").text("Cập nhật thông tin thu hồi/hủy bỏ phôi văn bằng, chứng chỉ");

    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        {
            id: id,
        }
    );

    if (!result.Err) {
        let data = result.Result;
        $("#HuyPhoiVBCCID").val(data.id);
        $("#SoQuyetDinh").value(data.SoQuyetDinh),
            $("#NgayLap").value(data.NgayLap),
            $("#CapHocID").value(data.CapHocID),
            $("#NhanVienID_NguoiLap").value(data.NhanVienID_NguoiLap),
            $("#ChucVuID_NguoiLap").value(data.ChucVuID_NguoiLap),
            $("#LoaiPhoiVanBangChungChiID").value(data.LoaiPhoiVanBangChungChiID),
            $("#DonViID_SuDung").value(data.DonViID_SuDung),
            $("#SoLuongHuy").value(data.SoLuongHuy),
            $("#SoHieuTu").value(data.SoHieuTu),
            $("#SoHieuDen").value(data.SoHieuDen),
            $("#LyDoID").value(data.LyDoID),
            $("#GhiChu").value(data.GhiChu),
            // Handle attachments preview (if you want)
            uploadedFileUrls = data.DinhKem ? data.DinhKem.split("|").filter((u) => u) : [];
        $("#HuyPhoiVBCC_txtDuongDanFileVB").value(data.DinhKem);
        $("#HuyPhoiVBCC_list-file").empty();

        uploadedFileUrls.forEach((url) => {
            renderAttachment(url);
        });
        tempthem = "sua";
        $("#mdThemMoi").modal("show");
    }
    return false;
}
async function XemThongTin(id) {
    if (!QuyenSua()) { return; }
    $("#tieuDeModalXem").text("Xem thông tin hủy phôi bằng");
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        {
            id: id,
        }
    );
    if (!result.Err) {
        let data = result.Result;
        $("#lblSoPhieu").html(" Số phiếu:<b> " + data.SoPhieu + "</b>");
        $("#lblNgayHuy").html(" Ngày cấp hủy:<b> " + data.txtNgayHuy + "</b>");
        $("#lblNhanVienID_HuyPhoi").html("Nhân viên chịu trách nhiệm:<b> " + data.TenNhanVien + "</b>");
        $("#lblCapHocID").html(" Cấp học:<b> " + data.TenCapHoc + "</b>");
        $("#lblLoaiPhoiVanBangChungChiID").html("Loại phôi văn bằng chứng chỉ:<b> " + data.TenLoaiPhoiVanBangChungChi + "</b>");
        $("#lblNamTotNghiep").html("Năm tốt nghiệp:<b> " + data.NamTotNghiep + "</b>");
        $("#lblSoSeriBatDau").html("Số seri bắt đầu:<b> " + data.SoSeriBatDau + "</b>");
        $("#lblSoSeriKetThuc").html("Số seri kết thúc:<b> " + data.SoSeriKetThuc + "</b>");
        $("#lblSoLuongHuy").html("Số lượng hủy:<b> " + data.SoLuongHuy + "</b>");
        $("#lblLyDoHuy").html("Ghi chú: <b>" + data.LyDoHuy + "</b>");
        $("#lblGhiChu").html("Ghi chú: <b>" + data.GhiChu + "</b>");

        $("#mdXemThongTin").modal("show");
    }
    return false;
}
async function XoaThongTin(id) {
    if (!QuyenXoa()) { return; }
    var result_ktxoa = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.layouts.KiemTraXoa,
        {
            ma: id,
            model: "QuanLy\\HuyPhoiVBCC",
        }
    );

    if (!result_ktxoa.Err) {
        CanhBaoXoa(async () => {
            var result = await NTS.getAjaxAPIAsync(
                "DELETE",
                window.location.pathname + "/xoa",
                { ma: id }
            );
            if (!result.Err) {
                LoadDataTable();
                NTS.thanhcong(result.Msg);
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
    } else {
        NTS.canhbao(result_ktxoa.Msg);
        return false;
    }
}



function htmlDuLieu(cell, formatterParams, onRendered) {
    let anhDaiDien = "";
    anhDaiDien =
        `<img src="${window.Laravel.local.imgLuoi}" alt="` +
        cell.getData().SoPhieu +
        `" class="img-thumbnail rounded lazy">`;
    return `<div class="list-item col-md-12" style="padding: 0px;">
                        <div class="card card-luoi shadow-sm mb-2 ">
                            <div id="card_${cell.getData().id
        }" class="card-body profile-user-box">
                                <div class="row">
                                    <div class="col-sm-2 text-center" style=" width: 20%; " >
                                        <div class="profile-picture" style=" height: 159px; ">
                                            ${anhDaiDien}
                                        </div>
                                        <span style="
                                            display: block;
                                            margin-top: 10px;
                                            font-weight: bold;
                                            font-size: 1rem;
                                            text-align: center;
                                            padding: 6px 0;
                                            background-color: ${cell.getData().MauSac};
                                            color: white;
                                            border-radius: 6px;
                                     
                                            ">
                                             ${cell.getData().TenTrangThai}
                                        </span>
                                    </div>
                                    <div class="col-sm-10" style=" width: 80%; ">
                                       
                                        <div class="row">
                                            <div class="col-md-3">
                                                <p class="fs-big my-1">Số: <b>${cell.getData().SoQuyetDinh || ChuaCoThongTin}</b></p>
                                            </div>
                                            <div class="col-md-3">
                                                <p class="fs-big my-1">Ngày lập: <b>${cell.getData().NgayLapConVer ||
        ChuaCoThongTin
        }</b></p>
                                            </div>  
                                            <div class="col-md-3">
                                                <p class="fs-big my-1">Người lập: <b>${cell.getData().TenNhanVien ||
        ChuaCoThongTin
        }</b></p>
                                            </div>   
                                                 <div class="col-md-3">
                                                <p class="fs-big my-1">Chức vụ: <b>${cell.getData().TenChucVu ||
        ChuaCoThongTin
        }</b></p>
                                            </div>                                         
                                        </div>
                                         <hr style="border: 1px solid var(--main-color);    margin: 0.5rem 0;"/>
                                        <div class="row">
                                          
                                                  <div class="col-md-6">
                                                <p class="fs-big my-1">Đơn vị sử dụng: <b>${cell.getData().TenDonViSuDung ||
        ChuaCoThongTin
        }</b></p>
                                            </div>   
                                        
                                                 <div class="col-md-3">
                                                <p class="fs-big my-1">Cấp học: <b>${cell.getData().TenCapHoc ||
        ChuaCoThongTin
        }</b></p>
                                            </div>    
                                     
                                                 <div class="col-md-3">
                                                <p class="fs-big my-1">Số lượng: <b>${cell.getData().SoLuongHuy ||
        ChuaCoThongTin
        }</b></p>
                                            </div>    
                                           
                                        </div>

                                        <div class="row">
                                          
                                                
                                        
                                                 <div class="col-md-6">
                                                <p class="fs-big my-1">Loại phôi: <b>${cell.getData().TenLoaiPhoiVanBangChungChi ||
        ChuaCoThongTin
        }</b></p>
                                            </div>    
                                       <div class="col-md-3">
                                                <p class="fs-big my-1">Số hiệu: <b>${cell.getData().SoHieuTu + ' - ' + cell.getData().SoHieuDen ||
        ChuaCoThongTin
        }</b></p>
                                            </div>  
                                                     
                                        </div>
                                        <div class="row">
                                          
                                                 <div class="col-md-6">
                                                <p class="fs-big my-1">Lý do: <b>${cell.getData().LyDo ||
        ChuaCoThongTin
        }</b></p>
                                            </div>    
                                        
                                                 
                                           <div class="col-md-3">
                                                <p class="fs-big my-1">Đính kèm: <b> <span id="lblDinhKem_View"><a href="#" data="${cell.getData().DinhKem}" onclick="XemDinhKem_us('${cell.getData().DinhKem ||
        ChuaCoThongTin
        }')" data="">Xem đính kèm</a></span></b></p>
                                            </div>  
                                        </div>

                                  

                                        <div class="row">
                                            <div class="col-md-12">
                                                <p class="fs-big my-1">Ghi chú: <b>${cell.getData().GhiChu ||
        ChuaCoThongTin
        }</b></p>
                                            </div>   
                                        </div>
                                        <hr style="border: 1px solid var(--main-color);    margin: 0.5rem 0;"/>


                                        <div class="row">
                                            <div class="col-md-3">
                                                <p class="fs-big my-1">Ngày phê duyệt: <b>${cell.getData().NgayLap_PheDuyetConVer ||
        ChuaCoThongTin
        }</b></p>
                                            </div>   
                                             <div class="col-md-3">
                                                <p class="fs-big my-1">Người phê duyệt: <b>${cell.getData().TenNhanVien_PheDuyet ||
        ChuaCoThongTin
        }</b></p>
                                            </div>   
                                             <div class="col-md-3">
                                                <p class="fs-big my-1">Chức vụ: <b>${cell.getData().TenChucVu_PheDuyet ||
        ChuaCoThongTin
        }</b></p>
                                            </div>   
                                        </div>

                                          <div class="row">
                                            <div class="col-md-12">
                                                <p class="fs-big my-1">Nội dung phê duyệt: <b>${cell.getData().NoiDung_PheDuyet ||
        ChuaCoThongTin
        }</b></p>
                                            </div>   
                                        </div>
     <div class="dropdown text-end" style="position: absolute; top: 10px; right: 10px">
                                            <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                                            </button>
                                            <div class="dropdown-menu dropdown-menu-end w-auto" >
                                                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '32' ? 'display: none;' : ''}" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="SuaThongTin('${cell.getData().id}'); return false;"><i class=" text-primary fa fa-pencil-square-o iconsize-item"></i>&ensp; Chỉnh sửa phiếu nhập kho</a>
                                                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '32' ? 'display: none;' : ''}" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="PheDuyetThongTin('${cell.getData().id}','${cell.getData().TenDonViSuDung}','${cell.getData().NgayLapConVer}','${cell.getData().SoQuyetDinh}'); return false;"><i class="fas fa-check-circle text-success" aria-hidden="true"></i>&ensp; Phê duyệt phiếu nhập kho</a>
                                                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '0' || cell.getData().TrangThai == '34' || cell.getData().TrangThai == '33' ? 'display: none;' : ''}" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="ThuHoiThongTin('${cell.getData().id}'); return false;"><i  class="fas fa-rotate-left text-primary" aria-hidden="true"></i>&ensp; Thu hồi phiếu nhập kho</a>
                                                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '32' ? 'display: none;' : ''}"" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XoaThongTin('${cell.getData().id}')"><i class="fa fa-trash-o iconsize-item text-danger"></i>&ensp; Xóa thông tin phiếu nhập kho</a>
                                      

                                            </div>
                                        </div>
                                    </div>
                                  
                                </div>
                            </div>
                        </div>
                    </div>`;
}




var table = new Tabulator("#GridMainDS", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "600",
    HeaderVertAlign: "center",
    headerVisible: false,
    columns: [
        {
            title: "Thông tin",
            field: "",
            formatter: htmlDuLieu,
            visible: true,
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

async function LoadDataTable() {
    table.clearData();
    GridMainLuoi.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
        {
            SearchKey: $("#SearchKey").value(),
            TuNgay_Loc: $("#TuNgay_Loc").value(),
            DenNgay_Loc: $("#DenNgay_Loc").value(),
            CapHocID_Loc: $("#CapHocID_Loc").value(),
            LoaiPhoiVanBangChungChiID_Loc: $("#LoaiPhoiVanBangChungChiID_Loc").value(),
            LyDoID_Loc: $("#LyDoID_Loc").value(),
            DonViID_Loc: $("#DonViID_Loc").value(),
            CbSapXep: $("#CbSapXep").value(),
        }
    );
    if (!result.Err) {
      debugger
        
          LoadBieuDo()
        table.setData(result.result);
        GridMainLuoi.setData(result.result);

    } else {
        table.setData(null);
        GridMainLuoi.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}
//#region Lưới 2
function actionDropdownFormatter(cell) {
    var ID = cell.getData().id;
    var button = document.createElement("button");
    button.className = "btn btn-sm btn-white dropdown-toggle-hide-arrow";
    button.innerHTML = `<i class="fa fa-ellipsis-h" style="color: var(--primary)"></i>`;
    button.style.boxShadow = "none";

    button.onclick = function (e) {
        e.stopPropagation(); // Không lan click ra ngoài

        // Đóng dropdown cũ nếu có
        document.querySelectorAll('.custom-dropdown-menu').forEach(el => el.remove());

        // Tạo dropdown mới
        const dropdown = document.createElement("div");
        dropdown.className = "custom-dropdown-menu dropdown-menu dropdown-menu-end show";
        dropdown.style.position = "absolute";
        dropdown.style.zIndex = 9999;
        dropdown.style.minWidth = "200px";
        dropdown.innerHTML = `
                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '32' ? 'display: none;' : ''}" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="SuaThongTin('${cell.getData().id}'); return false;"><i class=" text-primary fa fa-pencil-square-o iconsize-item"></i>&ensp; Chỉnh sửa phiếu nhập kho</a>
                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '32' ? 'display: none;' : ''}" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="PheDuyetThongTin('${cell.getData().id}','${cell.getData().TenDonViSuDung}','${cell.getData().NgayLapConVer}','${cell.getData().SoQuyetDinh}'); return false;"><i class="fas fa-check-circle text-success" aria-hidden="true"></i>&ensp; Phê duyệt phiếu nhập kho</a>
                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '0' || cell.getData().TrangThai == '33' ? 'display: none;' : ''}" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="ThuHoiThongTin('${cell.getData().id}'); return false;"><i  class="fas fa-rotate-left text-primary" aria-hidden="true"></i>&ensp; Thu hồi phiếu nhập kho</a>
                <a style="padding: 5px 12px; ${cell.getData().TrangThai == '32' ? 'display: none;' : ''}"" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XoaThongTin('${cell.getData().id}')"><i class="fa fa-trash-o iconsize-item text-danger"></i>&ensp; Xóa thông tin phiếu nhập kho</a>
                                        
        `;

        // Tính vị trí button
        const rect = button.getBoundingClientRect();
        dropdown.style.left = `${rect.left + window.scrollX}px`;
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;

        // Gắn ra body
        document.body.appendChild(dropdown);
    };

    return button;
}
var GridMainLuoi = new Tabulator("#GridMainLuoi", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: actionDropdownFormatter,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false,
        },
        {
            title: "Số",
            field: "SoQuyetDinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Ngày lập",
            field: "NgayLapConVer",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Nhân người lập",
            field: "TenNhanVien",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Cấp học",
            field: "TenCapHoc",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Loại phôi",
            field: "TenLoaiPhoiVanBangChungChi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 200,
        },
        {
            title: "Số lượng hủy",
            field: "SoLuongHuy",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Lý do hủy",
            field: "LyDo",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 250,
        },
        {
            title: "Ghi chú",
            field: "GhiChu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        }

    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
// Đóng dropdown khi click vào item bất kỳ
document.addEventListener("click", function (e) {
    if (e.target.closest(".custom-dropdown-menu")) {
        // Cho phép xử lý logic trước khi đóng (nếu cần)
        setTimeout(() => {
            document.querySelectorAll(".custom-dropdown-menu").forEach(el => el.remove());
        }, 0);
    }
});
function PheDuyetThongTin(id, DonViNhan, NgayLap, SoQuyetDinh) {
    $("#NgayPheDuyet").val(defaultDate)
    $("#HuyPhoiVBCCID").val(id)
    $('#SoDeNghiPD').text(SoQuyetDinh)
    $('#NgayDeNghiPD').text(NgayLap)
    $('#DonViDeNghiPD').text(DonViNhan)
    $("#NoiDung_PheDuyet").text(`Phê duyệt thu hồi/hủy bỏ phôi văn bằng, chứng chỉ số: ${SoQuyetDinh} ngày lập ${NgayLap} của ${DonViNhan}.`);
    $("#NhanVienID_PheDuyet").value(NhanVienID)
    $("#ChucVuID_PheDuyet").value(ChucVuID)
    $('#mdQPheDuyet').modal('show')
}

$("#btnBanHanh").on("click", async function () {
    const validate = new NTSValidate("#mdQPheDuyet");
    if (!validate.trim().check()) return false;
    const payload = {
        HuyPhoiVBCCID: $("#HuyPhoiVBCCID").val(),
        NhanVienID_PheDuyet: $("#NhanVienID_PheDuyet").value(),
        ChucVuID_PheDuyet: $("#ChucVuID_PheDuyet").value(),
        NoiDung_PheDuyet: $("#NoiDung_PheDuyet").text(),
        NgayPheDuyet: $("#NgayPheDuyet").value(),
        TrangThai: "32" // phê duyệt
    };

    var result = await NTS.getAjaxAPIAsync(
        "post",
        window.location.pathname + "/PheDuyet",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdQPheDuyet").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});
async function ThuHoiThongTin(id) {
    $("#HuyPhoiVBCCID").val(id)
    CanhBaoThuHoiDuyet(async () => {
        const payload = {
            HuyPhoiVBCCID: $("#HuyPhoiVBCCID").val(),
            NhanVienID_PheDuyet: '',
            ChucVuID_PheDuyet: '',
            NoiDung_PheDuyet: '',
            NgayPheDuyet: '',
            TrangThai: "33" // thu hồi
        };

        var result = await NTS.getAjaxAPIAsync(
            "post",
            window.location.pathname + "/PheDuyet",
            payload
        );

        if (!result.Err) {
            LoadDataTable();
            NTS.thanhcong(result.Msg);
        } else {
            result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        }
    });
    return false;
}



$(document).on('keyup', '#SearchKey', function (e) {
    if (e.keyCode == '13') {
        GridMainLuoi.setFilter(matchAny, { value: $(this).val() });
        table.setFilter(matchAny, { value: $(this).val() });
    }
});




async function LoadBieuDo() {
    // Giả lập result nếu bạn đang test
    $('#XepLoaiChart').html(``)
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getThongKeHuyPhoiTheoTenLyDo",
        {
            SearchKey: $("#SearchKey").value(),
            TuNgay_Loc: $("#TuNgay_Loc").value(),
            DenNgay_Loc: $("#DenNgay_Loc").value(),
            CapHocID_Loc: $("#CapHocID_Loc").value(),
            LoaiPhoiVanBangChungChiID_Loc: $("#LoaiPhoiVanBangChungChiID_Loc").value(),
            LyDoID_Loc: $("#LyDoID_Loc").value(),
            DonViID_Loc: $("#DonViID_Loc").value(),
            CbSapXep: $("#CbSapXep").value(),
        }
    );
    $('#LisdSoLuongPhoi').empty();
    if (!result || result.Err || !result.Result || result.Result.length === 0) {
        $('#XepLoaiChart').text("Không có dữ liệu để hiển thị biểu đồ.");
        return;
    }

    var tongSoLuongHuy = result.Result.reduce((sum, item) => sum + (parseInt(item.SoLuongHuy) || 0), 0);
    $('#txtTongPhoi').text(tongSoLuongHuy)
    if (result && !result.Err && result.Result.length > 0) {
        result.Result.forEach(function (item) {
            var html = `
                <div class="col-md-6" style="padding-bottom: 10px;">
                    <h3 class="card-title" style="color: #d7b324ff">
                        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                            <path d="M14.5303 3.6184L8.3724 1.56577C8.26706 1.53065 8.15317 1.53065 8.04783 1.56577L1.88993 3.6184C1.78775 3.65246 1.69888 3.71781 1.63591 3.80519C1.57294 3.89257 1.53905 3.99755 1.53906 4.10526V9.23684C1.53906 9.37293 1.59313 9.50346 1.68936 9.59969C1.7856 9.69593 1.91612 9.74999 2.05222 9.74999C2.18832 9.74999 2.31884 9.69593 2.41508 9.59969C2.51131 9.50346 2.56538 9.37293 2.56538 9.23684V4.81726L4.72 5.53504C4.14755 6.45988 3.96551 7.5741 4.21386 8.63304C4.46221 9.69199 5.12064 10.6091 6.04459 11.183C4.88998 11.6358 3.89189 12.455 3.16192 13.5749C4.98813 12.6525 6.51477 11.8026 8.21012 11.8026C9.90546 11.8026 11.4321 12.6525 12.3988 14.1356C12.474 14.2474 12.5903 14.3252 12.7224 14.352C12.8546 14.3788 12.9919 14.3526 13.1049 14.2789C13.2178 14.2053 13.2972 14.0901 13.3259 13.9584C13.3546 13.8267 13.3303 13.6889 13.2583 13.5749C12.5283 12.455 11.5264 11.6358 10.3756 11.183C11.2987 10.6091 11.9565 9.69261 12.2048 8.63444C12.4531 7.57627 12.2717 6.46282 11.7002 5.53825L14.5303 4.59532C14.6325 4.56128 14.7214 4.49594 14.7844 4.40856C14.8474 4.32117 14.8813 4.21618 14.8813 4.10846C14.8813 4.00074 14.8474 3.89575 14.7844 3.80837C14.7214 3.72099 14.6325 3.65565 14.5303 3.6216V3.6184Z" fill="#D63939"></path>
                        </svg>
                        ${item.TenLyDo}: <b><u>${item.SoLuongHuy}</u></b>
                    </h3>
                </div>
            `;
            $('#LisdSoLuongPhoi').append(html);
        });
    }
    // Tách labels và series từ result
    const labels = result.Result.map(x => x.TenLyDo);
    const series = result.Result.map(x => x.SoLuongHuy);

    const total = series.reduce((a, b) => a + b, 0);

    // Khởi tạo biểu đồ ApexCharts
    var options = {
        series: series,
        chart: {
            type: 'donut',
            height: 150
        },
        labels: labels,
        colors: ['#7BA829', '#E53935', '#FB8C00', '#00ACC1', '#8E24AA', '#FDD835'],
        stroke: {
            width: 5
        },
        legend: {
            position: 'right',
            fontSize: '13px',
            fontFamily: 'Arial'
        },
        tooltip: {
            enabled: true,
            y: {
                formatter: function (val) {
                    return val + " phôi";
                }
            }
        },
        dataLabels: {
            enabled: true,
            formatter: function (val, opts) {
                const absolute = series[opts.seriesIndex];
                const percent = (absolute / total) * 100;
                return ` ${Math.round(percent)}%`;
            },
            style: {
                fontSize: '12px',
                fontWeight: 'bold',
                colors: ['#ffffff'],
                fontFamily: 'Arial'
            }
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '70%',
                    labels: {
                        show: false
                    }
                }
            }
        }
    };

    // Vẽ lại biểu đồ
    $("#XepLoaiChart").empty(); // Xóa biểu đồ cũ nếu có
    var chart = new ApexCharts(document.querySelector("#XepLoaiChart"), options);
    chart.render();
}
$(document).on("click", "#btnAnHienTQ", function () {
    var divThongKe = document.getElementById("DivThongKe");
    var textAnHien = document.getElementById("textAnHien");
    var iconAnHien = document.getElementById("iconAnHien");
    if (iconAnHien.className == "fa fa-eye-slash") {
        divThongKe.classList.add("d-none");
        iconAnHien.className = "fa fa-eye";
        textAnHien.textContent = " Hiện trang tổng quan";
    } else {
        divThongKe.classList.remove("d-none");
        iconAnHien.className = "fa fa-eye-slash";
        textAnHien.textContent = " Ẩn trang tổng quan";
    }
});