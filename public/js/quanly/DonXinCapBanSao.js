var tempthem = "them";
var tempTrangThai = "40";
var ChuaCoThongTin = "";
///////// PHÍM TẮT /////////
var hotKey = 0; // 1 thêm\
var currentAvatarPath = "";
const today = new Date();
const dd = String(today.getDate()).padStart(2, '0');
const mm = String(today.getMonth() + 1).padStart(2, '0');
const yyyy = today.getFullYear();
const defaultDate = `${dd}/${mm}/${yyyy}`;
var NhanVienID='';
var ChucVuID ='';
function setNgayTrongThang(inputTuNgayId, inputDenNgayId) {
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth(); // 0 = January
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);

  // Định dạng dd/mm/yyyy
  const format = (d) => {
    const dd = String(d.getDate()).padStart(2, '0');
    const mm = String(d.getMonth() + 1).padStart(2, '0');
    const yyyy = d.getFullYear();
    return `${dd}/${mm}/${yyyy}`;
  };

  // Gán giá trị cho input
  document.getElementById(inputTuNgayId).value = format(firstDay);
  document.getElementById(inputDenNgayId).value = format(lastDay);
}
$(async function () {
     var resultNV = await NTS.getAjaxAPIAsync("GET", Laravel.layouts.getCurrentUserInfo,{});    
    NhanVienID=resultNV.data.nhanVien.id
    ChucVuID=resultNV.data.nhanVien.chucVuID
       setNgayTrongThang("TuNgay_Loc", "DenNgay_Loc")
    $(document).on("keydown", function (e) {
        var Showmodal = $('.modal.show').attr('id')
        var isShowingmdChonHocSinh = $('#mdChonHocSinh').hasClass('show');
        if((Showmodal == 'mdThemMoi' || Showmodal == undefined) && isShowingmdChonHocSinh==false){
            switch (e.keyCode) {
                case 113:
                    if (hotKey == 0) $("#btnThemMoi").trigger("click");
                    e.preventDefault();
                    break;
                case 114:
                    if (hotKey == 0) $(".nav-search-input").focus();
                    e.preventDefault();
                    break;
                case 115:
                    if (hotKey == 1) $("#mdThemMoi").modal("hide");
                    e.preventDefault();
                    break;
                case 120:
                    if (hotKey == 1) $("#btnLuuVaDong").trigger("click");
                    e.preventDefault();
                    break;
            }
        }
        if(Showmodal == 'mdQPheDuyet'){
            switch (e.keyCode) {
                case 115: //f4
                    if ($('#mdQPheDuyet').hasClass('show')) {
                        $('#mdQPheDuyet').modal('hide');
                    }
                    break;
                case 120: //f9
                    if ($('#mdQPheDuyet').hasClass('show')) {
                        $('#btnBanHanh').trigger('click');
                    }
                    break;
            }
        }
        if(Showmodal == 'mdQThuHoi'){
            switch (e.keyCode) {
                case 115: //f4
                    if ($('#mdQThuHoi').hasClass('show')) {
                        $('#mdQThuHoi').modal('hide');
                    }
                    break;
                case 120: //f9
                    if ($('#mdQThuHoi').hasClass('show')) {
                        $('#btnThuHoi').trigger('click');
                    }
                    break;
            }
        }
         if(isShowingmdChonHocSinh==true){
            switch (e.keyCode) {
                case 115: //f4
                    if ($('#mdChonHocSinh').hasClass('show')) {
                        $('#mdChonHocSinh').modal('hide');
                    }
                    break;
                case 120: //f9
                    if ($('#mdChonHocSinh').hasClass('show')) {
                        $('#btnChonVaDong').trigger('click');
                    }
                    break;
            }
        }
    });
    $("#TrangThaiXuLyID_Loc").select2({
        width: "100%"  // ✅ đúng cú pháp
    });
    LoadDataComBo();
    LoadDataComBo_Loc();
    LoadDataTable();
    LoadDataComBo_GuiDonXinCapPhoiBang();

    
});

$(document).on("shown.bs.modal", "#mdThemMoi", function () {
    hotKey = 1;
});
$(document).on("hidden.bs.modal", "#mdThemMoi", function () {
    hotKey = 0;
});
$(document).on("click", "#btnThemMoi", function () {
    ThemDuLieu();
});

async function ThemDuLieu() {
    if (!QuyenThem()) {
        return;
    }
  $("#dinhKemQD_txtDuongDanFileVB").val('');
  $("#dinhKemQD_list-file").empty();
    resetForm("#mdThemMoi");
    $("#tieuDeModal").text("Thêm mới đơn xin cấp bản sao văn bằng, chứng chỉ");
    $("#DonYeuCauID").val("");
      $("#NhanVienID_NguoiLap").value(NhanVienID);
        $("#ChucVuID_NguoiLap").value(ChucVuID);
    tempthem = "them";
    try {
        const input = document.getElementById("NgayLap");
        const today = new Date();
        const dd = String(today.getDate()).padStart(2, '0');
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const yyyy = today.getFullYear();

        input.value = `${dd}/${mm}/${yyyy}`;
         document.getElementById("NgayCap").value = `${dd}/${mm}/${yyyy}`;
    } catch { }

         try {
        var result = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.local.soPhieuTuTangUrl,
            {}
        );
        $("#SoDonXinCapBanSao").value(result.SoChungTu);
    } catch { }
    $("#mdThemMoi").modal("show");

}

async function SuaDuLieu(id) {
    if (!QuyenSua()) {
        return false;
    }

    $("#tieuDeModal").text("Cập nhật đơn xin cấp bản sao văn bằng, chứng chỉ");
    const result = await NTS.getAjaxAPIAsync("GET", window.location.pathname + "/loaddulieusua", { id: id });

    if (!result.Err) {
        const data = result.result[0];
       $('#DonXinCapBanSaoID').value(id);
       $('#NgayLap').value(data.NgayLapConVer || "");
       $('#NhanVienID_NguoiLap').value(data.NhanVienID_NguoiLap || "");
       $('#TenTruongHoc').value(data.MaDoiTuong+': '+data.HocSinh+' - '+data.TenDonViHoc || "");
       $('#ChucVuID_NguoiLap').value(data.ChucVuID_NguoiLap || "");
       $('#HocSinhID').value(data.DoiTuongID || "");
       $('#LoaiVanBangChungChiID').value(data.LoaiVanBangChungChiID || "");
       $('#NamTotNghiep').value(data.NamTotNghiep || "");
       $('#XepLoai').value(data.XepLoaiID || "");
       $('#HinhThucDaoTaoID').value(data.HinhThucDaoTaoID || "");
       $('#NgayCap').value(data.NgayCap || "");
       $('#SoHieu').value(data.SoHieu || "");
       $('#SoVaoSo').value(data.SoVaoSo || "");
       $('#GhiChu').value(data.GhiChu || "");
       $('#LyDo').value(data.LyDo || "");
       $('#SoDonXinCapBanSao').value(data.SoDonXinCapBanSao || "");

        if (data.DinhKem) {
            $("#dinhKemQD_txtDuongDanFileVB").val(data.DinhKem);
            $("#dinhKemQD_list-file").empty();

        var links = data.DinhKem.split('|'); // Tách các đường dẫn theo dấu |
        links.forEach(function(link) {
            link = link.trim(); // Xoá khoảng trắng thừa nếu có
            if (link) {
                renderAttachment(link);
            }
        });
        }

        $("#mdThemMoi").modal("show");
        tempthem = "sua";
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

function GuiDonXinCapPhoiBang(id) {
    $("#DonYeuCauID").val(id);
    $("#NgayXuLy").val(defaultDate);

    $("#tieuDe_mdGuiDonXinCapPhoiBang").text("Gửi đơn xin cấp phôi bằng");
    $("#alertMessage").html('Bạn đang thực hiện gửi đơn xin cấp phôi bằng cho <b id="TenDonViNhan"></b>');
    $("#btnGuiDon").html('<i class="fa fa-check"></i> Gửi (F9)');

    tempTrangThai = "40";
    $('#mdGuiDonXinCapPhoiBang').modal('show');
}
function ThuHoiDonXinCapPhoiBang(id) {
    $("#DonYeuCauID").val(id);
    $("#NgayXuLy").val(defaultDate);

    $("#tieuDe_mdGuiDonXinCapPhoiBang").text("Thu hồi đơn xin cấp phôi bằng");
    $("#alertMessage").html('Bạn đang thực hiện <b>thu hồi</b> đơn xin cấp phôi bằng cho <b id="TenDonViNhan"></b>');
    $("#btnGuiDon").html('<i class="fa fa-undo"></i> Thu hồi (F9)');

    tempTrangThai = "41";
    $('#mdGuiDonXinCapPhoiBang').modal('show');
}

let commonComboConfig = {
    columns: 1,
    indexValue: 0,
    indexText: 2, // assuming your result rows are [id, code, name]
    textShowTatCa: "-Chọn-",
    showTatCa: true,
};

function LoadDataComBo() {
    NTS.loadDataComboAsync({
        name: "#NhanVienID_NguoiLap, #NhanVienID_ThuHoi",
        type: "GET",
        ajaxUrl: window.Laravel.local.comboNhanVien,
        ajaxParam: {},
        ...commonComboConfig
    });
   
    NTS.loadDataComboAsync({
        name: "#ChucVuID_NguoiLap, #ChucVuID_ThuHoi",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListChucVu,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#LoaiVanBangChungChiID, #LoaiPhoiID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 1,
        indexValue: 2,
        indexText: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: true,
    });
    NTS.loadDataComboAsync({
        name: "#DonViNhanID,#DonViID_Nhan",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListDonVi,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#NamTotNghiep",
        type: "GET",
        ajaxUrl: window.Laravel.layouts.getDSNienDo,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 0,
        indexText1: 0,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });


       NTS.loadDataComboAsync({
        name: "#XepLoai",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListXepLoai,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });

       NTS.loadDataComboAsync({
        name: "#HinhThucDaoTaoID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListHTDT,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
}

function LoadDataComBo_Loc() {
    NTS.loadDataComboAsync({
        name: "#LoaiPhoiID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Tất cả-",
        showTatCa: true,
    });
}

function LoadDataComBo_GuiDonXinCapPhoiBang() {
    NTS.loadDataComboAsync({
        name: "#NhanVienID_PheDuyet",
        type: "GET",
        ajaxUrl: window.Laravel.local.comboNhanVien,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2, // assuming your result rows are [id, code, name]
        textShowTatCa: "-Chọn-",
        showTatCa: true,
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuID_PheDuyet",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListChucVu,
        ajaxParam: {},
           columns: 1,
    indexValue: 0,
    indexText: 2, // assuming your result rows are [id, code, name]
    textShowTatCa: "-Chọn-",
    showTatCa: true,
    });
}

$(document).on("click", "#TimKiemNangCao", function () {
    if ($("#KhungTimKiem").css("display") == "block") {
        $("#KhungTimKiem").slideUp(200);
    } else {
        $("#KhungTimKiem").slideDown(200);
    }
    return false;
});
$(document).on("click", "#DongTimKiem", function () {
    $("#KhungTimKiem").slideUp(200);
    return false;
});

$(document).on("click", "#btnAnHienTQ", function () {
    var divThongKe = document.getElementById("DivThongKe");
    var textAnHien = document.getElementById("textAnHien");
    var iconAnHien = document.getElementById("iconAnHien");
    if (iconAnHien.className == "fa fa-eye-slash") {
        divThongKe.classList.add("d-none");
        iconAnHien.className = "fa fa-eye";
        textAnHien.textContent = " Hiện trang tổng quan";
    } else {
        divThongKe.classList.remove("d-none");
        iconAnHien.className = "fa fa-eye-slash";
        textAnHien.textContent = " Ẩn trang tổng quan";
    }
});

$("#btnLuuVaDong").on("click", async function () {
    const validate = new NTSValidate("#mdThemMoi");
    if (!validate.trim().check()) return false;
    const dinhKemQDValue = $("#dinhKemQD").val();
    const payload = {
         DonXinCapBanSaoID:$('#DonXinCapBanSaoID').value(),
        NgayLap:$('#NgayLap').value(),
        NhanVienID_NguoiLap:$('#NhanVienID_NguoiLap').value(),
        ChucVuID_NguoiLap:$('#ChucVuID_NguoiLap').value(),
        DoiTuongID:$('#HocSinhID').value(),
        LoaiVanBangChungChiID:$('#LoaiVanBangChungChiID').value(),
        DonViID_Nhan:'',
        NamTotNghiep:$('#NamTotNghiep').value(),
        XepLoaiID:$('#XepLoai').value(),
        HinhThucDaoTaoID:$('#HinhThucDaoTaoID').value(),
        NgayCap:$('#NgayCap').value(),
        SoHieu:$('#SoHieu').value(),
        SoVaoSo:$('#SoVaoSo').value(),
        GhiChu:$('#GhiChu').value(),
        LyDo:$('#LyDo').value(),
        DinhKem: dinhKemQDValue,
        SoDonXinCapBanSao: $('#SoDonXinCapBanSao').value()
    };
    var met = "POST";
    if (tempthem == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }
    var result = await NTS.getAjaxAPIAsync(
        met,
        window.location.pathname + "/luuthongtin",
        payload
    );
    if (!result.Err) {
        NTS.thanhcong(result.Msg);
        LoadDataTable();
        $("#mdThemMoi").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});

function htmlDuLieu(cell, formatterParams, onRendered) {
    const data = cell.getData();

    const fileName = data.TenTepDinhKem || "Không rõ";
    const fileLink = data.DinhKem || "#";
    const dinhKemHTML = data.DinhKem
        ? `<a href="${fileLink}" target="_blank">${fileName}</a>`
        : "Không có";
    return `<div class="list-item col-md-12" style="padding: 0px;">
        <div class="card card-luoi shadow-sm mb-2">
            <div id="card_${data.id}" class="card-body profile-user-box">
                <div class="row">
                    <div class="col-sm-2 text-center" style="width:18%;">
                        <div class="profile-picture" style=" height: 110px;margin-top: 9px;">
                            <img src="${window.Laravel.local.linkAnhDonXin}" alt="ảnh đơn xin" class="img-thumbnail rounded lazy mb-2" >
                        </div>
                        <div style="display: flex; justify-content: center;">
                            <span style="
                                display: block;
                                margin-top: 10px;
                                font-weight: bold;
                                font-size: 1rem;
                                text-align: center;
                                padding: 6px 0;
                                background-color: ${data.MauSac};
                                color: white;
                                border-radius: 6px;
                                width: 100%;
                                ">
                                ${data.TenTrangThai}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-10" style="width:82%;">
                        <div class="row">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Số: <b>${data.SoDonXinCapBanSao || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Ngày lập: <b>${data.NgayLapConVer || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Người đề nghị: <b>${data.TenNhanVien_Lap || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Chức vụ: <b>${data.TenChucVu_Lap || ""}</b></p>
                                           <div class="dropdown text-end" style="position: absolute; top: 10px; right: 10px;">
                                    <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-end w-auto">
                                        <a style="padding: 5px 12px;" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XemThongTin('${cell.getData().id}'); return false;"><i  class="fa fa-eye text-success " aria-hidden="true"></i>&ensp; Xem đơn đề nghị cấp bản sao</a>
                                        <a style="padding: 5px 12px; ${cell.getData().TrangThai == '50' || cell.getData().TrangThai == '52' || cell.getData().TrangThai == '42' ? '' :'display: none;' }" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="SuaDuLieu('${cell.getData().id}'); return false;"><i class=" text-primary fa fa-pencil-square-o iconsize-item"></i>&ensp; Chỉnh sửa đơn xin cấp bản sau</a>
                                        <a style="padding: 5px 12px; ${cell.getData().TrangThai == '50' || cell.getData().TrangThai == '52' || cell.getData().TrangThai == '42' ? '' :'display: none;' }" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="PheDuyetThongTin('${cell.getData().id}','${cell.getData().SoDonXinCapBanSao}','${cell.getData().NgayLapConVer}','${cell.getData().TenDonVi_YeuCau}' ); return false;"><i class="fas fa-check-circle text-success" aria-hidden="true"></i>&ensp; Gửi đơn xin cấp bản sau</a>
                                        <a style="padding: 5px 12px; ${cell.getData().TrangThai == '40' || cell.getData().TrangThai == '51' || cell.getData().TrangThai == '33' ? '' :'display: none;' }" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="ThuHoiThongTin('${cell.getData().id}','${cell.getData().SoDonXinCapBanSao}','${cell.getData().NgayLapConVer}','${cell.getData().TenDonVi_YeuCau}'); return false;"><i  class="fas fa-rotate-left text-primary" aria-hidden="true"></i>&ensp; Thu hồi đơn xin cấp bản sau</a>
                                        <a style="padding: 5px 12px; ${cell.getData().TrangThai == '50' || cell.getData().TrangThai == '52' || cell.getData().TrangThai == '42' ? '' :'display: none;' }" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XoaThongTin('${cell.getData().id }')"><i class="fa fa-trash-o iconsize-item text-danger"></i>&ensp; Xóa đơn xin cấp bản sau</a>
                                    </div>


                                      
                                </div>
                               
                              
                            </div>
                           
                        </div>
                         <hr style="border-top: 2px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />
                        <div class="row">
                                <div class="col-md-6">
                                    <p class="fs-big my-1">Đơn vị đề nghị: <b>${data.TenDonVi_YeuCau || ""}</b></p>
                                </div>
                                <div class="col-md-3">
                                    <p class="fs-big my-1">Học sinh: <b>${data.HocSinh || ""}</b></p>
                                </div>
                                <div class="col-md-3">
                                    <p class="fs-big my-1">Năm tốt nghiệp: <b>${data.NamTotNghiep || ""}</b></p>
                                </div>
                            </div>


                            <div class="row">
                                <div class="col-md-6">
                                    <p class="fs-big my-1">Loại văn bằng: <b>${data.TenLoaiVanBang || ""}</b></p>
                                </div>
                                 <div class="col-md-3">
                                    <p class="fs-big my-1">Số hiệu: <b>${data.SoHieu || ""}</b></p>
                                </div>
                                <div class="col-md-3">
                                    <p class="fs-big my-1">Ngày cấp: <b>${data.NgayCapConVer || ""}</b></p>
                                </div>
                                
                            </div>
                             <div class="row" style=" display: none; ">
                                <div class="col-md-6">
                                    <p class="fs-big my-1">Lý do cấp: <b>${data.LyDo || ""}</b></p>
                                </div>
                                <div class="col-md-6">
                                      <p class="fs-big my-1">Đính kèm: <b> <span id="lblDinhKem_View"><a href="#" data="${cell.getData().DinhKem}" onclick="XemDinhKem_us('${cell.getData().DinhKem  ||''}')" data="">${cell.getData().DinhKem !='' ? 'Xem đính kèm' : 'Không có đính kèm'}</a></span></b></p>
                                 </div>
                            </div>
                         
                            <hr style="border-top: 2px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />
                                <div class="row" >
                                    <div class="col-md-12">
                                        <p class="fs-big my-1">Đơn vị tiếp nhận: <b>${data.TenDonVi_Nhan || ""}</b></p>
                                    </div>
                                </div>

                                  <div class="row" style="${cell.getData().TrangThai == '40' ? '' :'display: none;' }">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Ngày gửi: <b>${data.NgayGuiConVer || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Người gửi: <b>${data.TenNhanVien_Gui || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Chức vụ: <b>${data.TenChucVu_Gui || ""}</b></p>
                                    </div>
                                </div>

                                  <div class="row" style="${cell.getData().TrangThai == '32' ? '' :'display: none;' }">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Ngày phê duyệt: <b>${data.NgayPheDuyetConVer || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Người phê duyệt: <b>${data.TenNhanVien_PheDuyet || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Chức vụ: <b>${data.TenChucVu_PheDuyet || ""}</b></p>
                                    </div>
                                </div>


                                 <div class="row" style="${cell.getData().TrangThai == '33' ? '' :'display: none;' }">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Ngày thu hồi phê duyệt: <b>${data.NgayThuHoiConVer || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Người thu hồi phê duyệt: <b>${data.TenNhanVien_ThuHoi || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Chức vụ: <b>${data.TenChucVu_ThuHoi || ""}</b></p>
                                    </div>
                                </div>

                                 <div class="row" style="${cell.getData().TrangThai == '42' ? '' :'display: none;' }">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Ngày từ chối: <b>${data.NgayTuChoiConVer || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Người từ chối: <b>${data.TenNhanVien_TuChoi || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Chức vụ: <b>${data.TenChucVu_TuChoi || ""}</b></p>
                                    </div>
                                </div>

                               


                       
                    </div>

                    </div>
                </div>
            </div>
        </div>
    </div>`;
}

var table = new Tabulator("#Grid1", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "600",
    HeaderVertAlign: "center",
    headerVisible: false,
    columns: [
        {
            title: "Thông tin",
            field: "ThongTinHoGiaDinh",
            formatter: htmlDuLieu,
            visible: true,
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
$(document).on("change", "#CbSapXep", function () {
    LoadDataTable();
});
var DuLieuBieuDo ;
async function LoadDataTable() {
    table.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
        {
           TuNgay_Loc: $("#TuNgay_Loc").value(),
            DenNgay_Loc: $("#DenNgay_Loc").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            TrangThai_Loc: $("#TrangThaiXuLyID_Loc").val(),
            CbSapXep: $("#CbSapXep").value(),
        }
    );
    if (!result.Err) {
  
        DuLieuBieuDo=result.result ;
          VeChart();
        table.setData(result.result);
        GridMainLuoi.setData(result.result);
    } else {        
        table.setData(null);
        GridMainLuoi.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}
async function XoaThongTin(id) {
    if (!QuyenXoa()) { return; }
    var result_ktxoa = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.layouts.KiemTraXoa,
        {
            ma: id,
            model: "QuanLy\\donxincapbansao",
        }
    );

    if (!result_ktxoa.Err) {
        CanhBaoXoa(async () => {
            var result = await NTS.getAjaxAPIAsync(
                "DELETE",
                window.location.pathname + "/xoa",
                { ma: id }
            );
            if (!result.Err) {
                LoadDataTable();
                NTS.thanhcong(result.Msg);
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
    } else {
        NTS.canhbao(result_ktxoa.Msg);
        return false;
    }
}
$(document).on("click", "#TimKiem", async function () {
    $("#KhungTimKiem").slideUp(200);
    await LoadDataTable();
    return false;
});

$(document).on("keyup", "#SearchKey", async function (e) {
    
   if (e.keyCode == '13') {
        GridMainLuoi.setFilter(matchAny, { value: $(this).val() });
        table.setFilter(matchAny, { value: $(this).val() });
    }
});





$(document).on("click", "#btn-layout-1", async function () {
    $("#grid-layout").fadeIn(200);
    $("#list-layout").hide();
    $("#list-layout").removeClass("show");
    $("#grid-layout").addClass("show");
    await LoadDataTable();
});

$(document).on("click", "#btn-layout-2", async function () {
    $("#grid-layout").hide();
    $("#list-layout").fadeIn(200);
    $("#list-layout").addClass("show");
    $("#grid-layout").removeClass("show");
    $(".divThaoTacNhanh").hide();
    GridMainLuoi.setFilter(matchAny, { value: $('#SearchKey').value() });
});
function actionDropdownFormatter(cell) {
    var ID = cell.getData().id;
    var button = document.createElement("button");
    button.className = "btn btn-sm btn-white dropdown-toggle-hide-arrow";
    button.innerHTML = `<i class="fa fa-ellipsis-h" style="color: var(--primary)"></i>`;
    button.style.boxShadow = "none";

    button.onclick = function (e) {
        e.stopPropagation(); // Không lan click ra ngoài

        // Đóng dropdown cũ nếu có
        document.querySelectorAll('.custom-dropdown-menu').forEach(el => el.remove());

        // Tạo dropdown mới
        const dropdown = document.createElement("div");
        dropdown.className = "custom-dropdown-menu dropdown-menu dropdown-menu-end show";
        dropdown.style.position = "absolute";
        dropdown.style.zIndex = 9999;
        dropdown.style.minWidth = "200px";
        dropdown.innerHTML = `
                                     <a style="padding: 5px 12px;" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XemThongTin('${cell.getData().id}'); return false;"><i  class="fa fa-eye text-success " aria-hidden="true"></i>&ensp; Xem đơn đề nghị cấp bản sao</a>
                                        <a style="padding: 5px 12px; ${cell.getData().TrangThai == '50' || cell.getData().TrangThai == '52' || cell.getData().TrangThai == '42' ? '' :'display: none;' }" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="SuaDuLieu('${cell.getData().id}'); return false;"><i class=" text-primary fa fa-pencil-square-o iconsize-item"></i>&ensp; Chỉnh sửa đơn xin cấp bản sau</a>
                                        <a style="padding: 5px 12px; ${cell.getData().TrangThai == '50' || cell.getData().TrangThai == '52' || cell.getData().TrangThai == '42' ? '' :'display: none;' }" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="PheDuyetThongTin('${cell.getData().id}','${cell.getData().SoDonXinCapBanSao}','${cell.getData().NgayLapConVer}','${cell.getData().TenDonVi_YeuCau}' ); return false;"><i class="fas fa-check-circle text-success" aria-hidden="true"></i>&ensp; Gửi đơn xin cấp bản sau</a>
                                        <a style="padding: 5px 12px; ${cell.getData().TrangThai == '40' || cell.getData().TrangThai == '51' || cell.getData().TrangThai == '33' ? '' :'display: none;' }" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="ThuHoiThongTin('${cell.getData().id}','${cell.getData().SoDonXinCapBanSao}','${cell.getData().NgayLapConVer}','${cell.getData().TenDonVi_YeuCau}'); return false;"><i  class="fas fa-rotate-left text-primary" aria-hidden="true"></i>&ensp; Thu hồi đơn xin cấp bản sau</a>
                                        <a style="padding: 5px 12px; ${cell.getData().TrangThai == '50' || cell.getData().TrangThai == '52' || cell.getData().TrangThai == '42' ? '' :'display: none;' }" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XoaThongTin('${cell.getData().id }')"><i class="fa fa-trash-o iconsize-item text-danger"></i>&ensp; Xóa đơn xin cấp bản sau</a>
                             
              `;

        // Tính vị trí button
        const rect = button.getBoundingClientRect();
        dropdown.style.left = `${rect.left + window.scrollX}px`;
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;

        // Gắn ra body
        document.body.appendChild(dropdown);
    };

    return button;
}
var GridMainLuoi = new Tabulator("#GridMainLuoi", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: actionDropdownFormatter,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false,
        },
         {
            title: "Số",
            field: "SoDonXinCapBanSao",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Người lập",
            field: "TenNhanVien_Lap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 100,
        },
       
        {
            title: "Người lập",
            field: "TenNhanVien_Lap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Chức vụ",
            field: "TenChucVu_Lap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Đơn vị yêu cầu",
            field: "TenDonVi_YeuCau",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Học sinh",
            field: "HocSinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Năm tốt nghiệp",
            field: "NamTotNghiep",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Loại văn bằng",
            field: "TenLoaiVanBang",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 200,
        },
        {
            title: "Số hiệu",
            field: "SoHieu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Ngày cấp",
            field: "NgayCapConVer",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Số vào sổ",
            field: "SoVaoSo",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
        {
            title: "Đơn vị tiếp nhận",
            field: "TenDonVi_Nhan",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
        {
            title: "Lý do điều chỉnh",
            field: "LyDo",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
        {
            title: "Ghi chú",
            field: "GhiChu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        }

    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});


async function VeChart() {
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.getThongKe,
        {}
    );
    try {
        $("#txtTong").text(result.Result[0].TongSo);
        $("#txtDaDeNghi").text(result.Result[0].SoLuongDaDuyet);
        $("#txtChoDuyet").text(result.Result[0].SoLuongChoDuyet);
        $("#txtBiTuChoi").text(result.Result[0].SoLuongTuChoi);




        var group = {};
        DuLieuBieuDo.forEach(function (item) {
            var ten = item.TenLoaiVanBang || 'Không xác định';
            group[ten] = (group[ten] || 0) + 1;
        });
        // Xóa nội dung cũ (nếu có)
        $('#LisdSoLuongPhoi').html('');
        // Duyệt và gắn từng dòng vào
        $.each(group, function (ten, soluong) {
            var color = '#7AA802'; // màu tùy loại
            var icon = `<svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17"
                                                fill="none">
                                                <path
                                                    d="M2.73756 2.05261C1.97809 2.05261 1.36914 2.66156 1.36914 3.42103V10.2631C1.36914 10.6261 1.51331 10.9741 1.76994 11.2308C2.02657 11.4874 2.37463 11.6316 2.73756 11.6316H8.21125V15.0526L10.2639 13L12.3165 15.0526V11.6316H13.6849C14.0479 11.6316 14.3959 11.4874 14.6525 11.2308C14.9092 10.9741 15.0534 10.6261 15.0534 10.2631V3.42103C15.0534 3.05811 14.9092 2.71004 14.6525 2.45341C14.3959 2.19678 14.0479 2.05261 13.6849 2.05261H2.73756ZM8.21125 3.42103L10.2639 4.78945L12.3165 3.42103V5.81577L14.3691 6.84209L12.3165 7.8684V10.2631L10.2639 8.89472L8.21125 10.2631V7.8684L6.15861 6.84209L8.21125 5.81577V3.42103ZM2.73756 3.42103H6.15861V4.78945H2.73756V3.42103ZM2.73756 6.15788H4.79019V7.5263H2.73756V6.15788ZM2.73756 8.89472H6.15861V10.2631H2.73756V8.89472Z"
                                                    fill="#7AA802" />
                                            </svg>`; // hoặc SVG nếu muốn

            var html = `
            <h3 style="color: ${color}" class="card-title">
                ${icon} ${ten}: <b><u><b>${soluong}</b></u></b>
            </h3>
            `;
            $('#LisdSoLuongPhoi').append(html);
       });

$('#XepLoaiChart').html(``)
      var options = {
        series: [result.Result[0].SoLuongDaDuyet, result.Result[0].SoLuongChoDuyet, result.Result[0].SoLuongTuChoi],
        chart: {
            type: 'donut',
            height: 150
        },
        labels: ['Đã duyệt', 'Chờ duyệt', 'Bị từ chối'],
        colors: [result.Result[0].MauSacDaDuyet, result.Result[0].MauSacChoDuyet, result.Result[0].MauSacBiTuChoi],
        stroke: {
            width: 5
        },
        legend: {
            position: 'right',
            fontSize: '13px',
            fontFamily: 'Arial'
        },
        tooltip: {
            enabled: false
        },
         dataLabels: {
            enabled: true,
            formatter: function (val) {
            return Math.round(val) + "%";
            },
            style: {
            fontSize: '12px',
            fontWeight: 'bold',
            colors: ['#ffffff'],
            fontFamily: 'Arial'
            }
        },
        plotOptions: {
            pie: {
            donut: {
                size: '70%',
                labels: {
                show: false
                }
            }
            }
        }
    };

    var chart = new ApexCharts(document.querySelector("#XepLoaiChart"), options);
    chart.render();
    } catch { }
}
$(document).on("keyup", "#timKiemTruongHoc", async function (e) {
   if (e.keyCode == '13') {
        gridTruongHoc.setFilter(matchAny, { value: $(this).val() });
    }
});

var gridTruongHoc = new Tabulator("#gridTruongHoc", {
    height: "420px",
    layout: "fitColumns",
    placeholder: "Không có dữ liệu trường học",
    headerVisible: false,
    selectable:true,
    columns: [
        {
            field: "action",
            width: "30",
            formatter: function () {
                return '<div class="cell-center"><i class="fas fa-hand-point-right text-nts-primary"></i> </div>';
            },
            hozAlign: "center"
        },
        {
            title: "Tên trường",
            cssClass: "cell-wrap",
       
            field: "name", // use 'name' since that's your data key
            formatter: function (cell) {
                const data = cell.getData();
                const name = data.name || "";
                const count = data.count || 0;

                // Badge style using your --main-color (rgb(247 109 35))
                const badgeStyle = `
                    background-color: rgb(247, 109, 35);
                    color: white;
                    border-radius: 3px;
                    padding: 0.25em 0.6em;
                    font-size: 0.75rem;
                    font-weight: 600;
                    margin-left: 8px;
                    display: inline-block;
                    min-width: 22px;
                    text-align: center;
                    line-height: 1;
                    vertical-align: middle;
                `;

                return `
                    <span style="  white-space: normal;">${name}</span>
                  
                `;
            },
        },
    ],
    rowClick: function (e, row) {
        // Load students for clicked school row
        var donViId = row.getData().id;
        loadStudentsByDonViId(donViId);
    },
});

$(document).on("keyup", "#timKiemHocSinh", async function (e) {
    
   if (e.keyCode == '13') {
        gridChonHocSinh.setFilter(matchAny, { value: $(this).val() });
    }
});

var gridChonHocSinh =new Tabulator("#gridChonHocSinh", {
        height: "420px",
        layout: "fitColumns",
        placeholder: "Không có học sinh được chọn",
        selectable: false,
        columns: [
            {
                formatter: "rowSelection",
                hozAlign: "center",
                headerSort: false,
                width: 50,
                selectable: true,
                cellClick: function (e, cell) {
                    cell.getRow().toggleSelect();
                },
            },
            {
                title: "Mã học sinh",
                field: "MaDoiTuong",
                width: 120,
            },
            {
                title: "Họ và tên",
                field: "Hovaten",
                width: 150,
                formatter: "textarea",
            },
            {
                title: "Số CMND/CCCD",
                field: "CCCD",
                width: 150,
            },
            {
                title: "Ngày sinh",
                field: "Ngaysinh",
                sorter: "date",
                hozAlign: "center",
                width: 120,
                formatter: function (cell) {
                    const val = cell.getValue();
                    if (!val) return "";
                    const date = new Date(val);
                    return date.toLocaleDateString(); // format as needed
                },
            },
            {
                title: "Giới tính",
                field: "Gioitinh",
                hozAlign: "center",
                width: 100,
            },
            {
                title: "Dân tộc",
                field: "dan_toc.tenDanToc", // Use nested related field here
                width: 120,
            },
            {
                title: "Nơi sinh",
                field: "Noisinh",
                width: 150,
            },
            {
                title: "Lớp học",
                field: "lopHoc", // Note: make sure API returns this field, or remove if not available
                width: 120,
            },
            {
                title: "Địa chỉ",
                field: "DiaChi",
                width: 200,
            },
            {
                title: "Đơn vị học",
                field: "don_vi_hoc.TenDonVi", // nested related field
                width: 200,
                formatter: function (cell) {
                    return cell.getValue() || "";
                },
            },
            {
                title: "Địa bàn tỉnh",
                field: "dia_ban_tinh.TenDiaBan", // nested related field
                minWidth: 200,
                formatter: function (cell) {
                    return cell.getValue() || "";
                },
            },
        ],
});
$("#btnChonDonViCha").on("click", async function () {

    $('#mdChonHocSinh').modal('show')
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.getListTruongHoc,
        { }
    );
 if (!result.Err) {
        gridTruongHoc.setData(result.Result);
    } else {        
        gridTruongHoc.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
});
gridTruongHoc.on("rowClick", async (e, row) => {
    // Load students for clicked school row
    let donViId = row.getData().id;
    selectedTruongHoc = row.getData();

    loadStudentsByDonViId(donViId);
});
function loadStudentsByDonViId(donViId) {
    const url = window.Laravel.local.getListHS(donViId);
    NTS.getAjaxAPIAsync("GET", url, {})
        .then((res) => {
            //Xử lý kết quả trả về
            if (!res.Err) {
                gridChonHocSinh.setData(res.Result);
            } else {
                NTS.loi("Lỗi khi tải danh sách học sinh");
                gridChonHocSinh.clearData();
            }
        })
        .catch(() => {
            NTS.loi("Lỗi khi tải danh sách học sinh");
            gridChonHocSinh.clearData();
        });
}
$(document).on("click", "#btnChonVaDong", async function () {
    let selectedRows = gridChonHocSinh.getSelectedData();
    if (selectedRows.length === 0) {
        NTS.canhbao("Vui lòng chọn ít nhất một học sinh.");
        return;
    }
    $('#HocSinhID').value(selectedRows[0].id)
    $('#TenTruongHoc').value(selectedRows[0].MaDoiTuong+": "+selectedRows[0].Hovaten+" - "+selectedRows[0].don_vi_hoc.TenDonVi)
    $("#mdChonHocSinh").modal("hide");
});
function PheDuyetThongTin(id,SoDonXinCapBanSao,NgayLapConVer,TenDonVi_YeuCau){
     $("#NgayPheDuyet").val(defaultDate )
     $("#DonXinCapBanSaoID").val(id)
    $('#SoDeNghi').text(SoDonXinCapBanSao)
    $('#NgayDeNghi').text(NgayLapConVer)
    $('#DonViDeNghi').text(TenDonVi_YeuCau)
    $("#NhanVienID_PheDuyet").value(NhanVienID);

 $("#NoiDung_PheDuyet").text(`Gửi phiếu đề nghị cấp bản sao văn bằng, chứng chỉ số: ${SoDonXinCapBanSao} ngày lập ${NgayLapConVer} của ${TenDonVi_YeuCau}.`);
   

    $("#ChucVuID_PheDuyet").value(ChucVuID);
    $('#mdQPheDuyet').modal('show')
}
$("#btnBanHanh").on("click", async function () {
     const validate = new NTSValidate("#mdQPheDuyet");
    if (!validate.trim().check()) return false;
    const payload = {
        DonXinCapBanSaoID:  $("#DonXinCapBanSaoID").val(),
        NhanVienID_PheDuyet:$("#NhanVienID_PheDuyet").value(),
        ChucVuID_PheDuyet:$("#ChucVuID_PheDuyet").value(),
        NoiDung_PheDuyet:$("#NoiDung_PheDuyet").text(),
        NgayPheDuyet:$("#NgayPheDuyet").value(),
        DonViID_Nhan:$("#DonViID_Nhan").value(),
        TrangThai:"40", // phê duyệt,
        Loai:"Gui"
    };
    var result = await NTS.getAjaxAPIAsync(
        "post",
        window.location.pathname + "/PheDuyet",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdQPheDuyet").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});

async function ThuHoiThongTin(id,SoDonXinCapBanSao,NgayLapConVer,TenDonVi_YeuCau){
    $("#DonXinCapBanSaoID").val(id)
    $("#NgayThuHoi").val(defaultDate )
    $('#SoDeNghiTH').text(SoDonXinCapBanSao)
    $('#NgayDeNghiTH').text(NgayLapConVer)
    $('#DonViDeNghiTH').text(TenDonVi_YeuCau)
    $("#NhanVienID_ThuHoi").value(NhanVienID);
    $("#ChucVuID_ThuHoi").value(ChucVuID);
    $("#NoiDung_ThuHoi").text(`Thu hồi gửi phiếu đề nghị cấp bản sao phôi văn bằng, chứng chỉ số: ${SoDonXinCapBanSao} ngày lập ${NgayLapConVer} của ${TenDonVi_YeuCau}.`);
    $("#mdQThuHoi").modal("show");
    return false;
}
$("#btnThuHoi").on("click", async function () {
     const validate = new NTSValidate("#mdQThuHoi");
    if (!validate.trim().check()) return false;
    const payload = {
        DonXinCapBanSaoID:  $("#DonXinCapBanSaoID").val(),
        NhanVienID_PheDuyet:$("#NhanVienID_ThuHoi").value(),
        ChucVuID_PheDuyet:$("#ChucVuID_ThuHoi").value(),
        NoiDung_PheDuyet:$("#NoiDung_ThuHoi").text(),
        NgayPheDuyet:$("#NgayThuHoi").value(),
        TrangThai:"52", // phê duyệt
        Loai:"ThuHoi"
    };
    var result = await NTS.getAjaxAPIAsync(
        "post",
        window.location.pathname + "/PheDuyet",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdQThuHoi").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});


async function XemThongTin(id){
 selectedId = id;
$('#mdXemThongTin').modal('show')
 const result = await NTS.getAjaxAPIAsync("GET", window.location.pathname + "/loaddulieusua", { id: id });
    if (!result.Err) {
        const data = result.result[0];


        $('#txtSoPhieu').text(data.SoDonXinCapBanSao || "");
        $('#txtNgayLap').text(data.NgayLapConVer || "");
        $('#txtNguoiDeNghi').text(data.TenNhanVien_Lap || "");
        $('#txtChuVuNguoiDeNghi').text(data.TenChucVu_Lap || "");
        $('#txtDonViDeNghi').text(data.TenDonVi_YeuCau || "");
        $('#txtHocSinh').text(data.HocSinh || "");
        $('#txtNamTotNghiep').text(data.NamTotNghiep || "");
        $('#txtLoaiVanBang').text(data.TenLoaiVanBang || "");
        $('#txtSoHieu').text(data.SoHieu || "");
        $('#txtNgayCap').text(data.NgayCapConVer || "");
        $('#txtLyDo').text(data.LyDo || "");

        $('#txtNgayGui').text(data.NgayGuiConVer || "");
        $('#txtNguoiGui').text(data.TenNhanVien_Gui || "");
        $('#txtChucVuGui').text(data.TenChucVu_Gui || "");

        $('#txtNgayPheDuyet').text(data.NgayPheDuyetConVer || "");
        $('#txtNguoiPheDuyet').text(data.TenNhanVien_PheDuyet || "");
        $('#txtChucVuPheDuyet').text(data.TenChucVu_PheDuyet || "");
        
        $('#txtNgayTuChoi').text(data.SoDonXinCapBanSao || "");
        $('#txtNguoiTuChoi').text(data.NgayTuChoiConVer || "");
        $('#txtChucVuTuChoi').text(data.TenChucVu_TuChoi || "");

        $('#txtNgayThuHoi').text(data.NgayThuHoiPheDuyetConVer || "");
        $('#txtNguoiThuHoi').text(data.TenNhanVien_ThuHoiPheDuyet || "");
        $('#txtChucVuThuHoi').text(data.TenChucVu_ThuHoiPheDuyet || "");

        $('#txtNoiDungGui').text(data.NoiDung_Gui || "");
        $('#txtNoiDungPheDuyet').text(data.NoiDung_PheDuyet || "");
        $('#txtNoiDungTuChoi').text(data.NoiDung_TuChoi || "");
        $('#txtNoiDungThuHoi').text(data.NoiDung_ThuHoiPheDuyet || "");




        $('#txtTrangThai').html(`     <p class="fs-big my-1">Trạng thái:<span style="
                                display: inline-block;
                                font-weight: bold;
                                font-size: 1rem;
                                text-align: center;
                                padding: 2px 0;
                                background-color: `+data.MauSac+`;
                                color: white;
                                border-radius: 6px;
                                width: 14%;
                                margin-left: 4px;
                                ">
                                `+data.TenTrangThai+`
                                </span> <b>   ` );
        //Phê duyệt
        if(data.TrangThai =='32'){
            $('#rowThongTinPheDuyet').show()
            $('#rowTuChoi').hide()
            $('#rowThuHoi').hide()
        }
        //Từ chối
        if(data.TrangThai =='42'){
            $('#rowTuChoi').show()
            $('#rowThuHoi').hide()
            $('#rowThongTinPheDuyet').hide()
        }

        //Thu hồi phê duyệt
        if(data.TrangThai =='33'){
            $('#rowThuHoi').show()
            $('#rowTuChoi').hide()
            $('#rowThongTinPheDuyet').hide()
        }
        $('#txtDinhKem').html(`<p class="fs-big my-1">
                            Đính kèm: 
                            <a href="#" data="" onclick="XemDinhKem_us('`+data.DinhKem+`')">
                                <i class="fa fa-paperclip me-1"></i> Xem đính kèm
                            </a>
                            </p> `)
   

        tempthem = "sua";
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}