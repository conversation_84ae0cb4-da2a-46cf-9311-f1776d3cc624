const btnThaoTac = function (cell) {
    let ID = cell.getData().id;
    return `<div class="show-or-hide"><a class='text-danger btnXoaGrid1' title="Xoá" data='${ID}'><i class='fa fa-trash-o'></i></a></div>`;
};

const fmTrangThai = function (cell) {
    return formaterTrangThai(cell.getValue(), cell.getData().id);
};

//Flags
var viewGridCard = true;
var thaoTac = "them";
var selectedId;
// var selectedHocSinh;
// var selectedTruongHoc;
var selectedBangDiem;
//Grids
var table;
// var gridSelectDonVi;
var tabGridHocSinh;
var tabGridHocSinhChiTiet;
// var gridTruongHoc;
// var gridChonHocSinh;

var modeThemHS = 0;
const enumModeThemHS = Object.freeze({
    Them1: 0,
    ThemNhieu: 1,
});
var svgMainGrid;

NTS.getAjaxAPIAsync("GET", Laravel.getSvg, {})
    .then((doc) => {
        const serializer = new XMLSerializer();
        svgMainGrid = serializer.serializeToString(doc);
    })
    .catch((err) => console.error("Failed to load SVG:", err));

/**
 * Cấu hình chung cho combo box.
 *
 * @function commonComboConfig
 * @param {number} [numCol=2] - Số cột trong combo box. Mặc định là 2.
 * @returns {Object} Cấu hình combo box.
 * @property {number} columns - Số cột trong combo box.
 * @property {number} indexValue - Chỉ số giá trị trong combo box.
 * @property {number} indexText - Chỉ số văn bản trong combo box.
 * @property {number} [indexText1] - Chỉ số văn bản thứ hai (chỉ có khi numCol là 2).
 * @property {string} textShowTatCa - Văn bản hiển thị cho tùy chọn "Tất cả".
 * @property {boolean} showTatCa - Xác định có hiển thị tùy chọn "Tất cả" hay không.
 */
const commonComboConfig = function (numCol = 2) {
    if ((numCol = 2))
        return {
            columns: 2, // indexValue=0, indexText=1
            indexValue: 0,
            indexText: 1,
            indexText1: 2,
            textShowTatCa: "-Chọn-",
            showTatCa: true,
        };
    else {
        return {
            columns: 1, // indexValue=0, indexText=1
            indexValue: 0,
            indexText: 1,
            textShowTatCa: "-Chọn-",
            showTatCa: true,
        };
    }
};

const staticCols = [
    {
        title: "<i class='fa fa-ellipsis-h'></i>",
        formatter: btnThaoTac,
        width: 40,
        hozAlign: "center",
        headerHozAlign: "middle",
        headerSort: false,
        ...commonColumnConfig,
    },
    {
        title: "Số báo danh",
        field: "SBD",
        hozAlign: "left",
        ...commonColumnConfig,
        editor: "textarea",
        editorParams: {
            elementAttributes: {
                maxlength: "15", //set the maximum character length of the textarea element to 10 characters
            },
        },
    },
    {
        title: "Họ và tên",
        field: "DoiTuong.Hovaten",
        hozAlign: "left",
        ...commonColumnConfig,
        formatter: "textarea",
    },
    {
        title: "Số CMND/CCCD",
        field: "DoiTuong.CCCD",
        hozAlign: "left",
        ...commonColumnConfig,
    },
    {
        title: "Ngày sinh",
        field: "DoiTuong.Ngaysinh",
        hozAlign: "center",
        ...commonColumnConfig,
        formatter: "textarea",
    },
    {
        title: "Giới tính",
        field: "DoiTuong.TenGioiTinh",
        hozAlign: "left",
        ...commonColumnConfig,
    },
    {
        title: "Nơi sinh",
        field: "DoiTuong.Noisinh",
        hozAlign: "left",
        ...commonColumnConfig,
    },
    {
        title: "Học sinh trường",
        field: "DoiTuong.ten_truong",
        hozAlign: "left",
        width: 150,
        ...commonColumnConfig,
        formatter: "textarea",
    },
    {
        title: "Phòng thi",
        field: "PhongThi",
        hozAlign: "left",
        width: 150,
        ...commonColumnConfig,
        formatter: "textarea",
        editor: "textarea",
        editorParams: {
            elementAttributes: {
                maxlength: "255", //set the maximum character length of the textarea element to 10 characters
            },
        },
    },
    {
        title: "Địa điểm thi",
        field: "DiaDiemThi",
        hozAlign: "left",
        width: 150,
        ...commonColumnConfig,
        formatter: "textarea",
        editor: "textarea",
        editorParams: {
            elementAttributes: {
                maxlength: "255", //set the maximum character length of the textarea element to 10 characters
            },
        },
    },
    // placeholder for Điểm môn thi
    {
        title: "Điểm môn thi",
        columns: [
            {
                field: "_dummy_",
                title: "",
                width: 100,
                visible: true,
                resizable: true,
            },
        ],
        hozAlign: "left",
        visible: true,
    },
    // placeholder for Điểm cuối cấp
    {
        title: "Điểm cuối cấp",
        columns: [
            {
                field: "_dummy_",
                title: "",
                width: 100,
                visible: true,
                resizable: true,
            },
        ],
        hozAlign: "left",
        visible: true,
    },
    {
        title: "Điểm ưu tiên",
        field: "DiemUT",
        hozAlign: "left",
        ...commonColumnConfig,
        formatter: "number",
        editor: "number",
        editorParams: {
            step: 0.5, // allows two decimal places
        },

        minWidth: 100,
    },
    {
        title: "Ghi chú",
        field: "GhiChu",
        hozAlign: "left",
        ...commonColumnConfig,
        editor: "textarea",
        editorParams: {
            elementAttributes: {
                maxlength: "255", //set the maximum character length of the textarea element to 10 characters
            },
        },

        minWidth: 250,
    },
];

//#region Render card
function handleFileLinks(event) {
    event.preventDefault();

    const link = event.currentTarget;
    const filesString = link.getAttribute("data-files") || "";

    // Split file string by pipe '|'
    const filesArray = filesString.split("|").filter((f) => f.trim() !== "");

    // Populate modal with these files
    populateMdXemDinhKem(filesArray);

    // Show modal
    hienMdXemDinhKem();
}

function buildTrangThaiBadge(mauNen, label) {
    return `
    <span class="badge m-2 py-2 px-4"
        style="font-weight: 600; font-size: 12px; cursor: default; user-select: none;
        background-color: ${mauNen ?? "#f76707"};">
        ${label ?? "Chưa phê duyệt"}
    </span>`;
}
/**
 * Hàm `htmlDuLieu` tạo HTML hiển thị thông tin bảng điểm tốt nghiệp dựa trên dữ liệu đầu vào.
 *
 * @param {Object} cell - Đối tượng cell từ Tabulator, chứa thông tin của một hàng dữ liệu.
 * @param {Object} formatterParams - Các tham số định dạng (không sử dụng trong hàm này).
 * @param {Function} onRendered - Hàm callback được gọi sau khi nội dung được render (không sử dụng trong hàm này).
 *
 * @returns {string} - Chuỗi HTML hiển thị thông tin bảng điểm tốt nghiệp, bao gồm các thao tác như xem chi tiết, chỉnh sửa, Phê duyệt, thu hồi, xóa, và các thông tin liên quan.
 *
 * @description
 * - Hàm kiểm tra trạng thái Phê duyệt của bảng điểm (`tinhTrangBanHanh`) để hiển thị các thao tác phù hợp.
 * - Hiển thị thông tin như số bảng điểm, ngày ký, người ký, chức vụ, cơ quan Phê duyệt, kỳ thi, cấp học, số học sinh công nhận, file đính kèm, ghi chú, ngày Phê duyệt, người Phê duyệt, chức vụ Phê duyệt, và nội dung Phê duyệt.
 * - Định dạng ngày tháng theo kiểu `dd/mm/yyyy`.
 * - Nếu có file đính kèm, hiển thị liên kết để xem file; nếu không, hiển thị thông báo "Không có file đính kèm".
 * - Hiển thị trạng thái bảng điểm (Đã Phê duyệt hoặc Chưa Phê duyệt) dưới dạng badge.
 */
function htmlDuLieu(cell, formatterParams, onRendered) {
    const data = cell.getData();

    // Determine issued state
    const daBanHanh = data.TrangThai === "32";

    // Action strings
    let chuoiThaoTacBanHanh = "";
    let chuoiThaoTacXoa = "";
    let chuoiThaoTacThuHoi = "";
    let chuoiThaoTacSua = "";
    let chuoiThaoTacOCR = "";
    let dataTrangThai = data.trang_thai_label;
    let badgeTrangThai = buildTrangThaiBadge(
        dataTrangThai?.MauSac,
        dataTrangThai?.TenTrangThai
    );
    // If NOT issued, show Phê duyệt & Xóa
    if (!daBanHanh) {
        chuoiThaoTacBanHanh = `
        <a class="dropdown-item textsize-item btnBanHanhQD" href="javascript:void(0);" data-id="${data.id}" id="btnBanHanhQD">
            ${icons["giao"]}&ensp; Phê duyệt bảng điểm
        </a>`;
        chuoiThaoTacXoa = `
        <a class="dropdown-item textsize-item btnXoaQD" href="javascript:void(0);" data-id="${data.id}" id="btnXoaQD">
            ${icons["xoa"]}&ensp; Xóa bảng điểm
        </a>`;
        chuoiThaoTacSua = `
        <a class="dropdown-item textsize-item btnChinhSuaQD" href="javascript:void(0);" data-id="${data.id}" id="btnChinhSuaQD">
            ${icons["sua"]}&ensp; Chỉnh sửa bảng điểm
        </a>`;
        chuoiThaoTacOCR = `
        <a class="dropdown-item textsize-item btnOCR" href="javascript:void(0);" data-id="${data.id}">
            ${icons["ocr"]}&ensp; Trích xuất bảng điểm tự động (OCR)
        </a>`;
    } else {
        // If issued, show Thu hồi Phê duyệt, hide Xóa & Phê duyệt
        chuoiThaoTacThuHoi = `
        <a class="dropdown-item textsize-item btnThuHoiBanHanhQD" href="javascript:void(0);" data-id="${data.id}" id="btnThuHoiBanHanhQD">
            ${icons["thuhoi"]}&ensp; Thu hồi phê duyệt
        </a>`;
    }

    const chuoiThaoTac = `
    <a class="dropdown-item textsize-item btnXemChiTietQD" href="javascript:void(0);" data-id="${
        data.id
    }">
        ${icons["xem"]}&ensp; Xem chi tiết bảng điểm
    </a>
    ${chuoiThaoTacSua || ""}
    ${chuoiThaoTacBanHanh || ""}
    ${chuoiThaoTacThuHoi || ""}
    ${chuoiThaoTacOCR || ""}
    ${chuoiThaoTacXoa || ""}
`;

    // Format NgayKy and NgayBanHanh to dd/mm/yyyy
    function formatDate(dateStr) {
        return dateStr;
    }
    const fileLinks =
        Array.isArray(data.FileDinhKem) && data.FileDinhKem.length > 0
            ? data.FileDinhKem.join("|") // join files with a separator like pipe |
            : "";
    let html = `
    <div class="list-item col-md-12">
        <div class="card card-luoi shadow-sm mb-3">
            <div class="card-body profile-user-box mb-2" style="padding-bottom:0px !important; padding-top:6px;">
                <div class="row">
                    <div class="col-12 col-xs-6 col-md-2 text-center" style="display: flex
                        ; flex-direction: column; width: 13.5%;
                        justify-content: space-around;align-items: center;">
                        <div class="row mt-2">
                            ${svgMainGrid}
                        </div>
                        ${badgeTrangThai}
                    </div>

                    <div class="col-md-10 px-2" style="width: 86.5%;">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <p class="fs-big mb-0">Số bảng điểm: <b>${
                                    data.SoQuyetDinh || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3">
                                <p class="fs-big mb-0">Ngày ký: <b>${
                                    data.NgayKy || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3 px-0">
                                <p class="fs-big mb-0">Người ký: <b>${
                                    data.NguoiKy || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3 d-flex justify-content-between align-items-center px-0">
                                <p class="fs-big mb-2">Chức vụ: <b>${
                                    data.chuc_vu?.tenChucVu || ""
                                }</b></p>
                                <div class="dropdown text-end me-2 ">
                                    <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-lg-end w-auto dropdown-thao-tac" style="margin-left:-175px; inset:0 auto auto auto; position:fixed !important;">
                                    ${chuoiThaoTac}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr style="border-top: 1px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />
                        <div class="row align-items-center">
                        <div class="row">
                            <div class="col-12">
                            <p class="fs-big mb-2">Trích yếu/nội dung: <strong>${
                                data.TrichYeu || ""
                            }</strong></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                            <p class="fs-big mb-2">Cơ quan Phê duyệt: <b>${
                                data.CoQuanBanHanh || ""
                            }</b></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <p class="fs-big mb-2">Kỳ thi: <b>${
                                    data.ky_thi?.TenKyThi || ""
                                }</b></p>
                            </div>
                             <div class="col-6">
                                <p class="fs-big mb-2">Cấp học: <b>${
                                    data.ten_cap_hoc || ""
                                }</b></p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <p class="fs-big mb-0">Đính kèm:  ${
                                    fileLinks
                                        ? `<a href="#" class="file-links" data-files="${fileLinks}" onclick="handleFileLinks(event)"><i class="fa fa-paperclip"></i>&ensp;Xem đính kèm</a>`
                                        : "Không có file đính kèm"
                                }</p>
                            </div>
                        </div>
                        </div>

                        <hr style="border-top: 1px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />
                        <div class="row">
                            <div class="col-md-3">
                                <p class="fs-big mb-2">Ngày phê duyệt: <b>${
                                    data.NgayBanHanh || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3">
                                <p class="fs-big mb-2">Người phê duyệt: <b>${
                                    data.nhan_vien_ban_hanh?.tenNhanVien || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3 px-0">
                                <p class="fs-big mb-2">Chức vụ phê duyệt: <b>${
                                    data.chuc_vu_ban_hanh?.tenChucVu || ""
                                }</b></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <p class="fs-big mb-2">Nội dung phê duyệt: <b>${
                                    data.NoiDungBH ?? ""
                                }</b></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    `;

    return html;
}
//#endregion

/**
 * Dùng để chuyển từ view lưới sang view card
 *
 * Hàm này kiểm tra xem cột được chỉ định có phải là cột duy nhất đang hiển thị hay không.
 * Nếu đúng, nó sẽ hiển thị tất cả các cột. Nếu không, nó sẽ ẩn tất cả các cột ngoại trừ cột được chỉ định.
 *
 * @param {Object} table - Đối tượng bảng (table) cần thao tác.
 * @param {string} fieldName - Tên trường của cột cần hiển thị.
 */
function showCard(tableId, cardField, show) {
    const table = Tabulator.findTable(`#${tableId}`)[0];
    if (!table) {
        console.error(`Tabulator table with id '${tableId}' not found`);
        return;
    }

    const columns = table.getColumns();

    if (show) {
        // Hide all except the cardField column
        columns.forEach((col) => {
            if (col.getField() === cardField) {
                col.show();
            } else {
                col.hide();
            }
        });
    } else {
        // Show all columns again
        columns.forEach((col) => col.show());
    }

    table.redraw(true);
}
function resetMdThemMoi() {
    showStep(1);
    resetFileInput();
}
function prepareThemMoiData() {
    return Promise.all([LoadComboMdThemMoi()]);
}
$(document).on("click", ".file-links", (event) => handleFileLinks(event));

document.addEventListener("DOMContentLoaded", function () {
    //#region Sự kiện
    document
        .getElementById("btnThemMoi")
        .addEventListener("click", function () {
            if (!QuyenThem()) return;
            $("#formThongTinChung")[0].reset();

            //Mở modal thêm mới
            selectedId = null;
            resetMdThemMoi();
            thaoTac = "them";

            $("#lblTieuDeMultiStep").text(
                "THÊM MỚI THÔNG TIN BẢNG ĐIỂM HỌC SINH"
            );
            prepareThemMoiData()
                .then(() => {
                    //Chờ load xong  grid và combo mới hiển thị
                    document.getElementById("mdThemMoi").style.display =
                        "block";
                })
                .catch((err) => {
                    console.error("Error preparing data:", err);
                });
        });

    document
        .querySelector("#mdThemMoi .btn-close")
        .addEventListener("click", function () {
            document.getElementById("mdThemMoi").style.display = "none";
        });
    document
        .querySelector("#mdThemMoi #btnKetThuc")
        .addEventListener("click", function () {
            document.getElementById("mdThemMoi").style.display = "none";
        });

    $(document).on("click", ".btnChinhSuaQD", async function () {
        if (!QuyenSua()) {
            return;
        }
        let id = $(this).data("id"); //Mở modal chỉnh sửa
        resetMdThemMoi();
        selectedId = id;

        prepareThemMoiData()
            .then(() => {
                //Chờ load xong grid và combo mới hiển thị

                SuaDuLieu(id);
                $("#lblTieuDeMultiStep").text(
                    "SỬA THÔNG TIN BẢNG ĐIỂM HỌC SINH"
                );
                document.getElementById("mdThemMoi").style.display = "block";
            })
            .catch((err) => {
                console.error("Error preparing data:", err);
            });

        thaoTac = "sua";
    });

    $(document).on("click", ".btnXemChiTietQD", async function () {
        document.getElementById("mdThemMoi").style.display = "none";
    });

    $(document).on("click", ".btnXoaQD", async function () {
        if (!QuyenXoa()) return;
        const id = $(this).data("id"); // gets data-id attribute properly
        XoaDuLieu(id);
    });
    //#region Xoá dữ liệu
    async function XoaDuLieu(ID) {
        var result_ktxoa = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.layouts.KiemTraXoa,
            {
                ma: ID,
                model: "QuanLy\\BangDiem",
            }
        );

        if (!result_ktxoa.Err) {
            CanhBaoXoa(async () => {
                var result = await NTS.getAjaxAPIAsync(
                    "DELETE",
                    window.location.pathname + "/xoa",
                    { ma: ID }
                );
                if (!result.Err) {
                    // grid1.setData();
                    SetMainGridData();
                    NTS.thanhcong(result.Msg);
                } else {
                    result.canhbao
                        ? NTS.canhbao(result.Msg)
                        : NTS.loi(result.Msg);
                }
            });
            return false;
        } else {
            NTS.canhbao(result_ktxoa.Msg);
            return false;
        }
    }

    $(document).on("click", "#btnTiepTuc", async function () {
        const validate = new NTSValidate("#mdThemMoi");
        if (!validate.trim().check()) return false;
        $("button.nav-link#tab-ds-tab").click();

        const soVanBan = $("#SoVanBan").val().trim();
        if (!soVanBan) {
            NTS.canhbao("Vui lòng nhập Số văn bản.");
            $("#SoBangDiem").focus();
            return false;
        }

        // Collect basic input/select/textarea fields inside the fieldset
        const dinhKemQDValue = $("#dinhKemQD").val(); // get the string value
        const filesArray = dinhKemQDValue ? dinhKemQDValue.split("|") : [];
        const newKyThi = $("#KyThi").val();

        const kyThiChanged =
            selectedBangDiem && selectedBangDiem.KyThiID !== newKyThi;

        const dataThongTinChung = {
            SoVanBan: $("#SoVanBan").val(),
            NguoiKy: $("#NguoiKy").val(),
            ChucVu: $("#ChucVu").val(),
            NgayKy: $("#NgayKy").val(),
            CoQuanBanHanh: $("#CoQuanBanHanh").val(),
            KyThi: $("#KyThi").val(),
            HinhThucDaoTao: $("#HinhThucDaoTao").val(),
            HoiDong: $("#HoiDong").val(),
            NamHoc: $("#NamHoc").val(),
            TrichYeu: $("#TrichYeu").val(),
            DinhKemQD: filesArray,
            kyThiChanged: kyThiChanged, // ← new flag
        };

        let method = "POST";

        if (typeof selectedId !== "undefined" && selectedId) {
            method = "PUT";
            dataThongTinChung.BangDiemID = selectedId; // match your Laravel controller expectation
        }

        const doSave = async () => {
            let result = await NTS.getAjaxAPIAsync(
                method,
                window.Laravel.luuThongTin,
                dataThongTinChung
            );
            if (!result.Err) {
                NTS.thanhcong(result.Msg);

                if (method === "POST") {
                    selectedId = result.Data.id;
                }
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
                return false;
            }
            if (method === "POST") selectedId = result.Data.id;

            NTS.getAjaxAPIAsync("GET", Laravel.loadDuLieuSua, {
                id: selectedId,
            }).then((result) => {
                if (result.Err) {
                    NTS.loi("Lỗi khi tải dữ liệu: " + (result.Msg || ""));
                    return;
                }

                const data = result.Result;
                selectedBangDiem = data;

                // Format NgayBanHanh to dd/mm/yyyy if needed

                const soBangDiem = data.SoQuyetDinh || "";

                const fileLinks =
                    Array.isArray(data.FileDinhKem) &&
                    data.FileDinhKem.length > 0
                        ? data.FileDinhKem.join("|") // join files with a separator like pipe |
                        : "";

                let message = `
           <div class="row">
                <!-- icon + badge -->
                <div class="col-6 col-md-2 text-center">
                    <div class="row mt-2">
                    ${svgMainGrid}
                    </div>
                </div>

                <!-- main info -->
                <div class="col-6 col-md-10 px-2">
                    <!-- first line -->
                    <div class="row align-items-center mb-2">
                    <div class="col-6 col-md-3">
                        <p class="fs-big mb-2">Số bảng điểm: <b>${
                            soBangDiem || ""
                        }</b></p>
                    </div>
                    <div class="col-6 col-md-3">
                        <p class="fs-big mb-2">Ngày ký: <b>${
                            data.NgayKy || ""
                        }</b></p>
                    </div>
                    <div class="col-6 col-md-3">
                        <p class="fs-big mb-2">Người ký: <b>${
                            data.NguoiKy || ""
                        }</b></p>
                    </div>
                    <div class="col-6 col-md-3">
                        <p class="fs-big mb-2">Chức vụ: <b>${
                            data.chuc_vu?.tenChucVu || ""
                        }</b></p>
                    </div>
                    </div>

                    <hr style="border-top: 1px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />

                    <!-- trích yếu -->
                    <div class="row mb-2">
                    <div class="col-12">
                        <p class="fs-big mb-2">
                        Trích yếu/nội dung:
                        <strong>${data.TrichYeu || ""}</strong>
                        </p>
                    </div>
                    </div>

                    <!-- second line -->
                    <div class="row mb-2">
                    <div class="col-12 col-md-12">
                        <p class="fs-big mb-2">Cơ quan Phê duyệt: <b>${
                            data.CoQuanBanHanh || ""
                        }</b></p>
                    </div>

                    </div>

                    <!-- third line -->
                    <div class="row mb-2">
                    <div class="col-6 ">
                        <p class="fs-big mb-2">Kỳ thi: <b>${
                            data.ky_thi?.TenKyThi || ""
                        }</b></p>
                    </div>
                    <div class="col-6">
                        <p class="fs-big mb-2">Cấp học: <b>${
                            data.ten_cap_hoc || ""
                        }</b></p>
                    </div>
                    <div class="col-6 col-md-3">

                    </div>
                    </div>

                    <!-- ghi chú -->
                    <div class="row">
                    <div class="col-12">
                       <p class="fs-big mb-2">
                        Đính kèm:
                        ${
                            fileLinks
                                ? `<a href="#" class="file-links" data-files="${fileLinks}" onclick="handleFileLinks(event)"><i class="fa fa-paperclip"></i>&ensp; Xem đính kèm</a>`
                                : "Không có file đính kèm"
                        }
                        </p>
                    </div>
                    </div>
                </div>
            </div>
            `;
                // Now you can use `message` where needed, e.g.
                $("#contentCapNhatVB").html(message);
            });
            showStep(2);
            tabGridHocSinh.setData(Laravel.getListHSByQD(result.Data.id));
            setTimeout(() => {
                tabGridHocSinh.redraw(true);
            }, "300");
            // grid1.setData();
            SetMainGridData();
        };

        if (
            method === "PUT" &&
            selectedBangDiem?.KyThiID !== dataThongTinChung.KyThi
        ) {
            $.confirm({
                title: '<span style="font-size:18px;" class="text-danger">Cảnh báo!</span>',
                content: "Chỉnh sửa kỳ thi sẽ cập nhật điểm của học sinh",
                type: "red",
                icon: "fa fa-exclamation-triangle",
                theme: "material",
                buttons: {
                    cancel: {
                        text: '<i class="fa fa-close"></i> Không',
                        btnClass: "btn-danger nts-color-dongCanhBao",
                        keys: ["enter", "esc", "space"],
                        action: function () {
                            // reset the select back to original KyThi
                            $("#KyThi").value(selectedBangDiem.KyThiID);
                        },
                    },
                    confirm: {
                        text: '<i class="fa fa-check"></i> Có',
                        btnClass: "btn-primary nts-color-CoCanhBao",
                        keys: ["shift"],
                        action: doSave,
                    },
                },
            });
        } else {
            // either create or KyThi unchanged
            await doSave();
        }
    });

    //#region Chọn học sinh
    $(document).on("click", "#btnChonVaDong", async function () {
        // Get selected rows data
        let selectedRows = gridChonHocSinh.getSelectedData();

        if (selectedRows.length === 0) {
            NTS.canhbao("Vui lòng chọn ít nhất một học sinh.");
            return;
        }

        switch (modeThemHS) {
            case enumModeThemHS.ThemNhieu: // Thêm nhiều (batch save)
                {
                    let allSelectedHocSinh =
                        Object.values(selectedHocSinhMap).flat();

                    let savePromises = allSelectedHocSinh.map((hocSinhID) => {
                        let data = {
                            BangDiemId: selectedId,
                            hocSinhID: hocSinhID,
                            KyThiID: selectedBangDiem?.ky_thi?.id || null,
                        };

                        return NTS.getAjaxAPIAsync(
                            "POST",
                            Laravel.luuThongTinHocSinh,
                            data
                        )
                            .then((response) => {
                                if (response.Err) {
                                    return Promise.reject({
                                        message:
                                            response.Msg ||
                                            "Lỗi khi lưu học sinh",
                                        response,
                                        hocSinh: hocSinhID,
                                    });
                                }
                                return { success: true, hocSinh: hocSinhID };
                            })
                            .catch((error) => ({
                                success: false,
                                hocSinh: hocSinhID,
                                error,
                            }));
                    });

                    Promise.all(savePromises).then((results) => {
                        const successList = results
                            .filter((r) => r.success)
                            .map((r) => r.hocSinh);
                        const failList = results.filter((r) => !r.success);

                        if (successList.length > 0) {
                            tabGridHocSinh.setData(
                                Laravel.getListHSByQD(selectedId)
                            );
                            setTimeout(() => {
                                tabGridHocSinh.redraw(true);
                            }, 300);
                        }

                        $("#mdThemMoiHocSinh").modal("hide");

                        if (failList.length === 0) {
                            NTS.thanhcong(
                                `Đã lưu thành công ${successList.length} học sinh.`
                            );
                        } else if (successList.length === 0) {
                            NTS.loi(
                                `Lưu thất bại toàn bộ ${failList.length} học sinh: ` +
                                    failList.map((f) => f.hocSinh).join(", ")
                            );
                        } else {
                            NTS.canhbao(
                                `Đã lưu thành công ${successList.length} học sinh.\n` +
                                    `Có ${failList.length} học sinh lưu thất bại: ` +
                                    failList.map((f) => f.hocSinh).join(", ")
                            );
                        }
                    });
                }
                break;
        }

        $("#mdChonHocSinh").modal("hide");
    });
    //#endregion
    //#endregion

    //#region Main grid QĐ
    table = new Tabulator("#Grid1", {
        ajaxURL: Laravel.getListQD, // Replace with your actual data URL
        ...commonTabulatorConfig,
        height: "80vh",
        headerVisible: false,
        paginationMode: "remote",
        columns: [
            {
                title: "Thông tin",
                field: "BangDiemID",
                formatter: htmlDuLieu,
                visible: true,
            },
            {
                title: "Số bảng điểm",
                field: "SoBangDiem",
                ...commonColumnConfig,
            },
            { title: "Người Ký", field: "NguoiKy", ...commonColumnConfig },
            {
                title: "Cấp Học",
                field: "CapHocID",
                hozAlign: "center",
                ...commonColumnConfig,
            },
            {
                title: "Ngày Ký",
                field: "NgayKy",
                hozAlign: "center",
                sorter: "date",
                ...commonColumnConfig,
            },
            {
                title: "Học Sinh TN Count",
                field: "HocSinhTN",
                hozAlign: "center",
                formatter: function (cell) {
                    let val = cell.getValue();
                    return Array.isArray(val) ? val.length : 0;
                },
                ...commonColumnConfig,
            },
            { title: "Ghi Chú", field: "GhiChu", ...commonColumnConfig },
            {
                title: "Trạng Thái",
                field: "TrangThai",
                hozAlign: "center",
                formatter: fmTrangThai,
                ...commonColumnConfig,
            },
        ],
    });
    table.on("dataLoaded", function (data) {
        showCard("Grid1", "BangDiemID", viewGridCard);
    });
    //#endregion

    //#region Grid HS

    // Khởi tạo bảng Tabulator cho tabGridHocSinh (danh sách học sinh trong bảng điểm)
    tabGridHocSinh = new Tabulator("#tabGridHocSinh", {
        ajaxParams: {},

        ...tabuLatorAjaxOptions,

        layout: "fitDataStretch",
        height: "70vh",
        maxHeight: "80vh",
        minWidth: "100%",
        placeholder: "Không có dữ liệu",
        // Sử dụng các cột tĩnh ban đầu, sẽ cập nhật động sau
        columns: staticCols,
        resizable: true,
        // Xử lý dữ liệu trả về từ AJAX để cấu hình lại các cột động theo môn thi
        ajaxResponse: (url, params, response) => {
            const rows = response.Result || [];

            // Lấy danh sách môn thi từ dòng đầu tiên (giả định tất cả các dòng đều có cùng cấu trúc môn thi)
            const subjects = rows[0]?.mon_thi_list || [];

            // Tạo mảng cột con cho "Điểm môn thi" dựa trên danh sách môn
            const diemMonThiCols = subjects.length
                ? subjects.map((s) => ({
                      title: s.tenMonHoc,
                      field: `DiemMonThi.${s.id}`,
                      headerHozAlign: "center",
                      editor: "number",
                      editorParams: {
                          step: 0.5, // allows two decimal places
                      },
                      validator: [
                          { type: "min", parameters: 0 },
                          { type: "max", parameters: s.heSo }, // hard check
                      ],

                      minWidth: 100,
                      headerSort: false,
                      resizable: true,
                  }))
                : [
                      {
                          field: "_dummy_",
                          title: "",
                          width: 100,
                          visible: true,
                          resizable: true,
                      },
                  ];

            // Tạo mảng cột con cho "Điểm cuối cấp" dựa trên danh sách môn
            const diemCuoiCapCols = subjects.length
                ? subjects.map((s) => ({
                      title: s.tenMonHoc,
                      field: `DiemCuoiCap.${s.id}`,
                      headerHozAlign: "center",
                      editor: "number",
                      editorParams: {
                          step: 0.5, // allows two decimal places
                      },
                      validator: [
                          { type: "min", parameters: 0 },
                          { type: "max", parameters: s.heSo }, // hard check
                      ],
                      resizable: true,

                      minWidth: 100,
                      headerSort: false,
                  }))
                : [
                      {
                          field: "_dummy_",
                          title: "",
                          width: 100,

                          visible: true,
                          resizable: true,
                      },
                  ];

            // Ghép lại mảng cột hoàn chỉnh: các cột tĩnh, 2 nhóm cột động, rồi các cột tĩnh còn lại
            const newCols = [
                ...staticCols.slice(0, 10), // các cột tĩnh trước nhóm điểm
                {
                    title: "Điểm môn thi",
                    field: "DiemMonThi",
                    columns: diemMonThiCols,
                    headerHozAlign: "center",
                },
                {
                    title: "Điểm cuối cấp",
                    field: "DiemCuoiCap",
                    columns: diemCuoiCapCols,
                    headerHozAlign: "center",
                },
                ...staticCols.slice(12), // cột "Ghi chú" và các cột sau (nếu có)
            ];

            // Gán lại cấu hình cột cho bảng
            tabGridHocSinh.setColumns(newCols);
            setTimeout(() => {
                tabGridHocSinh.redraw(true);
            }, 100);
            // Trả về dữ liệu để Tabulator render
            return rows;
        },
    });

    tabGridHocSinh.on("validationFailed", function (cell, value, validators) {
        const maxRule = validators.find((v) => v.type === "max");
        const maxVal = maxRule.parameters;

        NTS.canhbao([`Điểm đã nhập bị vượt quá hệ số điểm ${maxVal}!`]);
    });

    tabGridHocSinh.on("cellEdited", function (cell) {
        let rowData = cell.getRow().getData();

        let data = {
            BangDiemCTID: rowData.id,
            BangDiemId: rowData.BangDiemId,
            hocSinhID: rowData.HocSinhID,
            SBD: rowData.SBD,
            KyThiID: null, //Không cập nhật kỳ thi
            DiemMonThi: rowData.DiemMonThi,
            DiemCuoiCap: rowData.DiemCuoiCap,
            PhongThi: rowData.PhongThi,
            DiaDiemThi: rowData.DiaDiemThi,
            DiemUT: rowData.DiemUT,
            GhiChu: rowData.GhiChu,
        };
        // Lưu thông tin điểm và ghi chú
        NTS.getAjaxAPIAsync("PUT", Laravel.luuThongTinHocSinh, data)
            .then((payload) => {
                if (!payload.Err) {
                    cell.getElement().classList.add("update-ok");

                    // cập nhật luôn row với giá trị trả về
                    const updated = payload.Result;
                    cell.getRow().update({
                        DiemMonThi: updated.DiemMonThi,
                        DiemCuoiCap: updated.DiemCuoiCap,
                        GhiChu: updated.GhiChu,
                    });

                    NTS.thanhcong(payload.Msg);
                } else {
                    cell.getElement().classList.add("update-failed");
                    // dùng NTS.canhbao để show msg trả về
                    NTS.canhbao([payload.Msg]);
                }
            })
            .catch((err) => console.error("AJAX error", err));
    });

    $(document).on("click", "#tabGridHocSinh  .btnSuaGrid1", async function () {
        await LoadComboMdThemMoiHocSinh();
        SuaDuLieu_HS($(this).attr("data"));
    });

    $(document).on("click", "#tabGridHocSinh .btnXoaGrid1", function () {
        if (!QuyenXoa()) return;

        XoaDuLieu_HS($(this).attr("data"));
    });

    //#region Thêm mới học sinh
    //1 học sinh
    $("#btnThemMoiHS").on("click", function () {
        LoadComboMdThemMoiHocSinh();
        $("#mdThemMoiHocSinh").modal("show");
        thaotacHocSinhTN = "them";
        modeThemHS = enumModeThemHS.Them1;
        $("#tenTruong").val("");

        $("#mdThemMoiHocSinh h5.modal-title").text("Thêm mới học sinh");
        $("#selectedOption").html("Chọn học sinh");
    });
    //Nhiều học sinh
    $("#btnNhapNhieuHS").on("click", function () {
        $("#mdChonHocSinh").modal("show");
        thaotacHocSinhTN = "them";
        modeThemHS = enumModeThemHS.ThemNhieu;

        loadTruongHoc(selectedId);
        if (gridChonHocSinh && typeof gridChonHocSinh.destroy === "function") {
            gridChonHocSinh.destroy();
        }

        gridChonHocSinh = initGridChonHocSinh(modeThemHS);

        gridChonHocSinh.on("rowSelected", (row) => {
            const idHS = row.getData().id;
            const dv = selectedTruongHoc.id;
            selectedHocSinhMap[dv] = selectedHocSinhMap[dv] || [];
            if (!selectedHocSinhMap[dv].includes(idHS)) {
                selectedHocSinhMap[dv].push(idHS);
            }
        });

        gridChonHocSinh.on("rowDeselected", (row) => {
            const idHS = row.getData().id;
            const dv = selectedTruongHoc.id;
            selectedHocSinhMap[dv] = (selectedHocSinhMap[dv] || []).filter(
                (i) => i !== idHS
            );
        });
    });
    //#endregion
    //#endregion
});
var thaotacHocSinhTN;

function SuaDuLieu_HS(ID) {
    thaotacHocSinhTN = "sua";

    $("#mdThemMoiHocSinh h5.modal-title").text("Sửa học sinh");

    // Construct URL by replacing placeholder or just append ID
    const url = Laravel.getHSByID.replace("ID", ID);
    NTS.getAjaxAPIAsync("GET", url, {})
        .then((response) => {
            if (response.Err) {
                NTS.loi(
                    "Lỗi khi tải dữ liệu học sinh: " + (response.Msg || "")
                );
                return;
            }

            const hsData = response.Result;

            // Save selected HocSinhTN data for later use if needed
            selectedHocSinh = [hsData.hoc_sinh] || [];

            // Set input values properly using jQuery val()
            $("#hocSinhID").val(hsData.id || ""); // HocSinhTN document id
            $("#ketQuaRenLuyen").value(hsData.KetQua_RL);
            $("#ketQuaHocTap").value(hsData.KetQua_HT);
            $("#chkUuTien").value(hsData.ThuocDienUuTien);
            $("#ketQuaTotNghiep").value(hsData.KetQuaTN);
            $("#ghiChu").val(hsData.GhiChu || "");

            // Optionally, fill some hoc_sinh info if needed:
            // e.g. $("#hoVaTen").val(hsData.hoc_sinh?.Hovaten || "");
            const selectedOptionElement =
                document.getElementById("selectedOption");

            const student = hsData.hoc_sinh;
            const ma = student.MaDoiTuong || "";
            const ten = student.Hovaten || "";
            const cccd = student.CCCD || "N/A";
            const ngaySinhRaw = student.Ngaysinh || "";
            const ngaySinh = ngaySinhRaw
                ? new Date(ngaySinhRaw).toLocaleDateString("vi-VN")
                : "";

            const displayStr = `Họ và tên: <strong>${ma} - ${ten}</strong>; CMND/CCCD: <strong>${cccd}</strong>; Ngày sinh: <strong>${ngaySinh}</strong>`;

            selectedOptionElement.innerHTML = `${displayStr}`;

            // Open modal
            $("#mdThemMoiHocSinh").modal("show");
        })
        .catch((err) => {
            console.error("Lỗi khi gọi API getHocSinhTNById:", err);
            NTS.loi("Lỗi hệ thống khi tải dữ liệu học sinh.");
        });
}

async function XoaDuLieu_HS(ID) {
    var result_ktxoa = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.layouts.KiemTraXoa,
        {
            ma: ID,
            model: "QuanLy\\BangDiem_ChiTiet",
        }
    );

    if (!result_ktxoa.Err) {
        CanhBaoXoa(async () => {
            var result = await NTS.getAjaxAPIAsync("DELETE", Laravel.xoaHSTN, {
                BangDiemId: selectedId,
                ma: ID,
            });
            if (!result.Err) {
                NTS.thanhcong(result.Msg);
                tabGridHocSinh.setData(Laravel.getListHSByQD(selectedId));
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
    } else {
        NTS.canhbao(result_ktxoa.Msg);
        return false;
    }
}
async function LoadComboMdThemMoi() {
    return loadDataCombos([
        // 1. Chức vụ
        {
            name: "#ChucVu",
            ajaxUrl: Laravel.getListChucVu,
            ...commonComboConfig(),
        },
        // 2. Kỳ thi
        {
            name: "#KyThi",
            ajaxUrl: Laravel.getListKyThi,
            ...commonComboConfig(),
        },
        // 3. Hình thức đào tạo
        {
            name: "#HinhThucDaoTao",
            ajaxUrl: Laravel.getListHTDT,
            ...commonComboConfig(),
        },
        // 4. Hội đồng
        {
            name: "#HoiDong",
            ajaxUrl: Laravel.getListHoiDong,
            ...commonComboConfig(),
            columnSizes: [2, 6], // ← will render `<div class="col-6">…</div>`
        },
        {
            name: "#NamHoc",
            ajaxUrl: Laravel.getListNamHoc,
            ...commonComboConfig(),
            columns: 1, // indexValue=0, indexText=1
            indexValue: 0,
            indexText: 1,
        },
    ]);
}

async function LoadComboMdThemMoiHocSinh() {
    return loadDataCombos([
        // 1. Chức vụ
        {
            name: "#ketQuaRenLuyen",
            ajaxUrl: Laravel.getListXepLoai,
            ...commonComboConfig(),
        },
        // 2. Kỳ thi
        {
            name: "#ketQuaHocTap",
            ajaxUrl: Laravel.getListXepLoai,
            ...commonComboConfig(),
        },
        // 3. Hình thức đào tạo
        {
            name: "#ketQuaTotNghiep",
            ajaxUrl: Laravel.getListXepLoai,
            ...commonComboConfig(),
        },
        {
            name: "#chkUuTien",
            ajaxUrl: Laravel.getListDienUuTien,
            ...commonComboConfig(),
        },
    ]);
}
//#endregion

//#region Chuyển step modal thêm mới
// Simple step switch logic
function showStep(step) {
    document.getElementById("step-1-content").style.display =
        step === 1 ? "" : "none";
    document.getElementById("step-2-content").style.display =
        step === 2 ? "" : "none";

    // Update step sidebar highlight
    document
        .getElementById("sidebar-step-1")
        .classList.toggle("active", step === 1);
    document
        .getElementById("sidebar-step-2")
        .classList.toggle("active", step === 2);
}

// Attach next/prev button events (call showStep with 1 or 2)
document.addEventListener("DOMContentLoaded", function () {
    // "Tiếp tục" on Step 1

    // "Quay lại" on Step 2
    document.getElementById("btnQuayLaiBuoc1").onclick = function (e) {
        e.preventDefault();
        showStep(1);
    };
    // Initialize step 1
    showStep(1);
});

//#endregion Chuyển step

//#region Sửa dữ liệu
async function SuaDuLieu(id) {
    let BangDiemInfo = await NTS.getAjaxAPIAsync("GET", Laravel.loadDuLieuSua, {
        id: id,
    });

    if (BangDiemInfo.Err) {
        NTS.loi(BangDiemInfo.Msg || "Lỗi tải dữ liệu");
        return;
    }

    const data = BangDiemInfo.Result;
    selectedBangDiem = data; // remember original

    // Populate inputs
    $("#SoVanBan").val(data.SoQuyetDinh || "");
    $("#NguoiKy").val(data.NguoiKy || "");
    $("#ChucVu").value(data.ChucVuID_NK);
    $("#NgayKy").value(data.NgayKy || "");
    $("#CoQuanBanHanh").val(data.CoQuanBanHanh || "");
    $("#KyThi").value(data.KyThiID);
    $("#HinhThucDaoTao").value(data.HinhThucID);
    $("#NamHoc").value(data.NamHoc);
    $("#HoiDong").value(data.HoiDongID);

    $("#TrichYeu").val(data.TrichYeu || "");

    // Populate attached files if needed
    if (data.FileDinhKem) {
        let dinhKemArr = data.FileDinhKem;

        dinhKemArr?.forEach((url) => {
            renderAttachment(url);
        });
        uploadedFileUrls = dinhKemArr ?? [];

        $("#dinhKemQD").val(dinhKemArr?.join("|")).trigger("change");
    }
}

//#region Chọn học sinh
const customSelect = document.getElementById("customSelect");
const dropdown = document.getElementById("customDropdown");
const selectedOption = document.getElementById("selectedOption");
const hiddenSelect = document.getElementById("hocSinhID");
const btnDropdownHS = document.getElementById("btnDropdownHS");

btnDropdownHS.addEventListener("click", function () {
    $("#mdChonHocSinh").modal("show");

    loadTruongHoc(selectedId);
    if (gridChonHocSinh && typeof gridChonHocSinh.destroy === "function") {
        gridChonHocSinh.destroy();
    }

    // gridChonHocSinh = initGridChonHocSinh(modeThemHS);
});

//#endregion

//#region Shortcut
$(document).on("keydown", function (e) {
    const modalSelectors = [
        "#mdThemMoi",
        "#mdThemMoiHocSinh",
        "#mdChonHocSinh",
        "#mdBangDiemTotNghiep",
    ];

    // Filter visible modals/panels
    const visibleModals = modalSelectors.filter((sel) => {
        const el = document.querySelector(sel);
        if (!el) return false;

        if (sel === "#mdThemMoi") {
            // Use computed style to check visibility
            const style = window.getComputedStyle(el);
            return (
                style.display !== "none" &&
                style.visibility !== "hidden" &&
                style.opacity !== "0"
            );
        } else {
            return el.classList.contains("show");
        }
    });

    if (visibleModals.length === 0) return; // no open modal, ignore

    // Topmost modal is last in visibleModals
    const topModalSelector = visibleModals[visibleModals.length - 1];
    const topModal = document.querySelector(topModalSelector);

    // Helper: close the top modal
    function closeTopModal() {
        if (topModalSelector === "#mdThemMoi") {
            topModal.style.display = "none";
        } else {
            // Bootstrap modal hide
            const bsModal = bootstrap.Modal.getInstance(topModal);
            if (bsModal) bsModal.hide();
        }
    }

    // Helper: trigger submit on top modal's submit button if exists
    function submitTopModal() {
        let btn = null;

        switch (topModalSelector) {
            case "#mdThemMoi":
                // Determine current step: step 1 or step 2
                const step1Displayed =
                    document.getElementById("step-1-content").style.display !==
                    "none";
                const step2Displayed =
                    document.getElementById("step-2-content").style.display !==
                    "none";

                // Check which step is visible
                if (step1Displayed) {
                    // Step 1 active — trigger "Tiếp tục" button
                    btn = $("#btnTiepTuc");
                } else if (step2Displayed) {
                    // Step 2 active — trigger "Lưu và đóng" button
                    btn = $("#btnKetThuc");
                }
                break;
            case "#mdThemMoiHocSinh":
                btn = topModal.querySelector("#btnLuuVaDong");
                break;
            case "#mdChonHocSinh":
                btn = topModal.querySelector("#btnChonVaDong");
                break;
            case "#mdBangDiemTotNghiep":
                btn = topModal.querySelector("#btnBanHanh");
                break;
        }
        if (btn) btn.click();
    }

    // Only react if focus is inside top modal
    // if (!topModal.contains(document.activeElement)) return;

    if (e.key === "Escape" || e.key === "F4") {
        e.preventDefault();
        closeTopModal();
    } else if (e.key === "F9") {
        e.preventDefault();
        submitTopModal();
    }
});
//#endregion

//#region TÌM KIẾM NÂNG CAO

$("#TimKiemNangCao").on("click", function () {
    $("#TrangThai_Loc").select2({
        data: [
            { id: "all", text: "-Tất cả-" },
            { id: "34", text: "Chưa Phê duyệt" },
            { id: "32", text: "Đã Phê duyệt" },
            { id: "33", text: "Thu hồi phê duyệt" },
        ],
        placeholder: "--Tất cả--", // placeholder text

        allowClear: false,
        width: "100%", // force full width
    });
    let cacheCapHoc = $("#CapHoc_Loc").val();
    loadDataCombos([
        // 1. Cap hoc
        {
            name: "#CapHoc_Loc",
            ajaxUrl: Laravel.getListToChuc,
            columns: 2, // indexValue=0, indexText=1
            indexValue: 0,
            indexText: 1,
            indexText1: 2,
            textShowTatCa: "--Tất cả--",
            showTatCa: true,
        },
    ]).then(() => $("#CapHoc_Loc").value(cacheCapHoc));
});
$("#TimKiem").on("click", function () {
    SetMainGridData();
    if ($("#KhungTimKiem").css("display") == "block") {
        $("#KhungTimKiem").slideUp(200);
    } else {
        $("#KhungTimKiem").slideDown(200);
    }
});

function SetMainGridData() {
    const trangThaiLoc = ["32", "33", "34"].includes($("#TrangThai_Loc").val())
        ? $("#TrangThai_Loc").val()
        : null;
    const data = {
        tuNgay: $("#TuNgay_Loc").val() || null,
        denNgay: $("#DenNgay_Loc").val() || null,
        capHocID: $("#CapHoc_Loc").val() || null,
        trangThai: trangThaiLoc,
    };

    // Optional: clean empty strings to null
    for (const key in data) {
        if (data[key] === "" || data[key] === undefined) {
            data[key] = null;
        }
    }
    table.setData(Laravel.getListQD, data);
}
//#endregion

//#region Tìm kiếm chữ
$(document).on("keyup", "#timKiem", function (e) {
    var searchValue = $(this).val().trim().toLowerCase();

    if (!searchValue) {
        table.clearFilter();
        return;
    }

    table.setFilter(function (data) {
        // Defensive checks and lowercase conversion
        const soBangDiem = data.SoBangDiem
            ? data.SoBangDiem.toString().toLowerCase()
            : "";
        const nguoiKy = data.NguoiKy
            ? data.NguoiKy.toString().toLowerCase()
            : "";
        const capHocID =
            data.CapHocID !== null && data.CapHocID !== undefined
                ? data.CapHocID.toString().toLowerCase()
                : "";
        const ghiChu = data.GhiChu ? data.GhiChu.toString().toLowerCase() : "";

        return (
            soBangDiem.includes(searchValue) ||
            nguoiKy.includes(searchValue) ||
            capHocID.includes(searchValue) ||
            ghiChu.includes(searchValue)
        );
    });
});

$(document).on("keyup", "#searchContent", function (e) {
    const searchValue = $(this).val().trim().toLowerCase();

    if (!searchValue) {
        tabGridHocSinh.clearFilter();
        return;
    }

    tabGridHocSinh.setFilter(function (data) {
        // Defensive checks and lowercase conversion for nested fields
        const maDoiTuong =
            data.DoiTuong && data.DoiTuong.MaDoiTuong
                ? data.DoiTuong.MaDoiTuong.toString().toLowerCase()
                : "";
        const hoVaTen =
            data.DoiTuong && data.DoiTuong.Hovaten
                ? data.DoiTuong.Hovaten.toString().toLowerCase()
                : "";
        const cccd =
            data.DoiTuong && data.DoiTuong.CCCD
                ? data.DoiTuong.CCCD.toString().toLowerCase()
                : "";
        const gioiTinh =
            data.DoiTuong && data.DoiTuong.TenGioiTinh
                ? data.DoiTuong.TenGioiTinh.toString().toLowerCase()
                : "";
        const ngaySinh =
            data.DoiTuong && data.DoiTuong.NgaySinh
                ? data.DoiTuong.NgaySinh.toString().toLowerCase()
                : "";
        const noiSinh =
            data.DoiTuong && data.DoiTuong.Noisinh
                ? data.DoiTuong.Noisinh.toString().toLowerCase()
                : "";
        const ghiChu = data.GhiChu ? data.GhiChu.toString().toLowerCase() : "";
        const ketQuaRenLuyen =
            data.xep_loai_ren_luyen && data.xep_loai_ren_luyen.tenXepLoai
                ? data.xep_loai_ren_luyen.tenXepLoai.toString().toLowerCase()
                : "";
        const ketQuaHocTap =
            data.xep_loai_hoc_tap && data.xep_loai_hoc_tap.tenXepLoai
                ? data.xep_loai_hoc_tap.tenXepLoai.toString().toLowerCase()
                : "";
        const ketQuaTotNghiep =
            data.xep_loai_tot_nghiep && data.xep_loai_tot_nghiep.tenXepLoai
                ? data.xep_loai_tot_nghiep.tenXepLoai.toString().toLowerCase()
                : "";
        const uuTien = data.ThuocDienUuTien ? "x" : "";

        return (
            maDoiTuong.includes(searchValue) ||
            hoVaTen.includes(searchValue) ||
            cccd.includes(searchValue) ||
            gioiTinh.includes(searchValue) ||
            noiSinh.includes(searchValue) ||
            ghiChu.includes(searchValue) ||
            ketQuaRenLuyen.includes(searchValue) ||
            ketQuaHocTap.includes(searchValue) ||
            ketQuaTotNghiep.includes(searchValue) ||
            uuTien.includes(searchValue) ||
            ngaySinh.includes(searchValue)
        );
    });
});
//#endregion

//#region Xem chi tiết

//#region Xử lý sự kiện xem chi tiết bảng điểm
$(document).on("click", ".btnXemChiTietQD", async function () {
    // grab the ID and store it globally
    selectedId = $(this).data("id");
    if (!selectedId) return;

    // 2) Show the detail modal (or panel)
    $("#mdChiTietQD").show();

    // 3) Fetch and render the bảng điểm summary
    try {
        const resQD = await NTS.getAjaxAPIAsync("GET", Laravel.loadDuLieuSua, {
            id: selectedId,
        });
        if (resQD.Err) {
            return NTS.loi(resQD.Msg || "Lỗi tải dữ liệu QĐ");
        }
        const data = resQD.Result;
        // build the exact step‐2 HTML
        const fileLinks = Array.isArray(data.FileDinhKem)
            ? data.FileDinhKem.join("|")
            : "";
        const html = `
      <div class="list-item col-md-12">
        <div class="card card-luoi shadow-sm mb-3">
            <div class="card-body profile-user-box mb-2" style="padding-bottom:0px !important; padding-top:6px;">
                <div class="row">
                    <div class="col-12 col-xs-6 col-md-2 text-center">
                        <div class="row mt-2">
                            ${svgMainGrid}
                        </div>
                    </div>

                    <div class="col-md-10 px-2">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <p class="fs-big mb-2">Số bảng điểm: <b>${
                                    data.SoQuyetDinh || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3">
                                <p class="fs-big mb-2">Ngày ký: <b>${
                                    data.NgayKy || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3 px-0">
                                <p class="fs-big mb-2">Người ký: <b>${
                                    data.NguoiKy || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3 d-flex justify-content-between align-items-center px-0">
                                <p class="fs-big mb-2">Chức vụ: <b>${
                                    data.chuc_vu?.tenChucVu || ""
                                }</b></p>
                            </div>
                        </div>
                        <hr style="border-top: 1px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />
                        <div class="row align-items-center">
                        <div class="row">
                            <div class="col-12">
                            <p class="fs-big mb-2">Trích yếu/nội dung: <strong>${
                                data.TrichYeu || ""
                            }</strong></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                            <p class="fs-big mb-2">Cơ quan Phê duyệt: <b>${
                                data.CoQuanBanHanh || ""
                            }</b></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6 ">
                                <p class="fs-big mb-2">Kỳ thi: <b>${
                                    data.ky_thi?.TenKyThi || ""
                                }</b></p>
                            </div>
                            <div class="col-6">
                                <p class="fs-big mb-2">Cấp học: <b>${
                                    data.ten_cap_hoc || ""
                                }</b></p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                            <p class="fs-big mb-2">Đính kèm:  ${
                                fileLinks
                                    ? `<a href="#" class="file-links" data-files="${fileLinks}" onclick="handleFileLinks(event)"> <i class="fa fa-paperclip"></i>&ensp;Xem đính kèm</a>`
                                    : "Không có file đính kèm"
                            }</p>
                            </div>
                        </div>
                        </div>

                        <hr style="border-top: 1px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />
                        <div class="row">
                            <div class="col-md-3">
                                <p class="fs-big mb-2">Ngày Phê duyệt: <b>${
                                    data.NgayBanHanh || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3">
                                <p class="fs-big mb-2">Người Phê duyệt: <b>${
                                    data.nhan_vien_ban_hanh?.tenNhanVien || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3 px-0">
                                <p class="fs-big mb-2">Chức vụ Phê duyệt: <b>${
                                     data.chuc_vu_ban_hanh?.tenChucVu || ""
                                }</b></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <p class="fs-big mb-2">Nội dung Phê duyệt: <b>${
                                    data.NoiDungBH ?? ""
                                }</b></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    `;
        $("#contentCapNhatVBChiTiet").html(html);
    } catch (err) {
        console.error(err);
        NTS.loi("Lỗi khi tải chi tiết bảng điểm");
    }

    // 4) initialize/refresh the HS grid in the first tab
    try {
        if (
            tabGridHocSinhChiTiet &&
            typeof tabGridHocSinhChiTiet.destroy === "function"
        ) {
            tabGridHocSinhChiTiet.destroy();
            tabGridHocSinhChiTiet = null; // optional: clear reference
        }
        tabGridHocSinhChiTiet = new Tabulator("#tabGridHocSinhChiTiet", {
            ajaxURL: Laravel.getListHSByQD(selectedId),
            ...tabuLatorAjaxOptions,
            layout: "fitColumns",
            height: "60vh",
            placeholder: "Không có dữ liệu",

            // 2) Static columns for DoiTuong and other fixed fields
            columns: [
                {
                    title: "Mã HS",
                    field: "DoiTuong.MaDoiTuong",
                    headerHozAlign: "center",
                    formatter: "textarea",
                    width: 110,
                },
                {
                    title: "Họ và tên",
                    field: "DoiTuong.Hovaten",
                    headerHozAlign: "left",
                    formatter: "textarea",
                    width: 150,
                },
                {
                    title: "Ngày sinh",
                    field: "DoiTuong.Ngaysinh",
                    formatter: "textarea",
                    headerHozAlign: "center",
                },
                {
                    title: "Nơi sinh",
                    field: "DoiTuong.Noisinh",
                    formatter: "textarea",
                    headerHozAlign: "left",
                    width: 100,
                },
            ],

            // 3) ajaxResponse: rebuild dynamic columns + re-render data
            ajaxResponse: (url, params, response) => {
                const rows = response.Result || [];
                const subjects = rows[0]?.mon_thi_list || [];

                // 3a) “Điểm môn thi” columns
                const diemMonThiCols = subjects.length
                    ? subjects.map((s) => ({
                          title: s.tenMonHoc,
                          field: `DiemMonThi.${s.id}`, // nested object key
                          hozAlign: "center",
                          minWidth: 80,
                          formatter: "textarea",
                          headerSort: false,
                      }))
                    : [{ field: "_dummy_", title: "", width: 100 }];

                // 3b) “Điểm cuối cấp” columns
                const diemCuoiCapCols = subjects.length
                    ? subjects.map((s) => ({
                          title: s.tenMonHoc,
                          field: `DiemCuoiCap.${s.id}`,
                          hozAlign: "center",
                          formatter: "textarea",

                          minWidth: 80,
                          headerSort: false,
                      }))
                    : [{ field: "_dummy_", title: "", width: 100 }];

                // 3c) Kết hợp tất cả cột lại
                const newCols = [
                    // giữ lại các cột tĩnh bạn muốn trước nhóm điểm
                    ...tabGridHocSinhChiTiet
                        .getColumns()
                        .slice(0, 4)
                        .map((col) => col.getDefinition()),

                    // nhóm “Điểm môn thi”
                    {
                        title: "Điểm môn thi",
                        headerHozAlign: "center",
                        columns: diemMonThiCols,
                    },

                    // nhóm “Điểm cuối cấp”
                    {
                        title: "Điểm cuối cấp",
                        headerHozAlign: "center",
                        columns: diemCuoiCapCols,
                    },
                    {
                        title: "Điểm ưu tiên",
                        field: "DiemUT",
                        hozAlign: "left",
                        ...commonColumnConfig,
                        formatter: "number",

                        minWidth: 100,
                    },

                    // cột tĩnh còn lại (ví dụ ghi chú)
                    {
                        title: "Ghi chú",
                        field: "GhiChu",
                        formatter: "textarea",

                        minWidth: 300,
                        widthGrow: 1,
                    },
                ];

                // 4) Cập nhật cột và render lại
                tabGridHocSinhChiTiet.setColumns(newCols);
                tabGridHocSinhChiTiet.redraw(true);

                return rows;
            },
        });
    } catch (err) {
        console.error(err);
        NTS.loi("Không thể tải danh sách học sinh");
    }
});
//#endregion
