// mở modal nhận Excel
$(document).on("click", "#btnNhanExcelHS", () => {
    $("#mdNhanExcel").modal("show");
    $("#ChonFileNhanExcel").val("");
    $("#btnKiemTraFile").prop("disabled", true);
    setTimeout(async () => {
        const cols = await TabulatorColumns(selectedId);

        GridExcel = new Tabulator("#GridExcel", {
            ...commonTabulatorConfig,
            height: getAvailableHeight() + "px",
            pagination: "local",
            layout: "fitData",
            selectable: true,
            width: 120,

            rowFormatter(row) {
                const s = row.getData().TrangThai;
                row.getElement().style.color =
                    s === "Chờ nhận"
                        ? "#333"
                        : s === "Nhận thành công"
                        ? "#27ae60"
                        : "#c00";
            },

            columns: cols,
        });
    }, 300);
});

// khi chọn file
$("#ChonFileNhanExcel").on("change", async function () {
    const fileInput = $("#ChonFileNhanExcel")[0];
    if (!fileInput.files.length) return;

    const file = fileInput.files[0];
    const ext = file.name.split(".").pop().toLowerCase();
    if (!["xlsx", "xls"].includes(ext)) {
        return alert("Chỉ cho phép file .xlsx hoặc .xls");
    }

    const form = new FormData();
    form.append("file", file);
    form.append("thumuc", "nhanvien");
    const res = await NTS.getAjaxAPIAsync(
        "POST",
        window.Laravel.uploadDocUrl,
        form
    );

    if (res.Err) {
        return alert(res.Msg);
    }

    // save the returned path
    Path = res.url[0];
    const fullUrl = new URL(`${Path}`, window.location.origin).href;

    await NTS.loadDataComboAsync({
        name: "#TenSheet",
        ajaxUrl: window.Laravel.loadTenSheetUrl,
        ajaxParam: { path: fullUrl },
        columns: 1,
        indexValue: 0,
        indexText: 0,
        textShowTatCa: "-Chọn-",
        showTatCa: true,
    });

    $("#btnKiemTraFile").prop("disabled", !this.files.length);
});

function getStaticCols() {
    return [
        {
            formatter: "rowSelection",
            titleFormatter: "rowSelection",
            hozAlign: "center",
            headerSort: false,
            width: 40,
        },
        {
            title: "Số báo danh",
            field: "SBD",
            hozAlign: "left",
            ...commonColumnConfig,
        },
        {
            title: "Họ và tên",
            field: "HoVaTen",
            hozAlign: "left",
            ...commonColumnConfig,
            formatter: "textarea",
        },
        {
            title: "Số CMND/CCCD",
            field: "SoCMND",
            hozAlign: "left",
            ...commonColumnConfig,
        },
        {
            title: "Ngày sinh",
            field: "NgaySinh",
            hozAlign: "center",
            ...commonColumnConfig,
            formatter: (cell) => {
                let v = cell.getValue();
                return v ? new Date(v).toLocaleDateString("vi-VN") : "";
            },
        },
        {
            title: "Giới tính",
            field: "GioiTinh",
            hozAlign: "left",
            ...commonColumnConfig,
        },
        {
            title: "Nơi sinh",
            field: "NoiSinh",
            hozAlign: "left",
            ...commonColumnConfig,
        },
        {
            title: "Học sinh trường",
            field: "Truong",
            hozAlign: "left",
            width: 150,
            ...commonColumnConfig,
            formatter: "textarea",
            resizable: true,
        },
        // placeholder for Điểm môn thi
        {
            title: "Điểm môn thi",
            columns: [],
            hozAlign: "left",
            visible: true,
        },
        // placeholder for Điểm cuối cấp
        {
            title: "Điểm cuối cấp",
            columns: [],
            hozAlign: "left",
            visible: true,
        },
        {
            title: "Điểm ưu tiên",
            field: "DiemUT",
            hozAlign: "left",
            ...commonColumnConfig,
            formatter: "number",
            minWidth: 150,
        },
        {
            title: "Ghi chú",
            field: "GhiChu",
            hozAlign: "left",
            ...commonColumnConfig,
            formatter: "textarea",

            minWidth: 150,
        },
        {
            title: "Trạng thái",
            field: "TrangThai",
            headerFilter: "select",
            headerFilterPlaceholder: "Lọc", // Vietnamese placeholder
            headerFilterParams: {
                values: {
                    "Chờ nhận": "Chờ nhận",
                    "Nhận thành công": "Nhận thành công",
                    Lỗi: "Lỗi",
                },
            },
            formatter: function (cell) {
                const value = cell.getValue();
                switch (value) {
                    case "Chờ nhận":
                        cell.getElement().style.color = "#333";
                        break;
                    case "Nhận thành công":
                        cell.getElement().style.color = "#27ae60";
                        break;
                    case "Lỗi":
                        cell.getElement().style.color = "#c00";
                        break;
                    default:
                        cell.getElement().style.color = "#000";
                }
                return value;
            },
        },
        {
            title: "Diễn giải",
            field: "DienGiaiTrangThai",
            hozAlign: "left",
            ...commonColumnConfig,
            formatter: "textarea",
            widthGrow: 2,
        },
    ];
}

// Hàm tạo cấu trúc cột cho
async function TabulatorColumns(selectedId) {
    // Prepare empty dynamic column arrays
    let diemMonThiCols = [
        {
            field: "_dummy_",
            title: "",
            width: 100,
            visible: true,
            resizable: true,
        },
    ];

    let diemCuoiCapCols = [
        {
            field: "_dummy_",
            title: "",
            width: 100,
            visible: true,
            resizable: true,
        },
    ];

    try {
        const res = await NTS.getAjaxAPIAsync("GET", window.Laravel.getMonThi, {
            bangDiemId: selectedId,
        });
        // simple accent‐removing slug function
        function slugify(str) {
            // 1) strip accents
            const ascii = str.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
            // 2) lowercase
            const lower = ascii.toLowerCase();
            // 3) replace non-alnum runs with _
            let slug = lower.replace(/[^a-z0-9]+/g, "_");
            // 4) trim extra _
            return slug.replace(/^_+|_+$/g, "");
        }

        if (!res.Err && Array.isArray(res.Result) && res.Result.length) {
            const monThis = res.Result;

            diemMonThiCols = monThis.map((mon) => {
                const key = slugify(mon.tenMonHoc);
                return {
                    title: mon.tenMonHoc,
                    field: `DiemMonThi.${key}`,
                    hozAlign: "center",
                    formatter: "textarea",
                };
            });

            // Điểm cuối cấp columns
            diemCuoiCapCols = monThis.map((mon) => {
                const key = slugify(mon.tenMonHoc);
                return {
                    title: mon.tenMonHoc,
                    field: `DiemCuoiCap.${key}`,
                    hozAlign: "center",
                    formatter: "textarea",
                };
            });
        } else {
            // Show warning but keep the dummy column
            NTS.canhbao(
                res.Err
                    ? res.Msg
                    : "Không có môn thi nào được chọn cho quyết định này."
            );
        }
    } catch (e) {
        // In case of network / unexpected error
        NTS.canhbao("Lỗi khi lấy môn thi: " + e.message);
    }

    const staticCols = getStaticCols();

    return [
        ...staticCols.slice(0, 8),
        {
            title: "Điểm môn thi",
            columns: diemMonThiCols,
            headerHozAlign: "center",
        },
        {
            title: "Điểm cuối cấp",
            columns: diemCuoiCapCols,
            headerHozAlign: "center",
        },
        ...staticCols.slice(10),
    ];
}

function getAvailableHeight() {
    const container = document.getElementById("colThaoTac");
    if (!container) return window.innerHeight * 0.6; // fallback

    const containerHeight = container.clientHeight;

    // Get the first two fieldsets inside colThaoTac
    const fieldsets = container.querySelectorAll("fieldset.KhungVien");
    if (fieldsets.length < 3) return containerHeight * 0.6; // fallback if layout changes

    // Heights of the first 2 fieldsets (steps 1 and 2)
    const firstHeight = fieldsets[0].offsetHeight;
    const secondHeight = fieldsets[1].offsetHeight;

    // Add some buffer for margins/paddings between fieldsets, adjust if needed
    const buffer = 20;

    // Calculate remaining height for the 3rd fieldset containing the grid
    const availableHeight =
        containerHeight - firstHeight - secondHeight - buffer;

    return availableHeight > 0 ? availableHeight : 300; // minimum height fallback
}

var GridExcel;
window.addEventListener("load", () => {
    const modalDialog = document.querySelector("#mdNhanExcel .modal-dialog");
    if (modalDialog) {
        modalDialog.classList.add("modal-fullscreen");
    }
});
// then your existing

// kiểm tra cấu trúc
$("#btnKiemTraFile").on("click", async () => {
    const sheet = $("#TenSheet").val();
    if (!sheet) return alert("Bạn chưa chọn sheet!");

    const res = await fetch(window.Laravel.checkExcel, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": $("meta[name=csrf-token]").attr("content"),
        },
        body: JSON.stringify({
            path: Path, // ← server path, e.g. "/storage/uploads/…xlsx"
            sheet: sheet, // ← selected sheet name
        }),
    }).then((r) => r.json());

    if (res.Err) return NTS.canhbao(res.Msg);

    // now load the preview (also reading from server‐stored data)
    const data = await fetch(window.Laravel.loadExcel, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": $("meta[name=csrf-token]").attr("content"),
        },
        body: JSON.stringify({
            path: Path,
            sheet: sheet,
            bangDiemId: selectedId,
        }),
    }).then((r) => r.json());

    GridExcel.setData(data);
});

// cuối cùng: import những dòng đang chọn
$("#btnLuuVaDongExcel").on("click", async () => {
    // get the row components that are selected
    const selectedRows = GridExcel.getSelectedRows();
    // extract their positions for the POST
    const rows = selectedRows.map((r) => r.getPosition() - 1);

    const res = await fetch(window.Laravel.importExcel, {
        method: "POST",
        headers: {
            "X-CSRF-TOKEN": $("meta[name=csrf-token]").attr("content"),
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            rows: rows,
            quyetDinhId: selectedId,
        }),
    }).then((r) => r.json());

    // now update each RowComponent
    selectedRows.forEach((rowComp) => {
        const pos = rowComp.getPosition();
        const result = res.Result[pos - 1]; // adjust for 0-based index
        if (result.Err) {
            rowComp.update({
                TrangThai: "Lỗi",
                DienGiaiTrangThai: result.Msg || "", // clear any old message
            });
            rowComp.getElement().style.color = "red";
        } else {
            rowComp.update({
                TrangThai: "Nhận thành công",
                DienGiaiTrangThai: result.Msg || "", // clear any old message
            });
            rowComp.getElement().style.color = "green";
        }
        rowComp.deselect();
    });

    const resultMap = res.Result || {};
    const successCount = Object.values(resultMap).filter(
        (r) => r.Err === false
    ).length;
    const totalCount = Object.keys(resultMap).length;
    NTS.thongbao({
        type: "info",
        message: `Lưu thành công ${successCount}/${totalCount} dòng.`,
    });
});

$("#dropzone")
    .on("dragover", (e) => e.preventDefault())
    .on("drop", (e) => {
        e.preventDefault();
        const files = e.originalEvent.dataTransfer.files;
        $("#ChonFileNhanExcel")[0].files = files;
        $("#ChonFileNhanExcel").trigger("change");
    });

$("#mdNhanExcel").on("hidden.bs.modal", function (e) {
    tabGridHocSinh.setData(Laravel.getListHSByQD(selectedId));
    setTimeout(() => {
        tabGridHocSinh.redraw(true);
    }, "300");
});
