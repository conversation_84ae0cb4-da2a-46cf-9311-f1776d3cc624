// 1. Populate modal fields from an object
/**
 * Hàm điền thông tin vào modal Phê duyệt bảng điểm tốt nghiệp.
 *
 * @param {string} tieuDe - Tiêu đề của modal.
 * @param {Object} data - Dữ liệu liên quan đến thông tin Phê duyệt.
 * @param {string} [data.NgayBanHanh] - Ngày Phê duyệt bảng điểm.
 * @param {string} [data.NguoiBanHanh] - Người Phê duyệt bảng điểm.
 * @param {string} [data.ChucVuBanHanh] - Chức vụ của người Phê duyệt.
 * @param {string} [data.NoiDungBanHanh] - Nội dung Phê duyệt bảng điểm.
 * @param {Object} dataQuyetDinh - Dữ liệu liên quan đến bảng điểm tốt nghiệp.
 * @param {string} [dataQuyetDinh.SoQuyetDinh] - <PERSON><PERSON> bảng điểm tốt nghiệp.
 * @param {string} [dataQuyetDinh.CoQuanBanHanh] - Cơ quan Phê duyệt bảng điểm.
 * @param {string} [dataQuyetDinh.TrichYeu] - Trích yếu nội dung bảng điểm.
 *
 * @description Hàm này cập nhật các trường thông tin trong modal Phê duyệt bảng điểm tốt nghiệp
 * và hiển thị thông báo hướng dẫn người dùng điền thông tin cần thiết để thực hiện thao tác Phê duyệt.
 */
var UserID_NguoiThaoTac;
var ChucVuID_NguoiThaoTac;
function dienMdBanHanh(tieuDe, data, dataQuyetDinh) {
    $("#ngayBanHanh").val(data?.NgayBanHanh || "");
    $("#nguoiBanHanh").val(data?.NguoiBanHanh || "");
    $("#chucVuBanHanh").val(data?.ChucVuBanHanh || "");
    $("#noiDungBanHanh").val(data?.NoiDungBanHanh || "");
    let textAlert = `
    Bạn đang thực hiện phê duyệt bảng điểm số:
                                <b>${dataQuyetDinh?.SoQuyetDinh || ""}</b>
                                ngày ký <b>${ dataQuyetDinh?.NgayKy || ""}</b>
                                của <b>${dataQuyetDinh?.CoQuanBanHanh || ""}</b>
                                về việc <b>${dataQuyetDinh?.TrichYeu || ""}</b>. Vui
                                lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>“Phê duyệt”</b> để thực hiện
                                thao tác phê duyệt cho bảng điểm.
    `;
    // Optionally update alertMessage or fieldsetLegend if included in data
    $("#tieuDe_mdBangDiemHocSinh").text(tieuDe);
    $("#alertMessage").html(textAlert);
}

// 2. Show the modal
function hienMdBanHanh() {
    $("#mdBangDiemHocSinh").modal("show");
}

// 3. Clear all modal fields
function resetMdBanHanh() {
    $("#ngayBanHanh").val("");
    $("#nguoiBanHanh").val("");
    $("#chucVuBanHanh").val("");
    $("#noiDungBanHanh").val("");

    // Reset alertMessage and fieldsetLegend if needed
    $("#alertMessage").html("");
    $("#fieldsetLegend").text("Thông tin phê duyệt");
}

// 4. Hide the modal
function anMdBanHanh() {
    const modalEl = document.getElementById("mdBangDiemHocSinh");
    const modal = bootstrap.Modal.getInstance(modalEl);
    if (modal) modal.hide();
}
async function loadComboMdBanHanh() {
    loadDataCombos([
        // Load Chức vụ combo
        {
            name: "#chucVuBanHanh",
            ajaxUrl: Laravel.getListChucVu,
            columns: 2,
            indexValue: 0,
            indexText: 1,
            indexText1: 2,
            textShowTatCa: "-Chọn-",
            showTatCa: true,
        },
        // Load Nhân viên combo
        {
            name: "#nguoiBanHanh",
            ajaxUrl: Laravel.getListNhanvien,
            columns: 2,
            indexValue: 0,
            indexText: 1,
            indexText1: 2,
            textShowTatCa: "-Chọn-",
            showTatCa: true,
        },
    ]);
}

// Lây dữ liệu Phê duyệt
function layDuLieuMdBanHanh() {
    return {
        NgayBanHanh: $("#ngayBanHanh").val().trim(),
        NguoiBanHanh: $("#nguoiBanHanh").val(),
        ChucVuBanHanh: $("#chucVuBanHanh").val(),
        NoiDungBanHanh: $("#noiDungBanHanh").val().trim(),
    };
}

//Tạo nội dung Phê duyệt
function taoNoiDungBH(ngayKy, soQuyetDinh, coQuanBanHanh, trichYeu) {
    if (!ngayKy) ngayKy = "[Ngày phê duyệt]";
    if (!soQuyetDinh) soQuyetDinh = "";
    if (!coQuanBanHanh) coQuanBanHanh = "[Sở giáo dục và đào tạo]";
    if (!trichYeu) trichYeu = "[trích yếu]";

    return `Phê duyệt bảng điểm số: <b>${soQuyetDinh}</b> ngày ký <b>${ngayKy}</b> của <b>${coQuanBanHanh}</b> về việc <b>${trichYeu}</b>`;
}

function taoNoiDungTH(ngayKy, soQuyetDinh, coQuanBanHanh, trichYeu) {
    if (!ngayKy) ngayKy = "[Ngày phê duyệt]";
    if (!soQuyetDinh) soQuyetDinh = "";
    if (!coQuanBanHanh) coQuanBanHanh = "[Sở giáo dục và đào tạo]";
    if (!trichYeu) trichYeu = "[trích yếu]";

    return `Thu hồi bảng điểm số: <b>${soQuyetDinh}</b> ngày ký <b>${ngayKy}</b> của <b>${coQuanBanHanh}</b> về việc <b>${trichYeu}</b>`;
}

//#region Phê duyệt
//Show modal
function setLabelText(selector, newText, keepSpan = false) {
    const $lbl = $(selector);

    if (keepSpan) {
        // grab the existing <span class="text-danger">…</span> if present
        const $star = $lbl.children("span.text-danger").detach();
        // remove all text nodes
        $lbl.contents()
            .filter((i, n) => n.nodeType === 3)
            .remove();
        // prepend the new text plus a space
        $lbl.prepend(newText + " ");
        // reattach the star
        $lbl.append($star);
    } else {
        // simple text replace (no span to keep)
        $lbl.text(newText);
    }
}
function formatDateToDDMMYYYY(date) {
    const d = date.getDate().toString().padStart(2, "0");
    const m = (date.getMonth() + 1).toString().padStart(2, "0");
    const y = date.getFullYear();
    return `${d}/${m}/${y}`;
}

$(document).on("click", ".btnBanHanhQD", async function () {
    let id = $(this).data("id");
    selectedId = id;
    // lấy ra
    var currentUrl = window.location.href;
    var domain = new URL(currentUrl).origin;

    NTS.getAjaxAPIAsync("GET", `${domain}/api/get-current-user-info/getCurrentUserInfo`, {})
        .then(userInfo => {
            UserID_NguoiThaoTac = userInfo.data?.userInfo?.NhanVienID || "";
            ChucVuID_NguoiThaoTac = userInfo.data?.nhanVien?.chucVuID || "";
        })
    .catch(err => {});
    loadComboMdBanHanh();
    $("#btnBanHanh").html(`
        <i class="fa fa-check"></i>&nbsp;Phê duyệt (F9)`);

    let quyetDinhInfo = await NTS.getAjaxAPIAsync(
        "GET",
        Laravel.loadDuLieuSua,
        { id: id }
    );

    if (!quyetDinhInfo.Err) {
        let result = quyetDinhInfo.Result;

        dienMdBanHanh("Phê duyệt bảng điểm học sinh", null, result);

        const resultString = taoNoiDungBH(
            result.NgayKy,
            result.SoQuyetDinh,
            result.CoQuanBanHanh,
            result.TrichYeu
        );

        // Update somewhere on the page — for example the textarea
        $("#noiDungBanHanh").html(resultString);
    } else {
        NTS.loi(quyetDinhInfo.Msg);
    }

    $("#ngayBanHanh").val(formatDateToDDMMYYYY(new Date()));

    $("#fieldsetLegend").text("Thông tin phê duyệt");
    setLabelText(".form-label[for=ngayBanHanh]", "Ngày phê duyệt", true);
    setLabelText(
        ".form-label[for=noiDungBanHanh]",
        "Nội dung phê duyệt",
        false
    );
    setLabelText(".form-label[for=nguoiBanHanh]", "Người phê duyệt", false);

    $("#mdBangDiemHocSinh").modal("show");
    $('#nguoiBanHanh').value(UserID_NguoiThaoTac);
    $('#chucVuBanHanh').value(ChucVuID_NguoiThaoTac);
});

//#region Thu hồi
$(document).on("click", ".btnThuHoiBanHanhQD", async function () {
    if (!QuyenSua()) return;

    $("#btnBanHanh").html(`
        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
  <path d="M14.5 10C14.5 9.32 14.44 8.66 14.36 8H17.74C17.9 8.64 18 9.31 18 10C18 10.37 17.97 10.73 17.92 11.08C18.61 11.18 19.25 11.4 19.84 11.72C19.94 11.16 20 10.59 20 10C20 4.5 15.5 0 10 0C4.47 0 0 4.5 0 10C0 15.5 4.5 20 10 20C10.59 20 11.16 19.94 11.72 19.84C11.245 18.9689 10.9974 17.9921 11 17C11 16.71 11.03 16.43 11.07 16.15C10.75 16.78 10.4 17.39 10 17.96C9.17 16.76 8.5 15.43 8.09 14H11.81C12.4106 12.9564 13.3137 12.1195 14.4 11.6C14.46 11.07 14.5 10.54 14.5 10ZM10 2.03C10.83 3.23 11.5 4.57 11.91 6H8.09C8.5 4.57 9.17 3.23 10 2.03ZM2.26 12C2.1 11.36 2 10.69 2 10C2 9.31 2.1 8.64 2.26 8H5.64C5.56 8.66 5.5 9.32 5.5 10C5.5 10.68 5.56 11.34 5.64 12H2.26ZM3.08 14H6C6.35 15.25 6.8 16.45 7.4 17.56C5.57827 16.9323 4.04429 15.6682 3.08 14ZM6 6H3.08C4.03864 4.32703 5.57466 3.06124 7.4 2.44C6.8 3.55 6.35 4.75 6 6ZM12.34 12H7.66C7.56 11.34 7.5 10.68 7.5 10C7.5 9.32 7.56 8.65 7.66 8H12.34C12.43 8.65 12.5 9.32 12.5 10C12.5 10.68 12.43 11.34 12.34 12ZM12.59 2.44C14.43 3.07 15.96 4.34 16.92 6H13.97C13.6565 4.76161 13.1931 3.56611 12.59 2.44ZM20.5 15.25L15.75 20L13 17L14.16 15.84L15.75 17.43L19.34 13.84L20.5 15.25Z" fill="white"/>
</svg>&nbsp;Thu hồi (F9)`);
    let id = $(this).data("id");
    selectedId = id;

    loadComboMdBanHanh();

    let quyetDinhInfo = await NTS.getAjaxAPIAsync(
        "GET",
        Laravel.loadDuLieuSua,
        { id: id }
    );
    $("#ngayBanHanh").val(formatDateToDDMMYYYY(new Date()));

    if (!quyetDinhInfo.Err) {
        let result = quyetDinhInfo.Result;

        // Populate modal with Thu hồi info if any or clear fields
        dienMdBanHanh("Thu hồi Phê duyệt bảng điểm học sinh", null, result);

        // Adjust alert message for thu hồi
        $("#alertMessage").html(`
            Bạn đang thực hiện thu hồi phê duyệt bảng điểm số:
            <b>${result.SoQuyetDinh}</b> ngày ký <b>${result.NgayKy}</b>.
            Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>“Thu hồi”</b> để thực hiện thao tác.
        `);

        const resultString = taoNoiDungTH(
            result.NgayKy,
            result.SoQuyetDinh,
            result.CoQuanBanHanh,
            result.TrichYeu
        );

        // Update somewhere on the page — for example the textarea
        $("#noiDungBanHanh").html(resultString);
    } else {
        NTS.loi(quyetDinhInfo.Msg);
    }
    $("#fieldsetLegend").text("Thông tin thu hồi");

    setLabelText(".form-label[for=ngayBanHanh]", "Ngày thu hồi", true);
    setLabelText(".form-label[for=noiDungBanHanh]", "Nội dung thu hồi", false);
    setLabelText(".form-label[for=nguoiBanHanh]", "Người thu hồi", false);

    $("#mdBangDiemHocSinh").modal("show");
});

//region Lưu info thu hồi
$(document).on("click", "#btnBanHanh", async function () {
    if (!QuyenSua()) return;
    if (!selectedId) {
        NTS.loi("ID bảng điểm không hợp lệ");
        return;
    }

    // Detect if modal is in Thu hồi mode by title (or use a flag)
    const isThuHoi = $("#tieuDe_mdBangDiemHocSinh")
        .text()
        .toLowerCase()
        .includes("thu hồi");

    const ngay = $("#ngayBanHanh").val().trim();
    if (!ngay) {
        if (!isThuHoi) NTS.loi("Vui lòng nhập ngày phê duyệt");
        else {
            NTS.loi("Vui lòng nhập ngày thu hồi");
        }
        $("#ngayBanHanh").focus();
        return;
    }
    // Collect data (adjust keys for Thu hồi)
    const payload = isThuHoi
        ? {
              NgayThuHoi: $("#ngayBanHanh").val().trim(),
              NhanVienID_thuhoi: $("#nguoiBanHanh").val(),
              ChucVuQL_thuhoi: $("#chucVuBanHanh").val(),
              NoiDung_thuhoi: $("#noiDungBanHanh").html().trim(),
          }
        : {
              NgayBanHanh: $("#ngayBanHanh").val().trim(),
              NhanVienID: $("#nguoiBanHanh").val(),
              ChucVuQL_BH: $("#chucVuBanHanh").val(),
              NoiDungBH: $("#noiDungBanHanh").html().trim(),
          };

    const url = isThuHoi
        ? Laravel.luuThongTinThuHoi.replace("BangDiemID", selectedId)
        : Laravel.luuThongTinBanHanh.replace("BangDiemID", selectedId);
    debugger;
    try {
        const response = await NTS.getAjaxAPIAsync("PUT", url, payload);

        if (!response.Err) {
            NTS.thanhcong(
                isThuHoi
                    ? "Thu hồi phê duyệt bảng điểm thành công"
                    : "Phê duyệt bảng điểm thành công"
            );
            table.setData();
            anMdBanHanh();
        } else {
            NTS.loi(
                response.Msg ||
                    (isThuHoi
                        ? "Lỗi khi thu hồi"
                        : "Lỗi khi lưu thông tin phê duyệt")
            );
        }
    } catch (error) {
        console.error("Lỗi khi gọi API lưu thông tin:", error);
        NTS.loi("Lỗi hệ thống, vui lòng thử lại sau");
    }
});

//#region Shortcut
$(document).on("keydown", function (e) {
    const modalIsVisible = $("#mdBangDiemHocSinh").hasClass("show");

    if (!modalIsVisible) return; // Only react if modal is open

    if (e.key === "Escape" || e.key === "F4") {
        e.preventDefault();
        anMdBanHanh();
    } else if (e.key === "F9") {
        e.preventDefault();
        $("#btnBanHanh").trigger("click");
    }
});
//#endregion
