/*
 * Global state shared by the drawing tool.
 */
let images = []; // list of { imageElement, container, boxElements }
let selectedImageIndex = null; // currently selected image
let selectedBox = null; // selected box overlay
let isDrawing = false; // flag while drawing a new box
let drawStart = { x: 0, y: 0 }; // start point of a draw
let tempBox = null; // temporary box element

const $window = $(window);
const slugify = (str) =>
    str
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "")
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "_")
        .replace(/^_+|_+$/g, "");
//#region Trường dữ liệu
const $fieldSel = $("#fieldSelector");
let modeThemMoiOCR = 0; //0: Tạo bảng điểm mới, 1: thêm vào bảng điểm đã tạo
const mapTruongDuLieu = {
    QuyetDinh: {
        Type: "TextInput",
        ID: "#SoQuyetDinh_OCR",
    },
    NgayKy: {
        Type: "TextInput",
        ID: "#NgayKy_OCR",
    },
    NguoiKy: {
        Type: "TextInput",
        ID: "#NguoiKy_OCR",
    },
    ChucVu: {
        Type: "SelectInput",
        ID: "#ChucVu_OCR",
    },
    KyThi: {
        Type: "SelectInput",
        ID: "#KyThi_OCR",
    },
    HinhThucDaoTao: {
        Type: "SelectInput",
        ID: "#HinhThucDaoTao_OCR",
    },
    CoQuanBanHanh: {
        Type: "TextInput",
        ID: "#CoQuanBanHanh_OCR",
    },
    TrichYeu: {
        Type: "TextInput",
        ID: "#TrichYeu_OCR",
    },
    SBD: {
        Type: "Grid",
        Col: "SBD",
    },
    HoVaTen: {
        Type: "Grid",
        Col: "HoVaTen",
    },
    "CCCD/CMT": {
        Type: "Grid",
        Col: "CCCD",
    },
    NgaySinh: {
        Type: "Grid",
        Col: "NgaySinh",
    },
    Truong: {
        Type: "Grid",
        Col: "TenTruong",
    },
    GioiTinh: {
        Type: "Grid",
        Col: "GioiTinh",
    },
    NoiSinh: {
        Type: "Grid",
        Col: "NoiSinh",
    },
    DiemMonThi: {
        Type: "Grid",
        Col: "DiemMonThi",
    },
    DiemCuoiCap: {
        Type: "Grid",
        Col: "DiemCuoiCap",
    },
};
resizeObserver = new ResizeObserver(() => {
    images.forEach((imgObj) => {
        imgObj.boxElements.forEach((box) =>
            updateBoxPosition(box, imgObj.imageElement)
        );
    });
});
$fieldSel.on("select2:select", (e) => {
    if (selectedBox) selectedBox.assignedField = e.params.data.id;
});
$fieldSel.on("select2:unselect", () => {
    if (selectedBox) selectedBox.assignedField = null;
});
//#endregion
//#region Grid
// ─── now your two group‐column defs just call that helper ──────────────
const monThiGroup = {
    title: `Điểm môn thi `,
    field: "DiemMonThi",
    columns: [{ field: "_dummy_", title: "", width: 100, resizable: true }],
    headerHozAlign: "center",
    headerSort: false,
};

const cuoiCapGroup = {
    title: `Điểm cuối cấp`,
    field: "DiemCuoiCap",
    columns: [{ field: "_dummy_", title: "", width: 100, resizable: true }],
    headerHozAlign: "center",
    headerSort: false,
};

let staticColsOCR = [
    {
        title: "<i class='fa fa-ellipsis-h'></i>",
        formatter: btnThaoTac,
        width: 40,
        hozAlign: "center",
        cellClick: function (e, cell) {
            // only delete if the trash icon was clicked
            if (e.target.closest(".btnXoaGrid1")) {
                cell.getRow().delete();
            }
        },
        headerSort: false,
        ...commonColumnConfig,
    },
    {
        formatter: "rowSelection",
        titleFormatter: "rowSelection",
        hozAlign: "center",
        headerHozAlign: "center",
        headerSort: false,
        cellClick: function (e, cell) {
            cell.getRow().toggleSelect();
        },
        width: 40,
    },
    {
        title: "Số báo danh",
        field: "SBD",
        hozAlign: "left",
        width: 110,
        ...commonColumnConfig,
    },
    {
        title: "Họ và tên",
        field: "HoVaTen",
        hozAlign: "left",
        width: 150,
        ...commonColumnConfig,
        formatter: "textarea",
    },
    {
        title: "Số CMND/CCCD",
        field: "CCCD",
        hozAlign: "left",
        ...commonColumnConfig,
    },
    {
        title: "Ngày sinh",
        field: "NgaySinh",
        hozAlign: "center",
        ...commonColumnConfig,
        formatter: "textarea",
    },
    {
        title: "Giới tính",
        field: "GioiTinh",
        hozAlign: "left",
        ...commonColumnConfig,
    },
    {
        title: "Nơi sinh",
        field: "NoiSinh",
        hozAlign: "left",
        width: 100,

        ...commonColumnConfig,
    },
    {
        title: "Học sinh trường",
        field: "TenTruong",
        hozAlign: "left",
        width: 150,
        ...commonColumnConfig,
        formatter: "textarea",
        resizable: true,
    },
    {
        title: "Địa điểm thi",
        field: "DiaDiemThi",
        hozAlign: "left",
        width: 150,
        ...commonColumnConfig,
        formatter: "textarea",
        resizable: true,
    },
    {
        title: "Phòng thi",
        field: "PhongThi",
        hozAlign: "left",
        width: 150,
        ...commonColumnConfig,
        formatter: "textarea",
        resizable: true,
    },
    // placeholder for Điểm môn thi
    monThiGroup,
    // placeholder for Điểm cuối cấp
    cuoiCapGroup,
    {
        title: "Điểm ưu tiên",
        field: "DiemUT",
        hozAlign: "left",
        ...commonColumnConfig,
        formatter: "number",
        editor: "number",
        editorParams: {
            step: 0.5, // allows two decimal places
        },

        width: 100,
    },
    {
        title: "Ghi chú",
        field: "GhiChu",
        hozAlign: "left",
        ...commonColumnConfig,
        editor: "textarea",
        editorParams: {
            elementAttributes: {
                maxlength: "255", //set the maximum character length of the textarea element to 10 characters
            },
        },

        minWidth: 150,
    },
];
function enrichComboWithSubjects($fieldSel, monThis, mapTruongDuLieu) {
    // 1) grab the Select2 instance
    const select2Instance = $fieldSel.data("select2");
    if (!select2Instance) {
        console.error("Select2 not initialized on", $fieldSel);
        return;
    }

    // 2) grab the adapter
    const adapter = select2Instance.dataAdapter;
    if (!adapter || typeof adapter.addOptions !== "function") {
        console.error("Select2 adapter.addOptions is unavailable");
        return;
    }

    // 3) recover the original _comboSettings so your templates still work
    //    (they were stored on each item created in loadDataComboAsync)
    const existingData = adapter.currentData || [];
    const settings = existingData[0]?._comboSettings;
    if (!settings) {
        console.warn("No _comboSettings found; templates may mis‑render");
    }

    // 4) build an array of <option> nodes, tagging each with the extra data
    const newOptions = monThis.flatMap((m) => {
        const key = slugify(m.tenMonHoc);
        return ["DiemMonThi", "DiemCuoiCap"].map((prefix) => {
            const id = `${prefix}.${key}`;
            const text = prefix;
            const text1 = m.tenMonHoc;

            // 3) create a real <option> element…
            const opt = document.createElement("option");
            opt.value = id;
            opt.textContent = prefix + " - " + text1;
            opt.disabled = false;

            // 4) stash your second‑column value in a data-* attr

            // 5) record your OCR mapping
            mapTruongDuLieu[id] = { Type: "Grid", Col: id };

            // 6) inject into the in‑memory ArrayAdapter
            adapter.addOptions([opt]);
        });
    });

    // 5) inject them into the existing dropdown
    adapter.addOptions(newOptions);

    // 6) tell Select2 to refresh its view
    $fieldSel.trigger("change.select2");
}

async function TabulatorColumnsByKyThi(kyThiId) {
    // slug function for field keys

    // 1.1) fetch the môn thi list for this KyThi
    let diemMonThiCols = [
        { field: "_dummy_", title: "", width: 100, resizable: true },
    ];
    let diemCuoiCapCols = [
        { field: "_dummy_", title: "", width: 100, resizable: true },
    ];

    try {
        const res = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.getMonThiFromKyThi,
            { kyThiId }
        );
        if (!res.Err && Array.isArray(res.Result) && res.Result.length) {
            window.monThiSlugToIdMap = {};
            res.Result.forEach((mon) => {
                const slug = slugify(mon.tenMonHoc);
                // mon.MonThiID or mon.id depending on your API
                window.monThiSlugToIdMap[slug] = mon.MonThiID || mon.id;
            });

            diemMonThiCols = res.Result.map((mon) => ({
                title: mon.tenMonHoc,

                field: `DiemMonThi.${slugify(mon.tenMonHoc)}`,
                hozAlign: "center",
                formatter: "number",
                editor: "number",
                editorParams: {
                    step: 0.5, // allows two decimal places
                },
            }));
            diemCuoiCapCols = res.Result.map((mon) => ({
                title: mon.tenMonHoc,

                field: `DiemCuoiCap.${slugify(mon.tenMonHoc)}`,
                hozAlign: "center",
                formatter: "number",
                editor: "number",
                editorParams: {
                    step: 0.5, // allows two decimal places
                },
            }));

            const monThis =
                !res.Err && Array.isArray(res.Result) ? res.Result : [];
            enrichComboWithSubjects($fieldSel, monThis, mapTruongDuLieu);
        } else {
            // warn & leave dummy col
            NTS.canhbao(res.Err ? res.Msg : "Kỳ thi này chưa có môn thi nào.");
        }
    } catch (e) {
        NTS.canhbao("Lỗi khi lấy môn thi: " + e.message);
    }

    // 1.2) your static cols before the two dynamic groups
    const staticPrefix = [
        {
            title: "<i class='fa fa-ellipsis-h'></i>",
            formatter: btnThaoTac,
            width: 40,
            hozAlign: "center",
            ...commonColumnConfig,
            headerSort: false,
        },
        {
            formatter: "rowSelection",
            titleFormatter: "rowSelection",
            hozAlign: "center",
            headerSort: false,
            cellClick: function (e, cell) {
                cell.getRow().toggleSelect();
            },
            width: 40,
        },
        {
            title: "Số báo danh",
            field: "SBD",
            hozAlign: "left",
            ...commonColumnConfig,
            editor: "textarea",
            validator: ["required", "integer"],
        },
        {
            title: "Họ và tên",
            field: "HoVaTen",
            hozAlign: "left",
            formatter: "textarea",
            editor: "textarea",
            validator: ["required"],
            ...commonColumnConfig,
        },
        {
            title: "Số CMND/CCCD",
            field: "CCCD",
            hozAlign: "left",
            editor: "input",
            ...commonColumnConfig,
        },
        {
            title: "Ngày sinh",
            field: "NgaySinh",
            hozAlign: "center",
            formatter: "textarea",
            editor: "input",
            editorParams: {
                placeholder: "dd/mm/yyyy",
            },
            ...commonColumnConfig,
        },
        {
            title: "Giới tính",
            field: "GioiTinh",
            hozAlign: "left",
            editor: "select",
            editorParams: {
                values: { Nam: "Nam", Nữ: "Nữ" },
            },
            ...commonColumnConfig,
        },
        {
            title: "Nơi sinh",
            field: "NoiSinh",
            hozAlign: "left",
            editor: "input",
            ...commonColumnConfig,
        },
        {
            title: "Học sinh trường",
            field: "TenTruong",
            hozAlign: "left",
            width: 150,
            formatter: "textarea",
            formatter: "textarea",
            editor: "textarea",
            ...commonColumnConfig,
        },
    ];

    // 1.3) your static cols after the two dynamic groups
    const staticSuffix = [
        {
            title: "Điểm ưu tiên",
            field: "DiemUT",
            hozAlign: "left",
            ...commonColumnConfig,
            formatter: "number",
            editor: "number",
            editorParams: {
                step: 0.5, // allows two decimal places
            },

            minWidth: 150,
        },
        {
            title: "Ghi chú",
            field: "GhiChu",
            hozAlign: "left",
            editor: "textarea",
            editorParams: { elementAttributes: { maxlength: "255" } },
            minWidth: 150,
            ...commonColumnConfig,
        },
    ];

    // 1.4) assemble & return
    return [
        ...staticPrefix,
        {
            title: "Điểm môn thi",
            columns: diemMonThiCols,
            headerHozAlign: "center",
        },
        {
            title: "Điểm cuối cấp",
            columns: diemCuoiCapCols,
            headerHozAlign: "center",
        },
        ...staticSuffix,
    ];
}
let tabGridHocSinhChiTietOCR = new Tabulator("#tabGridHocSinhChiTietOCR", {
    ajaxParams: {},
    nestedLookup: false, // ← disable dot‑path parsing
    selectable: 2, // allow row selection
    layout: "fitDataStretch",
    minHeight: "50vh",
    maxHeight: "80vh",
    minWidth: "100%",
    placeholder: "Không có dữ liệu",
    // Sử dụng các cột tĩnh ban đầu, sẽ cập nhật động sau
    columns: staticColsOCR,
    resizable: true,
});
tabGridHocSinhChiTietOCR.on("cellClick", function (e, cell) {
    if (e.target.closest(".btnXoaGrid1")) {
        cell.getRow().delete();
    }
});
tabGridHocSinhChiTietOCR.on(
    "validationFailed",
    function (cell, value, validators) {
        const maxRule = validators.find((v) => v.type === "max");
        const maxVal = maxRule.parameters;

        NTS.canhbao([`Điểm đã nhập bị vượt quá hệ số điểm ${maxVal}!`]);
    }
);

//#endregion
//#region Mở modal
function resetMdOCR() {
    $("#SoQuyetDinh_OCR").val("");
    $("#NguoiKy_OCR").val("");
    $("#ChucVu_OCR").val("");
    $("#NgayKy_OCR").val("");
    $("#CoQuanBanHanh_OCR").val("");
    $("#KyThi_OCR").val("");
    $("#HinhThucDaoTao_OCR").val("");
    $("#NamHoc_OCR").val("");
    $("#HoiDong_OCR").val("");
    $("#TrichYeu_OCR").val("");
    $("#ocrImagePreview").empty();
    resetFileInputV2("dinhKemOCR");
}
$(document).on("click", "#btnThemMoiOCR", async function () {
    if (!QuyenThem()) return;

    modeThemMoiOCR = 0;
    tabGridHocSinhChiTietOCR.setData([]);
    resetMdOCR();

    // 1) load your Select2s, then wait for them…
    await loadComboOCR();

    // 2) load the “Trường dữ liệu” dropdown before enrichment
    loadAllTruongDuLieu();

    // 3) kick off the grid‑columns for whatever KyThi is selected by default
    const defaultKyThi = $("#KyThi_OCR").val();
    if (defaultKyThi) {
        const cols = await TabulatorColumnsByKyThi(defaultKyThi);
        tabGridHocSinhChiTietOCR.setColumns(cols);
        // and re‑enrich the Trường combo with new subject options:
        loadAllTruongDuLieu();
    }

    readMode = "line";
    activateGroup("#btnReadLine, #btnReadParagraph", "#btnReadLine");
    $("#mdTrichXuatOCR").show();
});

$(document).on("click", ".btnOCR", async function () {
    if (!QuyenSua()) return;
    modeThemMoiOCR = 1;
    tabGridHocSinhChiTietOCR.setData([]); //Reset
    selectedId = $(this).data("id");
    if (!selectedId) return;
    resetMdOCR();

    // 2) Show the detail modal (or panel)
    $("#mdTrichXuatOCR").show();

    loadAllTruongDuLieu();
    loadComboOCR().then(() => {
        NTS.getAjaxAPIAsync("GET", Laravel.loadDuLieuSua, {
            id: selectedId,
        })
            .then(async (BangDiemInfo) => {
                if (BangDiemInfo.Err) {
                    NTS.loi(BangDiemInfo.Msg || "Lỗi tải dữ liệu");
                    throw new Error("Fetch failed");
                }
                const data = BangDiemInfo.Result;
                // 4) Populate simple inputs
                $("#SoQuyetDinh_OCR").val(data.SoQuyetDinh || "");
                $("#NguoiKy_OCR").val(data.NguoiKy || "");
                $("#CoQuanBanHanh_OCR").val(data.CoQuanBanHanh || "");
                $("#NgayKy_OCR").val(data.NgayKy || "");
                $("#TrichYeu_OCR").val(data.TrichYeu || "");

                // 5) Populate Select2s (must trigger change for Select2 to render)

                $("#ChucVu_OCR").value(data.ChucVuID_NK);
                $("#KyThi_OCR").value(data.KyThiID);
                $("#HinhThucDaoTao_OCR").value(data.HinhThucID);
                $("#NamHoc_OCR").value(data.NamHoc);
                $("#HoiDong_OCR").value(data.HoiDongID);

                const uploader = window.fileUploaderInstances["dinhKemOCR"];
                if (uploader && Array.isArray(data.FileDinhKemOCR)) {
                    // 1) clear out any old state
                    uploader.clear();

                    // 2) restore the URLs
                    uploader.urls = data.FileDinhKemOCR.slice();

                    // 3) re‑build real File objects by fetching each URL
                    uploader.files = await Promise.all(
                        data.FileDinhKemOCR.map(async (url) => {
                            const res = await fetch(url);
                            const blob = await res.blob();
                            const name = url.split("/").pop();
                            return new File([blob], name, { type: blob.type });
                        })
                    );

                    // 4) redraw thumbnails & emit complete
                    uploader._renderPreviews();
                    uploader._emitComplete();
                }

                return TabulatorColumnsByKyThi($("#KyThi_OCR").val());

                // 6) Rebuild your grid columns & clear rows
            })
            .then((cols) => {
                tabGridHocSinhChiTietOCR.setColumns(cols);
                tabGridHocSinhChiTietOCR.setData([]); // clear any old rows

                // reset read‐mode UI
                readMode = "line";
                activateGroup(
                    "#btnReadLine, #btnReadParagraph",
                    "#btnReadLine"
                );
            })
            .catch((err) => {
                // you can choose to hide modal on error, or just log
                console.error("Error initializing OCR modal:", err);
            });
    });
});

async function loadComboOCR() {
    const combos = [
        {
            name: "#ChucVu_OCR",
            ajaxUrl: Laravel.getListChucVu,
            ...commonComboConfig(),
        },
        {
            name: "#KyThi_OCR",
            ajaxUrl: Laravel.getListKyThi,
            ...commonComboConfig(),
        },
        {
            name: "#HinhThucDaoTao_OCR",
            ajaxUrl: Laravel.getListHTDT,
            ...commonComboConfig(),
        },
        {
            name: "#HoiDong_OCR",
            ajaxUrl: Laravel.getListHoiDong,
            ...commonComboConfig(),
        },
        {
            name: "#NamHoc_OCR",
            ajaxUrl: Laravel.getListNamHoc,
            columns: 1, // 1‑column combo
            indexValue: 0,
            indexText: 1,
            ...commonComboConfig(),
        },
    ];
    Promise.all(combos.map((cfg) => loadDataComboLookups(cfg, "GET")));
    return loadDataCombos(combos);
}

//#endregion
//#region Đính kèm
// **CRUCIAL**: when the user picks a file via your partial uploader…
function getCurrentImageObj() {
    if (selectedImageIndex === null) return null;
    return images[selectedImageIndex];
}

function addImageToPreview(url) {
    const container = document.createElement("div");
    container.classList.add("ocr-image-container");
    container.style.position = "relative";
    container.style.width = "100%";
    container.style.marginBottom = "1rem";

    const img = new Image();
    img.src = url;
    img.style.width = "100%";
    img.style.height = "auto";
    img.style.display = "block";

    container.appendChild(img);

    const imageObj = {
        imageElement: img,
        boxElements: [],
        container,
        resizeObserver: new ResizeObserver(() => {
            imageObj.boxElements.forEach((box) => updateBoxPosition(box, img));
        }),
    };
    images.push(imageObj);

    img.onload = () => {
        imageObj.resizeObserver.observe(img);
    };

    return imageObj;
}

document
    .getElementById("dinhKemOCR_dropArea")
    .addEventListener("fileUploaderUploadComplete", (e) => {
        const { urls, files } = e.detail;
        images = [];
        currentPage = 1;

        urls.forEach((url, i) => {
            const imageObj = addImageToPreview(url);
            imageObj.file = files[i];
        });

        renderImages();
    });
//#endregion

// and when they click your “Trích xuất dữ liệu (OCR)” button…
//#region Đọc OCR

$(document).on("click", "#btnTrichXuatOCR", async function () {
    tabGridHocSinhChiTietOCR.setData([]);
    const uploader = window.fileUploaderInstances["dinhKemOCR"];
    const allFiles = uploader.files; // Array of File objects
    const allBoxes = images
        .map((imgObj, idx) => ({
            file: imgObj.file,
            boxes: getBoxesForSend(idx),
            index: idx,
        }))
        .filter((item) => item.boxes.length > 0);

    if (!allFiles.length) {
        return NTS.canhbao("Chọn ảnh để trích xuất dữ liệu");
    }

    NTS.loadding();
    try {
        // pass ALL your images + box-coords to scanImage
        await Promise.all(allBoxes.map(scanImage));
        NTS.thanhcong("Hoàn tất trích xuất tất cả ảnh");
    } catch (err) {
        NTS.canhbao(err.message);
    } finally {
        NTS.unloadding();
    }
});

async function detectTextParagraph(file, boxes = null) {
    const fd = new FormData();
    fd.append("file", file);
    if (boxes) {
        fd.append("boxes", JSON.stringify(boxes));
    }
    fd.append("readParagraph", readMode === "paragraph");

    NTS.loadding();
    const res = await fetch(Laravel.scanOCR, {
        method: "POST",
        body: fd,
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    NTS.unloadding();

    if (!res.ok) {
        const text = await res.text().catch(() => "");
        return alert("Text detection failed: " + text);
    }

    // 1) parse the array of { box, lines }
    const blocks = await res.json();
    const gridLines = {}; // will hold: { SBD: [...], HoVaTen: [...], … }

    // 1) Distribute OCR output into grid vs inputs
    const currentBoxes =
        selectedImageIndex !== null
            ? images[selectedImageIndex].boxElements
            : [];

    blocks.forEach((blk, idx) => {
        const boxEl = currentBoxes[idx];
        if (!boxEl?.assignedField) return;

        const cfg = mapTruongDuLieu[boxEl.assignedField];
        if (!cfg) return;

        const lines = blk.lines.map((l) => l.trim());

        if (cfg.Type === "Grid") {
            // store each column’s lines array
            gridLines[cfg.Col] = lines;
        } else {
            // TextInput or SelectInput
            const text = lines.join(" ");
            setValueFromOCR($(cfg.ID), text);
        }
    });

    // 2) If we saw any grid columns, build & set table data
    const cols = Object.keys(gridLines);
    if (!cols.length) return blocks;

    const rowCount = Math.max(...cols.map((c) => gridLines[c].length));
    const flatData = [];
    for (let i = 0; i < rowCount; i++) {
        const row = {};
        cols.forEach((col) => {
            row[col] = gridLines[col][i] || "";
        });
        flatData.push(row);
    }

    console.log("flatData:", flatData);
    debugger;
    // 3) transform to nested
    const nestedData = flatData.map((flat) => {
        const nested = {};
        Object.entries(flat).forEach(([key, val]) => {
            if (key.includes(".")) {
                const [parent, child] = key.split(".");
                nested[parent] = nested[parent] || {};
                nested[parent][child] = val;
            } else {
                nested[key] = val;
            }
        });
        return nested;
    });

    console.log("nestedData:", nestedData);

    // 4) feed it to Tabulator
    tabGridHocSinhChiTietOCR.addData(nested);

    return blocks;
}
//#endregion

//#region Lưu dữ liệu
$(document).on("click", "#btnLuuVaDongOCR", async function () {
    const validate = new NTSValidate("#mdTrichXuatOCR");
    if (!validate.trim().check()) return;
    // 1) Collect the main form fields
    const payload = {
        SoQuyetDinh: $("#SoQuyetDinh_OCR").val().trim(),
        NgayKy: $("#NgayKy_OCR").val().trim(),
        NguoiKy: $("#NguoiKy_OCR").val().trim(),
        ChucVu: $("#ChucVu_OCR").val(),
        KyThi: $("#KyThi_OCR").val(),
        HinhThucDaoTao: $("#HinhThucDaoTao_OCR").val(),
        NamHoc: $("#NamHoc_OCR").val(),
        HoiDong: $("#HoiDong_OCR").val(),
        CoQuanBanHanh: $("#CoQuanBanHanh_OCR").val().trim(),
        TrichYeu: $("#TrichYeu_OCR").val().trim(),
    };
    const uploader = window.fileUploaderInstances["dinhKemOCR"];
    payload.DinhKem = uploader?.urls;

    // 2) If editing, include the BangDiemID so the PUT knows which record
    if (modeThemMoiOCR === 1) {
        payload.BangDiemID = selectedId; // assume selectedId was set when you opened in edit mode
    }

    // 3) Shape the grid data into exactly the keys your endpoint wants
    const slugMap = window.monThiSlugToIdMap || {};
    const rawRows = tabGridHocSinhChiTietOCR.getData();
    payload.DanhSachHocSinh = rawRows.map((row) => {
        const out = { ...row };

        if (out.DiemMonThi) {
            const byId = {};
            Object.entries(out.DiemMonThi).forEach(([slug, val]) => {
                const id = slugMap[slug];
                if (id != null) byId[id] = val;
            });
            out.DiemMonThi = byId;
        }

        if (out.DiemCuoiCap) {
            const byId = {};
            Object.entries(out.DiemCuoiCap).forEach(([slug, val]) => {
                const id = slugMap[slug];
                if (id != null) byId[id] = val;
            });
            out.DiemCuoiCap = byId;
        }

        return out;
    });

    console.log("Payload ready to send:", payload);

    // 4) Send it
    try {
        NTS.loadding();
        const method = modeThemMoiOCR === 0 ? "POST" : "PUT";
        const res = await NTS.getAjaxAPIAsync(
            method,
            Laravel.luuThongTinOCR,
            payload
        );
        NTS.unloadding();

        if (res.Err) {
            return NTS.canhbao(res.Msg || "Lỗi khi lưu dữ liệu OCR");
        } else {
            NTS.thanhcong("Lưu thông tin thành công");
            SetMainGridData();
        }

        // success!
        // $("#mdTrichXuatOCR").hide();
        // reload your parent list/table here…
    } catch (err) {
        NTS.unloadding();
        NTS.loi("Không thể lưu dữ liệu: " + err.message);
    }
});
//#endregion

//#region Helpers
/**
 * Returns an array of { file, boxes } for every image.
 */
function collectAllBoxes() {
    return images.map((imgObj, idx) => ({
        file: imgObj.file, // your stored File
        boxes: getBoxesForSend(idx), // pixel coords of drawn boxes
        index: idx, // to know which image later
    }));
}

async function scanImage({ file, boxes, index }) {
    const fd = new FormData();
    fd.append("file", file);
    const coordsOnly = boxes.map((b) => b.box);
    const fields = boxes.map((b) => b.field);
    if (boxes) fd.append("boxes", JSON.stringify(coordsOnly));
    fd.append("readParagraph", readMode === "paragraph");

    const res = await fetch(Laravel.scanOCR, {
        method: "POST",
        body: fd,
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    debugger;
    if (!res.ok) {
        const txt = await res.text().catch(() => "");
        throw new Error(`Scan failed for image #${index}: ${txt}`);
    }

    const blocks = await res.json();

    // re‐draw the boxes returned by the OCR engine,
    // so you never lose your overlays after upload/reset
    drawBoxesForImage(
        index,
        blocks.map((b) => b.box)
    );

    // grab the imageObj we just drew into:
    const imageObj = images[index];

    // boxes is your original [ { field, box }, … ]
    boxes.forEach(({ field }, i) => {
        const el = imageObj.boxElements[i];
        if (el) el.assignedField = field;
    });

    // distribute each block’s text into your grid / inputs
    // temporarily switch selectedImageIndex to `index`
    const prev = selectedImageIndex;
    selectedImageIndex = index;
    distributeOCR(blocks, index);
    selectedImageIndex = prev;

    return blocks;
}

function distributeOCR(blocks, imageIndex) {
    debugger;
    const gridLines = {};
    const boxEls = images[imageIndex].boxElements;

    blocks.forEach((blk, idx) => {
        const boxEl = boxEls[idx];

        if (!boxEl?.assignedField) return;

        const cfg = mapTruongDuLieu[boxEl.assignedField];

        if (!cfg) return;

        const lines = blk.lines.map((l) => l.trim());

        if (cfg.Type === "Grid") {
            // store each column’s lines array
            gridLines[cfg.Col] = lines;
            console.log("🗺️ distributing to fields:", cfg);
            console.log("🗺️ distributing to fields:", gridLines[cfg.Col]);
        } else {
            // TextInput or SelectInput
            const text = lines.join(" ");
            setValueFromOCR($(cfg.ID), text);
        }
    });
    // console.log(gridLines);
    const cols = Object.keys(gridLines);
    if (!cols.length) return;

    const rowCount = Math.max(...cols.map((c) => gridLines[c].length));
    const flat = [];
    for (let i = 0; i < rowCount; i++) {
        const row = {};
        cols.forEach((c) => (row[c] = gridLines[c][i] || ""));
        flat.push(row);
    }
    const nested = flat.map((row) => {
        const out = {};
        Object.entries(row).forEach(([k, v]) => {
            if (k.includes(".")) {
                const [p, c] = k.split(".");
                out[p] = out[p] || {};
                out[p][c] = v;
            } else {
                out[k] = v;
            }
        });
        return out;
    });
    tabGridHocSinhChiTietOCR.addData(nested);
}

function updateSelectedImageVisual() {
    images.forEach((imgObj, idx) => {
        if (idx === selectedImageIndex) {
            imgObj.container.classList.add("selected");
        } else {
            imgObj.container.classList.remove("selected");
        }
    });
}
function createBox(x, y, w, h, imageElement) {
    const box = document.createElement("div");
    box.classList.add("box");

    const natW = imageElement.naturalWidth;
    const natH = imageElement.naturalHeight;

    box.ratioLeft = x / natW;
    box.ratioTop = y / natH;
    box.ratioWidth = w / natW;
    box.ratioHeight = h / natH;
    box.assignedField = null; // <-- initialize here

    updateBoxPosition(box, imageElement);
    attachBoxEventHandlers(box, imageElement);
    return box;
}
let _onMouseDown, _onMouseMove, _onMouseUp;

function bindDrawingEvents() {
    if (selectedImageIndex === null) return;
    const { container, imageElement: imgEl } = images[selectedImageIndex];

    // 1) ensure this container & img can receive pointer events
    container.style.pointerEvents = "auto";
    imgEl.style.pointerEvents = "auto";

    // 2) remove any old handlers
    if (_onMouseDown) {
        container.removeEventListener("mousedown", _onMouseDown);
        window.removeEventListener("mousemove", _onMouseMove);
        window.removeEventListener("mouseup", _onMouseUp);
    }

    // 3) define fresh handlers
    _onMouseDown = function onMouseDown(e) {
        if (mode !== "draw" || e.target !== imgEl) return;

        isDrawing = true;
        const rect = container.getBoundingClientRect();
        drawStart.x = e.clientX - rect.left;
        drawStart.y = e.clientY - rect.top;

        tempBox = document.createElement("div");
        tempBox.classList.add("box", "drawing");
        Object.assign(tempBox.style, {
            position: "absolute",
            left: `${drawStart.x}px`,
            top: `${drawStart.y}px`,
            width: "0px",
            height: "0px",
            border: "2px dashed #0d6efd",
        });
        container.appendChild(tempBox);

        // bind move/up now that drawing has started
        window.addEventListener("mousemove", _onMouseMove);
        window.addEventListener("mouseup", _onMouseUp);

        e.preventDefault();
    };

    _onMouseMove = function onMouseMove(e) {
        if (!isDrawing || !tempBox) return;
        const rect = container.getBoundingClientRect();
        const x = Math.min(Math.max(e.clientX - rect.left, 0), rect.width);
        const y = Math.min(Math.max(e.clientY - rect.top, 0), rect.height);
        const dx = x - drawStart.x,
            dy = y - drawStart.y;

        tempBox.style.left = `${dx < 0 ? x : drawStart.x}px`;
        tempBox.style.top = `${dy < 0 ? y : drawStart.y}px`;
        tempBox.style.width = `${Math.abs(dx)}px`;
        tempBox.style.height = `${Math.abs(dy)}px`;
    };

    _onMouseUp = function onMouseUp() {
        if (!isDrawing) return;
        isDrawing = false;
        // finalize tempBox here (e.g. remove “drawing” class, add to boxElements, etc.)
        if (tempBox) {
            tempBox.classList.remove("drawing");
            // wire up drag/resize on the just‐drawn box:
            attachBoxEventHandlers(tempBox, imgEl);
            images[selectedImageIndex].boxElements.push(tempBox);
            tempBox = null;
        }
        // unbind move/up
        window.removeEventListener("mousemove", _onMouseMove);
        window.removeEventListener("mouseup", _onMouseUp);
    };

    // 4) finally attach the down listener
    container.addEventListener("mousedown", _onMouseDown);
}
function selectImage(index) {
    selectedImageIndex = index;
    updateSelectedImageVisual();
    bindDrawingEvents();
}

/* Update ratio properties after dragging or resizing a box. */
function updateRatios(box, imageElement) {
    const w = imageElement.clientWidth;
    const h = imageElement.clientHeight;
    box.ratioLeft = box.offsetLeft / w;
    box.ratioTop = box.offsetTop / h;
    box.ratioWidth = box.clientWidth / w;
    box.ratioHeight = box.clientHeight / h;
}
// Call this function whenever you change selectedImageIndex or after loading images
function updateDrawingBindings() {
    $(window).off(".drawing");
    if (selectedImageIndex !== null) {
        bindDrawingEvents();
    }
}

let copiedBoxRatios = null;

// Utility: position a box DIV over the <img>
function updateBoxPosition(box, imageElement) {
    const w = imageElement.clientWidth,
        h = imageElement.clientHeight;
    box.style.left = box.ratioLeft * w + "px";
    box.style.top = box.ratioTop * h + "px";
    box.style.width = box.ratioWidth * w + "px";
    box.style.height = box.ratioHeight * h + "px";
}

async function loadAllTruongDuLieu() {
    loadDataCombos([
        // 1. Cap hoc
        {
            name: "#fieldSelector",
            ajaxUrl: Laravel.getTruongDuLieu,
            columns: 1, // indexValue=0, indexText=1
            ajaxParam: {
                Models: ["QuanLy\\BangDiem", "QuanLy\\BangDiem_ChiTiet"],
            },
            indexValue: 0,
            indexText: 3,
            // indexText1: 3,
            textShowTatCa: "-chọn-",
            showTatCa: true,
            columnSizes: [4, 8], // ← will render `<div class="col-6">…</div>`
        },
    ]);
}
$(document).on("change", "#KyThi_OCR", async function () {
    const kyThiId = $(this).val();
    if (!kyThiId) return;

    // 1) rebuild the Tabulator columns (and let it re‑enrich mapTruongDuLieu internally)
    const cols = await TabulatorColumnsByKyThi(kyThiId);
    tabGridHocSinhChiTietOCR.setColumns(cols);

    // 2) reload the “Trường dữ liệu” combo so enrichComboWithSubjects can inject the new DiemMonThi / DiemCuoiCap options
    loadAllTruongDuLieu();

    try {
        const res = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.getMonThiFromKyThi,
            { kyThiId }
        );
        if (!res.Err && Array.isArray(res.Result) && res.Result.length) {
            window.monThiSlugToIdMap = {};
            res.Result.forEach((mon) => {
                const slug = slugify(mon.tenMonHoc);
                // mon.MonThiID or mon.id depending on your API
                window.monThiSlugToIdMap[slug] = mon.MonThiID || mon.id;
            });

            diemMonThiCols = res.Result.map((mon) => ({
                title: mon.tenMonHoc,

                field: `DiemMonThi.${slugify(mon.tenMonHoc)}`,
                hozAlign: "center",
                formatter: "number",
                editor: "number",
                editorParams: {
                    step: 0.5, // allows two decimal places
                },
            }));
            diemCuoiCapCols = res.Result.map((mon) => ({
                title: mon.tenMonHoc,

                field: `DiemCuoiCap.${slugify(mon.tenMonHoc)}`,
                hozAlign: "center",
                formatter: "number",
                editor: "number",
                editorParams: {
                    step: 0.5, // allows two decimal places
                },
            }));

            const monThis =
                !res.Err && Array.isArray(res.Result) ? res.Result : [];
            enrichComboWithSubjects($fieldSel, monThis, mapTruongDuLieu);
        } else {
            // warn & leave dummy col
            NTS.canhbao(res.Err ? res.Msg : "Kỳ thi này chưa có môn thi nào.");
        }
    } catch (e) {
        NTS.canhbao("Lỗi khi lấy môn thi: " + e.message);
    }
});

function attachBoxEventHandlers(box, imageElement) {
    let dragging = false;
    let resizing = false;
    let offsetX, offsetY;
    let startX, startY, startW, startH;
    $(document).on("click", ".box", function (e) {
        // don’t fire when you’re finishing a drag or resize
        if (isDrawing || dragging || resizing) return;
        selectBox(this, e);
    });

    box.addEventListener("mousedown", (e) => {
        if (e.target.classList.contains("resize-handle")) return;
        dragging = true;
        offsetX = e.clientX - box.offsetLeft;
        offsetY = e.clientY - box.offsetTop;
        selectBox(box, e);
        e.preventDefault();
    });

    const handle = document.createElement("div");
    handle.classList.add("resize-handle");
    box.appendChild(handle);

    handle.addEventListener("mousedown", (e) => {
        resizing = true;
        startX = e.clientX;
        startY = e.clientY;
        startW = box.clientWidth;
        startH = box.clientHeight;
        selectBox(box, e);
        e.stopPropagation();
        e.preventDefault();
    });

    $window.on("mousemove.box", (e) => {
        if (dragging && selectedBox === box) {
            const imgW = imageElement.clientWidth;
            const imgH = imageElement.clientHeight;
            let nx = e.clientX - offsetX;
            let ny = e.clientY - offsetY;
            nx = Math.max(0, Math.min(nx, imgW - box.clientWidth));
            ny = Math.max(0, Math.min(ny, imgH - box.clientHeight));
            box.style.left = nx + "px";
            box.style.top = ny + "px";
            return;
        }
        if (resizing && selectedBox === box) {
            const imgW = imageElement.clientWidth;
            const imgH = imageElement.clientHeight;
            let newW = startW + (e.clientX - startX);
            let newH = startH + (e.clientY - startY);
            newW = Math.max(20, Math.min(newW, imgW - box.offsetLeft));
            newH = Math.max(20, Math.min(newH, imgH - box.offsetTop));
            box.style.width = newW + "px";
            box.style.height = newH + "px";
        }
    });

    $window.on("mouseup.box", () => {
        if (dragging) {
            dragging = false;
            updateRatios(box, imageElement);
        }
        if (resizing) {
            resizing = false;
            updateRatios(box, imageElement);
        }
    });
}
// remove all boxes
function clearBoxes() {
    if (selectedImageIndex === null) return;
    images[selectedImageIndex].boxElements.forEach((b) => b.remove());
    images[selectedImageIndex].boxElements = [];
}

function clearBoxesForImage(imageIndex) {
    if (!images[imageIndex]) return;
    images[imageIndex].boxElements.forEach((b) => b.remove());
    images[imageIndex].boxElements = [];
}

// draw many boxes from API result
function drawBoxes(detectedBoxes) {
    if (selectedImageIndex === null) return;
    clearBoxes();

    const imageObj = images[selectedImageIndex];
    const container = imageObj.container;
    const img = imageObj.imageElement;

    detectedBoxes.forEach((box) => {
        const xs = box.map((pt) => pt[0]);
        const ys = box.map((pt) => pt[1]);
        const left = Math.min(...xs);
        const right = Math.max(...xs);
        const top = Math.min(...ys);
        const bottom = Math.max(...ys);

        const el = createBox(left, top, right - left, bottom - top, img);
        container.appendChild(el);
        imageObj.boxElements.push(el);
    });
}

function drawBoxesForImage(imageIndex, detectedBoxes) {
    clearBoxesForImage(imageIndex);
    const imageObj = images[imageIndex];
    const container = imageObj.container;

    detectedBoxes.forEach((box) => {
        const xs = box.map((p) => p[0]),
            ys = box.map((p) => p[1]);
        const left = Math.min(...xs),
            right = Math.max(...xs),
            top = Math.min(...ys),
            bottom = Math.max(...ys);
        const el = createBox(
            left,
            top,
            right - left,
            bottom - top,
            imageObj.imageElement
        );
        container.appendChild(el);
        imageObj.boxElements.push(el);
    });
}

// gather boxes (in natural‑pixel coords) to send back
function getBoxesForSend(imageIndex) {
    const imgObj = images[imageIndex];
    const natW = imgObj.imageElement.naturalWidth;
    const natH = imgObj.imageElement.naturalHeight;

    return imgObj.boxElements.map((b) => {
        const L = Math.round(b.ratioLeft * natW);
        const T = Math.round(b.ratioTop * natH);
        const R = Math.round((b.ratioLeft + b.ratioWidth) * natW);
        const B = Math.round((b.ratioTop + b.ratioHeight) * natH);
        return {
            field: b.assignedField,
            box: [
                [L, T],
                [R, T],
                [R, B],
                [L, B],
            ],
        };
    });
}

// text‐detection & recognition as before
// select/highlight a box
function selectBox(box, e) {
    const $wrap = $("#fieldSelectorContainer");

    // un‑highlight prev
    if (selectedBox) selectedBox.style.borderColor = "#dc3545";
    selectedBox = box;

    if (!box) {
        $wrap.hide();
        return;
    }

    // highlight new
    box.style.borderColor = "#0d6efd";

    // MUST have the event
    if (!e || e.pageX == null) {
        console.warn(
            "selectBox called without event – dropdown may mis‑position"
        );
    }
    console.log(e.pageX);
    console.log(e.pageY);

    // position at the click
    $wrap
        .css({
            top: e.pageY + 4 + "px",
            left: e.pageX + "px",
        })
        .show();

    // restore previous value & open
    const val = box.assignedField || null;
    $fieldSel.val(val).trigger("change");
}

// clear selection on container click
$("#ocrImagePreview").on("click", function (e) {
    // e.target === the EXACT element clicked
    // e.currentTarget === #ocrImagePreview
    if (e.target === this) {
        selectBox(null);
    }
});
//#endregion

//#region Shortcut THAO TÁC VẼ TRƯỜNG DỮ LIỆU
// delete key support
window.addEventListener("keydown", (e) => {
    if (!$("#mdTrichXuatOCR").is(":visible")) return;
    if (selectedImageIndex === null) return;

    if ((e.ctrlKey || e.metaKey) && (e.key === "c" || e.key === "C")) {
        if (selectedBox) {
            // store normalized coords
            copiedBoxRatios = {
                left: selectedBox.ratioLeft,
                top: selectedBox.ratioTop,
                width: selectedBox.ratioWidth,
                height: selectedBox.ratioHeight,
            };
        }
        e.preventDefault();
    }

    // Paste (Ctrl+V)
    if ((e.ctrlKey || e.metaKey) && (e.key === "v" || e.key === "V")) {
        if (copiedBoxRatios && selectedImageIndex !== null) {
            const imgObj = images[selectedImageIndex];
            if (!imgObj) return;

            const natW = imgObj.imageElement.naturalWidth,
                natH = imgObj.imageElement.naturalHeight;

            // re-compute pixel coords
            const x = copiedBoxRatios.left * natW,
                y = copiedBoxRatios.top * natH,
                w = copiedBoxRatios.width * natW,
                h = copiedBoxRatios.height * natH;

            // create & append the new box
            const newBox = createBox(x, y, w, h, imgObj.imageElement);
            newBox.assignedField = null;
            imgObj.container.appendChild(newBox);
            imgObj.boxElements.push(newBox);

            // select the pasted box
            selectBox(newBox);
        }
        e.preventDefault();
    }

    if ((e.key === "Delete" || e.key === "Backspace") && selectedBox) {
        const active = document.activeElement;
        const tag = active.tagName.toLowerCase();
        if (tag === "input" || tag === "textarea" || active.isContentEditable) {
            return; // typing in a field → don’t delete the box
        }

        selectedBox.remove();

        // remove from current image's boxElements array
        images[selectedImageIndex].boxElements = images[
            selectedImageIndex
        ].boxElements.filter((b) => b !== selectedBox);
        selectedBox = null;
    }
});

let mode = "draw"; // current tool
let readMode = "line"; // 'line' or 'paragraph'

function activate(btn) {
    $("#ocrToolbar .btn").removeClass("active");
    $(btn).addClass("active");
}

$("#btnDraw").on("click", function () {
    mode = "draw";
    activate(this);
});

$("#btnSelect").on("click", function () {
    mode = "select";
    activate(this);
});

$("#btnCopy").on("click", () => {
    if (selectedBox) {
        copiedBoxRatios = {
            left: selectedBox.ratioLeft,
            top: selectedBox.ratioTop,
            width: selectedBox.ratioWidth,
            height: selectedBox.ratioHeight,
        };
    }
});
$("#btnPaste").on("click", () => {
    if (!copiedBoxRatios || selectedImageIndex === null) return;

    const imgObj = images[selectedImageIndex];
    if (!imgObj) return;

    const natW = imgObj.imageElement.naturalWidth,
        natH = imgObj.imageElement.naturalHeight;

    const x = copiedBoxRatios.left * natW,
        y = copiedBoxRatios.top * natH,
        w = copiedBoxRatios.width * natW,
        h = copiedBoxRatios.height * natH;

    const newBox = createBox(x, y, w, h, imgObj.imageElement);
    newBox.assignedField = null;

    imgObj.container.appendChild(newBox);
    imgObj.boxElements.push(newBox);
    selectBox(newBox);
});
$("#btnDelete").on("click", () => {
    if (selectedBox && selectedImageIndex !== null) {
        selectedBox.remove();
        images[selectedImageIndex].boxElements = images[
            selectedImageIndex
        ].boxElements.filter((b) => b !== selectedBox);
        selectedBox = null;
    }
});

// ─── Resize temp box as mouse moves ──────────────────────────────────────

$(document).on("click", "#btnDeleteAll", function (e) {
    e.preventDefault();
    if (selectedImageIndex === null) return;

    clearBoxes(); // removes all .box DIVs
    // reset the array (clearBoxes already does this)
    // images[selectedImageIndex].boxElements = [];

    // clear the UI highlight on any selected box
    selectBox(null);
});

// Hiển thị btn đang được kích hoạt
function activateGroup(selector, btnId) {
    $(selector).removeClass("active");
    $(btnId).addClass("active");
}
//#endregion
// Đọc dòng vs đoạn

//#region Helper khi đọc OCR
$("#btnReadLine").on("click", () => {
    readMode = "line";
    activateGroup("#btnReadLine, #btnReadParagraph", "#btnReadLine");
});
$("#btnReadParagraph").on("click", () => {
    readMode = "paragraph";
    activateGroup("#btnReadLine, #btnReadParagraph", "#btnReadParagraph");
});

$(document).on("click", function (e) {
    if (!$(e.target).closest(".box, #fieldSelectorContainer").length) {
        selectedBox && (selectedBox.style.borderColor = "#dc3545");
        selectedBox = null;
        $("#fieldSelectorContainer").hide();
    }
});
// helper: set an element's value by matching OCR text to option labels or input values
function setValueFromOCR($el, text) {
    const t = text.trim();

    // 1) <select> (including Select2)
    if ($el.is("select")) {
        const selId = $el.attr("id");
        const lookup = window.selectLookup?.[selId] || {};

        // 1a) try direct lookup by the “name” column
        if (lookup[t]) {
            $el.val(lookup[t]).trigger("change");
            return;
        }

        // 1b) fallback: plain‑option match if you still need it
        const $opt = $el
            .find("option")
            .filter(function () {
                return $(this).text().trim() === t;
            })
            .first();
        if ($opt.length) {
            $el.val($opt.val()).trigger("change");
            return;
        }
        NTS.canhbao(`Chưa có dữ liệu "${t}" trong hệ thống`);

        console.warn(`No matching option for "${t}" in #${selId}`);
        return;
    }

    // 2) checkbox/radio “checklist” container
    // e.g. <div id="fooChecklist"> <label><input type="checkbox" value="ID1"> Foo</label> …</div>
    if (
        $el.is("div") &&
        $el.find("input[type=checkbox], input[type=radio]").length
    ) {
        // find the input whose adjacent label text matches
        const $input = $el
            .find("label")
            .filter(function () {
                return $(this).text().trim() === t;
            })
            .find("input")
            .first();

        if ($input.length) {
            $input.prop("checked", true).trigger("change");
        } else {
            console.warn(
                `OCR text "${t}" did not match any checklist labels in`,
                $el
            );
        }
        return;
    }

    // 3) all other inputs/textareas
    $el.val(t);
}
//#endregion

$(document).on("click", ".ocr-image-container", function () {
    const idx = images.findIndex((imgObj) => imgObj.container === this);
    if (idx !== -1) {
        selectImage(idx);
    }
});

//#region Chuyen trang hinh anh
let imagesPerPage = 1;
let currentPage = 1;

$(function () {
    const $pageSelect = $("#pageSelect");

    $pageSelect
        .select2({
            width: "75%",
            minimumResultsForSearch: Infinity, // no search box
            dropdownCssClass: "page-select-dropdown",
        })
        .on("change", function () {
            currentPage = parseInt($(this).val(), 10) || 1;
            renderImages();
        });
});
function renderImages() {
    const $container = $("#ocrImagePreview").empty();
    const total = images.length;
    const totalPages = Math.ceil(total / imagesPerPage);
    const $select = $("#pageSelect");

    // clear out old options
    $select.empty();

    if (totalPages <= 0) {
        // no pages
        $select
            .append(new Option("Trang 0/0", 0, false, false))
            .prop("disabled", true)
            .trigger("change.select2");
        return;
    }

    // make sure user can pick now
    $select.prop("disabled", false);

    // add one <option> per page
    for (let p = 1; p <= totalPages; p++) {
        // text = “Trang X/Y”, value = p, selected if p===currentPage
        const text = `Trang ${p}/${totalPages}`;
        const opt = new Option(text, p, p === currentPage, p === currentPage);
        $select.append(opt);
    }

    // tell Select2 to re‑read the <option>s and update the UI
    $select.trigger("change.select2");

    // clamp currentPage
    currentPage = Math.min(Math.max(currentPage, 1), totalPages);

    // now render only the images for this page
    const start = (currentPage - 1) * imagesPerPage;
    const end = Math.min(start + imagesPerPage, total);
    for (let i = start; i < end; i++) {
        $container.append(images[i].container);
    }

    // highlight & bind drawing to the first image of this page
    selectImage(start);
}

// wire up change on the dropdown
$("#pageSelect").on("change", function () {
    currentPage = parseInt(this.value, 10) || 1;
    renderImages();
});

$("#dinhKemOCR_list").on("click", ".file-preview", function () {
    // get zero-based index of clicked preview
    const idx = $(this).index();
    // set currentPage (1-based)
    currentPage = idx + 1;
    renderImages();
});
//#endregion

//#region Merge hàng

function requireTwoSelected() {
    const sel = tabGridHocSinhChiTietOCR.getSelectedRows();
    if (sel.length !== 2) {
        return NTS.canhbao(
            "Vui lòng chọn đúng 2 dòng để thực hiện thao tác này."
        );
    }
    return sel;
}

// 1) Merge by concatenating *all* non-empty cells
document.getElementById("btnMergeRows").addEventListener("click", () => {
    const rows = requireTwoSelected();
    if (!rows) return;

    const merged = {};
    tabGridHocSinhChiTietOCR.getColumns().forEach((col) => {
        const field = col.getField();
        if (!field) return; // skip non-data columns
        // grab both values, filter out blanks, join with a space
        const values = rows
            .map((r) => r.getData()[field] || "")
            .filter((v) => v.trim());
        merged[field] = values.join(" ").trim();
    });

    // remove originals, add the merged row
    rows.forEach((r) => r.delete());
    tabGridHocSinhChiTietOCR.addRow(merged, true).then((r) => r.select());
});

// 2) Merge by picking the non-blank cell “from the other row”
document.getElementById("btnMergeMissingData").addEventListener("click", () => {
    const rows = requireTwoSelected();
    if (!rows) return;

    // list of real data-fields
    const fields = tabGridHocSinhChiTietOCR
        .getColumns()
        .map((c) => c.getField())
        .filter((f) => f);

    const [a, b] = rows.map((r) => r.getData());
    const merged = {};

    fields.forEach((f) => {
        const v1 = (a[f] || "").toString().trim();
        const v2 = (b[f] || "").toString().trim();
        // if a has text keep it, otherwise take b
        merged[f] = v1 !== "" ? v1 : v2;
    });

    rows.forEach((r) => r.delete());
    tabGridHocSinhChiTietOCR.addRow(merged, true).then((r) => r.select());
});
//#endregion

//#region zoom
let zoomLevel = 1;

// apply `scale(zoomLevel)` to every .ocr-image-container
function applyZoom() {
    images.forEach((imgObj) => {
        imgObj.container.style.transform = `scale(${zoomLevel})`;
    });
}

// zoom in by +20%, up to 300%
$("#btnZoomIn").on("click", () => {
    zoomLevel = Math.min(zoomLevel + 0.2, 3);
    applyZoom();
});

// zoom out by –20%, down to 20%
$("#btnZoomOut").on("click", () => {
    zoomLevel = Math.max(zoomLevel - 0.2, 0.2);
    applyZoom();
});

// if you re‑render images (e.g. page change), re‑apply the current zoom:
// const originalRenderImages = renderImages;
// renderImages = function () {
//     originalRenderImages();
//     applyZoom();
// };
//#endregion
