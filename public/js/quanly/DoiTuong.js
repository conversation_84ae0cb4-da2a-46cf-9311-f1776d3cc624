var tempthem = "them";
var ChuaCoThongTin = "";
///////// PHÍM TẮT /////////
var hotKey = 0; // 1 thêm\
var currentAvatarPath = "";
$(document).on("keydown", function (e) {
    switch (e.keyCode) {
        case 113:
            if (hotKey == 0) $("#btnThemMoi").trigger("click");
            e.preventDefault();
            break;
        case 114:
            if (hotKey == 0) $(".nav-search-input").focus();
            e.preventDefault();
            break;
        case 115:
            if (hotKey == 1) $("#mdThemMoi").modal("hide");
            e.preventDefault();
            break;
        case 120:
            if (hotKey == 1) $("#btnLuuVaDong").trigger("click");
            e.preventDefault();
            break;
    }
});
$(document).on("shown.bs.modal", "#mdThemMoi", function () {
    hotKey = 1;
});
$(document).on("hidden.bs.modal", "#mdThemMoi", function () {
    hotKey = 0;
});

//Thống kê
$(document).on("click", "#btnAnHienTQ", function () {
    var divThongKe = document.getElementById("DivThongKe");
    var textAnHien = document.getElementById("textAnHien");
    var iconAnHien = document.getElementById("iconAnHien");
    if (iconAnHien.className == "fa fa-eye-slash") {
        divThongKe.classList.add("d-none");
        iconAnHien.className = "fa fa-eye";
        textAnHien.textContent = " Hiện trang tổng quan";
    } else {
        divThongKe.classList.remove("d-none");
        iconAnHien.className = "fa fa-eye-slash";
        textAnHien.textContent = " Ẩn trang tổng quan";
    }
});
checkDangSD(".checkDangSD", "hoc_sinhs", "TrangThai");
$(document).on("click", "#TimKiem", async function () {
    $("#KhungTimKiem").slideUp(200);
    await LoadDataTable();
    return false;
});
$(document).on("keyup", "#SearchKey", async function (e) {
    if (e.keyCode == "13") {
        await LoadDataTable();
        $("#KhungTimKiem").slideUp(200);
        return false;
    }
});
$(document).on("input", "#Hovaten", function () {
    $("#lblHovaten").text($(this).val());
});
$(document).on("input", "#MaDoiTuong", function () {
    $("#lblMaHocSinh").text($(this).val());
});
$(document).on("input", "#DiaChi", function () {
    $("#lblDiaChi").text($(this).val());
});
$(document).on("input", "#Noisinh", function () {
    $("#lblNoiSinh").text($(this).val());
});
$(document).on("change", "#Gioitinh", function () {
    $("#lblGioiTinh").text(
        $("#select2-Gioitinh-container").text() == "-Chọn-"
            ? "..."
            : $("#select2-Gioitinh-container").text()
    );
});
$(document).on("change", "#DanTocID", function () {
    $("#lblDanToc").text(
        $("#select2-DanTocID-container").text() == "-Chọn-"
            ? "..."
            : $("#select2-DanTocID-container").text()
    );
});
$(document).on("change", "#Ngaysinh", function () {
    $("#lblNgaySinh").text($("#Ngaysinh").value());
});

$(document).on("change", "#CbSapXep", function () {
    LoadDataTable();
});

$(document).on("click", "#TimKiemNangCao", function () {
    if ($("#KhungTimKiem").css("display") == "block") {
        $("#KhungTimKiem").slideUp(200);
    } else {
        $("#KhungTimKiem").slideDown(200);
    }
    return false;
});
$(document).on("click", "#DongTimKiem", function () {
    $("#KhungTimKiem").slideUp(200);
    return false;
});
$(document).on("click", "#btn-layout-1", async function () {
    $("#grid-layout").fadeIn(200);
    $("#list-layout").hide();
    $("#list-layout").removeClass("show");
    $("#grid-layout").addClass("show");
    await LoadDataTable();
});
$(document).on("click", "#btn-layout-2", async function () {
    $("#grid-layout").hide();
    $("#list-layout").fadeIn(200);
    $("#list-layout").addClass("show");
    $("#grid-layout").removeClass("show");
    $("#DoiTuongID").value("");
    $(".divThaoTacNhanh").hide();
    $("#txtMaHocSinh_View").html("Mã học sinh: <b>---</b>");
    $("#txtTenHocSinh_View").html("Tên học sinh: <b>---</b>");
    $("#lblDiaChi_View").text("--");
    await LoadDataTable2();
});
//load dữ liệu tìm kiếm khi người dùng vào trang
function LoadCombo_Loc() {
    NTS.loadDataComboAsync({
        name: "#TinhID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDiaBanHC_Tinh,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#XaID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDiaBanHC_ByIDCha,
        ajaxParam: { DiaBanHCID_Cha: "", LoaiDiaBan: "02" },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#ThonID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDiaBanHC_ByIDCha,
        ajaxParam: { DiaBanHCID_Cha: "", LoaiDiaBan: "03" },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#DonViID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDonViAll,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
    //==//
    NTS.loadDataComboAsync({
        name: "#DanTocID",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDanToc,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#DiaBanHCID_NoiCap",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDiaBanHC_NoiCap,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#DiaBanHCID_Tinh",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDiaBanHC_Tinh,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#DiaBanHCID_Xa",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDiaBanHC_ByIDCha,
        ajaxParam: { DiaBanHCID_Cha: "", LoaiDiaBan: "02" },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#DiaBanHCID_Thon",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDiaBanHC_ByIDCha,
        ajaxParam: { DiaBanHCID_Cha: "", LoaiDiaBan: "03" },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#DonViID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDonViAll,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#DienUuTienID",
        type: "GET",
        ajaxUrl: window.Laravel.DoiTuong.getListDienUuTien,
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: true,
    });
    NTS.loadDataComboAsync({
        name: "#LopHocID",
        type: "GET",
        ajaxUrl: window.Laravel.DoiTuong.getListLopHoc,
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: true,
    });
    NTS.loadDataComboAsync({
        name: "#TrangThaiXuLy_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.DoiTuong.getListTrangThai,
        ajaxParam: {},
        indexValue: 1,
        columns: 2,
        indexText: 1,
        indexText1: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: true,

    });
}

function setDiaChi() {
    $("#DiaChi").value(
        ($("#SoNha").value() == ""
            ? ""
            : $("#SoNha").value() + ", ") +
        ($("#select2-DiaBanHCID_Xa-container").text() == "-Chọn-"
            ? ""
            : $("#select2-DiaBanHCID_Xa-container").text() + ", ") +
        ($("#select2-DiaBanHCID_Tinh-container").text() == "-Chọn-"
            ? ""
            : $("#select2-DiaBanHCID_Tinh-container").text())
    );
    $("#lblDiaChi").text($("#DiaChi").value());
}

//#region Main
//#region Lưới 1
var table = new Tabulator("#Grid1", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "600",
    HeaderVertAlign: "center",
    headerVisible: false,
    columns: [
        {
            title: "Thông tin",
            field: "ThongTinHoGiaDinh",
            formatter: htmlDuLieu,
            visible: true,
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
var tableHS = new Tabulator("#tableHS", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "600",
    HeaderVertAlign: "center",
    headerVisible: false,
    columns: [
        {
            title: "Mã học sinh",
            field: "MaDoiTuong",
            width: 120,
            headerHozAlign: "center",
            HeaderVertAlign: "middle",
            vertAlign: "middle",
        },
        {
            title: "Họ và tên",
            field: "Hovaten",
            width: 150,
            formatter: "textarea",
            headerHozAlign: "center",
            HeaderVertAlign: "middle",
            vertAlign: "middle",
        },
        {
            title: "Số CMND/CCCD",
            field: "CCCD",
            width: 150,
            headerHozAlign: "center",
            HeaderVertAlign: "middle",
            vertAlign: "middle",
        },
        {
            title: "Ngày sinh",
            field: "txtNgaysinh",
            sorter: "date",
            hozAlign: "center",
            width: 120,
            formatter: "textarea",
            headerHozAlign: "center",
            HeaderVertAlign: "middle",
            vertAlign: "middle",
        },
        {
            title: "Giới tính",
            field: "txtGioitinh",
            hozAlign: "center",
            width: 100,
            headerHozAlign: "center",
            HeaderVertAlign: "middle",
            vertAlign: "middle",
        },
        {
            title: "Dân tộc",
            field: "TenDanToc", // Use nested related field here
            width: 120,
            headerHozAlign: "center",
            HeaderVertAlign: "middle",
            vertAlign: "middle",
        },
        {
            title: "Nơi sinh",
            field: "Noisinh",
            width: 150,
            headerHozAlign: "center",
            HeaderVertAlign: "middle",
            vertAlign: "middle",
        },
        {
            title: "Lớp học",
            field: "TenLopHoc", // Note: make sure API returns this field, or remove if not available
            width: 120,
            headerHozAlign: "center",
            HeaderVertAlign: "middle",
            vertAlign: "middle",
        },
        {
            title: "Địa chỉ",
            field: "DiaChi",
            width: 200,
            headerHozAlign: "center",
            HeaderVertAlign: "middle",
            vertAlign: "middle",
        },
        {
            title: "Đơn vị học",
            field: "TenDonVi", // nested related field
            width: 200,
            headerHozAlign: "center",
            HeaderVertAlign: "middle",
            vertAlign: "middle",
            formatter: function (cell) {
                return cell.getValue() || "";
            },
            visible: false,
        },
        {
            title: "Địa bàn tỉnh",
            field: "Tinh", // nested related field
            width: 200,
            headerHozAlign: "center",
            HeaderVertAlign: "middle",
            vertAlign: "middle",
            formatter: "textarea",
        },
    ],
});
async function LoadDataTable() {
    table.clearData();
    tableHS.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
        {
            SearchKey: $("#SearchKey").value(),
            TinhID_Loc: $("#TinhID_Loc").value(),
            XaID_Loc: $("#XaID_Loc").value(),
            ThonID_Loc: $("#ThonID_Loc").value(),
            DonViID_Loc: $("#DonViID_Loc").value(),
            CbSapXep: $("#CbSapXep").value(),
        }
    );
    if (!result.Err) {
        VeChart();
        table.setData(result.result);
        tableHS.setData(result.result);
    } else {
        table.setData(null);
        tableHS.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}
//#endregion

//#region Lưới 2
var table2 = new Tabulator("#Grid2", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "500",
    HeaderVertAlign: "center",
    headerVisible: false,
    selectable: 1,
    //data: dataDoiTuong,
    columns: [
        {
            title: "Thông tin hộ gia đình",
            field: "ThongTinNguoiThamGia",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 150,
            headerSort: false,
        },
    ],
    rowFormatter: function (row) {
        var element = row.getElement(),
            data = row.getData(),
            width = element.offsetWidth,
            rowTable,
            cellContents;
        while (element.firstChild) element.removeChild(element.firstChild);
        rowTable = document.createElement("table");
        rowTable.style.width = width - 18 + "px";
        rowTabletr = document.createElement("tr");
        let anhDaiDien = "";
        let url_anhDaiDien = "";
        const baseAsset = window.Laravel.DoiTuong.fileIconUrl;

        if (!data.AnhDaiDien || data.AnhDaiDien.trim() === "") {
            url_anhDaiDien = window.Laravel.DoiTuong.imgUser;
        } else {
            url_anhDaiDien = baseAsset + data.AnhDaiDien.replaceAll("~", "").replaceAll("*", "");
        }
        cellContents =
            "<td><img style='width: 50px;height: 50px;max-width: unset !important;' src='" +
            url_anhDaiDien +
            "'></td>";
        cellContents +=
            "<td><div style='text-align: left'><strong>" +
            data.MaDoiTuong +
            " - " +
            data.Hovaten +
            "</strong> </div><div style='text-align: left !important;font-size: 12px!important;'> CCCD/CMT: <b>" +
            (data.CCCD || ChuaCoThongTin) + " - " + (data.txtNgayCap || ChuaCoThongTin) +
            "</b></div></td>";
        rowTabletr.innerHTML = cellContents;
        rowTable.appendChild(rowTabletr);
        element.append(rowTable);
    },
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
let currentSelectedRowID = null;
table2.on("rowClick", async function (e, row) {
    const selectedID = row.getData().id;

    if (selectedID !== currentSelectedRowID) {
        // Chọn dòng mới
        currentSelectedRowID = selectedID;
        $("#DoiTuongID").val(selectedID);
        await LoadTabThongTinDoiTuong_View();
    } else {
        // Bỏ chọn dòng nếu click lại
        currentSelectedRowID = null;
        $("#DoiTuongID").val("");
        // Có thể clear form hoặc không làm gì tuỳ mục đích
        await LoadTabThongTinDoiTuong_View();
    }
});
async function LoadDataTable2() {
    table2.clearData();
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
        {}
    );
    if (!result.Err) {
        table2.setData(result.result);
    } else {
        table2.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}
//#endregion

$(function () {
    LoadCombo_Loc(); //GetDSGioiTinh
    NTS.loadDataComboAsync({
        name: "#Gioitinh",
        type: "GET",
        ajaxUrl: window.Laravel.layouts.GetDSGioiTinh,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });

    setTimeout(() => {
        LoadDataTable();
    }, 50);
});
//#endregion

//#region Events

$("#btnQuayRa").click(async function () {
    $(".divmain").show(0);
    $(".divNoiDung").hide(0);
    $(".divNoiDung").removeClass("show");
    AnHienMenu(false);
    LoadDataTable();
});

$("#TrangThai").on("change", function () {
    UpdateLabelDangSD(this);
});
$(document).on("click", "#btnThemMoi", function () {
    ThemDuLieu();
});
//LƯỚI 1
$(document).on("click", "#btnSua2", function () {
    SuaDuLieu($("#DoiTuongID").value());
});
$(document).on("click", "#btnXoa2", function () {
    XoaDuLieu($("#DoiTuongID").value());
});
$(document).on("click", "#btnXem2", function () {
    XemChiTietHocSinh_us($("#DoiTuongID").value());
});

$("#chonAvatar_DoiTuong").on("click", function () {
    $("#avatar_us_DoiTuong").click();
});

$("#avatar_us_DoiTuong").on("change", (e) => {
    const file = e.target.files[0];
    if (!file) return;

    uploadFile(file);
});

$(document).on("click", "#btnChonDonViCha", function () {
    LoadGrid_ChonDonVi_us("");
    $("#mdChonDonVi_us").modal("show");
});
$(document).on("click", "#btnChonDonViVaDong_us", function () {
    if (Grid_ChonDonVi_us.getSelectedRows().length == 0) {
        NTS.canhbao("Vui lòng chọn 1 đơn vị!");
        return false;
    }

    var data = Grid_ChonDonVi_us.getSelectedRows()[0]._row.data;
    $("#TenTruongHoc").value("Mã trường: " + data.MaDonVi + " - Tên trường: " + data.TenDonVi);
    $("#lblTruong").text(data.TenDonVi);
    $("#DonViID_Hoc").value(data.id);
    $("#mdThemMoi").modal("show");
    $("#mdChonDonVi_us").modal("hide");
});

//#region Combos events
$("#DiaBanHCID_Tinh").on("change", function () {
    NTS.loadDataComboAsync({
        name: "#DiaBanHCID_Xa",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDiaBanHC_ByIDCha,
        ajaxParam: {
            DiaBanHCID_Cha: $("#DiaBanHCID_Tinh").value(),
            LoaiDiaBan: "02",
        },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    setTimeout(() => {
        setDiaChi();
    }, 1);
});
$("#SoNha").on("keyup", function () {
    setDiaChi();
});
$("#DiaBanHCID_Xa").on("change", function () {
    NTS.loadDataComboAsync({
        name: "#DiaBanHCID_Thon",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDiaBanHC_ByIDCha,
        ajaxParam: {
            DiaBanHCID_Cha: $("#DiaBanHCID_Xa").value(),
            LoaiDiaBan: "03",
        },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    setTimeout(() => {
        setDiaChi();
    }, 1);
});
$("#DiaBanHCID_Thon").on("change", function () {
    setTimeout(() => {
        setDiaChi();
    }, 1);
});
$("#TinhID_Loc").on("change", function () {
    NTS.loadDataComboAsync({
        name: "#XaID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDiaBanHC_ByIDCha,
        ajaxParam: {
            DiaBanHCID_Cha: $("#TinhID_Loc").value(),
            LoaiDiaBan: "02",
        },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
});
$("#XaID_Loc").on("change", function () {
    NTS.loadDataComboAsync({
        name: "#ThonID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.layouts.GetDSDiaBanHC_ByIDCha,
        ajaxParam: { DiaBanHCID_Cha: $("#XaID_Loc").value(), LoaiDiaBan: "03" },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
});
//#endregion
//#endregion

//#region Functions
async function uploadFile(file) {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("chucnang", "DoiTuong");
    await NTS.getAjaxAPIAsync("POST", "/api/dungchung/files/clearTemp", {
        chucnang: "DoiTuong"
    });
    const res = await NTS.getAjaxAPIAsync(
        "POST",
        window.Laravel.DoiTuong.uploadFile,
        formData
    );

    if (res.err) {
        return NTS.loi(res.msg);
    }
    const newPath = res.url[0];

    // save the returned path
    currentAvatarPath = newPath;
    const fullUrl = new URL(`${currentAvatarPath}`, window.location.origin)
        .href;

    const preview = document.getElementById("avatar_DoiTuong");
    if (preview) {
        preview.src = fullUrl;
        NTS.thanhcong("Cập nhật avatar thành công.");
    }
}

function setActiveTab() {
    $("#user-profile-2 .nav-link").removeClass("active");
    $("#user-profile-2 #btnTab1_CT").addClass("active");
    $("#user-profile-2 .tab-pane").removeClass("active");
    $("#user-profile-2 .tab-pane").removeClass("show");
    $("#user-profile-2 #Tab1_CT").addClass("active");
    $("#user-profile-2 #Tab1_CT").addClass("show");
}

async function ThemDuLieu() {
    if (!QuyenThem()) {
        return;
    }
    resetForm("#mdThemMoi");
    try {
        var result = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.DoiTuong.maTuTangUrl,
            {}
        );
        $("#MaDoiTuong").value(result.SoChungTu);
        NTS.getAjaxAPIAsync("GET", window.Laravel.DoiTuong.thietlaphethong, {})
            .then(userInfo => {
                if (userInfo.Result.length > 0) {
                    $("#DiaBanHCID_Tinh").value(userInfo.Result[0].DiaBanHCID_Tinh);
                    NTS.loadDataComboAsync({
                        name: "#DiaBanHCID_Xa",
                        type: "POST",
                        ajaxUrl: window.Laravel.layouts.GetDSDiaBanHC_ByIDCha,
                        ajaxParam: {
                            DiaBanHCID_Cha: $("#DiaBanHCID_Tinh").value(),
                            LoaiDiaBan: "02",
                        },
                        columns: 2,
                        indexValue: 2,
                        indexText: 0,
                        indexText1: 1,
                        textShowTatCa: "-Chọn-",
                        showTatCa: !0,
                    });
                    setTimeout(() => {
                        $("#DiaBanHCID_Xa").value(userInfo.Result[0].DiaBanHCID_Xa);
                    }, 500);
                }
            })
            .catch(err => { });
    } catch { }
    $("#mdThemMoi").modal("show");
    $("#tieuDeModal").text("Thêm mới thông tin học sinh");
    $("#DoiTuongID").val("");
    $("#TrangThai").prop("checked", true);
    $("#avatar_DoiTuong").prop("src", Laravel.DoiTuong.defaultAvatar);
    $("#DoiTuong_txtDuongDanFileVB").value("");
    $("#DoiTuong_list-file").empty();
    UpdateLabelDangSD("#TrangThai");
    tempthem = "them";
}

var fmThaoTac = function (cell) {
    return formaterbtnThaoTac(cell.getData().id);
};
var fmDangSD = function (cell) {
    return formaterDangSD(cell.getValue(), cell.getData().id);
};

//END LƯỚI 1

//LƯỚI 2

async function LoadTabThongTinDoiTuong_View() {
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        { id: $("#DoiTuongID").value() }
    );
    if (!result.Err && result.Result != null) {
        let data = result.Result;
        if (data != undefined) {
            $(".divThaoTacNhanh").show();
            $("#txtMaHocSinh_View").html(
                "Mã học sinh: <b>" + data.MaDoiTuong + "</b>"
            );
            $("#txtTenHocSinh_View").html(
                "Tên học sinh: <b>" + data.Hovaten + "</b>"
            );
            try {
                if (data.AnhDaiDien.length > 2) {
                    $("#avatar_View").attr(
                        "src",
                        data.AnhDaiDien.replaceAll("*", "").replaceAll("~", "")
                    );
                } else {
                    $("#avatar_View").attr(
                        "src",
                        window.Laravel.DoiTuong.imgUser
                    );
                }
            } catch { }
            $("#lblMaHocSinh_View").text(data.MaDoiTuong);
            $("#lblTenHocSinh_View").text(data.Hovaten);
            $("#lblNgaySinh_View").text(data.txtNgaysinh || ChuaCoThongTin);
            $("#lblNoiSinh_View").text(data.Noisinh || ChuaCoThongTin);
            $("#lblGioiTinh_View").text(data.txtGioitinh || ChuaCoThongTin);
            $("#lblCCCD_View").text(data.CCCD || ChuaCoThongTin);
            $("#lblNgayCap_View").text(data.txtNgayCap || ChuaCoThongTin);
            $("#lblNoiCap_View").text(data.TenNoiCap || ChuaCoThongTin);
            $("#lblDanToc_View").text(data.TenDanToc || ChuaCoThongTin);
            $("#lblSDT_View").text(data.SDT || ChuaCoThongTin);
            $("#lblEmail_View").text(data.Email || ChuaCoThongTin);
            $("#lblDiaChi_View").text(data.DiaChi || ChuaCoThongTin);
            $("#lblThuocDienUuTien_View").text(data.TenDienUuTien || ChuaCoThongTin);

            $("#lblTenDonVi_View").text(data.TenDonVi || ChuaCoThongTin);
            $("#lblTenLop_View").text(data.TenLopHoc || ChuaCoThongTin);
            $("#lblDinhKem_View").html(`<a href="#" onclick="XemDinhKem_us('${data.DinhKem}')" data="${data.DinhKem}">Xem đính kèm</a>` || ChuaCoThongTin);
            $("#lblGhiChu_View").html(data.GhiChu || ChuaCoThongTin);

            $("#spTinhTrangTN_View").html(`Tình trạng tốt nghiệp: <span id="lblTinhTrangTN_View" class="alert d-inline-block span-trangthai" style="background-color:${data.MauSac_TotNghiep};">
            ${data.TenTrangThai_TotNghiep}
                                       </span>`);
            $("#spTinhTrangCB_View").html(`Tình trạng cấp bằng: <span id="lblTinhTrangCB_View" class="alert d-inline-block span-trangthai" style="background-color:${data.MauSac_CapBang};">
                                                    <i class="fa fa-check-circle-o" aria-hidden="true"></i> ${data.TenTrangThai_CapBang}
                                        </span>`);
        }
    } else {
        $(".divThaoTacNhanh").hide();
        $("#avatar_View").attr("src", window.Laravel.DoiTuong.imgUser);
        $("#txtTenHocSinh_View").text(ChuaCoThongTin);
        $("#txtMaHocSinh_View").text(ChuaCoThongTin);
        $("#lblMaHocSinh_View").text(ChuaCoThongTin);
        $("#lblTenHocSinh_View").text(ChuaCoThongTin);
        $("#lblNgaySinh_View").text(ChuaCoThongTin);
        $("#lblNoiSinh_View").text(ChuaCoThongTin);
        $("#lblGioiTinh_View").text(ChuaCoThongTin);
        $("#lblCCCD_View").text(ChuaCoThongTin);
        $("#lblNgayCap_View").text(ChuaCoThongTin);
        $("#lblNoiCap_View").text(ChuaCoThongTin);
        $("#lblDanToc_View").text(ChuaCoThongTin);
        $("#lblSDT_View").text(ChuaCoThongTin);
        $("#lblEmail_View").text(ChuaCoThongTin);
        $("#lblDiaChi_View").text(ChuaCoThongTin);
        $("#lblThuocDienUuTien_View").text(ChuaCoThongTin);
        $("#lblTenDonVi_View").text(ChuaCoThongTin);
        $("#lblTenLop_View").text(ChuaCoThongTin);
        $("#lblDinhKem_View").html(ChuaCoThongTin);
        $("#lblGhiChu_View").html(ChuaCoThongTin);
        $("#spTinhTrangTN_View").html(`Trình trạng học tập: `);
        $("#spTinhTrangCB_View").html(`Trình trạng cấp bằng: `);
    }
}
//#endregion

//#region  Lưu dữ liệu
$("#btnLuuVaDong").on("click", async function () {
    const validate = new NTSValidate("#mdThemMoi");
    if (!validate.trim().check()) return false;
    const payload = {
        MaDoiTuong: $("#MaDoiTuong").value(),

        Hovaten: $("#Hovaten").value(),
        Ngaysinh: $("#Ngaysinh").value(),
        Noisinh: $("#Noisinh").value(),
        Gioitinh: $("#Gioitinh").value(),
        CCCD: $("#CCCD").value(),
        NgayCap: $("#NgayCap").value(),
        DiaBanHCID_NoiCap: $("#DiaBanHCID_NoiCap").value(),
        DanTocID: $("#DanTocID").value(),
        SDT: $("#SDT").value(),
        Email: $("#Email").value(),
        DiaBanHCID_Tinh: $("#DiaBanHCID_Tinh").value(),
        DiaBanHCID_Xa: $("#DiaBanHCID_Xa").value(),
        DiaBanHCID_Thon: $("#DiaBanHCID_Thon").value(),
        DiaChi: $("#DiaChi").value(),
        DonViID_Hoc: $("#DonViID_Hoc").value(),
        GhiChu: $("#GhiChu").value(),
        TrangThai: $("#TrangThai").value(),
        AnhDaiDien: currentAvatarPath,
        DoiTuongID: $("#DoiTuongID").value(),

        DienUuTienID: $("#DienUuTienID").value(),
        LopHocID: $("#LopHocID").value(),
        SoNha: $("#SoNha").value(),
        txtDuongDanFileVB: $("#DoiTuong_txtDuongDanFileVB").value(),
    };
    var met = "POST";
    if (tempthem == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }
    var result = await NTS.getAjaxAPIAsync(
        met,
        window.location.pathname + "/luuthongtin",
        payload
    );
    if (!result.Err) {
        //if (!$(".divNoiDung").hasClass("show")) {
        LoadDataTable();
        //}
        //LoadThongTinThanhVien_Main();
        NTS.thanhcong(result.Msg);
        $("#mdThemMoi").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});
//#endregion

//#region Xoá dữ liệu
async function XoaDuLieu(ID) {
    var result_ktxoa = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.layouts.KiemTraXoa,
        {
            ma: ID,
            model: "QuanLy\\DoiTuong",
        }
    );

    if (!result_ktxoa.Err) {
        CanhBaoXoa(async () => {
            var result = await NTS.getAjaxAPIAsync(
                "DELETE",
                window.location.pathname + "/xoa",
                { ma: ID }
            );
            if (!result.Err) {
                LoadDataTable();
                NTS.thanhcong(result.Msg);
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
    } else {
        NTS.canhbao(result_ktxoa.Msg);
        return false;
    }
}
//#endregion

//#region Load dữ liệu
async function SuaDuLieu(id) {
    if (!QuyenSua()) {
        return false;
    }
    $("#tieuDeModal").text("Cập nhật thông tin học sinh");
    $("#DoiTuongID").value(id);
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        {
            id: id,
        }
    );
    if (!result.Err) {
        let data = result.Result;
        $("#MaDoiTuong").value(data.MaDoiTuong);
        $("#lblMaHocSinh").text(data.MaDoiTuong);
        $("#Hovaten").value(data.Hovaten);
        $("#lblHovaten").text(data.Hovaten);
        $("#Ngaysinh").value(data.txtNgaysinh);
        $("#lblNgaySinh").text(data.txtNgaysinh);
        $("#Noisinh").value(data.Noisinh);
        $("#lblNoiSinh").text(data.Noisinh);
        $("#Gioitinh").value(data.Gioitinh);
        $("#lblGioiTinh").text(data.TenGioiTinh);
        $("#CCCD").value(data.CCCD);
        $("#lblTruong").text(data.TenDonVi);
        $("#NgayCap").value(data.txtNgayCap);
        $("#NgayCap").value(data.txtNgayCap);
        $("#DiaBanHCID_NoiCap").value(data.DiaBanHCID_NoiCap);
        $("#DanTocID").value(data.DanTocID);
        $("#lblDanToc").text(data.TenDanToc);
        $("#SDT").value(data.SDT);
        $("#Email").value(data.Email);
        $("#DiaBanHCID_Tinh").value(data.DiaBanHCID_Tinh);
        NTS.loadDataComboAsync({
            name: "#DiaBanHCID_Xa",
            type: "POST",
            ajaxUrl: window.Laravel.layouts.GetDSDiaBanHC_ByIDCha,
            ajaxParam: {
                DiaBanHCID_Cha: $("#DiaBanHCID_Tinh").value(),
                LoaiDiaBan: "02",
            },
            columns: 2,
            indexValue: 2,
            indexText: 0,
            indexText1: 1,
            textShowTatCa: "-Chọn-",
            showTatCa: !0,
        });
        setTimeout(() => {
            $("#DiaBanHCID_Xa").value(data.DiaBanHCID_Xa);
        }, 500);
        $("#DiaBanHCID_Thon").value(data.DiaBanHCID_Thon);
        $("#DonViID_Hoc").value(data.DonViID_Hoc);

        $("#DienUuTienID").value(data.DienUuTienID);
        $("#LopHocID").value(data.LopHocID);
        $("#SoNha").value(data.SoNha);

        currentAvatarPath = data.AnhDaiDien;
        if (currentAvatarPath != null) {
            const fullUrl = new URL(`${data.AnhDaiDien}`, window.location.origin)
                .href;

            $("#avatar_DoiTuong").prop("src", fullUrl);
        }
        $("#TenTruongHoc").value("Mã trường: " + data.MaDonVi + " - Tên trường: " + data.TenDonVi);
        setTimeout(() => {
            $("#DiaChi").value(data.DiaChi);
        }, 50);

        uploadedFileUrls = data.DinhKem || ""
            ? data.DinhKem.split("|").filter((u) => u)
            : [];
        $("#DoiTuong_txtDuongDanFileVB").value(data.DinhKem || "");
        $("#DoiTuong_list-file").empty();
        uploadedFileUrls.forEach((url) => {
            renderAttachment(url);
        });

        $("#TrangThai").value(data.TrangThai);
        UpdateLabelDangSD("#TrangThai");
        $("#GhiChu").value(data.GhiChu);
        $("#mdThemMoi").modal("show");
        tempthem = "sua";
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

async function LoadThongTinThanhVien_Main() {
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        { id: $("#DoiTuongID").value() }
    );

    if (!result.Err && result.Result != null) {
        let data = result.Result;
        if (data != undefined) {
            $("#lblHoTen_CT").text(data.Hovaten);
            $("#lblGioiTinh_CT").text(data.txtGioitinh);
            $("#lblCCCD_CT").text(data.CCCD);
            $("#lblTruong_CT").text(data.TenDonVi);
            $("#lblDiaChi_CT").text(data.DiaChi);
            $("#avatar").prop("src", data.AnhDaiDien);
        } else {
            $("#lblHoTen_CT").text(ChuaCoThongTin);
            $("#lblGioiTinh_CT").text(ChuaCoThongTin);
            $("#lblCCCD_CT").text(ChuaCoThongTin);
            $("#lblTruong_CT").text(ChuaCoThongTin);
            $("#lblDiaChi_CT").text(ChuaCoThongTin);
        }
    }
}
//end Load giao diện chi tiêt
async function XemChiTiet(ID) {
    AnHienMenu(true);
    $(".divmain").hide(250);
    $(".divNoiDung").show(250);
    $(".divNoiDung").addClass("show");
    $("#DoiTuongID").value(ID);
    setActiveTab();
    LoadThongTinThanhVien_Main();
}
function htmlDuLieu(cell, formatterParams, onRendered) {
    let anhDaiDien = "";
    let url_anhDaiDien = "";

    const data = cell.getData();
    const baseAsset = window.Laravel.DoiTuong.fileIconUrl;

    if (!data.AnhDaiDien || data.AnhDaiDien.trim() === "") {
        url_anhDaiDien = window.Laravel.DoiTuong.imgUser;
    } else {
        url_anhDaiDien = baseAsset + data.AnhDaiDien.replaceAll("~", "").replaceAll("*", "");
    }

    anhDaiDien = `<img src="${url_anhDaiDien}" alt="${data.Hovaten}" class="img-thumbnail rounded lazy">`;
    return `<div class="list-item col-md-12" style="padding: 0px;">
                        <div class="card card-luoi shadow-sm mb-2 ">
                            <div id="card_${cell.getData().id
        }" class="card-body profile-user-box">
                                <div class="row">
                                    <div class="col-12 col-xs-6 col-sm-2 center" style="margin: auto;">
                                        <div class="profile-picture">
                                            ${anhDaiDien}
                                        </div>
                                    </div>
                                    <div class="col-md-10">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="row">
                                                    <div class="col-md-10">
                                                        <p class="fs-big my-1">Mã học sinh: <b>${cell.getData()
            .MaDoiTuong ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                    <div class="col-sm-2">
                                                        <div class="dropdown text-end" style="position: absolute; top: 10px; right: 10px">
                                                            <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                                                            </button>
                                                            <div class="dropdown-menu dropdown-menu-end w-auto" >
                                                                <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XemChiTietHocSinh_us('${cell.getData().id
        }'); return false;">${icons["xem"]}&ensp; Xem học sinh</a>
                                                                <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="SuaDuLieu('${cell.getData().id
        }'); return false;">${icons["sua"]}&ensp; Chỉnh sửa học sinh</a>
                                                                <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XoaDuLieu('${cell.getData().id
        }')">${icons["xoa"]}&ensp; Xóa học sinh</a>
                                                            </div>
                                                        </div>
                                                </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <p class="fs-big my-1">Tên học sinh: <b>${cell.getData()
            .Hovaten ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <p class="fs-big my-1">Ngày sinh: <b>${cell.getData()
            .txtNgaysinh ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <p class="fs-big my-1">Giới tính: <b>${cell.getData()
            .txtGioitinh ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>

                                                    <div class="col-md-4">
                                                        <p class="fs-big my-1">Dân tộc: <b>${cell.getData()
            .TenDanToc ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <p class="fs-big my-1">CMND/CCCD: <b>${cell.getData()
            .CCCD ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <p class="fs-big my-1">Cấp ngày:<b> ${cell.getData()
            .txtNgayCap ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <p class="fs-big my-1">Nơi cấp: <b>${cell.getData()
            .TenNoiCap ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <p class="fs-big my-1">Địa chỉ: <b>${cell.getData()
            .DiaChi ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-top:4px; margin-bottom: -20px;">
                                    <div class="col-md-2" style="text-align: center;">
                                        <span id="txtTrangThaiTN" class="alert d-inline-block span-trangthai" style="background-color:${cell.getData().MauSac_TotNghiep};">
                                        ${cell.getData().TenTrangThai_TotNghiep}
                                        </span>
                                    </div>
                                    <div class="col-md-10">
                                        <hr/>
                                        <div class="row">
                                            <div class="col-md-10">
                                                <div class="row">
                                                    <div class="col-md-8">
                                                            <p class="fs-big my-1">Trường: <b>${cell.getData()
            .TenDonVi ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                    <div class="col-md-4">
                                                            <p class="fs-big my-1">Lớp: <b>${cell.getData().TenLopHoc ||
        ChuaCoThongTin
        }</b></p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-2 text-end mt-1">
                                                <span id="txtTrangThaiCapBang" class="alert d-inline-block span-trangthai" style="background-color:${cell.getData().MauSac_CapBang};">
                                                   <i class="fa fa-check-circle-o" aria-hidden="true"></i> ${cell.getData().TenTrangThai_CapBang}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>`;
}

//#endregion
var IDView_DoiTuong_us = "";

//region Biểu đồ
var XepLoaiChart = document.getElementById("XepLoaiChart");
var mixedXepLoaiChart;
async function VeChart() {
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.DoiTuong.getthongkedoituong,
        {}
    );
    try {
        $("#txtTongHS").text(result.Result.TS);
        $("#txtChuaXetTN").text(result.Result.TrangThai_TotNghiep_30);
        $("#txtDaXetTN").text(result.Result.TrangThai_TotNghiep_31);
        $("#txtChuaCapBang").text(result.Result.TrangThai_CapBang_35);
        $("#txtDaCapBang").text(result.Result.TrangThai_CapBang_36);
        document.querySelector("#XepLoaiChart").innerHTML = ""; // Xóa nội dung bên trong container
        mixedXepLoaiChart = new ApexCharts(
            document.querySelector("#XepLoaiChart"),
            {
                labels: ["Đã cấp bằng", "Chưa cấp bằng", "Chưa xét tốt nghiệp"],
                colors: ["#7AA802", "#EA1818", "#F78B2D"], // Thêm màu sắc tại đây
                series: [result.Result.TrangThai_CapBang_36, result.Result.TrangThai_CapBang_35, result.Result.TrangThai_TotNghiep_30],
                chart: {
                    type: "donut",
                    height: 110, // Thay đổi chiều cao tại đây
                },
                dataLabels: {
                    enabled: false, // Disable data labels
                },
                responsive: [
                    {
                        breakpoint: 480,
                        options: {
                            chart: {
                                width: 110,
                            },
                            legend: {
                                position: "bottom",
                            },
                        },
                    },
                ],
            }
        );
        mixedXepLoaiChart.render();
    } catch { }
}
