const btnThaoTac = function (cell) {
    return formaterbtnThaoTac(cell.getData().id);
};
const btnThaoTac2 = function (cell) {
    let ID = cell.getData().id;
    return `<div class="show-or-hide"><a class='text-danger btnXoaGrid1' title="Xoá" data='${ID}'><i class='fa fa-trash-o'></i></a></div>`;
};

const fmTrangThai = function (cell) {
    return formaterTrangThai(cell.getValue(), cell.getData().id);
};
let hotKey = 0;

$(document).on("keydown", function (e) {
    switch (e.keyCode) {
        case 113:
            if (hotKey == 0) $("#btnThemMoi").trigger("click");
            e.preventDefault();
            break;
        case 114:
            if (hotKey == 0) $(".nav-search-input").focus();
            e.preventDefault();
            break;
        case 115:
            if (hotKey == 1) $("#mdThemMoi").modal("hide");
            e.preventDefault();
            break;
        case 120:
            if (hotKey == 1) $("#btnLuuVaDong").trigger("click");
            e.preventDefault();
            break;
    }
});

//Flags
var viewGridCard = true;
var thaoTac = "them";
var selectedId;
// var selectedHocSinh;
// var selectedTruongHoc;
var danhSachXepLoaiRenLuyen = [];
var danhSachUuTien = [];
let tabGridHocSinhChiTiet;

//Grids
var table;
// var gridSelectDonVi;
var tabGridHocSinh;
// var gridTruongHoc;
// var gridChonHocSinh;

var modeThemHS = 0;
const enumModeThemHS = Object.freeze({
    Them1: 0,
    ThemNhieu: 1,
});

//Configs

/**
 * Cấu hình chung cho combo box.
 *
 * @function commonComboConfig
 * @param {number} [numCol=2] - Số cột trong combo box. Mặc định là 2.
 * @returns {Object} Cấu hình combo box.
 * @property {number} columns - Số cột trong combo box.
 * @property {number} indexValue - Chỉ số giá trị trong combo box.
 * @property {number} indexText - Chỉ số văn bản trong combo box.
 * @property {number} [indexText1] - Chỉ số văn bản thứ hai (chỉ có khi numCol là 2).
 * @property {string} textShowTatCa - Văn bản hiển thị cho tùy chọn "Tất cả".
 * @property {boolean} showTatCa - Xác định có hiển thị tùy chọn "Tất cả" hay không.
 */
const commonComboConfig = function (numCol = 2) {
    if ((numCol = 2))
        return {
            columns: 2, // indexValue=0, indexText=1
            indexValue: 0,
            indexText: 1,
            indexText1: 2,
            textShowTatCa: "-Chọn-",
            showTatCa: true,
        };
    else {
        return {
            columns: 1, // indexValue=0, indexText=1
            indexValue: 0,
            indexText: 1,
            textShowTatCa: "-Chọn-",
            showTatCa: true,
        };
    }
};

const getColsLuoiHocSinh = async function () {
    let listXepLoaiRenLuyen;
    let listDienUuTien;
    try {
        // Load xếp loại rèn luyện
        const resRL = await NTS.getAjaxAPIAsync(
            "GET",
            Laravel.getListXepLoai,
            {}
        );
        if (!resRL.Err) {
            listXepLoaiRenLuyen = resRL.Result;
        } else {
            NTS.loi("Lỗi khi tải danh sách xếp loại rèn luyện");
        }

        // Load ưu tiên
        const resUT = await NTS.getAjaxAPIAsync(
            "GET",
            Laravel.getListDienUuTien,
            {}
        );
        if (!resUT.Err) {
            listDienUuTien = resUT.Result;
        } else {
            NTS.loi("Lỗi khi tải danh sách ưu tiên");
        }
    } catch (error) {
        // network or unexpected error
        console.error(error);
        NTS.loi("Có lỗi xảy ra khi tải dữ liệu từ server");
    }
    const xepLoaiValues = listXepLoaiRenLuyen.map((i) => ({
        value: i.id,
        label: i.name,
    }));

    const uuTienValues = listDienUuTien.map((i) => ({
        value: i.id,
        label: i.name,
    }));

    return [
        {
            title: "<i class='fa fa-ellipsis-h'></i>",
            formatter: btnThaoTac2,
            hozAlign: "center",
            headerSort: false,
            width: 40,
            ...commonColumnConfig,
        },
        {
            title: "Mã học sinh",
            field: "DoiTuong.MaDoiTuong",
            headerSort: false,
            ...commonColumnConfig,
        },
        {
            title: "Họ và tên",
            field: "DoiTuong.Hovaten",
            headerSort: false,
            ...commonColumnConfig,
            formatter: "textarea",
        },
        {
            title: "Số CMND/CCCD",
            field: "DoiTuong.CCCD",
            headerSort: false,
            ...commonColumnConfig,
            width: 120,
        },
        {
            title: "Ngày sinh",
            field: "DoiTuong.Ngaysinh",
            headerSort: false,
            hozAlign: "center",
            ...commonColumnConfig,
            formatter: "textarea",
        },
        {
            title: "Giới tính",
            field: "DoiTuong.gioi_tinh_.TenGioiTinh",
            headerSort: false,
            ...commonColumnConfig,
        },
        {
            title: "Nơi sinh",
            field: "DoiTuong.Noisinh",
            headerSort: false,
            ...commonColumnConfig,
        },
        {
            title: "Kết quả đánh giá",
            columns: [
                {
                    title: "Rèn luyện",
                    field: "KetQua_RL",
                    editor: "list",
                    editorParams: {
                        values: xepLoaiValues,
                        clearable: true,
                        placeholderLoading: "Đang tải…",
                        placeholderEmpty: "Không có lựa chọn",
                    },
                    autocomplete: true, //enable autocomplete mode,
                    validator: (v) => (v ? true : "Chọn xếp loại"),
                    ...commonColumnConfig,
                    formatter: (cell) => {
                        const id = cell.getValue();
                        const found = listXepLoaiRenLuyen.find(
                            (i) => i.id === id
                        );
                        return found ? found.name : "";
                    },
                },
                {
                    title: "Học tập",
                    field: "KetQua_HT",
                    editor: "list",
                    editorParams: {
                        values: xepLoaiValues,
                        clearable: true,
                        placeholderLoading: "Đang tải…",
                        placeholderEmpty: "Không có lựa chọn",
                    },
                    autocomplete: true, //enable autocomplete mode,
                    validator: (v) => (v ? true : "Chọn xếp loại"),
                    ...commonColumnConfig,
                    formatter: (cell) => {
                        const id = cell.getValue();
                        const found = listXepLoaiRenLuyen.find(
                            (i) => i.id === id
                        );
                        return found ? found.name : "";
                    },
                },
            ],
            ...commonColumnConfig,
        },
        {
            title: "Ưu tiên",
            field: "dien_uu_tien.id",
            editor: "list",
            editorParams: {
                values: uuTienValues,
                clearable: true,
            },
            formatter: (cell) => {
                const id = cell.getValue();
                const found = listDienUuTien.find((i) => i.id === id);
                return found ? found.name : "";
            },
            autocomplete: true, //enable autocomplete mode,

            validator: (v) => (v ? true : "Chọn mức ưu tiên"),
            ...commonColumnConfig,
        },

        {
            title: "Kết quả tốt nghiệp",
            field: "KetQuaTN",
            editor: "list",
            width: 150,

            editorParams: {
                values: xepLoaiValues,
                clearable: true,
            },
            validator: (v) => (v ? true : "Chọn xếp loại"),
            formatter: (cell) => {
                const id = cell.getValue();
                const found = listXepLoaiRenLuyen.find((i) => i.id === id);
                return found ? found.name : "";
            },
            autocomplete: true, //enable autocomplete mode,

            ...commonColumnConfig,
        },
        {
            title: "Ghi chú",
            field: "GhiChu",
            headerSort: false,
            editor: "textarea",
            ...commonColumnConfig,
            formatter: "textarea",
            minWidth: 250,
        },
    ];
};

const getColsLuoiHocSinhXem = async function () {
    let listXepLoaiRenLuyen;
    let listDienUuTien;
    try {
        // Load xếp loại rèn luyện
        const resRL = await NTS.getAjaxAPIAsync(
            "GET",
            Laravel.getListXepLoai,
            {}
        );
        if (!resRL.Err) {
            listXepLoaiRenLuyen = resRL.Result;
        } else {
            NTS.loi("Lỗi khi tải danh sách xếp loại rèn luyện");
        }

        // Load ưu tiên
        const resUT = await NTS.getAjaxAPIAsync(
            "GET",
            Laravel.getListDienUuTien,
            {}
        );
        if (!resUT.Err) {
            listDienUuTien = resUT.Result;
        } else {
            NTS.loi("Lỗi khi tải danh sách ưu tiên");
        }
    } catch (error) {
        // network or unexpected error
        console.error(error);
        NTS.loi("Có lỗi xảy ra khi tải dữ liệu từ server");
    }
    const xepLoaiValues = listXepLoaiRenLuyen.map((i) => ({
        value: i.id,
        label: i.name,
    }));

    const uuTienValues = listDienUuTien.map((i) => ({
        value: i.id,
        label: i.name,
    }));

    return [
        {
            title: "Mã học sinh",
            field: "DoiTuong.MaDoiTuong",
            headerSort: false,
            ...commonColumnConfig,
        },
        {
            title: "Họ và tên",
            field: "DoiTuong.Hovaten",
            headerSort: false,
            ...commonColumnConfig,
            formatter: "textarea",
        },
        {
            title: "Số CMND/CCCD",
            field: "DoiTuong.CCCD",
            headerSort: false,
            ...commonColumnConfig,
            width: 120,
        },
        {
            title: "Ngày sinh",
            field: "DoiTuong.Ngaysinh",
            headerSort: false,
            hozAlign: "center",
            ...commonColumnConfig,
            formatter: "textarea",
        },
        {
            title: "Giới tính",
            field: "DoiTuong.gioi_tinh_.TenGioiTinh",
            headerSort: false,
            ...commonColumnConfig,
        },
        {
            title: "Nơi sinh",
            field: "DoiTuong.Noisinh",
            headerSort: false,
            ...commonColumnConfig,
        },
        {
            title: "Kết quả đánh giá",
            columns: [
                {
                    title: "Rèn luyện",
                    field: "KetQua_RL",

                    ...commonColumnConfig,
                    formatter: (cell) => {
                        const id = cell.getValue();
                        const found = listXepLoaiRenLuyen.find(
                            (i) => i.id === id
                        );
                        return found ? found.name : "";
                    },
                },
                {
                    title: "Học tập",
                    field: "KetQua_HT",

                    formatter: (cell) => {
                        const id = cell.getValue();
                        const found = listXepLoaiRenLuyen.find(
                            (i) => i.id === id
                        );
                        return found ? found.name : "";
                    },
                    ...commonColumnConfig,
                },
            ],
            ...commonColumnConfig,
        },
        {
            title: "Ưu tiên",
            field: "dien_uu_tien.id",

            formatter: (cell) => {
                const id = cell.getValue();
                const found = listDienUuTien.find((i) => i.id === id);
                return found ? found.name : "";
            },
            ...commonColumnConfig,
        },

        {
            title: "Kết quả tốt nghiệp",
            field: "KetQuaTN",
            width: 150,
            formatter: (cell) => {
                const id = cell.getValue();
                const found = listXepLoaiRenLuyen.find((i) => i.id === id);
                return found ? found.name : "";
            },
            ...commonColumnConfig,
        },
        {
            title: "Ghi chú",
            field: "GhiChu",
            headerSort: false,
            ...commonColumnConfig,
            formatter: "textarea",
        },
    ];
};

//#region Render card
function handleFileLinks(event) {
    event.preventDefault();

    const link = event.currentTarget;
    const filesString = link.getAttribute("data-files") || "";

    // Split file string by pipe '|'
    const filesArray = filesString.split("|").filter((f) => f.trim() !== "");

    // Populate modal with these files
    populateMdXemDinhKem(filesArray);

    // Show modal
    hienMdXemDinhKem();
}

function buildTrangThaiBadge(mauNen, label) {
    return `
    <span class="badge m-2 py-1 px-3"
        style="font-weight: 600; font-size: 0.85rem; cursor: default; user-select: none;
        background-color: ${mauNen ?? "#f76707"};">
        ${label ?? "Chưa ban hành"}
    </span>`;
}
/**
 * Hàm `htmlDuLieu` tạo HTML hiển thị thông tin quyết định tốt nghiệp dựa trên dữ liệu đầu vào.
 *
 * @param {Object} cell - Đối tượng cell từ Tabulator, chứa thông tin của một hàng dữ liệu.
 * @param {Object} formatterParams - Các tham số định dạng (không sử dụng trong hàm này).
 * @param {Function} onRendered - Hàm callback được gọi sau khi nội dung được render (không sử dụng trong hàm này).
 *
 * @returns {string} - Chuỗi HTML hiển thị thông tin quyết định tốt nghiệp, bao gồm các thao tác như xem chi tiết, chỉnh sửa, ban hành, thu hồi, xóa, và các thông tin liên quan.
 *
 * @description
 * - Hàm kiểm tra trạng thái ban hành của quyết định (`tinhTrangBanHanh`) để hiển thị các thao tác phù hợp.
 * - Hiển thị thông tin như số quyết định, ngày ký, người ký, chức vụ, cơ quan ban hành, kỳ thi, cấp học, số học sinh công nhận, file đính kèm, ghi chú, ngày ban hành, người ban hành, chức vụ ban hành, và nội dung ban hành.
 * - Định dạng ngày tháng theo kiểu `dd/mm/yyyy`.
 * - Nếu có file đính kèm, hiển thị liên kết để xem file; nếu không, hiển thị thông báo "Không có file đính kèm".
 * - Hiển thị trạng thái quyết định (Đã ban hành hoặc Chưa ban hành) dưới dạng badge.
 */
function htmlDuLieu(cell, formatterParams, onRendered) {
    const data = cell.getData();

    // Determine issued state
    const daBanHanh = data.tinhTrangBanHanh;

    // Action strings
    let chuoiThaoTacBanHanh = "";
    let chuoiThaoTacXoa = "";
    let chuoiThaoTacThuHoi = "";
    let chuoiThaoTacSua = "";
    let chuoiThaoTacIn = "";
    let dataTrangThai = data.trang_thai_label;
    let badgeTrangThai = buildTrangThaiBadge(
        dataTrangThai?.MauSac,
        dataTrangThai?.TenTrangThai
    );

    // If NOT issued, show Ban hành & Xóa
    if (!daBanHanh) {
        chuoiThaoTacBanHanh = `
        <a class="dropdown-item textsize-item btnBanHanhQD" href="javascript:void(0);" data-id="${data.id}" id="btnBanHanhQD">
            ${icons["giao"]}&ensp; Ban hành quyết định
        </a>`;
        chuoiThaoTacXoa = `
        <a class="dropdown-item textsize-item btnXoaQD" href="javascript:void(0);" data-id="${data.id}" id="btnXoaQD">
            ${icons["xoa"]}&ensp; Xóa quyết định
        </a>`;
        chuoiThaoTacSua = `
        <a class="dropdown-item textsize-item btnChinhSuaQD" href="javascript:void(0);" data-id="${data.id}" id="btnChinhSuaQD">
            ${icons["sua"]}&ensp; Chỉnh sửa quyết định
        </a>`;
        chuoiThaoTacIn = `
        <a class="dropdown-item textsize-item btnInQD" href="javascript:void(0);" data-id="${data.id}" id="btnChinhSuaQD">
            ${icons["in"]}&ensp; In GCN tốt nghiệp tạm thời
        </a>`;
    } else {
        // If issued, show Thu hồi ban hành, hide Xóa & Ban hành
        chuoiThaoTacThuHoi = `
        <a class="dropdown-item textsize-item btnThuHoiBanHanhQD" href="javascript:void(0);" data-id="${data.id}" id="btnThuHoiBanHanhQD">
            ${icons["thuhoiBH"]}&ensp; Thu hồi ban hành
        </a>`;
    }

    const chuoiThaoTac = `
    <a class="dropdown-item textsize-item btnXemChiTietQD" href="javascript:void(0);" data-id="${
        data.id
    }">
        ${icons["xem"]}&ensp; Xem chi tiết quyết định
    </a>
    ${chuoiThaoTacSua || ""}
    ${chuoiThaoTacBanHanh || ""}
    ${chuoiThaoTacThuHoi || ""}
    ${chuoiThaoTacIn || ""}
    ${chuoiThaoTacXoa || ""}
`;
    console.log(chuoiThaoTac);

    // Format NgayKy and NgayBanHanh to dd/mm/yyyy
    function formatDate(dateStr) {
        return dateStr;
    }
    const fileLinks =
        Array.isArray(data.FileDinhKem) && data.FileDinhKem.length > 0
            ? data.FileDinhKem.join("|") // join files with a separator like pipe |
            : "";
    let html = `
    <div class="list-item col-md-12">
        <div class="card card-luoi shadow-sm mb-3">
            <div class="card-body profile-user-box mb-2" style="padding-bottom:0px !important; padding-top:6px;">
                <div class="row">
                    <div class="col-12 col-xs-6 col-md-2 text-center">
                        <div class="row mt-2">
                            <img
                            class="d-block mx-auto w-75"
                            src="${Laravel.linkAnhHoSo}"
                            alt="icon quyết định">
                        </div>
                        ${badgeTrangThai}
                    </div>

                    <div class="col-md-10 px-2">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <p class="fs-big mb-2">Số quyết định: <b>${
                                    data.SoQuyetDinh || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3">
                                <p class="fs-big mb-2">Ngày ký: <b>${
                                    data.NgayKy || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3 px-0">
                                <p class="fs-big mb-2">Người ký: <b>${
                                    data.NguoiKy || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3 d-flex justify-content-between align-items-center px-0">
                                <p class="fs-big mb-2">Chức vụ: <b>${
                                    data.chuc_vu?.tenChucVu || ""
                                }</b></p>
                                <div class="dropdown text-end me-2 ">
                                    <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-lg-end w-auto dropdown-thao-tac" style="margin-left:-175px; inset:0 auto auto auto; position:fixed !important;">
                                    ${chuoiThaoTac}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr style="border-top: 2px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />
                        <div class="row align-items-center">
                        <div class="row">
                            <div class="col-12">
                            <p class="fs-big mb-2">Trích yếu/nội dung: <strong>${
                                data.TrichYeu || ""
                            }</strong></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                            <p class="fs-big mb-2">Cơ quan ban hành: <b>${
                                data.CoQuanBanHanh || ""
                            }</b></p>
                            </div>
                            <div class="col-md-6">
                            <p class="fs-big mb-2">Số học sinh công nhận: <b>${
                                Array.isArray(data.HocSinhTN)
                                    ? data.HocSinhTN?.length
                                    : 0
                            }</b></p>

                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <p class="fs-big mb-2">Kỳ thi: <b>${
                                    data.ky_thi?.TenKyThi || ""
                                }</b></p>
                            </div>
                             <div class="col-3">
                                <p class="fs-big mb-2">Cấp học: <b>${
                                    data.ten_cap_hoc || ""
                                }</b></p>
                            </div>
                             <div class="col-3">

                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                           <p class="fs-big mb-2">Đính kèm:  ${
                               fileLinks
                                   ? `<a href="#" class="file-links" data-files="${fileLinks}" onclick="handleFileLinks(event)"><i class="fa fa-paperclip"></i>&ensp;Xem đính kèm</a>`
                                   : "Không có file đính kèm"
                           }</p>
                            </div>
                        </div>
                        </div>

                        <hr style="border-top: 2px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />
                        <div class="row">
                            <div class="col-md-3">
                                <p class="fs-big mb-2">Ngày ban hành: <b>${formatDate(
                                    data.NgayBanHanh || ""
                                )}</b></p>
                            </div>
                            <div class="col-md-3">
                                <p class="fs-big mb-2">Người ban hành: <b>${
                                    data.TenNguoiBH || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3 px-0">
                                <p class="fs-big mb-2">Chức vụ ban hành: <b>${
                                    data.ChucVuNguoiBH || ""
                                }</b></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <p class="fs-big mb-2">Nội dung ban hành: <b>${
                                    data.NoiDungBH ?? ""
                                }</b></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    `;

    return html;
}
//#endregion

/**
 * Dùng để chuyển từ view lưới sang view card
 *
 * Hàm này kiểm tra xem cột được chỉ định có phải là cột duy nhất đang hiển thị hay không.
 * Nếu đúng, nó sẽ hiển thị tất cả các cột. Nếu không, nó sẽ ẩn tất cả các cột ngoại trừ cột được chỉ định.
 *
 * @param {Object} table - Đối tượng bảng (table) cần thao tác.
 * @param {string} fieldName - Tên trường của cột cần hiển thị.
 */
function showCard(tableId, cardField, show) {
    const table = Tabulator.findTable(`#${tableId}`)[0];
    if (!table) {
        console.error(`Tabulator table with id '${tableId}' not found`);
        return;
    }

    const columns = table.getColumns();

    if (show) {
        // Hide all except the cardField column
        columns.forEach((col) => {
            if (col.getField() === cardField) {
                col.show();
            } else {
                col.hide();
            }
        });
    } else {
        // Show all columns again
        columns.forEach((col) => col.show());
    }

    table.redraw(true);
}
function resetMdThemMoi() {
    showStep(1);
    resetFileInput();
}
function prepareThemMoiData() {
    return Promise.all([LoadComboMdThemMoi()]);
}
document.addEventListener("DOMContentLoaded", function () {
    //#region Sự kiện
    document
        .getElementById("btnThemMoi")
        .addEventListener("click", function () {
            if (!QuyenThem()) return;
            $("#formThongTinChung")[0].reset();

            //Mở modal thêm mới
            selectedId = null;
            resetMdThemMoi();
            thaoTac = "them";

            $("#lblTieuDeMultiStep").text(
                "THÊM MỚI QUYẾT ĐỊNH CÔNG NHẬN TỐT NGHIỆP"
            );
            prepareThemMoiData()
                .then(() => {
                    //Chờ load xong  grid và combo mới hiển thị
                    document.getElementById("mdThemMoi").style.display =
                        "block";
                })
                .catch((err) => {
                    console.error("Error preparing data:", err);
                });
        });

    document
        .querySelector("#mdThemMoi .btn-close")
        .addEventListener("click", function () {
            document.getElementById("mdThemMoi").style.display = "none";
        });
    document
        .querySelector("#mdThemMoi #btnKetThuc")
        .addEventListener("click", function () {
            document.getElementById("mdThemMoi").style.display = "none";
        });

    $(document).on("click", ".btnChinhSuaQD", async function () {
        if (!QuyenSua()) {
            return;
        }
        let id = $(this).data("id"); //Mở modal chỉnh sửa
        resetMdThemMoi();
        selectedId = id;

        prepareThemMoiData()
            .then(() => {
                //Chờ load xong grid và combo mới hiển thị
                $("#lblTieuDeMultiStep").text(
                    "SỬA QUYẾT ĐỊNH CÔNG NHẬN TỐT NGHIỆP"
                );
                SuaDuLieu(id);

                document.getElementById("mdThemMoi").style.display = "block";
            })
            .catch((err) => {
                console.error("Error preparing data:", err);
            });

        thaoTac = "sua";
    });

    $(document).on("click", ".btnXemChiTietQD", async function () {
        document.getElementById("mdThemMoi").style.display = "none";
    });

    $(document).on("click", ".btnXoaQD", async function () {
        if (!QuyenXoa()) return;
        const id = $(this).data("id"); // gets data-id attribute properly
        XoaDuLieu(id);
    });
    //#region Xoá dữ liệu
    async function XoaDuLieu(ID) {
        var result_ktxoa = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.layouts.KiemTraXoa,
            {
                ma: ID,
                model: "QuanLy\\QuyetDinh",
            }
        );

        if (!result_ktxoa.Err) {
            CanhBaoXoa(async () => {
                var result = await NTS.getAjaxAPIAsync(
                    "DELETE",
                    window.location.pathname + "/xoa",
                    { ma: ID }
                );
                if (!result.Err) {
                    // grid1.setData();
                    SetMainGridData();
                    NTS.thanhcong(result.Msg);
                } else {
                    result.canhbao
                        ? NTS.canhbao(result.Msg)
                        : NTS.loi(result.Msg);
                }
            });
            return false;
        } else {
            NTS.canhbao(result_ktxoa.Msg);
            return false;
        }
    }

    $(document).on("click", "#btnTiepTuc", async function () {
        const validate = new NTSValidate("#mdThemMoi");
        if (!validate.trim().check()) return false;
        const soQuyetDinh = $("#SoQuyetDinh").val().trim();
        if (!soQuyetDinh) {
            NTS.canhbao("Vui lòng nhập Số quyết định.");
            $("#SoQuyetDinh").focus();
            return false;
        }

        if (typeof selectedId !== "undefined" && selectedId) {
            renderThongKeXepLoai("#thongKeXepLoaiBuoc2", selectedId);
        }

        // Collect basic input/select/textarea fields inside the fieldset
        const dinhKemQDValue = $("#dinhKemQD").val(); // get the string value
        const filesArray = dinhKemQDValue ? dinhKemQDValue.split("|") : [];
        const dataThongTinChung = {
            SoQuyetDinh: $("#SoQuyetDinh").val(),
            NguoiKy: $("#NguoiKy").val(),
            ChucVu: $("#ChucVu").val(), // select
            NgayKy: $("#NgayKy").val(),
            CoQuanBanHanh: $("#CoQuanBanHanh").val(),
            KyThi: $("#KyThi").val(),
            HinhThucDaoTao: $("#HinhThucDaoTao").val(),
            HoiDong: $("#HoiDong").val(),
            TrichYeu: $("#TrichYeu").val(),
            DinhKemQD: filesArray, // hidden input
            NamTotNghiep: $("#NamHoc").val(),
        };

        // Get selected row from Tabulator gridDonVi
        // const selectedRows = gridSelectDonVi.getSelectedData();

        // Assuming only 1 selection allowed
        // dataThongTinChung.DonVi = selectedRows[0];
        let method = "POST";

        if (typeof selectedId !== "undefined" && selectedId) {
            method = "PUT"; // or "PATCH"
            dataThongTinChung.QuyetDinhID = selectedId; // match your Laravel controller expectation
        }

        var result = await NTS.getAjaxAPIAsync(
            method,
            window.Laravel.luuThongTin,
            dataThongTinChung
        );
        if (!result.Err) {
            NTS.thanhcong(result.Msg);

            if (method === "POST") {
                selectedId = result.Data.id;
            }
        } else {
            result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            return false;
        }

        NTS.getAjaxAPIAsync("GET", Laravel.loadDuLieuSua, {
            id: selectedId,
        }).then((result) => {
            if (result.Err) {
                NTS.loi("Lỗi khi tải dữ liệu: " + (result.Msg || ""));
                return;
            }

            const data = result.Result;
            selectedBangDiem = data;

            // Format NgayBanHanh to dd/mm/yyyy if needed

            const soBangDiem = data.SoQuyetDinh || "";

            const fileLinks =
                Array.isArray(data.FileDinhKem) && data.FileDinhKem.length > 0
                    ? data.FileDinhKem.join("|") // join files with a separator like pipe |
                    : "";

            let message = `
            <div class="row">
                    <div class="col-12 col-xs-6 col-md-2 text-center d-flex align-items-center"  style="width:13.5%;">
                        <div class="row mt-2">
                            <img
                            class="d-block mx-auto w-75"
                            src="${Laravel.linkAnhHoSo}"
                            alt="icon quyết định">
                        </div>
                    </div>

                    <div class="col-md-10 px-2"  style="width:86.5%;">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <p class="fs-big mb-2">Số quyết định: <b>${
                                    data.SoQuyetDinh || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3">
                                <p class="fs-big mb-2">Ngày ký: <b>${
                                    data.NgayKy || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3 px-0">
                                <p class="fs-big mb-2">Người ký: <b>${
                                    data.NguoiKy || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3 d-flex justify-content-between align-items-center px-0">
                                <p class="fs-big mb-2">Chức vụ: <b>${
                                    data.chuc_vu?.tenChucVu || ""
                                }</b></p>
                            </div>
                        </div>
                        <hr style="margin: 0 0 0.5rem 0;" />
                        <div class="row align-items-center">
                        <div class="row">
                            <div class="col-12">
                            <p class="fs-big mb-2">Trích yếu/nội dung: <strong>${
                                data.TrichYeu || ""
                            }</strong></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                            <p class="fs-big mb-2">Cơ quan ban hành: <b>${
                                data.CoQuanBanHanh || ""
                            }</b></p>
                            </div>
                            <div class="col-md-6">
                            <p class="fs-big mb-2">Số học sinh công nhận: <b>${
                                Array.isArray(data.HocSinhTN)
                                    ? data.HocSinhTN?.length
                                    : 0
                            }</b></p>

                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <p class="fs-big mb-2">Kỳ thi: <b>${
                                    data.ky_thi?.TenKyThi || ""
                                }</b></p>
                            </div>
                             <div class="col-6">
                                <p class="fs-big mb-2">Cấp học: <b>${
                                    data.ten_cap_hoc || ""
                                }</b></p>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-12">
                           <p class="fs-big mb-2">Đính kèm:  ${
                               fileLinks
                                   ? `<a href="#" class="file-links" data-files="${fileLinks}"><i class="fa fa-paperclip"></i>&ensp;Xem đính kèm</a>`
                                   : "Không có file đính kèm"
                           }</p>
                            </div>
                        </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <p class="fs-big mb-2">Nội dung ban hành: <b>${
                                    data.NoiDungBH ?? ""
                                }</b></p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            // Now you can use `message` where needed, e.g.
            $("#contentCapNhatVB").html(message);
        });
        showStep(2);
        const cols = await getColsLuoiHocSinh(
            danhSachUuTien,
            danhSachXepLoaiRenLuyen
        );
        //destroy previous instance if exists
        if (tabGridHocSinh) {
            tabGridHocSinh.destroy();
        }
        // Initialize new Tabulator grid for students
        tabGridHocSinh = new Tabulator("#tabGridHocSinh", {
            ajaxURL: Laravel.getListHSByQD(selectedId),
            ...tabuLatorAjaxOptions,

            layout: "fitColumns",
            height: "70vh",
            maxHeight: "80vh",
            placeholder: "Không có dữ liệu",
            columns: cols,
        });
        tabGridHocSinh.on("cellEdited", async function (cell) {
            const row = cell.getRow();
            const oldVal = cell.getOldValue();
            const hs = row.getData();
            // build payload from the *one* row
            const data = {
                quyetDinhId: selectedId,
                hocSinhID: hs.HocSinhID,
                ketQuaRenLuyen: hs.KetQua_RL,
                ketQuaHocTap: hs.KetQua_HT,
                uuTien: hs.dien_uu_tien.id,
                ketQuaTotNghiep: hs.KetQuaTN,
                ghiChu: hs.GhiChu,
            };

            const method = "PUT";

            try {
                const resp = await NTS.getAjaxAPIAsync(
                    method,
                    Laravel.luuThongTinHocSinh,
                    data
                );

                if (resp.Err) {
                    // server‐side validation error
                    NTS.loi(resp.Msg || "Lỗi khi lưu dữ liệu");
                    cell.restoreOldValue(); // revert the edit
                } else {
                    NTS.thanhcong("Lưu thành công");
                }
            } catch (err) {
                // network or unexpected error
                console.error(err);
                NTS.loi("Đã xảy ra lỗi khi lưu dữ liệu");
                cell.restoreOldValue();
            }
        });

        setTimeout(() => {
            tabGridHocSinh.redraw(true);
        }, "300");
        // grid1.setData();
        SetMainGridData();
    });

    $(document).on("click", ".file-links", (event) => handleFileLinks(event));
    //#region Chọn học sinh
    $(document).on("click", "#btnChonVaDong", async function () {
        // Get selected rows data
        let selectedRows = gridChonHocSinh.getSelectedData();

        if (selectedRows.length === 0) {
            NTS.canhbao("Vui lòng chọn ít nhất một học sinh.");
            return;
        }

        switch (modeThemHS) {
            case enumModeThemHS.ThemNhieu: // Thêm nhiều (batch save)
                {
                    let allSelectedHocSinh =
                        Object.values(selectedHocSinhMap).flat();

                    let savePromises = allSelectedHocSinh.map((hocSinhID) => {
                        let data = {
                            quyetDinhId: selectedId,
                            hocSinhID: hocSinhID,
                        };
                        let method =
                            thaotacHocSinhTN === "sua" ? "PUT" : "POST";

                        return NTS.getAjaxAPIAsync(
                            method,
                            Laravel.luuThongTinHocSinh,
                            data
                        )
                            .then((response) => {
                                // Check API returned Err or not
                                if (response.Err) {
                                    return Promise.reject({
                                        message:
                                            response.Msg || "Unknown error",
                                        response,
                                    });
                                }
                                return { success: true, hocSinh: hocSinhID };
                            })
                            .catch((error) => ({
                                success: false,
                                hocSinh: hocSinhID,
                                error,
                            }));
                    });

                    Promise.all(savePromises).then((results) => {
                        const successList = results
                            .filter((r) => r.success)
                            .map((r) => r.hocSinh);
                        const failList = results.filter((r) => !r.success);

                        if (successList.length > 0) {
                            tabGridHocSinh.setData(
                                Laravel.getListHSByQD(selectedId)
                            );
                            setTimeout(() => {
                                tabGridHocSinh.redraw(true);
                            }, 300);
                        }

                        $("#mdThemMoiHocSinh").modal("hide");

                        if (failList.length === 0) {
                            NTS.thanhcong(
                                `Đã lưu thành công ${successList.length} học sinh.`
                            );
                        } else if (successList.length === 0) {
                            NTS.loi(
                                `Lưu thất bại toàn bộ ${failList.length} học sinh: ` +
                                    failList.map((f) => f.hocSinh).join(", ")
                            );
                        } else {
                            NTS.canhbao(
                                `Đã lưu thành công ${successList.length} học sinh.\n` +
                                    `Có ${failList.length} học sinh lưu thất bại: ` +
                                    failList.map((f) => f.hocSinh).join(", ")
                            );
                        }
                    });
                }
                break;
        }

        $("#mdChonHocSinh").modal("hide");
    });
    //#endregion
    //#endregion

    //#region Main grid QĐ
    table = new Tabulator("#Grid1", {
        ajaxURL: Laravel.getListQD, // Replace with your actual data URL
        ...commonTabulatorConfig,
        height: "80vh",
        headerVisible: false,
        columns: [
            {
                title: "Thông tin",
                field: "QuyetDinhID",
                formatter: htmlDuLieu,
                visible: true,
            },
            {
                title: "Số Quyết Định",
                field: "SoQuyetDinh",
                ...commonColumnConfig,
            },
            { title: "Người Ký", field: "NguoiKy", ...commonColumnConfig },
            {
                title: "Cấp Học",
                field: "CapHocID",
                hozAlign: "center",
                ...commonColumnConfig,
            },
            {
                title: "Ngày Ký",
                field: "NgayKy",
                hozAlign: "center",
                sorter: "date",
                ...commonColumnConfig,
            },

            {
                title: "Học Sinh TN Count",
                field: "HocSinhTN",
                hozAlign: "center",
                formatter: function (cell) {
                    let val = cell.getValue();
                    return Array.isArray(val) ? val.length : 0;
                },
                ...commonColumnConfig,
            },
            { title: "Ghi Chú", field: "GhiChu", ...commonColumnConfig },
            {
                title: "Trạng Thái",
                field: "TrangThai",
                hozAlign: "center",
                formatter: fmTrangThai,
                ...commonColumnConfig,
            },
        ],
    });
    table.on("dataLoaded", function (data) {
        showCard("Grid1", "QuyetDinhID", viewGridCard);
        setTimeout(() => {
            table.redraw(1);
        }, 300);
    });

    //#endregion

    //Xếp loại

    //#region Grid HS
    tabGridHocSinh = new Tabulator("#tabGridHocSinh", {
        ajaxParams: {},

        ...tabuLatorAjaxOptions,

        layout: "fitColumns",
        height: "70vh",
        maxHeight: "80vh",
        placeholder: "Không có dữ liệu",
        columns: [
            {
                title: "<i class='fa fa-ellipsis-h'></i>",
                formatter: btnThaoTac2,
                hozAlign: "center",
                headerSort: false,
                width: 40,
                ...commonColumnConfig,
            },
            {
                title: "Mã học sinh",
                field: "DoiTuong.MaDoiTuong",
                headerSort: false,
                ...commonColumnConfig,
            },
            {
                title: "Họ và tên",
                field: "DoiTuong.Hovaten",
                headerSort: false,
                ...commonColumnConfig,
                formatter: "textarea",
            },
            {
                title: "Số CMND/CCCD",
                width: 120,
                field: "DoiTuong.CCCD",
                headerSort: false,
                ...commonColumnConfig,
            },
            {
                title: "Ngày sinh",
                field: "DoiTuong.Ngaysinh",
                headerSort: false,
                hozAlign: "center",
                ...commonColumnConfig,
                formatter: "textarea",
            },
            {
                title: "Giới tính",
                field: "DoiTuong.gioi_tinh_.TenGioiTinh",
                headerSort: false,
                ...commonColumnConfig,
            },
            {
                title: "Nơi sinh",
                field: "DoiTuong.Noisinh",
                headerSort: false,
                ...commonColumnConfig,
            },
            {
                title: "Kết quả đánh giá",
                columns: [
                    {
                        title: "Rèn luyện",
                        field: "xep_loai_ren_luyen.tenXepLoai",
                        headerSort: false,
                        ...commonColumnConfig,
                        //editable
                        editor: "select",
                        editorParams: function (cell) {
                            // Get the list of xep_loai_ren_luyen from the cell data
                            const xepLoaiList =
                                cell.getData().xep_loai_ren_luyen;
                            return xepLoaiList.map((item) => ({
                                value: item.id,
                                label: item.tenXepLoai,
                            }));
                        },
                        formatter: "textarea",
                        validator: function (value) {
                            // Validate that a value is selected
                            if (!value || value === "") {
                                return "Vui lòng chọn xếp loại rèn luyện";
                            }
                            return true; // Return true if valid
                        },
                    },
                    {
                        title: "Học tập",
                        field: "xep_loai_hoc_tap.tenXepLoai",
                        headerSort: false,
                        ...commonColumnConfig,
                        //editable
                        editor: "select",
                        editorParams: function (cell) {
                            // Get the list of xep_loai_ren_luyen from the cell data
                            const xepLoaiList =
                                cell.getData().xep_loai_ren_luyen;
                            return xepLoaiList.map((item) => ({
                                value: item.id,
                                label: item.tenXepLoai,
                            }));
                        },
                        formatter: "textarea",
                        validator: function (value) {
                            // Validate that a value is selected
                            if (!value || value === "") {
                                return "Vui lòng chọn xếp loại rèn luyện";
                            }
                            return true; // Return true if valid
                        },
                    },
                ],
                ...commonColumnConfig,
            },
            {
                title: "Ưu tiên",
                field: "dien_uu_tien.TenDienUuTien",
                headerSort: false,
                ...commonColumnConfig,
            },
            {
                title: "Kết quả tốt nghiệp",
                field: "xep_loai_tot_nghiep.tenXepLoai",
                width: 150,

                headerSort: false,
                ...commonColumnConfig,
                editor: "select",
                editorParams: function (cell) {
                    // Get the list of xep_loai_ren_luyen from the cell data
                    const xepLoaiList = cell.getData().xep_loai_ren_luyen;
                    return xepLoaiList.map((item) => ({
                        value: item.id,
                        label: item.tenXepLoai,
                    }));
                },
                formatter: "textarea",
                validator: function (value) {
                    // Validate that a value is selected
                    if (!value || value === "") {
                        return "Vui lòng chọn xếp loại rèn luyện";
                    }
                    return true; // Return true if valid
                },
            },
            {
                title: "Ghi chú",
                field: "GhiChu",
                headerSort: false,
                ...commonColumnConfig,
                formatter: "textarea",
            },
        ],
    });

    $(document).on("click", "#tabGridHocSinh .btnXoaGrid1", function () {
        if (!QuyenXoa()) return;

        XoaDuLieu_HS($(this).attr("data"));
    });

    //#region Thêm mới học sinh
    //Nhiều học sinh
    $("#btnNhapNhieuHS").on("click", function () {
        $("#mdChonHocSinh").modal("show");
        thaotacHocSinhTN = "them";
        modeThemHS = enumModeThemHS.ThemNhieu;

        // gridTruongHoc.setData(Laravel.getListTruongHoc);
        loadTruongHoc(selectedId);

        if (gridChonHocSinh && typeof gridChonHocSinh.destroy === "function") {
            gridChonHocSinh.destroy();
        }

        gridChonHocSinh = initGridChonHocSinh(modeThemHS);

        gridChonHocSinh.on("rowSelected", (row) => {
            const idHS = row.getData().id;
            const dv = selectedTruongHoc.id;
            selectedHocSinhMap[dv] = selectedHocSinhMap[dv] || [];
            if (!selectedHocSinhMap[dv].includes(idHS)) {
                selectedHocSinhMap[dv].push(idHS);
            }
        });

        gridChonHocSinh.on("rowDeselected", (row) => {
            const idHS = row.getData().id;
            const dv = selectedTruongHoc.id;
            selectedHocSinhMap[dv] = (selectedHocSinhMap[dv] || []).filter(
                (i) => i !== idHS
            );
        });
    });
    //#endregion
    //#endregion
});
var thaotacHocSinhTN;

async function XoaDuLieu_HS(ID) {
    var result_ktxoa = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.layouts.KiemTraXoa,
        {
            ma: ID,
            model: "QuanLy\\HocSinhTN",
        }
    );

    if (!result_ktxoa.Err) {
        CanhBaoXoa(async () => {
            var result = await NTS.getAjaxAPIAsync("DELETE", Laravel.xoaHSTN, {
                quyetDinhId: selectedId,
                ma: ID,
            });
            if (!result.Err) {
                NTS.thanhcong(result.Msg);
                tabGridHocSinh.setData(Laravel.getListHSByQD(selectedId));
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
    } else {
        NTS.canhbao(result_ktxoa.Msg);
        return false;
    }
}
async function LoadComboMdThemMoi() {
    loadDataCombos([
        // 1. Chức vụ
        {
            name: "#ChucVu",
            ajaxUrl: Laravel.getListChucVu,
            ...commonComboConfig(),
        },
        // 2. Kỳ thi
        {
            name: "#KyThi",
            ajaxUrl: Laravel.getListKyThi,
            ...commonComboConfig(),
        },
        // 3. Hình thức đào tạo
        {
            name: "#HinhThucDaoTao",
            ajaxUrl: Laravel.getListHTDT,
            ...commonComboConfig(),
        },
        // 4. Hội đồng
        {
            name: "#HoiDong",
            ajaxUrl: Laravel.getListHoiDong,
            ...commonComboConfig(),
        },
        {
            name: "#NamHoc",
            ajaxUrl: Laravel.getListNamHoc,
            ...commonComboConfig(),
            columns: 1, // indexValue=0, indexText=1
            indexValue: 0,
            indexText: 1,
        },
    ]);
}

$(document).on("click", "#btnLuuVaDong", function () {
    switch (modeThemHS) {
        case enumModeThemHS.ThemNhieu: // Thêm nhiều (batch save)
            {
                let allSelectedHocSinh =
                    Object.values(selectedHocSinhMap).flat();

                let savePromises = allSelectedHocSinh.map((hocSinhID) => {
                    let data = {
                        quyetDinhId: selectedId,
                        hocSinhID: hocSinhID,
                    };

                    let method = thaotacHocSinhTN === "sua" ? "PUT" : "POST";

                    return NTS.getAjaxAPIAsync(
                        method,
                        Laravel.luuThongTinHocSinh,
                        data
                    )
                        .then((response) => {
                            // Check API returned Err or not
                            if (response.Err) {
                                return Promise.reject({
                                    message: response.Msg || "Unknown error",
                                    response,
                                });
                            }
                            return { success: true, hocSinh: hs };
                        })
                        .catch((error) => ({
                            success: false,
                            hocSinh: hs,
                            error,
                        }));
                });

                Promise.all(savePromises).then((results) => {
                    const successList = results
                        .filter((r) => r.success)
                        .map((r) => r.hocSinh);
                    const failList = results.filter((r) => !r.success);

                    if (successList.length > 0) {
                        tabGridHocSinh.setData(
                            Laravel.getListHSByQD(selectedId)
                        );
                        setTimeout(() => {
                            tabGridHocSinh.redraw(true);
                        }, 300);
                    }

                    $("#mdThemMoiHocSinh").modal("hide");

                    if (failList.length === 0) {
                        // All succeeded
                        NTS.thanhcong(
                            `Đã lưu thành công ${successList.length} học sinh.`
                        );
                    } else if (successList.length === 0) {
                        // All failed
                        NTS.loi(
                            `Lưu thất bại toàn bộ ${failList.length} học sinh: ` +
                                failList
                                    .map((f) => f.hocSinh.ten || f.hocSinh.id)
                                    .join(", ")
                        );
                    } else {
                        // Partial success
                        NTS.canhbao(
                            `Đã lưu thành công ${successList.length} học sinh.\n` +
                                `Có ${failList.length} học sinh lưu thất bại: ` +
                                failList
                                    .map((f) => f.hocSinh.ten || f.hocSinh.id)
                                    .join(", ")
                        );
                    }
                });
            }
            break;
    }
});

//#endregion

//#region Chuyển step modal thêm mới
// Simple step switch logic
function showStep(step) {
    document.getElementById("step-1-content").style.display =
        step === 1 ? "" : "none";
    document.getElementById("step-2-content").style.display =
        step === 2 ? "" : "none";

    // Update step sidebar highlight
    document
        .getElementById("sidebar-step-1")
        .classList.toggle("active", step === 1);
    document
        .getElementById("sidebar-step-2")
        .classList.toggle("active", step === 2);
}

// Attach next/prev button events (call showStep with 1 or 2)
document.addEventListener("DOMContentLoaded", function () {
    // "Tiếp tục" on Step 1

    // "Quay lại" on Step 2
    document.getElementById("btnQuayLaiBuoc1").onclick = function (e) {
        e.preventDefault();
        showStep(1);
    };
    // Initialize step 1
    showStep(1);
});

//#endregion Chuyển step

//#region Sửa dữ liệu
async function SuaDuLieu(id) {
    let quyetDinhInfo = await NTS.getAjaxAPIAsync(
        "GET",
        Laravel.loadDuLieuSua,
        { id: id }
    );

    if (quyetDinhInfo.Err) {
        NTS.loi(quyetDinhInfo.Msg || "Lỗi tải dữ liệu");
        return;
    }

    const data = quyetDinhInfo.Result;
    // Populate inputs
    $("#SoQuyetDinh").val(data.SoQuyetDinh || "");
    $("#NguoiKy").val(data.NguoiKy || "");
    $("#ChucVu").value(data.ChucVuID_NK || "");
    $("#NgayKy").value(data.NgayKy || "");
    $("#CoQuanBanHanh").val(data.CoQuanBanHanh || "");
    $("#KyThi").value(data.KyThiID || "");
    $("#HinhThucDaoTao").value(data.HinhThucID || "");
    $("#HoiDong").value(data.HoiDongID || "");
    $("#NamHoc").value(data.NamTotNghiep || "");

    $("#TrichYeu").val(data.TrichYeu || "");

    // Populate attached files if needed
    if (data.FileDinhKem) {
        let dinhKemArr = data.FileDinhKem;

        dinhKemArr?.forEach((url) => {
            renderAttachment(url);
        });
        uploadedFileUrls = dinhKemArr ?? [];
        $("#dinhKemQD").val(dinhKemArr?.join("|")).trigger("change");
    }
}

//#region Chọn học sinh
const customSelect = document.getElementById("customSelect");
const dropdown = document.getElementById("customDropdown");
const selectedOption = document.getElementById("selectedOption");
const hiddenSelect = document.getElementById("hocSinhID");
const btnDropdownHS = document.getElementById("btnDropdownHS");

btnDropdownHS.addEventListener("click", function () {
    $("#mdChonHocSinh").modal("show");

    // gridTruongHoc.setData(Laravel.getListTruongHoc);
    loadTruongHoc(selectedId);

    if (gridChonHocSinh && typeof gridChonHocSinh.destroy === "function") {
        gridChonHocSinh.destroy();
    }

    gridChonHocSinh = initGridChonHocSinh(modeThemHS);

    gridChonHocSinh.on("rowSelected", (row) => {
        const idHS = row.getData().id;
        const dv = selectedTruongHoc.id;
        selectedHocSinhMap[dv] = selectedHocSinhMap[dv] || [];
        if (!selectedHocSinhMap[dv].includes(idHS)) {
            selectedHocSinhMap[dv].push(idHS);
        }
    });

    gridChonHocSinh.on("rowDeselected", (row) => {
        const idHS = row.getData().id;
        const dv = selectedTruongHoc.id;
        selectedHocSinhMap[dv] = (selectedHocSinhMap[dv] || []).filter(
            (i) => i !== idHS
        );
    });
});

//#endregion

//#region Shortcut
$(document).on("keydown", function (e) {
    const modalSelectors = [
        "#mdThemMoi", // full screen panel (display block)
        "#mdThemMoiHocSinh", // Bootstrap modal
        "#mdChonHocSinh", // Bootstrap modal
        "#mdQuyetDinhTotNghiep", // Bootstrap modal
    ];

    // Filter visible modals/panels
    const visibleModals = modalSelectors.filter((sel) => {
        const el = document.querySelector(sel);
        if (!el) return false;

        if (sel === "#mdThemMoi") {
            // Use computed style to check visibility
            const style = window.getComputedStyle(el);
            return (
                style.display !== "none" &&
                style.visibility !== "hidden" &&
                style.opacity !== "0"
            );
        } else {
            return el.classList.contains("show");
        }
    });

    if (visibleModals.length === 0) return; // no open modal, ignore

    // Topmost modal is last in visibleModals
    const topModalSelector = visibleModals[visibleModals.length - 1];
    const topModal = document.querySelector(topModalSelector);

    // Helper: close the top modal
    function closeTopModal() {
        if (topModalSelector === "#mdThemMoi") {
            topModal.style.display = "none";
        } else {
            // Bootstrap modal hide
            const bsModal = bootstrap.Modal.getInstance(topModal);
            if (bsModal) bsModal.hide();
        }
    }

    // Helper: trigger submit on top modal's submit button if exists
    function submitTopModal() {
        let btn = null;
        switch (topModalSelector) {
            case "#mdThemMoi":
                // Determine current step: step 1 or step 2
                const step1Displayed =
                    document.getElementById("step-1-content").style.display !==
                    "none";
                const step2Displayed =
                    document.getElementById("step-2-content").style.display !==
                    "none";

                // Check which step is visible
                if (step1Displayed) {
                    // Step 1 active — trigger "Tiếp tục" button
                    btn = $("#btnTiepTuc");
                } else if (step2Displayed) {
                    // Step 2 active — trigger "Lưu và đóng" button
                    btn = $("#btnKetThuc");
                }
                break;
            case "#mdThemMoiHocSinh":
                btn = topModal.querySelector("#btnLuuVaDong");
                break;
            case "#mdChonHocSinh":
                btn = topModal.querySelector("#btnChonVaDong");
                break;
            case "#mdQuyetDinhTotNghiep":
                //Đã xử lý bên script còn lại
                // btn = topModal.querySelector("#btnBanHanh");
                break;
        }
        if (btn) btn.click();
    }

    // Only react if focus is inside top modal
    // if (!topModal.contains(document.activeElement)) return;

    if (e.key === "Escape" || e.key === "F4") {
        e.preventDefault();
        closeTopModal();
    } else if (e.key === "F9") {
        e.preventDefault();
        submitTopModal();
    } else if (e.key === "F8") {
        const step2Displayed =
            document.getElementById("step-2-content").style.display !== "none";
        if (step2Displayed) showStep(1);
    }
});
//#endregion

//#region TÌM KIẾM NÂNG CAO

$("#TimKiemNangCao").on("click", function () {
    $("#TrangThai_Loc").select2({
        data: [
            { id: "all", text: "-Tất cả-" },
            { id: "22", text: "Chưa ban hành" },
            { id: "20", text: "Đã ban hành" },
            { id: "21", text: "Thu hồi ban hành" },
        ],
        placeholder: "-Tất cả-", // placeholder text

        allowClear: false,
        width: "100%", // force full width
    });
    let cacheCapHoc = $("#CapHoc_Loc").val();
    loadDataCombos([
        // 1. Cap hoc
        {
            name: "#CapHoc_Loc",
            ajaxUrl: Laravel.getListToChuc,
            columns: 2, // indexValue=0, indexText=1
            indexValue: 0,
            indexText: 1,
            indexText1: 2,
            textShowTatCa: "-Chọn-",
            showTatCa: true,
        },
    ]).then(() => $("#CapHoc_Loc").value(cacheCapHoc));
});
$("#TimKiem").on("click", function () {
    SetMainGridData();
    if ($("#KhungTimKiem").css("display") == "block") {
        $("#KhungTimKiem").slideUp(200);
    } else {
        $("#KhungTimKiem").slideDown(200);
    }
});

function SetMainGridData() {
    const trangThaiLoc = ["20", "21", "22"].includes($("#TrangThai_Loc").val())
        ? $("#TrangThai_Loc").val()
        : null;
    const data = {
        tuNgay: $("#TuNgay_Loc").val() || null,
        denNgay: $("#DenNgay_Loc").val() || null,
        capHocID: $("#CapHoc_Loc").val() || null,
        trangThai: trangThaiLoc,
    };

    // Optional: clean empty strings to null
    for (const key in data) {
        if (data[key] === "" || data[key] === undefined) {
            data[key] = null;
        }
    }
    table.setData(Laravel.getListQD, data);
    setTimeout(() => {
        table.redraw(1);
    }, 300);
}
//#endregion

//#region Tìm kiếm chữ
$(document).on("keyup", "#timKiem", function (e) {
    var searchValue = $(this).val().trim().toLowerCase();

    if (!searchValue) {
        table.clearFilter();
        return;
    }

    table.setFilter(function (data) {
        // Defensive checks and lowercase conversion
        const soQuyetDinh = data.SoQuyetDinh
            ? data.SoQuyetDinh.toString().toLowerCase()
            : "";
        const nguoiKy = data.NguoiKy
            ? data.NguoiKy.toString().toLowerCase()
            : "";
        const capHocID =
            data.CapHocID !== null && data.CapHocID !== undefined
                ? data.CapHocID.toString().toLowerCase()
                : "";
        const ghiChu = data.GhiChu ? data.GhiChu.toString().toLowerCase() : "";

        return (
            soQuyetDinh.includes(searchValue) ||
            nguoiKy.includes(searchValue) ||
            capHocID.includes(searchValue) ||
            ghiChu.includes(searchValue)
        );
    });
});

$(document).on("keyup", "#searchContent", function (e) {
    const searchValue = $(this).val().trim().toLowerCase();

    if (!searchValue) {
        tabGridHocSinh.clearFilter();
        return;
    }

    tabGridHocSinh.setFilter(function (data) {
        // Defensive checks and lowercase conversion for nested fields
        const maDoiTuong =
            data.DoiTuong && data.DoiTuong.MaDoiTuong
                ? data.DoiTuong.MaDoiTuong.toString().toLowerCase()
                : "";
        const hoVaTen =
            data.DoiTuong && data.DoiTuong.Hovaten
                ? data.DoiTuong.Hovaten.toString().toLowerCase()
                : "";
        const cccd =
            data.DoiTuong && data.DoiTuong.CCCD
                ? data.DoiTuong.CCCD.toString().toLowerCase()
                : "";
        const gioiTinh =
            data.DoiTuong && data.DoiTuong.Gioitinh
                ? data.DoiTuong.Gioitinh.toString().toLowerCase()
                : "";
        const ngaySinh =
            data.DoiTuong && data.DoiTuong.NgaySinh
                ? data.DoiTuong.NgaySinh.toString().toLowerCase()
                : "";
        const noiSinh =
            data.DoiTuong && data.DoiTuong.Noisinh
                ? data.DoiTuong.Noisinh.toString().toLowerCase()
                : "";
        const ghiChu = data.GhiChu ? data.GhiChu.toString().toLowerCase() : "";
        const ketQuaRenLuyen =
            data.xep_loai_ren_luyen && data.xep_loai_ren_luyen.tenXepLoai
                ? data.xep_loai_ren_luyen.tenXepLoai.toString().toLowerCase()
                : "";
        const ketQuaHocTap =
            data.xep_loai_hoc_tap && data.xep_loai_hoc_tap.tenXepLoai
                ? data.xep_loai_hoc_tap.tenXepLoai.toString().toLowerCase()
                : "";
        const ketQuaTotNghiep =
            data.xep_loai_tot_nghiep && data.xep_loai_tot_nghiep.tenXepLoai
                ? data.xep_loai_tot_nghiep.tenXepLoai.toString().toLowerCase()
                : "";
        const uuTien = data.ThuocDienUuTien ? "x" : "";

        return (
            maDoiTuong.includes(searchValue) ||
            hoVaTen.includes(searchValue) ||
            cccd.includes(searchValue) ||
            gioiTinh.includes(searchValue) ||
            noiSinh.includes(searchValue) ||
            ghiChu.includes(searchValue) ||
            ketQuaRenLuyen.includes(searchValue) ||
            ketQuaHocTap.includes(searchValue) ||
            ketQuaTotNghiep.includes(searchValue) ||
            uuTien.includes(searchValue) ||
            ngaySinh.includes(searchValue)
        );
    });
});
//#endregion

//#region Xem chi tiết

//#region Xử lý sự kiện xem chi tiết quyết định
$(document).on("click", ".btnXemChiTietQD", async function () {
    selectedId = $(this).data("id");
    if (typeof selectedId !== "undefined" && selectedId) {
        renderThongKeXepLoai("#thongKeXepLoaiXemCT", selectedId);
    }

    // 1) show the Chi Tiết panel
    $("#mdChiTietQD").show();

    // 2) load quyết định info
    try {
        const resQD = await NTS.getAjaxAPIAsync("GET", Laravel.loadDuLieuSua, {
            id: selectedId,
        });
        if (resQD.Err) {
            NTS.loi(resQD.Msg || "Lỗi tải dữ liệu QĐ");
            return;
        }
        const data = resQD.Result;

        // 3) build summary HTML exactly like step-2 content
        const fileLinks = Array.isArray(data.FileDinhKem)
            ? data.FileDinhKem.join("|")
            : "";
        const html = `
        <div class="row">
          <div class="col-12 col-xs-6 col-md-2 text-center d-flex align-items-center" style="width:13.5%;">
            <img class="d-block mx-auto w-75"
                 src="${Laravel.linkAnhHoSo}"
                 alt="icon quyết định">
          </div>
          <div class="col-md-10 px-2" style="width:86.5%;">
            <div class="row align-items-center">
              <div class="col-md-3"><p class="fs-big mb-2">Số QĐ: <b>${
                  data.SoQuyetDinh || ""
              }</b></p></div>
              <div class="col-md-3"><p class="fs-big mb-2">Ngày ký: <b>${
                  data.NgayKy || ""
              }</b></p></div>
              <div class="col-md-3 px-0"><p class="fs-big mb-2">Người ký: <b>${
                  data.NguoiKy || ""
              }</b></p></div>
              <div class="col-md-3 px-0"><p class="fs-big mb-2">Chức vụ: <b>${
                  data.chuc_vu?.tenChucVu || ""
              }</b></p></div>
            </div>
            <hr style="margin:.5rem 0;" />
            <div class="row"><div class="col-12"><p class="fs-big mb-2">Trích yếu: <strong>${
                data.TrichYeu || ""
            }</strong></p></div></div>
            <div class="row">
              <div class="col-md-6"><p class="fs-big mb-2">Cơ quan ban hành: <b>${
                  data.CoQuanBanHanh || ""
              }</b></p></div>
              <div class="col-md-6"><p class="fs-big mb-2">Số HS công nhận: <b>${
                  Array.isArray(data.HocSinhTN) ? data.HocSinhTN.length : 0
              }</b></p></div>
            </div>
            <div class="row">
              <div class="col-6"><p class="fs-big mb-2">Kỳ thi: <b>${
                  data.ky_thi?.TenKyThi || ""
              }</b></p></div>
              <div class="col-6"><p class="fs-big mb-2">Cấp học: <b>${
                  data.ten_cap_hoc || ""
              }</b></p></div>
            </div>
            <div class="row">
                <div class="col-12">
                  <p class="fs-big mb-2">Đính kèm: ${
                      fileLinks
                          ? `<a href="#" class="file-links" data-files="${fileLinks}" onclick="handleFileLinks(event)"><i class="fa fa-paperclip"></i>&ensp;Xem đính kèm</a>`
                          : "Không có file"
                  }</p>
                </div>
            </div>
            <hr style="border-top: 2px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />
            <div class="row">
                <div class="col-md-3">
                                <p class="fs-big mb-2">Ngày ban hành: <b>${
                                    data.NgayBanHanh || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3">
                                <p class="fs-big mb-2">Người ban hành: <b>${
                                    data.TenNguoiBH || ""
                                }</b></p>
                            </div>
                            <div class="col-md-3 px-0">
                                <p class="fs-big mb-2">Chức vụ ban hành: <b>${
                                    data.ChucVuNguoiBH || ""
                                }</b></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <p class="fs-big mb-2">Nội dung ban hành: <b>${
                                    data.NoiDungBH ?? ""
                                }</b></p>
                            </div>
            </div>
          </div>
        </div>`;
        $("#contentCapNhatVBChiTiet").html(html);
    } catch (err) {
        console.error(err);
        NTS.loi("Lỗi khi tải chi tiết quyết định");
        return;
    }

    // 4) switch to the first tab (danh sách HS)
    $("#detail-ds-tab-chi-tiet").trigger("click");

    // 5) load the student grid
    try {
        const cols = await getColsLuoiHocSinhXem(
            danhSachUuTien,
            danhSachXepLoaiRenLuyen
        );
        if (tabGridHocSinhChiTiet) {
            tabGridHocSinhChiTiet.destroy();
        }
        tabGridHocSinhChiTiet = new Tabulator("#tabGridHocSinhChiTiet", {
            ajaxURL: Laravel.getListHSByQD(selectedId),
            ...tabuLatorAjaxOptions,
            layout: "fitColumns",
            height: "60vh",
            placeholder: "Không có dữ liệu",
            columns: cols,
        });

        // 6) handle inline edits
        tabGridHocSinhChiTiet.on("cellEdited", async (cell) => {
            const hs = cell.getRow().getData();
            const payload = {
                quyetDinhId: selectedId,
                hocSinhID: hs.HocSinhID,
                ketQuaRenLuyen: hs.KetQua_RL,
                ketQuaHocTap: hs.KetQua_HT,
                uuTien: hs.dien_uu_tien.id,
                ketQuaTotNghiep: hs.KetQuaTN,
                ghiChu: hs.GhiChu,
            };
            try {
                const resp = await NTS.getAjaxAPIAsync(
                    "PUT",
                    Laravel.luuThongTinHocSinh,
                    payload
                );
                if (resp.Err) {
                    NTS.loi(resp.Msg || "Lỗi khi lưu");
                    cell.restoreOldValue();
                } else {
                    NTS.thanhcong("Lưu thành công");
                }
            } catch {
                NTS.loi("Lỗi mạng, không lưu được");
                cell.restoreOldValue();
            }
        });

        $("#searchContentDetail")
            .off("keyup")
            .on("keyup", function () {
                const term = $(this).val();

                if (term) {
                    tabGridHocSinhChiTiet.setFilter((data) => {
                        // recursion to handle nested objects
                        function matches(value) {
                            if (value == null) return false;
                            const lowcaseTerm = (term || "").toLowerCase();

                            // otherwise treat as primitive
                            return JSON.stringify(value)
                                .toLowerCase()
                                .includes(lowcaseTerm);
                        }

                        // start by checking the entire data object
                        return matches(data);
                    });
                } else {
                    tabGridHocSinhChiTiet.clearFilter();
                }
            });
    } catch (err) {
        console.error(err);
        NTS.loi("Không thể tải danh sách học sinh");
    }
});
//#endregion
/**
 * Fetches and renders the “thống kê xếp loại” card into the given container.
 *
 * @param {string|jQuery} container     — selector or jQuery element for the card
 * @param {string}             quyetDinhId
 */
async function renderThongKeXepLoai(container, quyetDinhId) {
    const $card = $(container);
    const $body = $card.find(".card-body").empty();

    try {
        const resp = await NTS.getAjaxAPIAsync("GET", Laravel.getThongKeHS, {
            id: quyetDinhId,
        });
        if (resp.Err) {
            console.error("Không thể lấy thống kê:", resp.Msg || resp);
            $body.append(
                $("<div>").addClass("text-danger").text("Lỗi tải thống kê")
            );
            return;
        }

        const data = resp.hocSinhCounts || [];
        const total = data.reduce((sum, x) => sum + x.count, 0);

        // map from name → bootstrap class or inline style
        const colorMap = {
            "Xuất sắc": "bg-danger",
            Giỏi: null, // we'll inline
            Khá: "bg-primary",
            "Trung bình": "bg-info",
            "N/A": "bg-secondary",
        };

        data.forEach((item) => {
            // bar element
            const $bar = $("<div>")
                .addClass("stats-bar")
                .text(`${item.count}/${total}`);

            const cls = colorMap[item.ketQuaTN_name];
            if (cls) {
                $bar.addClass(cls);
            } else if (item.ketQuaTN_name === "Giỏi") {
                $bar.css("background-color", "#fd7e14");
            }

            // row wrapper
            const $row = $("<div>")
                .addClass("stats-item mb-2")
                .append($bar)
                .append(
                    $("<div>").addClass("stats-label").text(item.ketQuaTN_name)
                );

            $body.append($row);
        });

        // if you want fixed-height rows, you can also do:
        // $card.find('.stats-item').css('height', `${100/data.length}%`);
    } catch (err) {
        console.error("Lỗi khi render thống kê:", err);
        $body.append(
            $("<div>").addClass("text-danger").text("Lỗi tải thống kê")
        );
    }
}

// Usage example:
//   renderThongKeXepLoai('#thongKeXepLoaiXemCT', selectedId);

// Or for another card:
//   renderThongKeXepLoai('#anotherCardId', selectedId);

// If inside a modal show handler:
$(document).on("show.bs.modal", "#mdChiTietQD", function () {
    if (typeof selectedId !== "undefined" && selectedId) {
        renderThongKeXepLoai("#thongKeXepLoaiXemCT", selectedId);
    }
});
