var tempthem = "them";
var ChuaCoThongTin = "";
const namHienTai = new Date().getFullYear();
//#region logic thao tác click
let hotKey = 0;
$(function () {
    $(document).on("keydown", function (e) {
        switch (e.keyCode) {
            case 113:
                if (!$("#mdChonHocSinh").hasClass("show") && !$("#mdGiaoSoGoc").hasClass("show")) {
                    if (hotKey == 0) $("#btnThemMoi").trigger("click");
                }
                e.preventDefault();
                break;
            case 114:
                if (hotKey == 0) $(".nav-search-input").focus();
                e.preventDefault();
                break;
            case 115:
                if (!$("#mdChonHocSinh").hasClass("show") && !$("#mdGiaoSoGoc").hasClass("show")) {
                    if (hotKey == 0) $("#mdThemMoi").modal("hide");
                }
                if ($("#mdChonHocSinh").hasClass("show")) {
                    $("#mdChonHocSinh").modal("hide");
                }
                if ($("#mdGiaoSoGoc").hasClass("show")) {
                    $("#mdGiaoSoGoc").modal("hide");
                }
                e.preventDefault();
                break;
            case 120:
                if (!$("#mdChonHocSinh").hasClass("show") && !$("#mdGiaoSoGoc").hasClass("show")) {
                    if (hotKey == 0) $("#btnTiepTuc").trigger("click");
                    if (hotKey == 1) $("#btnKetThuc").trigger("click");
                }
                if ($("#mdChonHocSinh").hasClass("show")) {
                    $("#btnChonVaDong").trigger("click");
                }
                if ($("#mdGiaoSoGoc").hasClass("show")) {
                    $("#btnGiaoSoGoc").trigger("click");
                }
                e.preventDefault();
                break;
            case 119:
                if (!$("#mdChonHocSinh").hasClass("show") && !$("#mdGiaoSoGoc").hasClass("show")) {
                    if (hotKey == 1) $("#btnQuayLaiBuoc1").trigger("click");
                }
                hotKey = 0;
                e.preventDefault();
                break;
        }
    });
    LoadDataComBo();
    LoadDataTable();
});
$(document).on("keyup", "#SearchKey", async function (e) {
    if (e.keyCode == "13") {
        await LoadDataTable();
        $("#KhungTimKiem").slideUp(200);
        return false;
    }
});
$(document).on("click", "#TimKiemNangCao", function () {
    if ($("#KhungTimKiem").css("display") == "block") {
        $("#KhungTimKiem").slideUp(200);
    } else {
        $("#KhungTimKiem").slideDown(200);
    }
    return false;
});
$(document).on("click", "#DongTimKiem", function () {
    $("#KhungTimKiem").slideUp(200);
    return false;
});
$(document).on("click", "#TimKiem", async function () {
    $("#KhungTimKiem").slideUp(200);
    await LoadDataTable();
    return false;
});
$(document).on("click", "#btn-layout-1", async function () {
    $("#grid-layout").fadeIn(200);
    $("#list-layout").hide();
    $("#list-layout").removeClass("show");
    $("#grid-layout").addClass("show");
    $("#DivDanhSach").show();
    $("#DivLuoi").hide();
    table.redraw(true);
    GridMainLuoi.redraw(true);
});
$(document).on("click", "#btn-layout-2", async function () {
    $("#grid-layout").hide();
    $("#list-layout").fadeIn(200);
    $("#list-layout").addClass("show");
    $("#grid-layout").removeClass("show");
    $("#DivDanhSach").hide();
    $("#DivLuoi").show();
    table.redraw(true);
    GridMainLuoi.redraw(true);
});
$(document).on("change", "#CbSapXep", function () {
    LoadDataTable();
});
var OK = true;
$(document).on("change", "#QuyetDinhID", async function () {
    if ($("#QuyetDinhID").value() != "" && OK) {
        var result = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.local.quyetdinhtotnghiep,
            { id: $("#QuyetDinhID").value() }
        ).then(async function (result) {
            $("#NgayKy").value(result.Result.NgayKy);
            $("#NhanVienID_NguoiKy").value(result.Result.NguoiKy);
            $("#ChucVuID_NguoiKy").value(result.Result.ChucVuID);
            $("#HinhThucDaoTaoID").value(result.Result.HinhThucID);
            $("#NamTotNghiep").value(result.Result.NamTotNghiep);
        });
    }
});
function LoadDataComBo() {
    NTS.loadDataComboAsync({
        name: "#QuyetDinhID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListQuyetDinh,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#QuyetDinhID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListQuyetDinh,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#NhanVienID_NguoiKy",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListNhanvien,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuID_NguoiKy",
        type: "GET",
        ajaxUrl: window.Laravel.layouts.getChucVuList,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });

    NTS.loadDataComboAsync({
        name: "#HinhThucDaoTaoID",
        type: "post",
        ajaxUrl: window.Laravel.layouts.getHinhThucList,
        ajaxParam: {},
        columns: 1,
        indexValue: 2,
        indexText: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#QuyetDinhID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListQuyetDinh,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });

    NTS.loadDataComboAsync({
        name: "#NamTotNghiep",
        type: "GET",
        ajaxUrl: window.Laravel.layouts.getListNamHoc,
        columns: 1,
        indexValue: 1,
        indexText: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#NamTotNghiep_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.layouts.getListNamHoc,
        columns: 1,
        indexValue: 1,
        indexText: 1,
        textShowTatCa: "(Tất cả)",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#CapHocID_Loc",
        ajaxUrl: window.Laravel.layouts.GetDSCapHoc,
        columns: 1,
        indexValue: 2,
        indexText: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: true,
    });
    $("#TrangThai_Loc").select2({
        width: "100%"  // ✅ đúng cú pháp
    });
}
//#region Thêm xóa sửa...
$(document).on("click", "#btnThemMoi", function () {
    document.getElementById("mdThemMoi").style.display = "block";
    //$("#NamTotNghiep").value(namHienTai);
    // $("#QuyetDinhID").prop("disabled", false);
    // $("#NamTotNghiep").prop("disabled", false);
    // $("#TenTruongHoc").prop("disabled", false);
    // $("#DonViID_TruongHoc").prop("disabled", false);
    tempthem = "them";
    showStep(1);
    hotKey = 0;
});
$(document).on("click", ".modal-close", function () {
    document.getElementById("mdThemMoi").style.display = "none";
    tempthem = "them";
});
$(document).on("click", "#btnKetThuc", function () {
    document.getElementById("mdThemMoi").style.display = "none";
    tempthem = "them";
    LoadDataTable();
});
async function XemThongTin(id) {
    var result1 = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        {
            id: id,
        }
    );
    if (!result1.Err) {
        let data = result1.Result;
        $("#lblQuyetDinh_Xem").text(data.TenQuyetDinh);
        $("#lblNamTotNghiep").text(data.NamTotNghiep);
        $("#lblTenDonVi_Xem").text(data.TenDonVi);
        $("#lblTenHinhThuc_Xem").text(data.TenHinhThuc);
        $("#lblTenKyThi_Xem").text(data.TenKyThi);
        $("#lblTenKhoaThi_Xem").text(data.TenKhoaThi);
    }

    GridXemCT.clearData();
    let result2 = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.getallct,
        {
            SoGocID: id,
        }
    );
    if (!result2.Err) {
        GridXemCT.setData(result2.result);
    } else {
        GridXemCT.setData(null);
    }
    $("#mdXemThongTin").modal("show");
}
async function SuaThongTin(id) {
    hotKey = 0;
    OK = false;
    if (!QuyenSua()) {
        return;
    }
    resetForm("#mdThemMoi");
    $("#lblTieuDeMultiStep").text("THÊM MỚI SỔ GỐC CẤP VĂN BẰNG, CHỨNG CHỈ");
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        {
            id: id,
        }
    );
    if (!result.Err) {
        let data = result.Result;
        $("#QuyetDinhID").value(data.QuyetDinhID);
        $("#SoGocID").value(data.id);
        $("#NamTotNghiep").value(data.NamTotNghiep);
        $("#TenTruongHoc").value(data.TenDonVi);
        $("#DonViID_TruongHoc").value(data.DonViID_TruongHoc);
        $("#TrichYeu").value(data.TrichYeu);

        $("#SoSoGoc").value(data.SoSoGoc);
        $("#NgayKy").value(data.txtNgayKy);
        $("#NhanVienID_NguoiKy").value(data.NhanVienID_NguoiKy);
        $("#ChucVuID_NguoiKy").value(data.ChucVuID_NguoiKy);
        $("#HinhThucDaoTaoID").value(data.HinhThucDaoTaoID);
        $("#CoQuanBanHanh").value(data.CoQuanBanHanh);

        uploadedFileUrls = data.DinhKem
            ? data.DinhKem.split("|").filter((u) => u)
            : [];

        $("#SoGoc_txtDuongDanFileVB").value(data.DinhKem);
        $("#SoGoc_list-file").empty();

        uploadedFileUrls.forEach((url) => {
            renderAttachment(url);
        });
        tempthem = "sua";
        document.getElementById("mdThemMoi").style.display = "block";
        showStep(1);
        LoadSpanXepLoai();
    }
    OK = true;
}
async function OCRSoGoc(id) { NTS.canhbao("Chức năng đang phát triển.") }
async function InSoGoc(id) {
    NTS.loadding();
    await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.XuatExcelSoGoc,
        {
            SoGocID: id,
        }, false
    ).then(function (res) {

        if (!res.Err) {
            $('#divNoiDung_us').attr("src", window.location.origin + "/" + res.Result);
            document.getElementById("divNoiDung_us").setAttribute("data", window.location
                .origin + "/" + res.Result.replace(
                    ".pdf", ".xlsx"));
            $('#modal_xemtruockhiin_us').modal('show');
        } else res.canhbao ? NTS.canhbao(res.Msg) : NTS.loi(res.Msg);
    });
    NTS.unloadding();
}
async function ThuHoiSoGoc(id) {
    $("#SoGocID").value(id);
    NTS.loadDataComboAsync({
        name: "#NhanVienID_G",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListNhanvien,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuID_G",
        type: "GET",
        ajaxUrl: window.Laravel.layouts.getChucVuList,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        {
            id: id,
        }
    ).then(function (res) {
        $("#alertMessage_G").html(` Bạn đang thực hiện thu hồi sổ gốc cấp bằng tốt nghiệp số:
            <b>${res.Result.SoSoGoc || ""}</b> ngày ký <b>${res.Result.txtNgayKy || ""}</b> của <b>${res.Result.TenDonVi || ""}</b> về việc ${res.Result.TrichYeu || ""}. Vui
            lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>“Giao sổ”</b> để thực hiện
            thao tác ban hành cho quyết định.`);
        $("#NoiDungGiao_G").html(`Thu hồi sổ gốc số: <b>${res.Result.SoSoGoc || ""}</b> ngày ký <b>${res.Result.txtNgayKy || ""}</b> của <b>${res.Result.TenDonVi || ""}</b> về việc ${res.Result.TrichYeu || ""}`);
    });
    $("#md_NgayGiao").text('Ngày thu hồi');
    $("#md_NhanVien").text('Người thu hồi');
    $("#md_ChucVu").text('Chức vụ');
    $("#md_NoiDungGiao").text('Nội dung thu hồi');
    $("#btnGiaoSoGoc").html(`<i class="fa fa-close"></i> Thu hồi (F9)`);

    $("#NgayGiao_G").value('');
    $("#NhanVienID_G").value('');
    $("#ChucVuID_G").value('');

    $("#TrangThaiGiao").value("37");
    $("#mdGiaoSoGoc").modal("show");
};
async function GiaoSoGoc(id) {
    $("#SoGocID").value(id);
    NTS.loadDataComboAsync({
        name: "#NhanVienID_G",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListNhanvien,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuID_G",
        type: "GET",
        ajaxUrl: window.Laravel.layouts.getChucVuList,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/loaddulieusua",
        {
            id: id,
        }
    ).then(function (res) {
        debugger
        $("#alertMessage_G").html(` Bạn đang thực hiện giao sổ gốc cấp bằng tốt nghiệp số:
            <b>${res.Result.SoSoGoc || ""}</b> ngày ký <b>${res.Result.txtNgayKy || ""}</b> của <b>${res.Result.TenDonVi || ""}</b> về việc ${res.Result.TrichYeu || ""}. Vui
            lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>“Giao sổ”</b> để thực hiện
            thao tác ban hành cho quyết định.`);
        $("#NoiDungGiao_G").html(`Giao sổ gốc số: <b>${res.Result.SoSoGoc || ""}</b> ngày ký <b>${res.Result.txtNgayKy || ""}</b> của <b>${res.Result.TenDonVi || ""}</b> về việc ${res.Result.TrichYeu || ""}`);
    });
    $("#md_NgayGiao").text('Ngày giao');
    $("#md_NhanVien").text('Người giao');
    $("#md_ChucVu").text('Chức vụ');
    $("#md_NoiDungGiao").html('Nội dung giao');
    $("#btnGiaoSoGoc").html(`<i class="fa fa-check"></i> Giao sổ (F9)`);

    $("#NgayGiao_G").value('');
    $("#NhanVienID_G").value('');
    $("#ChucVuID_G").value('');

    $("#TrangThaiGiao").value("38");
    $("#mdGiaoSoGoc").modal("show");
};
$(document).on("click", "#btnGiaoSoGoc", async function () {
    const validate = new NTSValidate("#mdGiaoSoGoc");
    if (!validate.trim().check()) return false;
    const payload = {
        Ngay: $("#NgayGiao_G").value(),
        NhanVienID: $("#NhanVienID_G").value(),
        ChucVuID: $("#ChucVuID_G").value(),
        NoiDung: $("#NoiDungGiao_G").html(),
        SoGocID: $("#SoGocID").value(),
        TrangThai: $("#TrangThaiGiao").value(),
    };

    var result = await NTS.getAjaxAPIAsync(
        "POST",
        window.Laravel.local.Update_GiaoThuHoi,
        payload
    );

    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdGiaoSoGoc").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});
$(document).on("click", "#btnTiepTuc", async function () {
    const validate = new NTSValidate("#mdThemMoi");
    if (!validate.trim().check()) return false;
    // if ($("#DonViID_TruongHoc").value() == "") {
    //     NTS.canhbao("Trường học không được để trống!");
    //     return false;
    // }
    const payload = {
        SoSoGoc: $("#SoSoGoc").value(),
        NgayKy: $("#NgayKy").value(),
        NhanVienID_NguoiKy: $("#NhanVienID_NguoiKy").value(),
        ChucVuID_NguoiKy: $("#ChucVuID_NguoiKy").value(),
        HinhThucDaoTaoID: $("#HinhThucDaoTaoID").value(),
        CoQuanBanHanh: $("#CoQuanBanHanh").value(),
        QuyetDinhID: $("#QuyetDinhID").value(),
        NamTotNghiep: $("#NamTotNghiep").value(),
        DonViID_TruongHoc: $("#DonViID_TruongHoc").value(),
        TrichYeu: $("#TrichYeu").value(),
        DuongDanFileVB: $("#SoGoc_txtDuongDanFileVB").value(),

        SoGocID: $("#SoGocID").value(),
    };

    var met = "POST";
    if (tempthem == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }

    var result = await NTS.getAjaxAPIAsync(
        met,
        window.location.pathname + "/luuthongtin",
        payload
    );
    if (!result.Err) {
        //$("#lblQuyetDinh").text($("#select2-QuyetDinhID-container").text());
        $("#SoGocID").value(result.Result.id);
        try {
            $("#lblSoSoGoc_SG").text(result.Result.SoSoGoc);
            $("#lblNgayKy_SG").text(result.Result.txtNgayKy);
            $("#lblNguoiKy_SG").text(result.Result.NguoiKy);
            $("#lblChucVu_SG").text(result.Result.TenChucVu);
            $("#lblTrichYeu_SG").text(result.Result.TrichYeu);
            $("#lblCoQuanBanHanh_SG").text(result.Result.CoQuanBanHanh);
            $("#lblTruong_SG").text(result.Result.TenDonVi);
            $("#lblKyThi_SG").text(result.Result.TenKyThi);
            $("#lblCapHoc_SG").text(result.Result.TenCapHoc);
            $("#lblGhiChu_SG").text(result.Result.GhiChu);
            $("#lblDinhKem_SG").html(`Đính kèm: <a href="#" onclick="XemDinhKem_us('${result.Result.DinhKem}')" data="${result.Result.DinhKem}">Xem đính kèm</a>` || "Đính kèm: ");
        } catch { }
        LoadDataTableCT();
        NTS.thanhcong(result.Msg);
        showStep(2);
        hotKey = 1;
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});
async function XoaThongTin(id) {
    if (!QuyenXoa()) {
        return;
    }
    var result_ktxoa = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.layouts.KiemTraXoa,
        {
            ma: id,
            model: "QuanLy\\SoGoc",
        }
    );

    if (!result_ktxoa.Err) {
        CanhBaoXoa(async () => {
            var result = await NTS.getAjaxAPIAsync(
                "DELETE",
                window.location.pathname + "/xoa",
                { ma: id }
            );
            if (!result.Err) {
                LoadDataTable();
                NTS.thanhcong(result.Msg);
            } else {
                result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
            }
        });
        return false;
    } else {
        NTS.canhbao(result_ktxoa.Msg);
        return false;
    }
}
//#region Lưới 1
function htmlDuLieu(cell, formatterParams, onRendered) {

    let anhDaiDien = "";
    anhDaiDien =
        `<img style="object-fit:unset" src="${window.Laravel.local.imgLuoi}" alt="` +
        cell.getData().SoSoGoc +
        `" class="img-thumbnail rounded lazy">`;
    return `<div class="list-item col-md-12" style="padding: 0px;">
                        <div class="card card-luoi shadow-sm mb-2 ">
                            <div id="card_${cell.getData().id
        }" class="card-body profile-user-box">
                                <div class="row">
                                    <div class="col-sm-2 text-center" style="margin: auto;">
                                        <div class="profile-picture">
                                            ${anhDaiDien}
                                        </div>
                                    </div>
                                    <div class="col-sm-10">
                                        <div class="row">                                    
                                            <div class="col-sm-10">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <p class="fs-big my-1">Số sổ gốc: <b>${cell.getData().SoSoGoc || ChuaCoThongTin}</b></p>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <p class="fs-big my-1">Ngày ký: <b>${cell.getData().txtNgayKy || ChuaCoThongTin}</b></p>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <p class="fs-big my-1">Người ký: <b>${cell.getData().NguoiKy || ChuaCoThongTin}</b></p>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <p class="fs-big my-1">Chức vụ: <b>${cell.getData().TenChucVu || ChuaCoThongTin}</b></p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-2">
                                                <div class="dropdown text-end" style="position: absolute; top: 5px; right: 10px">
                                                    <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                        <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                                                    </button>
                                                    <div class="dropdown-menu dropdown-menu-end w-auto" >
                                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XemThongTin('${cell.getData().id
        }'); return false;">${xem_html}&ensp; Xem chi tiết sổ gốc cấp văn bằng, chứng chỉ</a>
                                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item ${cell.getData().Ma_Giao == "37" ? "" : "d-none"}" href="javascript:void(0);" onclick="SuaThongTin('${cell.getData().id
        }'); return false;">${sua_html}&ensp; Chỉnh sửa sổ gốc cấp văn bằng, chứng chỉ</a>
                                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item ${cell.getData().Ma_Giao == "38" ? "" : "d-none"}" href="javascript:void(0);" onclick="ThuHoiSoGoc('${cell.getData().id
        }')">${thuhoi_html}&ensp; Thu hồi sổ gốc cấp văn bằng, chứng chỉ</a>
                                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item ${cell.getData().Ma_Giao == "37" ? "" : "d-none"}" href="javascript:void(0);" onclick="GiaoSoGoc('${cell.getData().id
        }')">${giao_html}&ensp; Giao sổ gốc cấp văn bằng, chứng chỉ</a>
                                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item ${cell.getData().Ma_Giao == "37" ? "" : "d-none"}" href="javascript:void(0);" onclick="OCRSoGoc('${cell.getData().id
        }')">${ocr_html}&ensp; Trích xuất bảng điểm tự động (OCR)</a>
                                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item ${cell.getData().Ma_Giao == "37" ? "" : "d-none"}" href="javascript:void(0);" onclick="XoaThongTin('${cell.getData().id
        }')">${delete_html}&ensp; Xoá sổ gốc cấp văn bằng, chứng chỉ</a>
                                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="InSoGoc('${cell.getData().id
        }')">${in_html}&ensp; In sổ gốc cấp văn bằng, chứng chỉ</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <hr class="nts-color-them hr-custom">
                                        <div class="row">
                                            <div class="col-sm-10">
                                                    <div class="row">
                                                        <div class="col-md-12">
                                                            <p class="fs-big my-1">Trích yếu/nội dung: <b>${cell.getData().TrichYeu || ChuaCoThongTin}</b></p>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <p class="fs-big my-1">Cơ quan ban hành: <b>${cell.getData().CoQuanBanHanh || ChuaCoThongTin}</b></p>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <p class="fs-big my-1">Trường: <b>${cell.getData().TenDonVi || ChuaCoThongTin}</b></p>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-3">
                                                            <p class="fs-big my-1">Cấp học: <b>${cell.getData().TenCapHoc || ChuaCoThongTin}</b></p>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <p class="fs-big my-1">Kỳ thi: <b>${cell.getData().TenKyThi || ChuaCoThongTin}</b></p>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <p class="fs-big my-1">Đính kèm: ${cell.getData().DinhKem == "" ? "" : `<a href="#" onclick="XemDinhKem_us('${cell.getData().DinhKem}')" data="${cell.getData().DinhKem}">Xem đính kèm</a>`}</p>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-12">
                                                            <p class="fs-big my-1">Ghi chú: <b>${cell.getData().GhiChu || ChuaCoThongTin}</b></p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <hr class="nts-color-them hr-custom">
                                        </div>
                                         </div>
                                        <div class="row">
                                            <div class="col-md-2" style="text-align: center;">
                                                <span id="txtTrangThaiTN" class="alert d-inline-block span-trangthai" style="background-color:${cell.getData().MauSac_Giao};">
                                                ${cell.getData().TenTrangThai_Giao}
                                                </span>
                                            </div>
                                            <div class="col-sm-10">
                                                    <div class="row">
                                                        <div class="col-sm-10">
                                                            <div class="row">
                                                                <div class="col-md-3">
                                                                    <p class="fs-big my-1">Ngày giao: <b>${cell.getData().txtNgayGiao || ChuaCoThongTin}</b></p>
                                                                </div>
                                                                <div class="col-md-3">
                                                                    <p class="fs-big my-1">Người giao: <b>${cell.getData().TenNhanVienGiao || ChuaCoThongTin}</b></p>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <p class="fs-big my-1">Chức vụ: <b>${cell.getData().TenChucVuGiao || ChuaCoThongTin}</b></p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-sm-10">
                                                            <div class="row">
                                                                <div class="col-md-12">
                                                                    <p class="fs-big my-1">Nội dung giao: <b>${cell.getData().NoiDung_Giao || ChuaCoThongTin}</b></p>
                                                                </div>
                                                            </div>   
                                                        </div>   
                                                    </div>
                                                </div>
                                             </div>
                                        </div>
                                </div>
                            </div>
                        </div>
                    </div>`;
}
var table = new Tabulator("#GridMainDS", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "600",
    HeaderVertAlign: "center",
    headerVisible: false,
    columns: [
        {
            title: "Thông tin",
            field: "",
            formatter: htmlDuLieu,
            visible: true,
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

async function LoadDataTable() {
    table.clearData();
    GridMainLuoi.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getall",
        {
            SearchKey: $("#SearchKey").value(),
            QuyetDinhID: $("#QuyetDinhID_Loc").value(),
            CapHocID: $("#CapHocID_Loc").value(),
            NamTotNghiep: $("#NamTotNghiep_Loc").value(),
            TrangThai: $("#TrangThai_Loc").value(),
            CbSapXep: $("#CbSapXep").value(),
        }
    );
    if (!result.Err) {
        table.setData(result.result);
        GridMainLuoi.setData(result.result);
        table.redraw(true);
        GridMainLuoi.redraw(true);
    } else {
        table.setData(null);
        GridMainLuoi.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

//#region Lưới 2
function actionDropdownFormatter(cell) {
    var ID = cell.getData().id;
    var button = document.createElement("button");
    button.className = "btn btn-sm btn-white dropdown-toggle-hide-arrow";
    button.innerHTML = `<i class="fa fa-ellipsis-h" style="color: var(--primary)"></i>`;
    button.style.boxShadow = "none";

    button.onclick = function (e) {
        e.stopPropagation(); // Không lan click ra ngoài

        // Đóng dropdown cũ nếu có
        document
            .querySelectorAll(".custom-dropdown-menu")
            .forEach((el) => el.remove());

        // Tạo dropdown mới
        const dropdown = document.createElement("div");
        dropdown.className =
            "custom-dropdown-menu dropdown-menu dropdown-menu-end show";
        dropdown.style.position = "absolute";
        dropdown.style.zIndex = 9999;
        dropdown.style.minWidth = "200px";
        dropdown.innerHTML =
            `<a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XemThongTin('${ID
            }'); return false;">${xem_html}&ensp; Xem chi tiết sổ gốc cấp văn bằng, chứng ch</a>
                                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item ${cell.getData().Ma_Giao == "37" ? "" : "d-none"}" href="javascript:void(0);" onclick="SuaThongTin('${ID
            }'); return false;">${sua_html}&ensp; Chỉnh sửa sổ gốc cấp văn bằng, chứng chỉ</a>
                                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item ${cell.getData().Ma_Giao == "38" ? "" : "d-none"}" href="javascript:void(0);" onclick="ThuHoiSoGoc('${ID
            }')">${thuhoi_html}&ensp; Thu hồi sổ gốc cấp văn bằng, chứng chỉ</a>
                                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item ${cell.getData().Ma_Giao == "37" ? "" : "d-none"}" href="javascript:void(0);" onclick="GiaoSoGoc('${ID
            }')">${giao_html}&ensp; Giao sổ gốc cấp văn bằng, chứng chỉ</a>
                                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item ${cell.getData().Ma_Giao == "37" ? "" : "d-none"}" href="javascript:void(0);" onclick="OCRSoGoc('${ID
            }')">${ocr_html}&ensp; Trích xuất bảng điểm tự động (OCR)</a>
                                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item ${cell.getData().Ma_Giao == "37" ? "" : "d-none"}" href="javascript:void(0);" onclick="XoaThongTin('${ID
            }')">${delete_html}&ensp; Xoá sổ gốc cấp văn bằng, chứng chỉ</a>
                                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="InSoGoc('${ID
            }')">${in_html}&ensp; In sổ gốc cấp văn bằng, chứng chỉ</a>`;

        // Tính vị trí button
        const rect = button.getBoundingClientRect();
        dropdown.style.left = `${rect.left + window.scrollX}px`;
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;

        // Gắn ra body
        document.body.appendChild(dropdown);
    };

    return button;
}
var GridMainLuoi = new Tabulator("#GridMainLuoi", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: actionDropdownFormatter,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false,
        },
        {
            title: "Số sổ gốc",
            field: "SoSoGoc",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 150,
        },
        {
            title: "Ngày ký",
            field: "txtNgayKy",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 150,
        },
        {
            title: "Người ký",
            field: "NguoiKy",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 150,
        },
        {
            title: "Chức vụ",
            field: "TenChucVu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 150,
        },
        {
            title: "Cơ quan ban hành",
            field: "CoQuanBanHanh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 200,
        },
        {
            title: "Trường học",
            field: "TenDonVi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 200,
        },
        {
            title: "Trích yếu/nội dung",
            field: "TrichYeu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

//#region  Chọn đơn vị
$(document).on("click", "#btnChonDonViCha", function () {
    LoadGrid_ChonDonVi_us("");
    $("#mdChonDonVi_us").modal("show");
});
$(document).on("click", "#btnChonDonViVaDong_us", function () {
    if (Grid_ChonDonVi_us.getSelectedRows().length == 0) {
        NTS.canhbao("Vui lòng chọn 1 đơn vị!");
        return false;
    }

    var data = Grid_ChonDonVi_us.getSelectedRows()[0]._row.data;
    $("#TenTruongHoc").value(data.TenDonVi);
    $("#DonViID_TruongHoc").value(data.id);
    $("#mdChonDonVi_us").modal("hide");
});
//#endregion
//#region Chuyển step modal thêm mới
// Simple step switch logic
function showStep(step) {
    document.getElementById("step-1-content").style.display =
        step === 1 ? "" : "none";
    document.getElementById("step-2-content").style.display =
        step === 2 ? "" : "none";

    // Update step sidebar highlight
    document
        .getElementById("sidebar-step-1")
        .classList.toggle("active", step === 1);
    document
        .getElementById("sidebar-step-2")
        .classList.toggle("active", step === 2);

    // Optional: update title if needed
    document.getElementById("lblTieuDeMultiStep").textContent = "THÊM MỚI SỔ GỐC CẤP VĂN BẰNG, CHỨNG CHỈ";
}

// Attach next/prev button events (call showStep with 1 or 2)
document.addEventListener("DOMContentLoaded", function () {
    // "Tiếp tục" on Step 1
    // document.getElementById("btnTiepTuc").onclick = function (e) {
    //     e.preventDefault();
    //     showStep(2);
    // };
    // "Quay lại" on Step 2
    document.getElementById("btnQuayLaiBuoc1").onclick = function (e) {
        e.preventDefault();
        showStep(1);
    };
    // Initialize step 1
    showStep(1);
});

//#endregion Chuyển step

//#region Chi tiết
var fmThaoTac = function (cell) {
    return formaterbtnThaoTac(cell.getData().id);
};
$(document).on("click", ".btnSuaGrid1", function () {
    NTS.loadDataComboAsync({
        name: "#XepLoaiID_CT",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListXepLoai,
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    $("#SoGocCT").val($(this).attr("data"));
    SuaDuLieuCT($(this).attr("data"));
});

$(document).on("click", ".btnXoaGrid1", function () {
    $("#SoGocCT").val($(this).attr("data"));
    XoaDuLieuCT($(this).attr("data"));
});
async function SuaDuLieuCT(id) {
    if (!QuyenSua()) {
        return;
    }
    resetForm("#mdThemMoiHocSinh");
    $("#lblTieuDemdThemMoiHocSinh").text(
        "Cập nhật thông tin học sinh/sinh viên"
    );
    var result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.loaddulieusuact,
        {
            id: id,
        }
    );
    if (!result.Err) {
        let data = result.Result;
        $("#customSelect").text(data.TenHocSinh);
        $("#HocSinhID_CT").value(data.DoiTuongID_HocSinh);
        $("#txtDonViID_CT").value(data.TenTruong);
        $("#XepLoaiID_CT").value(data.XepLoaiID);
        $("#DiemThi_CT").value(data.DiemThi);
        $("#SoVaoSoGoc_CT").value(data.SoVaoSoGoc);
        $("#SoHieuVanBang_CT").value(data.SoHieuVanBang);
        $("#SoGocCTID").value(data.id);
        $("#TrichYeu_CT").value(data.TrichYeu);
        $("#mdThemMoiHocSinh").modal("show");
        tempthemCT = "sua";
    }
    LoadSpanXepLoai();
}
async function XoaDuLieuCT(id) {
    if (!(await QuyenXoa())) {
        return false;
    }
    CanhBaoXoa(async () => {
        var result = await NTS.getAjaxAPIAsync(
            "DELETE",
            window.Laravel.local.xoact,
            { ma: id }
        );
        if (!result.Err) {
            LoadDataTableCT();
            NTS.thanhcong(result.Msg);
        } else {
            result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        }
    });
    return false;
}
TextEditor = function (cell, onRendered, success, cancel, editorParams) {
    luoi = this.table;
    idLuoi = $(luoi.element).attr('id');
    cellValue = cell.getValue();
    editor = document.createElement("input");
    editor.setAttribute("id", "textedit");
    editor.setAttribute("type", "text");
    editor.setAttribute("maxlength", "510");
    editor.setAttribute("autocomplete", "off");
    editor.setAttribute("class", "form-control form-control-sm celledit");
    onRendered(function () {
        input = $("#textedit");
        input.value(cellValue);
        input.focus();
        input.on('change', async function (e) {
            await NTS.getAjaxAPIAsync(
                "POST",
                window.Laravel.local.Update_OneColum,
                {
                    type: 'TEXT',
                    model: 'App\\Models\\QuanLy\\SoGocCT',
                    id: cell.getRow().getData().id,
                    column: cell.getField(),
                    value: input.val(),
                }
            ).then(function (result) {
                //LoadDataTableCT();
                success(input.val());
                luoi.redraw(!0);
                luoi.scrollToRow(cell.getRow(), "center", false);
            });
        });
        input.on('focusout', function (e) {
            success(input.val());
            luoi.redraw(true);
            luoi.scrollToRow(cell.getRow(), "center", false);
        });
        input.on('blur', function (e) {
            cancel();
        });
    });
    return editor;
}
NumberEditor = function (cell, onRendered, success, cancel, editorParams) {

    luoi = this.table;
    idLuoi = $(luoi.element).attr('id');
    cellValue = cell.getValue();
    editor = document.createElement("input");
    editor.setAttribute("id", "textedit");
    editor.setAttribute("type", "text");
    editor.setAttribute("maxlength", "510");

    editor.setAttribute("class", "form-control form-control-sm celledit format-number");
    onRendered(function () {
        input = $("#textedit");
        input.value(cellValue);
        input.focus();
        input.on('change', async function (e) {
            await NTS.getAjaxAPIAsync(
                "POST",
                window.Laravel.local.Update_OneColum,
                {
                    type: 'TEXT',
                    model: 'App\\Models\\QuanLy\\SoGocCT',
                    id: cell.getRow().getData().id,
                    column: cell.getField(),
                    value: input.val(),
                }
            ).then(function (result) {
                //LoadDataTableCT();
                success(input.val());
                luoi.redraw(!0);
                luoi.scrollToRow(cell.getRow(), "center", false);
            });
        });
        input.on('focusout', function (e) {
            success(input.val());
            luoi.redraw(true);
            luoi.scrollToRow(cell.getRow(), "center", false);
        });
        input.on('blur', function (e) {
            cancel();
        });
    });
    return editor;
}
XepLoaiIDEditor = function (cell, onRendered, success, cancel, editorParams) {
    var luoi = this.table;
    var cellValue = cell.getValue();
    var IDKhoaChinh = cell.getRow().getData().id;
    var editor = document.createElement("select");
    var id = luoi.element.id + "editXepLoaiID";
    editor.setAttribute("id", id);
    editor.setAttribute("menu-width", "200%");
    editor.setAttribute("class", "form-control input-sm");
    onRendered(function () {
        var select_2 = $("#" + id);
        NTS.loadDataCombo({
            name: '#' + id,
            type: "GET",
            ajaxUrl: window.Laravel.local.getListXepLoai,
            columns: 1,
            indexValue: 0,
            indexText: 2,
            textShowTatCa: "",
            showTatCa: !0,
        });
        select_2.value(cell.getRow().getData().XepLoaiID);
        select_2.select2('open');
        select_2.on('select2:select', function (e) {
            var chuoi = select_2.value();
            var chuoiText = $('#select2-' + id + '-container').text();
            setTimeout(async function () {
                await NTS.getAjaxAPIAsync(
                    "POST",
                    window.Laravel.local.Update_OneColum,
                    {
                        type: 'GUID',
                        model: 'App\\Models\\QuanLy\\SoGocCT',
                        id: cell.getRow().getData().id,
                        column: "XepLoaiID",
                        value: chuoi,
                    }
                ).then(function (result) {
                    success(chuoiText);
                    luoi.redraw(true);
                    luoi.scrollToRow(cell.getRow(), "center", false)
                });
            }, 1);
        });

        select_2.on('change', function (e) {
            var chuoi = select_2.value();
            var chuoiText = $('#select2-' + id + '-container').text();
            setTimeout(async function () {
                await NTS.getAjaxAPIAsync(
                    "POST",
                    window.Laravel.local.Update_OneColum,
                    {
                        type: 'GUID',
                        model: 'App\\Models\\QuanLy\\SoGocCT',
                        id: cell.getRow().getData().id,
                        column: "XepLoaiID",
                        value: chuoi,
                    }
                ).then(function (result) {
                    success(chuoiText);
                    luoi.redraw(true);
                    luoi.scrollToRow(cell.getRow(), "center", false)
                });
            }, 1);
        });
        select_2.on('select2:closing', function (e) {
            var chuoi = select_2.value();
            var chuoiText = $('#select2-' + id + '-container').text();
            setTimeout(async function () {
                await NTS.getAjaxAPIAsync(
                    "POST",
                    window.Laravel.local.Update_OneColum,
                    {
                        type: 'GUID',
                        model: 'App\\Models\\QuanLy\\SoGocCT',
                        id: cell.getRow().getData().id,
                        column: "XepLoaiID",
                        value: chuoi,
                    }
                ).then(function (result) {
                    success(chuoiText);
                    luoi.redraw(true);
                    luoi.scrollToRow(cell.getRow(), "center", false)
                });;
            }, 1);
        });
        select_2.on('blur', function (e) {
            cancel();
        });
    });
    return editor;
}
var tableCT = new Tabulator("#GridCT", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    selectable: true,
    selectablePersistence: false, // disable rolling selection
    columns: [
        {
            formatter: "rowSelection", titleFormatter: "rowSelection", hozAlign: "center", vertAlign: "middle", cellClick: function (e, cell) {
                cell.getRow().toggleSelect();
            }, width: 40, headerSort: false, headerHozAlign: "center", frozen: true,
        },
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: fmThaoTac,
            width: 40,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false,
        },
        {
            title: "STT",
            field: "STT",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 60,
        },
        {
            title: "Họ và tên",
            field: "HoTenHocSinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 150,
        },
        {
            title: "CMND/CCCD",
            field: "CCCD",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "Center",
            vertAlign: "middle",
            visible: true,
            width: 130,
        },
        {
            title: "Ngày sinh",
            field: "txtNgaysinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "Center",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Nơi sinh",
            field: "NoiSinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "Center",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Giới tính",
            field: "TenGioiTinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Dân tộc",
            field: "TenDanToc",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Điểm thi",
            field: "DiemThi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            visible: true,
            width: 100,
            //editor: NumberEditor
        },
        {
            title: "Xếp loại",
            field: "TenXepLoai",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            visible: true,
            width: 100,
            //editor: XepLoaiIDEditor
        },
        { title: "XepLoaiID", field: "XepLoaiID", visible: false },
        {
            title: "Số hiệu",
            field: "SoHieuVanBang",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            visible: true,
            width: 130,
            //editor: TextEditor
        },
        {
            title: "Số vào sổ",
            field: "SoVaoSoGoc",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            visible: true,
            width: 130,
            //editor: TextEditor
        },
        {
            title: "Ghi chú",
            field: "GhiChu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
tableCT.on("rowSelectionChanged", function (data, rows) {
    DemSoRow();
});
function DemSoRow() {
    if (tableCT.getSelectedData().length == 0) {
        $('#txtSoRow').text(0);
        document.getElementById("spBoChon").style.color = "white";
    } else {
        document.getElementById("spBoChon").style.color = "red";
        $('#txtSoRow').text(tableCT.getSelectedData().length);
    }
};
function BoChon() {
    tableCT.deselectRow();
};
async function SuaGridTab1() {
    // Lấy dữ liệu các dòng được chọn
    let selectedData = tableCT.getSelectedData();
    if (selectedData.length == 0) {
        NTS.canhbao('Vui lòng chọn dữ liệu cần thao tác!');
        return false;
    }

    try {
        var result = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.local.LayMaTuTang,
            {
                kyhieuLoaiPhieu: "SoVaoSoGoc",
                bangDuLieu: "so_goc_c_t_s",
                cotDuLieu: "SoVaoSoGoc",
            }
        );

        $('#KyTuDauTien').value(result.KyTuDau);
        $('#SoTuTang').value(result.SoTuTang);
        $('#lblSoSoGoc_UD').text($("#lblSoSoGoc_SG").text());
        $('#lblNgayKy_UD').text($("#lblNgayKy_SG").text());
        $('#lblNguoiKy_UD').text($("#lblNguoiKy_SG").text());
        $('#lblTruong_UD').text($("#lblTruong_SG").text());
        $('#lblTrichYeu_UD').text($("#lblTrichYeu_SG").text());
        $('#lblSoDong_UD').text(selectedData.length + " dòng đã chọn");
    } catch { }
    $('#mdUpdateSoGocCT').modal('show');
    return false;
};
$(document).on("click", "#btnUpdateSoGocCT", async function () {
    let selectedData = tableCT.getSelectedData();
    let kyTuDau = $('#KyTuDauTien').val().trim();
    let soTuTang = parseInt($('#SoTuTang').val().trim());

    if (!kyTuDau || isNaN(soTuTang) || soTuTang <= 0) {
        NTS.canhbao("Vui lòng nhập đúng ký tự đầu và số tự tăng!");
        return false;
    }

    for (let i = 0; i < selectedData.length; i++) {
        let row = selectedData[i];
        // Gọi API update
        await NTS.getAjaxAPIAsync(
            "POST",
            window.Laravel.local.Update_SoVaoSoGoc,
            {
                id: row.id,
                KyTuDauTien: kyTuDau,
                SoTuTang: soTuTang,
            }
        ).then(function (res) {
            if (res.success) {
                soTuTang = res.SoTuTangKeTiep; // Cập nhật số kế tiếp từ server trả về
            }
        }).catch(function (e) {
            NTS.canhbao("❌ Lỗi cập nhật dòng có ID: " + row.id);
        });
    }
    NTS.thanhcong("Cập nhật hoàn tất!");
    $('#mdUpdateSoGocCT').modal('hide');
    LoadDataTableCT();
});
function XoaGridTab1() {
    let selectedData = tableCT.getSelectedData();
    var dem = 0;
    if (selectedData.length == 0) {
        NTS.canhbao('Vui lòng chọn dữ liệu cần thao tác!');
        return false;
    }
    CanhBaoXoaDongChon(async () => {
        var dem = 0;
        var demloi = "";
        for (let i = 0; i < selectedData.length; i++) {
            var result = await NTS.getAjaxAPIAsync(
                "DELETE",
                window.Laravel.local.xoact,
                { ma: selectedData[i].id }
            );
            if (!result.Err) {
                dem++;
            } else demloi = result_ktxoa.Result;
        }
        if (demloi != "") {
            NTS.thanhcong("Xóa thành công " + dem + "/" + selectedData.length + " dòng dữ liệu! <br/> <b>Dữ liệu đang sử dụng tại.</b><br/>" + demloi);
        } else {
            NTS.thanhcong("Xóa thành công " + dem + "/" + selectedData.length + " dòng dữ liệu!");
        }
        LoadDataTableCT();
    });
    return false;
};
$(document).on("keyup", "#searchContent", function (e) {
    var data = $(this).val();
    if (e.keyCode == "13") {
        if (data == "" || data == undefined) {
            data = $("#searchContent").value();
        }
        dulieuloc = data;
        tableCT.setFilter([
            [
                {
                    field: "HoTenHocSinh",
                    type: "like",
                    value: data,
                },
                {
                    field: "NoiSinh",
                    type: "like",
                    value: data,
                },
                {
                    field: "TenDanToc",
                    type: "like",
                    value: data,
                },
            ],
        ]);
    }
});
$(document).on("click", "#btnChonVaDong", async function () {
    if (GridChonHocSinh.getSelectedRows().length == 0) {
        NTS.canhbao("Vui lòng chọn 1 học sinh!");
        return false;
    }
    $("#mdChonHocSinh").modal("hide");

    let danhSachDaChon = GridChonHocSinh.getSelectedRows();

    for (let i = 0; i < danhSachDaChon.length; i++) {
        let hocSinh = danhSachDaChon[i];
        const payload = {
            DoiTuongID_HocSinh: hocSinh._row.data.id,
            XepLoaiID: hocSinh._row.data.KetQuaTN,
            DiemThi: "",
            SoVaoSoGoc: NTS.getAjaxAPI("GET", window.Laravel.local.maTuTangUrlSoVaoSoGoc, {}).SoChungTu,
            SoHieuVanBang: NTS.getAjaxAPI("GET", window.Laravel.local.maTuTangUrlSoHieuVanBang, {}).SoChungTu,
            GhiChu: "",
            SoGocID: $("#SoGocID").value(),
            SoGocCTID: $("#SoGocCTID").value(),
        };

        // var met = "POST";
        // if (tempthemCT == "them") {
        met = "POST";
        // } else {
        //     met = "PUT";
        // }
        var result = NTS.getAjaxAPI(
            met,
            window.Laravel.local.luuthongtinct,
            payload
        );
        // if (!result.Err) {
        //     LoadDataTableCT();
        //     NTS.thanhcong(result.Msg);
        //     $("#mdThemMoiHocSinh").modal("hide");
        // } else {
        //     result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        //     return false;
        // }
    }
    LoadDataTableCT();
    NTS.thanhcong(result.Msg);
    $("#mdThemMoiHocSinh").modal("hide");
    // $("#mdThemMoiHocSinh").modal("show");

    // var data = GridChonHocSinh.getSelectedRows()[0]._row.data;
    // $("#customSelect").text(data.Hovaten);
    // $("#HocSinhID_CT").value(data.id);
    // $("#txtDonViID_CT").value(data.TenDonVi);
    // $("#XepLoaiID_CT").value(data.KetQuaTN);
});
async function LoadDataTableCT() {
    tableCT.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.getallct,
        {
            SoGocID: $("#SoGocID").value(),
        }
    );
    if (!result.Err) {
        tableCT.setData(result.result);
        tableCT.redraw();
        $(".btnSuaGrid1").hide();
    } else {
        tableCT.setData(null);
        tableCT.redraw();
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

$("#btnThemMoiHS").on("click", function () {
    resetForm("#mdThemMoiHocSinh");
    $("#mdThemMoiHocSinh").modal("show");
    tempthemCT = "them";
    NTS.loadDataComboAsync({
        name: "#XepLoaiID_CT",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListXepLoai,
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    $("#lblTieuDemdThemMoiHocSinh").text("Thêm mới học sinh/sinh viên");
    $("#selectedOption").html("Chọn học sinh");
});
$("#btnLuuVaDongCT").on("click", async function () {
    const validate = new NTSValidate("#mdThemMoiHocSinh");
    if (!validate.trim().check()) return false;
    const payload = {
        DoiTuongID_HocSinh: $("#HocSinhID_CT").value(),
        XepLoaiID: $("#XepLoaiID_CT").value(),
        DiemThi: $("#DiemThi_CT").value(),
        SoVaoSoGoc: $("#SoVaoSoGoc_CT").value(),
        SoHieuVanBang: $("#SoHieuVanBang_CT").value(),
        TrichYeu: $("#TrichYeu_CT").value(),
        SoGocID: $("#SoGocID").value(),
        SoGocCTID: $("#SoGocCTID").value(),
    };
    var met = "POST";
    if (tempthemCT == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }

    var result = await NTS.getAjaxAPIAsync(
        met,
        window.Laravel.local.luuthongtinct,
        payload
    );
    if (!result.Err) {
        LoadDataTableCT();
        NTS.thanhcong(result.Msg);
        $("#mdThemMoiHocSinh").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});

$("#btnDropdownHS").on("click", function () {
    $("#mdChonHocSinh").modal("show");
    // $("#mdThemMoiHocSinh").modal("hide");
    LoadDataTableTruongHoc();
    LoadDataTableHocSinh("");
});

$(".close-mdChonHocSinh").on("click", function () {
    // $("#mdChonHocSinh").modal("hide");
    // $("#mdThemMoiHocSinh").modal("show");
});
//#region Lưới Trường Học
var GridTruongHoc = new Tabulator("#GridTruongHoc", {
    layout: "fitColumns",
    height: "350",
    selectableRows: 1,
    selectable: 1,
    HeaderVertAlign: "center",
    columns: [
        {
            field: "action",
            width: "30",
            formatter: function () {
                return '<i class="fas fa-hand-point-right text-nts-primary"></i>';
            },
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            minWidth: 30,
        },
        {
            title: "Tên trường",
            field: "name",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            minWidth: 100,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
$(document).on("keyup", "#timKiemTH", function (e) {
    var data = $(this).val();
    if (e.keyCode == "13") {
        if (data == "" || data == undefined) {
            data = $("#timKiemTH").value();
        }
        dulieuloc = data;
        GridTruongHoc.setFilter([
            [
                {
                    field: "name",
                    type: "like",
                    value: data,
                },
            ],
        ]);
    }
});
GridTruongHoc.on("rowClick", async function (e, row) {
    LoadDataTableHocSinh(row.getData().id);
});
async function LoadDataTableTruongHoc() {
    GridTruongHoc.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.GetListDonViCoHocSinh,
        {
            QuyetDinhID: $("#QuyetDinhID").value(),
            SoGocID: $("#SoGocID").value(),
        }
    );
    if (!result.Err) {
        GridTruongHoc.setData(result.Result);
    } else {
        GridTruongHoc.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}
//#region Lưới Học sinh
const commonColumnConfig = {
    headerHozAlign: "center",
    headerVertAlign: "center",
    vertAlign: "middle",
};
var GridChonHocSinh = new Tabulator("#GridChonHocSinh", {
    layout: "fitColumns",
    height: "350",
    HeaderVertAlign: "center",
    columns: [
        {
            formatter: "rowSelection",
            titleFormatter: "rowSelection",
            hozAlign: "center",
            headerSort: false,
            width: 50,
            selectable: true,
            cellClick: function (e, cell) {
                cell.getRow().toggleSelect();
            },
            ...commonColumnConfig,
        },
        {
            title: "Mã học sinh",
            field: "MaDoiTuong",
            width: 120,
            ...commonColumnConfig,
        },
        {
            title: "Họ và tên",
            field: "Hovaten",
            width: 150,
            formatter: "textarea",
            ...commonColumnConfig,
        },
        {
            title: "CMND/CCCD",
            field: "CCCD",
            width: 150,
            ...commonColumnConfig,
        },
        {
            title: "Ngày sinh",
            field: "txtNgaysinh",
            sorter: "date",
            hozAlign: "center",
            width: 120,
            ...commonColumnConfig,
        },
        {
            title: "Giới tính",
            field: "TenGioiTinh",
            hozAlign: "center",
            width: 100,
            ...commonColumnConfig,
        },
        {
            title: "Dân tộc",
            field: "TenDanToc",
            width: 120,
            ...commonColumnConfig,
        },
        {
            title: "Nơi sinh",
            field: "Noisinh",
            width: 150,
            ...commonColumnConfig,
        },
        {
            title: "Địa chỉ",
            field: "DiaChi",
            width: 200,
            ...commonColumnConfig,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
$(document).on("keyup", "#timKiemHS", function (e) {
    var data = $(this).val();
    if (e.keyCode == "13") {
        if (data == "" || data == undefined) {
            data = $("#timKiemHS").value();
        }
        dulieuloc = data;
        GridChonHocSinh.setFilter([
            [
                {
                    field: "Hovaten",
                    type: "like",
                    value: data,
                },
                {
                    field: "CCCD",
                    type: "like",
                    value: data,
                },
                {
                    field: "MaDoiTuong",
                    type: "like",
                    value: data,
                },
            ],
        ]);
    }
});
async function LoadDataTableHocSinh(ID) {
    GridChonHocSinh.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.GetListHocSinhByDonVi,
        {
            DonViID: ID,
            QuyetDinhID: $("#QuyetDinhID").value(),
        }
    );
    if (!result.Err) {
        GridChonHocSinh.setData(result.result);
    } else {
        GridChonHocSinh.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

//#region Xem chi tiết
var GridXemCT = new Tabulator("#GridXemCT", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "350",
    HeaderVertAlign: "center",
    columns: [
        {
            title: "STT",
            field: "STT",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            visible: true,
            width: 60,
        },
        {
            title: "Họ và tên người học",
            field: "HoTenHocSinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 250,
        },
        {
            title: "Ngày tháng năm sinh",
            field: "txtNgaysinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            visible: true,
            width: 160,
        },
        {
            title: "Nơi sinh",
            field: "NoiSinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 300,
        },
        {
            title: "Giới tính",
            field: "Gioitinh",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 110,
        },
        {
            title: "Dân tộc",
            field: "TenDanToc",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 110,
        },
        {
            title: "Điểm thi",
            field: "DiemThi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Xếp loại",
            field: "TenXepLoai",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 150,
        },
        {
            title: "Số hiệu văn bằng",
            field: "SoHieuVanBang",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
        {
            title: "Số vào sổ gốc cấp văn bằng",
            field: "SoVaoSoGoc",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
        {
            title: "Ghi chú",
            field: "TrichYeu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});
$(document).on("keyup", "#timKiem_Xem", function (e) {
    var data = $(this).val();
    if (e.keyCode == "13") {
        if (data == "" || data == undefined) {
            data = $("#timKiem_Xem").value();
        }
        dulieuloc = data;
        GridXemCT.setFilter([
            [
                {
                    field: "HoTenHocSinh",
                    type: "like",
                    value: data,
                },
                {
                    field: "NoiSinh",
                    type: "like",
                    value: data,
                },
                {
                    field: "TenDanToc",
                    type: "like",
                    value: data,
                },
            ],
        ]);
    }
});
function LoadSpanXepLoai() {
    let result = NTS.getAjaxAPI(
        "GET",
        window.Laravel.local.LayThongTinThongKeXepLoai,
        {
            SoGocID: $("#SoGocID").value(),
        }
    );
    const mauXepLoai = ['#e50000', '#fd7e14', '#1e90ff', '#66c3e9', '#6c757d', '#a0522d', '#ffc107', '#28a745'];
    var html = "";
    for (let i = 0; i < result.length; i++) {
        let bgColor = mauXepLoai[i % mauXepLoai.length]; // vòng lại nếu quá số màu
        html += `<div class="d-flex align-items-center mb-2">
                                            <div class="px-3 py-1 text-white fw-bold rounded me-2"
                                                style="background:${bgColor}; min-width: 60px; text-align:center;"
                                                id="spanXuatSac">
                                                ${result[i].SoLuong}/${result[i].TongCong}</div>
                                            <div class="fw-bold">${result[i].TenXepLoai}</div>
                                        </div>`;
    }
    $("#TKXepLoai").html(html);
}
var xem_html = `<i class="fa fa-eye text-success" aria-hidden="true"></i>`;
var sua_html = `<i class="fa fa-pencil text-primary" aria-hidden="true"></i>`;
var giao_html = `<i class="fa fa-paper-plane-o" style="color:#00aaffff" aria-hidden="true"></i>`;
var thuhoi_html = `<i class="fa fa-times" aria-hidden="true" style="color:#e50000"></i>`;
var ocr_html = `<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 20 20" fill="none">
  <path d="M3 1C2.46957 1 1.96086 1.21071 1.58579 1.58579C1.21071 1.96086 1 2.46957 1 3V7.5H3V3H8V1H3ZM1 17V11.5H3V17H8V19H3C2.46957 19 1.96086 18.7893 1.58579 18.4142C1.21071 18.0391 1 17.5304 1 17ZM12 17V19H17C17.5304 19 18.0391 18.7893 18.4142 18.4142C18.7893 18.0391 19 17.5304 19 17V11.5H17V17H12ZM17 7.5H19V3C19 2.46957 18.7893 1.96086 18.4142 1.58579C18.0391 1.21071 17.5304 1 17 1H12V3H17V7.5Z" fill="#F76707"/>
  <path d="M6 5H14V6H6V5ZM5 8H15V9H5V8ZM6 11H14V12H6V11ZM5 14H15V15H5V14Z" fill="#F76707"/>
</svg>`;
var delete_html = `<i class="fa fa-trash-o text-danger" aria-hidden="true"></i>`;
var in_html = `<svg xmlns="http://www.w3.org/2000/svg" width="15" height="20" viewBox="0 0 24 25" fill="none">
  <path d="M18 7.35303H6V3.35303H18V7.35303ZM18 12.853C18.2833 12.853 18.521 12.757 18.713 12.565C18.905 12.373 19.0007 12.1357 19 11.853C18.9993 11.5704 18.9033 11.333 18.712 11.141C18.5207 10.949 18.2833 10.853 18 10.853C17.7167 10.853 17.4793 10.949 17.288 11.141C17.0967 11.333 17.0007 11.5704 17 11.853C16.9993 12.1357 17.0953 12.3734 17.288 12.566C17.4807 12.7587 17.718 12.8544 18 12.853ZM16 19.353V15.353H8V19.353H16ZM18 21.353H6V17.353H2V11.353C2 10.503 2.29167 9.79069 2.875 9.21603C3.45833 8.64136 4.16667 8.35369 5 8.35303H19C19.85 8.35303 20.5627 8.64069 21.138 9.21603C21.7133 9.79136 22.0007 10.5037 22 11.353V17.353H18V21.353Z" fill="#F76707"/>
</svg>`;
