var tempthem = "them";
var tempthemCapPhoi = "them";
var tempTrangThai = "32";
var ChuaCoThongTin = "";
///////// PHÍM TẮT /////////
var hotKey = 0; // 1 thêm\
var currentAvatarPath = "";
const today = new Date();
const dd = String(today.getDate()).padStart(2, '0');
const mm = String(today.getMonth() + 1).padStart(2, '0');
const yyyy = today.getFullYear();
const defaultDate = `${dd}/${mm}/${yyyy}`;
var selectedId;

$(function () {
    $(document).on("keydown", function (e) {
        switch (e.keyCode) {
            case 113:
                if (hotKey == 0) $("#btnThemMoi").trigger("click");
                e.preventDefault();
                break;
            case 114:
                if (hotKey == 0) $(".nav-search-input").focus();
                e.preventDefault();
                break;
            case 115:
                if (hotKey == 1) $("#mdThemMoi").modal("hide");
                e.preventDefault();
                break;
            case 120:
                if (hotKey == 1) $("#btnLuuVaDong").trigger("click");
                e.preventDefault();
                break;
        }
    });

    LoadDataComBo();
    LoadDataComBo_Loc();
    LoadDataTable();
    LoadDataComBo_GuiDonXinCapPhoiBang();
    LoadDataComBo_CapPhoi();
});

let commonComboConfig = {
    columns: 2,
    indexValue: 0,
    indexText: 1, // assuming your result rows are [id, code, name]
    indexText1: 2, // assuming your result rows are [id, code, name]
    textShowTatCa: "-Tất cả-",
    showTatCa: true,
};

function LoadDataComBo() {
    NTS.loadDataComboAsync({
        name: "#NguoiLapID",
        type: "GET",
        ajaxUrl: window.Laravel.local.comboNhanVien,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#CapHocID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListCapHoc,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuNguoiLapID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListChucVu,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#LoaiPhoiID",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Tất cả-",
        showTatCa: true,
    });
    NTS.loadDataComboAsync({
        name: "#DonViNhanID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListDonVi,
        ajaxParam: {},
        ...commonComboConfig
    });

}

function LoadDataComBo_Loc() {


    NTS.loadDataComboAsync({
        name: "#LoaiPhoiID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Tất cả-",
        showTatCa: true,
    });

    NTS.loadDataComboAsync({
        name: "#TrangThaiXuLyID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListTrangThaiCapBang,
        ajaxParam: {},
        ...commonComboConfig,
        indexValue: 1,

    });
}

function LoadDataComBo_GuiDonXinCapPhoiBang() {
    NTS.loadDataComboAsync({
        name: "#NguoiTiepNhanXuLyID",
        type: "GET",
        ajaxUrl: window.Laravel.local.comboNhanVien,
        ajaxParam: {},
        ...commonComboConfig,
        textShowTatCa: "-Chọn-",
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuNguoiTiepNhanXuLyID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListChucVu,
        ajaxParam: {},
        ...commonComboConfig,
        textShowTatCa: "-Chọn-",
    });
}

function LoadDataComBo_CapPhoi() {
    NTS.loadDataComboAsync({
        name: "#CapHocID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListCapHoc,
        ajaxParam: {},
        ...commonComboConfig
    });

    NTS.loadDataComboAsync({
        name: "#LoaiPhoiVanBangChungChiID",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: true,
    });

    NTS.loadDataComboAsync({
        name: "#DonViID_Cap, #DonViID_Nhap",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListDonVi,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListChucVu,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#NhanVienID_Nhap",
        type: "GET",
        ajaxUrl: window.Laravel.local.comboNhanVien,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });

}

$(document).on("click", "#TimKiemNangCao", function () {
    if ($("#KhungTimKiem").css("display") == "block") {
        $("#KhungTimKiem").slideUp(200);
    } else {
        $("#KhungTimKiem").slideDown(200);
    }
    return false;
});
$(document).on("click", "#DongTimKiem", function () {
    $("#KhungTimKiem").slideUp(200);
    return false;
});

$(document).on("click", "#TimKiem", async function () {
    $("#KhungTimKiem").slideUp(200);
    await LoadDataTable();
    await LoadDataTable2();
    return false;
});

$(document).on("keyup", "#SearchKey", async function (e) {
    if (e.keyCode == "13") {
        await LoadDataTable();
        $("#KhungTimKiem").slideUp(200);
        return false;
    }
});

$(document).on("click", "#btnAnHienTQ", function () {
    var divThongKe = document.getElementById("DivThongKe");
    var textAnHien = document.getElementById("textAnHien");
    var iconAnHien = document.getElementById("iconAnHien");
    if (iconAnHien.className == "fa fa-eye-slash") {
        divThongKe.classList.add("d-none");
        iconAnHien.className = "fa fa-eye";
        textAnHien.textContent = " Hiện trang tổng quan";
    } else {
        divThongKe.classList.remove("d-none");
        iconAnHien.className = "fa fa-eye-slash";
        textAnHien.textContent = " Ẩn trang tổng quan";
    }
});

function XemChiTietNhatKy(id) {
    $("#DonYeuCauID").val(id);
    selectedId = id;
    $.ajax({
        url: window.location.pathname + '/loadDuLieuSua',
        method: 'GET',
        data: { id: id },
        success: function (res) {
            const configByTrangThai = {
                40: {
                    labelNgay: 'Ngày đề nghị    ',
                    labelNguoi: 'Người đề nghị',
                    labelChucVu: 'Chức vụ người đề nghị',
                    labelNoiDung: 'Nội dung đề nghị',

                },
                41: {
                    labelNgay: 'Ngày gửi',
                    labelNguoi: 'Người gửi',
                    labelChucVu: 'Chức vụ người gửi',
                    labelNoiDung: 'Nội dung gửi',

                },
                42: {
                    labelNgay: 'Ngày từ chối',
                    labelNguoi: 'Người từ chối',
                    labelChucVu: 'Chức vụ người từ chối',
                    labelNoiDung: 'Nội dung từ chối',

                },

                32: {
                    labelNgay: 'Ngày phê duyệt',
                    labelNguoi: 'Người phê duyệt',
                    labelChucVu: 'Chức vụ',
                    labelNoiDung: 'Nội dung phê duyệt',

                },
                default: {
                    labelNgay: 'Ngày xử lý',
                    labelNguoi: 'Người xử lý',
                    labelChucVu: 'Chức vụ',
                    labelNoiDung: 'Nội dung',

                }
            };
            if (!res.Err && res.Result) {
                var data = res.Result;
                if (window.Laravel && window.Laravel.local && window.Laravel.local.linkAnhDonXin) {
                    $('#dxcpb_imgDonXin').attr('src', window.Laravel.local.linkAnhDonXin);
                }
                $('#dxcpb_SoPhieu_ct').text(data.SoPhieu || '');
                $('#dxcpb_NgayLap_ct').text(data.txtNgayLap || '');
                $('#dxcpb_NguoiLap_ct').text(data.TenNguoiLap || '');
                $('#dxcpb_ChucVuNguoiLap_ct').text(data.TenChucVuNguoiLap || '');
                $('#dxcpb_DonViGui_ct').text(data.TenDonViGui || '');
                $('#dxcpb_NguoiLienHe_ct').text(data.NguoiLienHe || '');
                $('#dxcpb_SoDienThoai_ct').text(data.SoDienThoai || '');
                $('#dxcpb_Email_ct').text(data.Email || '');
                $('#dxcpb_DiaChi_ct').text(data.DiaChi || '');
                $('#dxcpb_HinhThucNhanPhoi_ct').text(data.TenHinhThucNhanPhoi || '');
                $('#dxcpb_DonViNhan_ct').text((data.TenDonViNhan || '') + (data.MaDonViNhan ? ` (${data.MaDonViNhan})` : ''));
                $('#dxcpb_LyDoXinCap_ct').text(data.LyDoXinCap || '');
                $('#dxcpb_TrangThai_ct')
                    .text(data.TenTrangThaiXuLy || 'Chưa rõ')
                    .css('background-color', data.MauSacTrangThaiXuLy || '#ccc');

                $('#dxcpb_CacMinhChung_ct').text(data.MinhChungKemTheo || '');
                $('#txtDinhKem').html(`<p class="fs-big my-1">
                            Đính kèm: 
                            <a href="#" data="" onclick="XemDinhKem_us('`+ data.DinhKem + `')">
                                <i class="fa fa-paperclip me-1"></i> Xem đính kèm
                            </a>
                            </p> `)
                $('#dxcpb_GhiChu_ct').text(data.GhiChu || '');
                var labelNgay = 'Ngày xử lý', labelNguoi = 'Người xử lý', labelChucVu = 'Chức vụ', labelNoiDung = 'Nội dung';
                if (data.TrangThaiXuLyID == 40) {
                    labelNgay = 'Ngày đề nghị';
                    labelNguoi = 'Người đề nghị';
                    labelChucVu = 'Chức vụ người đề nghị';
                    labelNoiDung = 'Nội dung đề nghị';
                    labelDonViTiepNhan = 'Đơn vị tiếp nhận';
                } else if (data.TrangThaiXuLyID == 41) {
                    labelNgay = 'Ngày gửi';
                    labelNguoi = 'Người gửi';
                    labelChucVu = 'Chức vụ người gửi';
                    labelNoiDung = 'Nội dung gửi';
                } else if (data.TrangThaiXuLyID == 42) {
                    labelNgay = 'Ngày từ chối';
                    labelNguoi = 'Người từ chối';
                    labelChucVu = 'Chức vụ người từ chối';
                    labelNoiDung = 'Lý do từ chối';
                }

                const tbody = document.querySelector('#phoiVBCCTable_ct tbody');
                tbody.innerHTML = '';
                let count = 1;
                data.PhoiVBCC.forEach(item => {
                    const tr = document.createElement('tr');

                    const tenPhoi = item.TenLoaiPhoiVanBangChungChi;
                    const donViTinh = item.TenDonViTinh;
                    const soLuong = item.SoLuongPhoi;

                    tr.innerHTML = `
                    <td>${count}</td>
                    <td>${tenPhoi}</td>
                    <td>${donViTinh}</td>
                    <td>${soLuong}</td>
                `;

                    tbody.appendChild(tr);
                    count++;
                });

                const config = configByTrangThai[parseInt(data.TrangThaiXuLyID)] || configByTrangThai.default;

                $('#dxcpb_LabelNgay_ct').html('<b>' + (data.txtNgayXuLy || '') + '</b>');
                $('#dxcpb_LabelNguoi_ct').html('<b>' + (data.TenNguoiXuLy || '') + '</b>');
                $('#dxcpb_LabelChucVu_ct').html('<b>' + (data.TenChucVuNguoiXuLy || '') + '</b>');
                $('#dxcpb_LabelNoiDung_ct').html('<b>' + (data.NoiDungXuLy || '') + '</b>');

                $('#dxcpb_LabelNgayTiepNhanXuLy_ct').text(config.labelNgay + ': ');
                $('#dxcpb_NgayTiepNhanXuLy_ct').text(data.txtNgayTiepNhanXuLy || '');

                $('#dxcpb_LabelNguoiTiepNhanXuLy_ct').text(config.labelNguoi + ': ');
                $('#dxcpb_NguoiTiepNhanXuLy_ct').text(data.TenNguoiTiepNhanXuLy || '');

                $('#dxcpb_LabelChucVuNguoiTiepNhanXuLy_ct').text(config.labelChucVu + ': ');
                $('#dxcpb_ChucVuNguoiTiepNhanXuLy_ct').text(data.TenChucVuNguoiTiepNhanXuLy || '');

                $('#dxcpb_LabelNoiDungTiepNhan_ct').text(config.labelNoiDung + ': ');
                $('#dxcpb_NoiDungTiepNhanXuLy_ct').text(data.NoiDungTiepNhanXuLy || '');

                $('#dxcpb_DonViTiepNhanXuLyID_ct').text(data.TenDonViTiepNhanXuLy || '');

            } else {
                $('#dxcpb_imgDonXin').attr('src', window.Laravel && window.Laravel.local ? window.Laravel.local.linkAnhDonXin : '');
                $('#dxcpb_TrangThaiLabel').text('').css('background-color', '#ccc');
                $('#dxcpb_NgayLap_ct, #dxcpb_NguoiLap_ct, #dxcpb_ChucVuNguoiLap_ct, #dxcpb_DonViGui_ct, #dxcpb_LoaiPhoi_ct, #dxcpb_SoLuong_ct, #dxcpb_DonViNhan_ct, #dxcpb_LyDoXinCap_ct, #dxcpb_DinhKem_ct, #dxcpb_GhiChu_ct, #dxcpb_LabelNgay_ct, #dxcpb_LabelNguoi_ct, #dxcpb_LabelChucVu_ct, #dxcpb_LabelNoiDung_ct').text('');
            }
            $('#mdChiTietQD').modal('show');
        },
        error: function () {
            $('#dxcpb_imgDonXin').attr('src', window.Laravel && window.Laravel.local ? window.Laravel.local.linkAnhDonXin : '');
            $('#dxcpb_TrangThaiLabel').text('').css('background-color', '#ccc');
            $('#dxcpb_NgayLap_ct, #dxcpb_NguoiLap_ct, #dxcpb_ChucVuNguoiLap_ct, #dxcpb_DonViGui_ct, #dxcpb_LoaiPhoi_ct, #dxcpb_SoLuong_ct, #dxcpb_DonViNhan_ct, #dxcpb_LyDoXinCap_ct, #dxcpb_DinhKem_ct, #dxcpb_GhiChu_ct, #dxcpb_LabelNgay_ct, #dxcpb_LabelNguoi_ct, #dxcpb_LabelChucVu_ct, #dxcpb_LabelNoiDung_ct').text('');
            $('#mdChiTietQD').modal('show');
        }
    });
}

function LoadDataComBo_Loc() {
    NTS.loadDataComboAsync({
        name: "#CapHocID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListCapHoc,
        ajaxParam: {},
        ...commonComboConfig

    });

    NTS.loadDataComboAsync({
        name: "#LoaiPhoiID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Tất cả-",
        showTatCa: true,
    });

    NTS.loadDataComboAsync({
        name: "#TrangThaiXuLyID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListTrangThaiCapBang,
        ajaxParam: {},
        ...commonComboConfig,
        indexValue: 1,

    });
}

function htmlDuLieu(cell) {
    const data = cell.getData();

    const fileName = data.TenTepDinhKem || "Không rõ";
    const fileLink = data.LinkDinhKem || "#";
    const dinhKemHTML = data.LinkDinhKem
        ? `<a href="${fileLink}" target="_blank">${fileName}</a>`
        : "Không có";
    let labelNgay, labelNguoi, labelChucVu, labelNoiDung;
    let txtNgay = data.txtNgayTiepNhanXuLy || "";
    let tenNguoi = data.TenNguoiTiepNhanXuLy || "";
    let chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
    let noiDung = data.NoiDungTiepNhanXuLy || "";
    switch (parseInt(data.TrangThaiXuLyID)) {
        case 40:
            labelNgay = "Ngày gửi";
            labelNguoi = "Người gửi";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung gửi";

            txtNgay = data.txtNgayXuLy || "";
            tenNguoi = data.TenNguoiXuLy || "";
            chucVu = data.TenChucVuNguoiXuLy || "";
            noiDung = data.NoiDungXuLy || "";
            break;
        case 41:
            labelNgay = "Ngày đề nghị";
            labelNguoi = "Người đề nghị";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung đề nghị";

            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        case 42:
            labelNgay = "Ngày từ chối";
            labelNguoi = "Người từ chối";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung từ chối";


            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        case 32:
            labelNgay = "Ngày duyệt";
            labelNguoi = "Người duyệt";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung duyệt";


            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        default:
            labelNgay = "Ngày đề nghị";
            labelNguoi = "Người đề nghị";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung đề nghị";

            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;

    }
    return `<div class="list-item col-md-12" style="padding: 0px;">
        <div class="card card-luoi shadow-sm mb-2">
            <div id="card_${data.id}" class="card-body profile-user-box">
                <div class="row">
                    <div class="col-sm-2 text-center" style="width:12%;">
                        <div class="profile-picture" style=" height: 109px;">
                            <img src="${window.Laravel.local.linkAnhDonXin}" alt="ảnh đơn xin" class="img-thumbnail rounded lazy mb-2" style="background: white;
                            border: none;
                            box-shadow: unset;" >
                        </div>
                        <div style="display: flex; justify-content: center;">
                            <span style="
                                display: block;
                                margin-top: 10px;
                                font-weight: bold;
                                font-size: 1rem;
                                text-align: center;
                                padding: 6px 0;
                                background-color: ${data.MauSacTrangThaiXuLy};
                                color: white;
                                border-radius: 6px;
                                width: 95%;
                                ">
                                ${data.TenTrangThaiXuLy}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-10" style="width:88%;">
                        <div class="row">
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Số phiếu: <b>${data.SoPhieu || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Ngày lập: <b>${data.txtNgayLap || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Người lập: <b>${data.TenNguoiLap || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Chức vụ: <b>${data.TenChucVuNguoiLap || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="dropdown text-end" style="position: absolute; top: 10px; right: 10px;">
                                    <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-end w-auto">
                                        <!-- Luôn hiển thị -->
                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XemChiTietNhatKy('${data.id}'); return false;">
                                            <i class="fa fa-eye" aria-hidden="true" style="color: #4265B6"></i>&ensp; Xem phiếu đề nghị
                                        </a>

                                        <!-- TrangThaiXuLyID = 40 (Đã gửi, chờ duyệt) -->
                                        ${data.TrangThaiXuLyID == '40' ? `
                                            <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="GuiDonXinCapPhoiBang('${data.id}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                                                <i class="fa fa-check-circle" aria-hidden="true" style="color: #4265B6"></i>&ensp; Duyệt phiếu đề nghị
                                            </a>
                                            <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="TuChoiDonXinCapPhoiBang('${data.id}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                                                <i class="fas fa-times-circle" aria-hidden="true" style="color: #EA1818"></i>&ensp; Từ chối duyệt phiếu xin đề nghị
                                            </a>
                                         
                                        ` : ''}

                                        <!-- TrangThaiXuLyID = 32 (Đã duyệt) -->
                                        ${data.TrangThaiXuLyID == '32' ? `
                                            <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="ThuHoiDuyetDonXinCapPhoiBang('${data.id}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                                                <i class="fa fa-refresh" aria-hidden="true" style="color: #EA1818"></i>&ensp; Thu hồi duyệt phiếu đề nghị    
                                            </a>
                                            <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="CapPhatPhoiBang('${data.id}', '${data.SoPhieu}', '${data.TenDonViGui}'); return false;">
                                                <i class="fa fa-arrow-right text-primary" aria-hidden="true" style="color: #146654"></i>&ensp; Cấp phát phôi bằng
                                            </a>
                                        ` : ''}

                                        <!-- TrangThaiXuLyID = 42 (Đã từ chối) -->
                                        ${data.TrangThaiXuLyID == '42' ? `
                                            <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="ThuHoiTuChoiDonXinCapPhoiBang('${data.id}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                                                <i class="fa fa-refresh" aria-hidden="true" style="color: #EA1818"></i>&ensp; Thu hồi từ chối phiếu đề nghị
                                            </a>
                                        ` : ''}

                                        <!-- Luôn hiển thị -->
                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="InPhieuDeNghi('${data.id}')">
                                            <i class="fa fa-print iconsize-item" style="color: #2A79FF"></i>&ensp; In phiếu đề nghị
                                        </a>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <hr style="border-top: 2px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-6">
                                        <p class="fs-big my-1">Đơn vị xin cấp phôi: <b>${data.TenDonViGui || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Người liên hệ: <b>${data.NguoiLienHe || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Số điện thoại: <b>${data.SoDienThoai || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-6">
                                        <p class="fs-big my-1">Địa chỉ: <b>${data.DiaChi || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Email: <b>${data.Email || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Hình thức nhận phôi: <b>${data.TenHinhThucNhanPhoi || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div> 
                        </div>
                        <div class="row">
                            <hr style="border-top: 2px solid #f76707; margin: 0 0 0.5rem 0; margin-top:0.5rem;" />
                            <div class="col-md-11">
                                <div class="row mb-12">
                                    <div class="col-md-6">
                                        <p class="fs-big my-1">Đơn vị tiếp nhận: <b>${data.TenDonViGui || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                        </div>
                        <div class="row">
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">${labelNgay}: <b>${txtNgay}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">${labelNguoi}: <b>${tenNguoi}</b></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="fs-big my-1">${labelChucVu}: <b>${chucVu}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>`;
}

var table = new Tabulator("#Grid1", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "600",
    HeaderVertAlign: "center",
    headerVisible: false,
    columns: [
        {
            title: "Thông tin",
            field: "ThongTinHoGiaDinh",
            formatter: htmlDuLieu,
            visible: true,
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

async function LoadDataTable() {
    table.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getAll",
        {
            SearchKey: $("#SearchKey").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            TrangThaiXuLyID_Loc: $("#TrangThaiXuLyID_Loc").value(),
        }
    );
    if (!result.Err) {
        VeChart();
        table.setData(result.result);
    } else {
        table.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

$(document).on("click", "#btn-layout-1", async function () {
    $("#grid-layout").fadeIn(200);
    $("#list-layout").hide();
    $("#list-layout").removeClass("show");
    $("#grid-layout").addClass("show");
    await LoadDataTable();
});

$(document).on("click", "#btn-layout-2", async function () {
    $("#grid-layout").hide();
    $("#list-layout").fadeIn(200);
    $("#list-layout").addClass("show");
    $("#grid-layout").removeClass("show");
    $("#DonYeuCauID").value("");
    $(".divThaoTacNhanh").hide();
    $("#txtTenTrangThaiXuLy_View").html("Trạng thái xử lý: <b>---</b>");
    $("#txtTenDonViGui_View").html("Đơn vị gửi: <b>---</b>");
    await LoadDataTable2();
});

function actionDropdownFormatter(cell) {
    const data = cell.getData();
    const ID = data.id;
    const button = document.createElement("button");
    button.className = "btn btn-sm btn-white dropdown-toggle-hide-arrow";
    button.innerHTML = `<i class="fa fa-ellipsis-h" style="color: #696cff;"></i>`;
    button.style.boxShadow = "none";

    button.onclick = function (e) {
        e.stopPropagation();
        document.querySelectorAll('.custom-dropdown-menu').forEach(el => el.remove());

        const dropdown = document.createElement("div");
        dropdown.className = "custom-dropdown-menu dropdown-menu dropdown-menu-end show";
        dropdown.style.position = "absolute";
        dropdown.style.zIndex = 9999;
        dropdown.style.minWidth = "250px";

        const TrangThai = data.TrangThaiXuLyID;
        let html = `
            <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                href="javascript:void(0);" onclick="XemChiTietNhatKy('${ID}'); return false;">
                <i class="fa fa-eye" aria-hidden="true" style="color: #4265B6"></i>&ensp; Xem phiếu đề nghị
            </a>
        `;

        if (TrangThai == '41') {
            html += `
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="SuaDuLieu('${ID}'); return false;">
                    <i class="fa fa-pencil-square-o iconsize-item" style="color: #F76707"></i>&ensp; Chỉnh sửa phiếu đề nghị
                </a>
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="GuiDonXinCapPhoiBang('${ID}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                    <i class="fa fa-check-square-o text-success" aria-hidden="true"></i>&ensp; Gửi phiếu đề nghị
                </a>
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="XoaDuLieu('${ID}')">
                    <i class="fa fa-trash-o iconsize-item" style="color: #D63939"></i>&ensp; Xóa phiếu đề nghị
                </a>
            `;
        }

        if (TrangThai == '40') {
            html += `
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="GuiDonXinCapPhoiBang('${ID}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                    <i class="fa fa-check-circle" style="color: #4265B6" aria-hidden="true"></i>&ensp; Duyệt phiếu đề nghị
                </a>
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="TuChoiDonXinCapPhoiBang('${ID}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                    <i class="fas fa-times-circle" style="color: #EA1818" aria-hidden="true"></i>&ensp; Từ chối duyệt phiếu xin đề nghị
                </a>
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="CapPhatPhoiBang('${ID}', '${data.SoPhieu}', '${data.TenDonViGui}'); return false;">
                    <i class="fa fa-code-fork text-primary" style="color: #146654" aria-hidden="true"></i>&ensp; Cấp phát phôi bằng
                </a>
            `;
        }

        if (TrangThai == '32') {
            html += `
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="ThuHoiDuyetDonXinCapPhoiBang('${ID}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                    <i class="fa fa-refresh" style="color: #EA1818" aria-hidden="true"></i>&ensp; Thu hồi duyệt phiếu đề nghị
                </a>
            `;
        }

        if (TrangThai == '42') {
            html += `
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="ThuHoiTuChoiDonXinCapPhoiBang('${ID}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                    <i class="fa fa-refresh" style="color: #EA1818" aria-hidden="true"></i>&ensp; Thu hồi từ chối phiếu đề nghị
                </a>
            `;
        }

        html += `
            <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                href="javascript:void(0);" onclick="InPhieuDeNghi('${ID}')">
                <i class="fa fa-print iconsize-item" style="color: #2A79FF"></i>&ensp; In phiếu đề nghị
            </a>
        `;

        dropdown.innerHTML = html;

        const rect = button.getBoundingClientRect();
        dropdown.style.left = `${rect.left + window.scrollX}px`;
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;

        document.body.appendChild(dropdown);

        const closeDropdown = (event) => {
            if (!dropdown.contains(event.target) && event.target !== button) {
                dropdown.remove();
                document.removeEventListener('click', closeDropdown);
            }
        };
        document.addEventListener('click', closeDropdown);
    };

    return button;
}



function trangThaiSpanFormatter(cell) {
    const data = cell.getData();

    const mauSac = data.MauSacTrangThaiXuLy || "#6c757d";
    const tenTrangThai = data.TenTrangThaiXuLy || "Không xác định";

    const container = document.createElement("div");
    container.style.display = "flex";
    container.style.justifyContent = "center";

    const span = document.createElement("span");
    span.style.display = "block";
    span.style.marginTop = "10px";
    span.style.fontWeight = "bold";
    span.style.fontSize = "1rem";
    span.style.textAlign = "center";
    span.style.padding = "6px 0";
    span.style.backgroundColor = mauSac;
    span.style.color = "white";
    span.style.borderRadius = "6px";
    span.style.width = "95%";
    span.innerText = tenTrangThai;

    container.appendChild(span);
    return container;
}


var GridMainLuoi = new Tabulator("#GridMainLuoi", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: actionDropdownFormatter,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false
        },
        {
            title: "Số phiếu",
            field: "SoPhieu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Ngày lập",
            field: "txtNgayLap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Người lập",
            field: "TenNguoiLap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Chức vụ người lập",
            field: "TenChucVuNguoiLap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Đơn vị xin cấp phôi",
            field: "TenDonViGui",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 300,
        },
        {
            title: "Đơn vị tiếp nhận",
            field: "TenDonViNhan",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 300,
        },
        {
            title: "Người liên hệ",
            field: "NguoiLienHe",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Số điện thoại",
            field: "SoDienThoai",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Địa chỉ",
            field: "DiaChi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 300,
        },
        {
            title: "Email",
            field: "Email",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Hình thực nhận phôi",
            field: "TenHinhThucNhanPhoi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Trạng thái",
            field: "TenTrangThaiXuLy",
            formatter: "textarea",
            hozAlign: "center",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 120,
        },
        {
            title: "Trạng thái",
            field: "TenTrangThaiXuLy",
            formatter: "textarea",
            hozAlign: "center",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 120,
        }
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

async function LoadDataTable2() {

    GridMainLuoi.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getAll",
        {
            SearchKey: $("#SearchKey").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            TrangThaiXuLyID_Loc: $("#TrangThaiXuLyID_Loc").value(),
            CbSapXep: $("#CbSapXep").value(),
        }
    );
    if (!result.Err) {
        GridMainLuoi.setData(result.result);
    } else {
        GridMainLuoi.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

async function VeChart() {
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.getThongKe,
        {
            SearchKey: $("#SearchKey").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            TrangThaiXuLyID_Loc: $("#TrangThaiXuLyID_Loc").value(),
        }
    );
    debugger
    try {
        $("#txtTong").text(result.Result.TS);

        result.Result.ThongKeTrangThai.forEach(item => {
            $("#txtTrangThai_" + item.TrangThaiXuLyID).text(item.SoLuong);
        });
        const ThongKeLoaiPhoi = result.Result.ThongKeLoaiPhoi;
        $("#txtPhoiTHCS").text(ThongKeLoaiPhoi[0].TenLoaiPhoiVanBangChungChi);
        $("#txtPhoiTHCSCount").text(ThongKeLoaiPhoi[0].SoLuong);

        $("#txtPhoiTHPT").text(ThongKeLoaiPhoi[1].TenLoaiPhoiVanBangChungChi);
        $("#txtPhoiTHPTCount").text(ThongKeLoaiPhoi[1].SoLuong);

        const chartLabels = [];
        const chartSeries = [];
        const chartColors = [];

        result.Result.ThongKeTrangThai.forEach(item => {
            chartLabels.push(item.TenTrangThai);
            chartSeries.push(item.SoLuong);
            chartColors.push(item.MauSac || "#999999");
        });

        document.querySelector("#XepLoaiChart").innerHTML = "";

        mixedXepLoaiChart = new ApexCharts(
            document.querySelector("#XepLoaiChart"),
            {
                labels: chartLabels,
                colors: chartColors,
                series: chartSeries,
                chart: {
                    type: "donut",
                    height: 110,
                },
                dataLabels: {
                    enabled: false,
                },
                responsive: [
                    {
                        breakpoint: 480,
                        options: {
                            chart: {
                                width: 110,
                            },
                            legend: {
                                position: "bottom",
                            },
                        },
                    },
                ],
            }
        );
        mixedXepLoaiChart.render();

    } catch (err) {
        console.error("Lỗi khi vẽ biểu đồ:", err);
    }
}

function GuiDonXinCapPhoiBang(id, SoPhieu, txtNgayLap, DonViGui) {
    $("#DonYeuCauID").val(id);
    $("#NgayTiepNhanXuLy").val(defaultDate);

    NTS.getAjaxAPIAsync("GET", window.Laravel.layouts.getCurrentUserInfo, {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NguoiTiepNhanXuLyID").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuNguoiTiepNhanXuLyID").value(nhanVien.chuc_vu.id);
                }
            }
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });

    const noiDungText = `Duyệt phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: ${SoPhieu} ngày lập ${txtNgayLap} của ${DonViGui}.`;
    $('#NoiDungTiepNhanXuLy').val(noiDungText);

    $("#tieuDe_mdGuiDonXinCapPhoiBang").text("Duyệt phiếu đề nghị cấp phôi văn bằng, chứng chỉ");
    $("#alertMessage").html('Bạn đang thực hiện duyệt phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: <b id="SoPhieu_lbl"></b> ngày lập <b id="txtNgayLap_lbl"></b> của <b id="TenDonViGui_lbl"></b>, Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>"Phê duyệt"</b> để thực hiện thao tác duyệt phiếu đề nghị.');
    $("#btnGuiDon").html('<i class="fa fa-paper-plane-o me-1"></i> Phê duyệt (F9)');
    $('#SoPhieu_lbl').text(SoPhieu)
    $('#txtNgayLap_lbl').text(txtNgayLap)
    $('#TenDonViGui_lbl').text(DonViGui)

    $('label[for="NgayTiepNhanXuLy"]').text('Ngày duyệt');
    $('label[for="NguoiTiepNhanXuLyID"]').text('Người duyệt');
    $('label[for="ChucVuNguoiTiepNhanXuLyID"]').text('Chức vụ');
    $('label[for="NoiDungTiepNhanXuLy"]').text('Nội dung duyệt');

    tempTrangThai = "32";
    $('#mdGuiDonXinCapPhoiBang').modal('show');
}

function TuChoiDonXinCapPhoiBang(id, SoPhieu, txtNgayLap, DonViGui) {
    $("#DonYeuCauID").val(id);
    $("#NgayTiepNhanXuLy").val(defaultDate);

    NTS.getAjaxAPIAsync("GET", window.Laravel.layouts.getCurrentUserInfo, {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NguoiTiepNhanXuLyID").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuNguoiTiepNhanXuLyID").value(nhanVien.chuc_vu.id);
                }
            }
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });

    const noiDungText = `Từ chối phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: ${SoPhieu} ngày lập ${txtNgayLap} của ${DonViGui}.`;
    $('#NoiDungTiepNhanXuLy').val(noiDungText);

    $("#tieuDe_mdGuiDonXinCapPhoiBang").text("Từ chối phiếu đề nghị cấp phôi văn bằng, chứng chỉ");
    $("#alertMessage").html('Bạn đang thực hiện từ chối phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: <b id="SoPhieu_lbl"></b> ngày lập <b id="txtNgayLap_lbl"></b> của <b id="TenDonViGui_lbl"></b>, Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>"Từ chối"</b> để thực hiện thao tác từ chối phiếu đề nghị.');
    $("#btnGuiDon").html('<i class="fa fa-paper-plane-o me-1"></i> Từ chối (F9)');
    $('#SoPhieu_lbl').text(SoPhieu)
    $('#txtNgayLap_lbl').text(txtNgayLap)
    $('#TenDonViGui_lbl').text(DonViGui)

    $('label[for="NgayTiepNhanXuLy"]').text('Ngày từ chối');
    $('label[for="NguoiTiepNhanXuLyID"]').text('Người từ chối');
    $('label[for="ChucVuNguoiTiepNhanXuLyID"]').text('Chức vụ');
    $('label[for="NoiDungTiepNhanXuLy"]').text('Nội dung từ chối');
    tempTrangThai = "42";
    $('#mdGuiDonXinCapPhoiBang').modal('show');
}

function ThuHoiTuChoiDonXinCapPhoiBang(id, SoPhieu, txtNgayLap, DonViGui) {
    $("#DonYeuCauID").val(id);
    $("#NgayTiepNhanXuLy").val(defaultDate);

    NTS.getAjaxAPIAsync("GET", window.Laravel.layouts.getCurrentUserInfo, {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NguoiTiepNhanXuLyID").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuNguoiTiepNhanXuLyID").value(nhanVien.chuc_vu.id);
                }
            }
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });

    const noiDungText = `Thu hồi từ chối phiếu đề nghị cấp phôi văn bằng,chứng chỉ số: ${SoPhieu} ngày lập ${txtNgayLap} của ${DonViGui}.`;
    $('#NoiDungTiepNhanXuLy').val(noiDungText);

    $("#tieuDe_mdGuiDonXinCapPhoiBang").text("Thu hồi Từ chối phiếu đề nghị cấp phôi văn bằng, chứng chỉ");
    $("#alertMessage").html('Bạn đang thực hiện thu hồi từ chối phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: <b id="SoPhieu_lbl"></b> ngày lập <b id="txtNgayLap_lbl"></b> của <b id="TenDonViGui_lbl"></b>, Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>"Thu hồi"</b> để thực hiện thao tác thu hồi từ chối phiếu đề nghị.');
    $("#btnGuiDon").html('<i class="fa fa-refresh me-1"></i> Thu hồi (F9)');
    $('#SoPhieu_lbl').text(SoPhieu)
    $('#txtNgayLap_lbl').text(txtNgayLap)
    $('#TenDonViGui_lbl').text(DonViGui)

    $('label[for="NgayTiepNhanXuLy"]').text('Ngày thu hồi');
    $('label[for="NguoiTiepNhanXuLyID"]').text('Người thu hồi');
    $('label[for="ChucVuNguoiTiepNhanXuLyID"]').text('Chức vụ');
    $('label[for="NoiDungTiepNhanXuLy"]').text('Nội dung thu hồi');
    tempTrangThai = "40";
    $('#mdGuiDonXinCapPhoiBang').modal('show');
}

function ThuHoiDuyetDonXinCapPhoiBang(id, SoPhieu, txtNgayLap, DonViGui) {
    $("#DonYeuCauID").val(id);
    $("#NgayTiepNhanXuLy").val(defaultDate);


    NTS.getAjaxAPIAsync("GET", window.Laravel.layouts.getCurrentUserInfo, {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NguoiTiepNhanXuLyID").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuNguoiTiepNhanXuLyID").value(nhanVien.chuc_vu.id);
                }
            }
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });
    const noiDungText = `Thu hồi duyệt phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: ${SoPhieu} ngày lập ${txtNgayLap} của ${DonViGui}.`;
    $('#NoiDungTiepNhanXuLy').val(noiDungText);

    $("#tieuDe_mdGuiDonXinCapPhoiBang").text("Thu hồi duyệt phiếu đề nghị cấp phôi văn bằng, chứng chỉ");
    $("#alertMessage").html('Bạn đang thực hiện thu hồi duyệt phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: <b id="SoPhieu_lbl"></b> ngày lập <b id="txtNgayLap_lbl"></b> của <b id="TenDonViGui_lbl"></b>, Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>"Thu hồi"</b> để thực hiện thao tác thu hồi duyệt phiếu đề nghị.');
    $("#btnGuiDon").html('<i class="fa fa-refresh me-1"></i> Thu hồi (F9)');
    $('#SoPhieu_lbl').text(SoPhieu)
    $('#txtNgayLap_lbl').text(txtNgayLap)
    $('#TenDonViGui_lbl').text(DonViGui)

    $('label[for="NgayTiepNhanXuLy"]').text('Ngày thu hồi');
    $('label[for="NguoiTiepNhanXuLyID"]').text('Người thu hồi');
    $('label[for="ChucVuNguoiTiepNhanXuLyID"]').text('Chức vụ');
    $('label[for="NoiDungTiepNhanXuLy"]').text('Nội dung thu hồi');
    tempTrangThai = "40";
    $('#mdGuiDonXinCapPhoiBang').modal('show');
}

async function CapPhatPhoiBang(id, soPhieu, tenDonViGui) {
    if (!QuyenThem()) {
        return;
    }
    resetForm("#mdCapPhatPhoiBang");
    uploadedFileUrls = [];
    $("#list-file").empty();
    $('#TiepNhanPhoiVBCCID').val("");
    $("#tieuDeModalCapPhatPhoiBang").text("Thêm mới thông tin nhập kho/cấp phát phôi bằng");
    $("#mdCapPhatPhoiBang").find("input, textarea").val('');
    $("#NgayNhap").value(defaultDate);
    $("#mdCapPhatPhoiBang").modal("show");
    $('#DonViID_YeuCau').val(id);
    const formattedValue = `Số phiếu: ${soPhieu}, Đơn vị: ${tenDonViGui}`;
    $('#DonViID_YeuCau_fm').val(formattedValue);

    try {
        var result = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.local.maTuTangUrl,
            {}
        );
        $("#SoHieuTu").value(result.SoChungTu);
        $("#SoHieuDen").value(addToCode(result.SoChungTu));
    } catch { }
    try {
        var result = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.local.soPhieuTuTangUrl,
            {}
        );
        $("#MaPhieu").value(result.SoChungTu);
    } catch { }
    $('#SoLuongNhap').val(1)
    tempthemCapPhoi = "them";
}

$("#btnLuuVaDongCapPhat").on("click", async function () {
    const validate = new NTSValidate("#mdCapPhatPhoiBang");
    if (!validate.trim().check()) return false;

    const payload = {
        TiepNhanPhoiVBCCID: $("#TiepNhanPhoiVBCCID").value(),
        MaPhieu: $("#MaPhieu").value(),
        NgayNhap: $("#NgayNhap").value(),
        CapHocID: $("#CapHocID").value(),
        NhanVienID_Nhap: $("#NhanVienID_Nhap").value(),
        ChucVuID: $("#ChucVuID").value(),
        LoaiPhoiVanBangChungChiID: $("#LoaiPhoiVanBangChungChiID").value(),
        DonViID_Cap: $("#DonViID_Cap").value(),
        DonViID_Nhap: $("#DonViID_Nhap").value(),
        DonViID_YeuCau: $("#DonViID_YeuCau").value(),
        SoLuongNhap: $("#SoLuongNhap").value(),
        SoHieuTu: $("#SoHieuTu").value(),
        SoHieuDen: $("#SoHieuDen").value(),
        GhiChu: $("#GhiChu").value(),
        txtDuongDanFileVB: $("#txtDuongDanFileVB").value()
    };
    var met = "POST";
    if (tempthemCapPhoi == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }
    var result = await NTS.getAjaxAPIAsync(
        met,
        window.location.pathname + "/CapPhatPhoiBang",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdCapPhatPhoiBang").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});


$("#btnGuiDon").on("click", async function () {

    const validate = new NTSValidate("#mdGuiDonXinCapPhoiBang");
    if (!validate.trim().check()) return false;

    const payload = {
        DonYeuCauID: $("#DonYeuCauID").val(),
        NguoiTiepNhanXuLyID: $("#NguoiTiepNhanXuLyID").val(),
        ChucVuNguoiTiepNhanXuLyID: $("#ChucVuNguoiTiepNhanXuLyID").val(),
        NoiDungTiepNhanXuLy: $("#NoiDungTiepNhanXuLy").val(),
        NgayTiepNhanXuLy: $("#NgayTiepNhanXuLy").val(),
        TrangThaiXuLyID: tempTrangThai
    };

    var result = await NTS.getAjaxAPIAsync(
        "post",
        window.location.pathname + "/GuiDon",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdGuiDonXinCapPhoiBang").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});


//#region Đính kèm

$("#btnChonTepVB_us").on("click", function () {
    $("#fileVB_us").trigger("click");
});

$("#fileVB_us").on("change", async function () {
    const files = Array.from(this.files);
    $("#list-file").empty();

    await NTS.getAjaxAPIAsync("POST", "/api/dungchung/files/clearTemp", {
        chucnang: "TiepNhanPhoiVBCC"
    });
    for (const file of files) {
        const form = new FormData();
        form.append("file", file);
        form.append("chucnang", "TiepNhanPhoiVBCC");
        try {
            const res = await NTS.getAjaxAPIAsync("POST",
                "/api/dungchung/files/uploadFileTemp",
                form,
            );
            if (!res.success) {
                NTS.loi(`Tải lên thất bại: ${file.name}`);
                continue;
            }
            // collect the returned URLs array (or wrap single URL)
            const url = Array.isArray(res.url) ? res.url[0] : res.url;
            uploadedFileUrls.push(url);
        } catch (err) {
            console.error("Upload error", err);
            NTS.loi(`Lỗi upload ${file.name}`);
        }
    }

    // 2) Render previews by iterating uploadedFileUrls
    uploadedFileUrls.forEach((url) => {
        renderAttachment(url);
    });

    const ttList = [].slice.call(
        document.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    ttList.forEach((el) => new bootstrap.Tooltip(el));

    $("#txtDuongDanFileVB").val(uploadedFileUrls.join("|"));
});

$("#btnXoaHetTepVB_us").on("click", async function () {
    CanhBaoXoa(async () => {
        $("#fileVB_us").val("");
        $("#list-file").empty();
        $("#txtDuongDanFileVB").val("");

        try {
            // call delete-multiple with our URL array
            if (uploadedFileUrls.length !== 0) {
                const res = await NTS.getAjaxAPIAsync("DELETE",
                    "/api/dungchung/files/delete-multiple",
                    { urls: uploadedFileUrls }

                );
                if (!res.success) {
                    return NTS.loi("Xóa tất cả thất bại: " + (res.message || ""));
                }
                if (res.loi) {
                    return NTS.loi("Xóa tất cả thất bại: " + (res.message || ""));
                }

                NTS.thanhcong("Xoá tất cả đính kèm thành công");
            }
        } catch (err) {
            console.error("Delete all error", err);
            NTS.loi("Có lỗi khi xóa tất cả đính kèm");
        } finally {
            uploadedFileUrls = []; // reset URL store
        }
    });
});
function renderAttachment(url) {
    const filename = url.split("/").pop();

    // 2) get the extension (e.g. "jpg")
    const ext = filename.split(".").pop().toLowerCase();

    // 3) decide if it’s an image
    const imageExts = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
    const isImage = imageExts.includes(ext);

    const $item = $(`
            <div class="file-preview position-relative d-inline-block text-center me-2 mb-2">
            <!-- delete button -->
                <button type="button" class="btn-close position-absolute top-0 end-0 m-1" aria-label="Delete"></button>
            </div>
              `);

    // image vs icon
    let $thumb;
    if (isImage) {
        $thumb = $(`
            <img class="img-thumbnail"
                 style="width:100px;height:100px;object-fit:cover;"
                 src="${url}">
          `);
    } else {
        $thumb = $(`
            <div class="file-icon bg-secondary text-white rounded
                        d-flex align-items-center justify-content-center mb-1"
                 style="width:100px;height:100px;font-size:2rem;">
              <i class="fa fa-file"></i>
            </div>
          `);
    }

    // 1) native tooltip
    $thumb.attr("title", filename);

    // 2) (optional) Bootstrap tooltip
    $thumb.attr("data-bs-toggle", "tooltip").attr("data-bs-placement", "top");

    // assemble
    $item.append($thumb);
    $item.append(
        $(
            '<a target="_blank" class="d-block small text-truncate" style="max-width:100px;"></a>'
        )
            .attr("href", url)
            .text(filename)
    );
    $("#list-file").append($item);

    $item.find(".btn-close").on("click", async () => {
        try {
            //call delete-multiple with a JSON array of this single URL
            const res = await NTS.getAjaxAPIAsync("DELETE",
                "/api/dungchung/files/delete-multiple",
                { urls: [url] }
            );
            if (!res.success) {
                return NTS.loi("Xóa file thất bại: " + filename);
            }
            // remove from uploadedFileUrls
            uploadedFileUrls = uploadedFileUrls.filter((u) => u !== url);
            // update hidden field
            // remove preview from DOM
            $item.remove();
            $("#txtDuongDanFileVB").val(uploadedFileUrls.join("|"));
        } catch (err) {
            console.error("Delete error", err);
            NTS.loi("Lỗi khi xóa " + filename);
        }
    });
}
//#endregion








