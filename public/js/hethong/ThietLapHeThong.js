
var mausactoi;
var mausacsang;
var userID = '';

$(function () {
    LoadCombo();
    // loadData();
    // initImageUpload2();
    $("#DangNgayBaoCao").select2({ width: "100%" });
    $("#DangNgayBaoCao").on("change", function () {
        if ($("#DangNgayBaoCao").value() == "1") {
            $("#NgayLapBaoCao1").value(new Date());
            $("#NgayLapBaoCao1").attr("disabled", true);
            $("#NgayLapBaoCao2").attr("disabled", true);
        }
        if ($("#DangNgayBaoCao").value() == "2") {
            $("#NgayLapBaoCao1").attr("disabled", false);
            $("#NgayLapBaoCao2").attr("disabled", true);
        }
        if ($("#DangNgayBaoCao").value() == "3") {
            $("#NgayLapBaoCao1").attr("disabled", true);
            $("#NgayLapBaoCao2").attr("disabled", false);
            $("#NgayLapBaoCao2").value("ngày... tháng...năm...");
        }
        return false;
    });

    var result = NTS.getAjaxAPI(
        "GET",
        window.location.pathname + "/getdata",
        {}
    );

    if (!result.Err) {
        let Data = result.Result[0];
        $("#DonViCapTren").value(Data.DonViCapTren);
        $("#DonViBaoCao").value(Data.DonViBaoCao);
        $("#KyThay").value(Data.KyThay);
        $("#NguoiKT").val(Data.NguoiKT);
        $("#ChucDanhNguoiKT").value(Data.ChucDanhNguoiKT);
        $("#NguoiKy").value(Data.NguoiKy);
        $("#ChucDanhNguoiKy").value(Data.ChucDanhNguoiKy);
        $("#NguoiLap").value(Data.NguoiLap);
        $("#ChucDanhNguoiLap").value(Data.ChucDanhNguoiLap);
        $("#DiaDanh").value(Data.DiaDanh);

        $("#DangNgayBaoCao").value(Data.DangNgayBaoCao);
        $("#NgayLapBaoCao1").value(Data.NgayLapBaoCao1);
        $("#NgayLapBaoCao2").value(Data.NgayLapBaoCao2);
        $("#ChucDanhNguoiKy").value(Data.ChucDanhNguoiKy);

        $("#TinhID").value(Data.DiaBanHCID_Tinh);
        $("#XaID").value(Data.DiaBanHCID_Xa);
        $("#ThonID").value(Data.DiaBanHCID_Thon);
    }
    // else {
    //     LuuThongTin();
    // }
    KiemTraLoaiNgayBC();
    ///////// PHÍM TẮT /////////
    $(document).on("keydown", function (e) {
        switch (e.keyCode) {
            case 120:
                $("#btnLuuVaDong").trigger("click");
                e.preventDefault();
                break;
        }
    });
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });



    // Thiết lập màu sắc
     $("#ThietLapMauHeThongID").val();
    NTS.getAjaxAPIAsync("GET", window.Laravel.thietlaphethong.getProfileInfo, {})
        .then(userInfo => {
            userID = userInfo.id;
        })
    .catch(err => { });
    // Gắn sự kiện cho các input màu
    ['light', 'dark'].forEach(mode => {
        [
            'light_them_color'
            , 'light_luu_color'
            , 'light_dong_color'
            , 'light_in_color'
            , 'light_xuat_color'
            , 'light_header_color'
            , 'light_header_title_color'
            , 'light_menu_bg_color'
            , 'light_menu_color'
        ].forEach(id => {
            updateColorValue(`${mode}_${id}`, `${mode}_${id}_value`);
        });
    });

    // Khởi tạo drag-and-drop và tải màu từ database
    setupDragAndDrop();
    setTimeout(() => {
        loadColorsFromDB_MauHT();
    }, 1000);
});

//load dữ liệu khi người dùng vào trang
function LoadCombo() {
    NTS.loadDataCombo({
        name: "#TinhID",
        type: "POST",
        ajaxUrl: window.Laravel.thietlaphethong.GetDSDiaBanHC_Tinh,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });

    NTS.loadDataCombo({
        name: "#XaID",
        type: "POST",
        ajaxUrl: window.Laravel.thietlaphethong.GetDSDiaBanHC_ByIDCha,
        ajaxParam: { DiaBanHCID_Cha: '', LoaiDiaBan: "02" },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });

    NTS.loadDataCombo({
        name: "#ThonID",
        type: "POST",
        ajaxUrl: window.Laravel.thietlaphethong.GetDSDiaBanHC_ByIDCha,
        ajaxParam: { DiaBanHCID_Cha: '', LoaiDiaBan: "03" },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
}
$("#TinhID").on("change", async function () {
    NTS.loadDataCombo({
        name: "#XaID",
        type: "POST",
        ajaxUrl: window.Laravel.thietlaphethong.GetDSDiaBanHC_ByIDCha,
        ajaxParam: { DiaBanHCID_Cha: $('#TinhID').value(), LoaiDiaBan: "02" },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
});
$("#XaID").on("change", async function () {
    NTS.loadDataCombo({
        name: "#ThonID",
        type: "POST",
        ajaxUrl: window.Laravel.thietlaphethong.GetDSDiaBanHC_ByIDCha,
        ajaxParam: { DiaBanHCID_Cha: $('#XaID').value(), LoaiDiaBan: "03" },
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
});
//lưu dữ liệu
// function btnLuuDuLieu() {
//     LuuThongTin();
//     return false;
// }

// function btnLuuDuLieuCauHinh() {
//     LuuThongTin();
//     return false;
// }

function LuuThongTin() {
    const payload = {
        DonViCapTren: $("#DonViCapTren").value(),
        DonViBaoCao: $("#DonViBaoCao").value(),
        KyThay: $("#KyThay").value(),
        ChucDanhNguoiKy: $("#ChucDanhNguoiKy").value(),
        NguoiKT: $("#NguoiKT").value(),
        ChucDanhNguoiKT: $("#ChucDanhNguoiKT").value(),
        NguoiLap: $("#NguoiLap").value(),
        ChucDanhNguoiLap: $("#ChucDanhNguoiLap").value(),
        DiaDanh: $("#DiaDanh").value(),
        DangNgayBaoCao: $("#DangNgayBaoCao").value(),
        NgayLapBaoCao1: $("#NgayLapBaoCao1").value(),
        NgayLapBaoCao2: $("#NgayLapBaoCao2").value(),
        TinhID: $("#TinhID").value(),
        XaID: $("#XaID").value(),
        ThonID: $("#ThonID").value(),
        NguoiKy: $("#NguoiKy").value(),
    };
    var result = NTS.getAjaxAPI(
        "POST",
        window.location.pathname + "/luuthongtin",
        payload
    );

    if (!result.Err) {
        NTS.thanhcong(result.Msg);
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
}

function KiemTraLoaiNgayBC() {
    if ($("#DangNgayBaoCao").value() == "1") {
        $("#NgayLapBaoCao1").attr("disabled", true);
        $("#NgayLapBaoCao2").attr("disabled", true);
    }
    if ($("#DangNgayBaoCao").value() == "2") {
        $("#NgayLapBaoCao1").attr("disabled", false);
        $("#NgayLapBaoCao2").attr("disabled", true);
    }
    if ($("#DangNgayBaoCao").value() == "3") {
        $("#NgayLapBaoCao1").attr("disabled", true);
        $("#NgayLapBaoCao2").attr("disabled", false);
        if ($("#NgayLapBaoCao2").value() != "" && $("#NgayLapBaoCao2").value() != null) {
        } else {
            $("#NgayLapBaoCao2").value("ngày... tháng...năm...");
        }
    }
    return false;
}

//====================== Thiết lập màu sắc==================================//

function updateColorValue(inputId, valueId) {
    const input = document.getElementById(inputId);
    const valueSpan = document.getElementById(valueId);
    if (input && valueSpan) {
        valueSpan.textContent = input.value.toUpperCase();
        input.addEventListener('input', () => {
            valueSpan.textContent = input.value.toUpperCase();
        });
    }
}

function addJsonLight() {
    const getColorValue = (id, defaultValue = '') => {
        const element = document.getElementById(id);
        return element ? element.value : defaultValue;
    };

    return [
        { tensukien: "them", class: ".nts-color-them", ID: "", color: getColorValue("light_them_color", "#F76707") },
        { tensukien: "luu", class: ".nts-color-luu", ID: "", color: getColorValue("light_luu_color", "#4265B6") },
        { tensukien: "dong", class: ".nts-color-dong", ID: "", color: getColorValue("light_dong_color", "#ffffff") },
        { tensukien: "in", class: ".nts-in,.fa.fa-print", ID: "", color: getColorValue("light_in_color", "#F76707") },
        { tensukien: "xuat", class: ".nts-xuat,.fa.fa-file-excel-o", ID: "", color: getColorValue("light_xuat_color", "#146654") },
        { tensukien: "header", class: ".nts-bg-header-primary", ID: "", color: getColorValue("light_header_color", "#F76707") },
        { tensukien: "menu-bg", class: ".nts-color-menu-bg,.nts-color-menu,.flyout-menu.full-height-from-header", ID: "", color: getColorValue("light_menu_bg_color", "#FFFFFF") },
        { tensukien: "header-text-color", class: ".nts-bg-header-primary>h5#tieuDeModal", ID: "", color: getColorValue("light_header_title_color", "#ffffff") },
        { tensukien: "menu", class: ".nts-color-menu-text,.nts-color-menu-text.nav-link-icon>i,.nts-color-menu-text.nav-link>span>i,.nts-color-menu-text.dropdown-item>span,.nts-color-menu-text.dropdown-item>span>i,.nts-color-menu-text.dropdown-item>i,.nts-sidebar-collapse,.li-sidebar_collapse::before", ID: "", color: getColorValue("light_menu_color", "#000000") },
        { tensukien: "khac-bg", class: ".nts-khac,.main-footer", ID: "", color: getColorValue("light_khac_bg_color", "#F76707") },
        { tensukien: "khac", class: ".nts-color-khac", ID: "", color: getColorValue("light_khac_color", "#F76707") }
    ];
}

function addJsonDark() {
    const getColorValue = (id, defaultValue = '') => {
        const element = document.getElementById(id);
        return element ? element.value : defaultValue;
    };

    return [
        { tensukien: "them", class: ".nts-color-them", ID: "btnThemMoi", color: getColorValue("dark_them_color", "#f76707") },
        { tensukien: "luu", class: ".nts-color-luu", ID: "", color: getColorValue("dark_luu_color", "#f76d23") },
        { tensukien: "dong", class: ".nts-color-dong", ID: "", color: getColorValue("dark_dong_color", "#ffffff") },
        { tensukien: "in", class: ".nts-in,.fa.fa-print", ID: "", color: getColorValue("dark_in_color", "#f76707") },
        { tensukien: "xuat", class: ".nts-xuat,.fa.fa-file-excel-o", ID: "", color: getColorValue("dark_xuat_color", "#2fb344") },
        { tensukien: "header", class: ".nts-bg-header-primary", ID: "", color: getColorValue("dark_header_color", "#1b1b3a") },
        { tensukien: "menu-bg", class: ".nts-color-menu-bg,.nts-color-menu,.flyout-menu.full-height-from-header", ID: "", color: getColorValue("dark_menu_bg_color", "#182433") },
        { tensukien: "header-text-color", class: ".nts-bg-header-primary.modal-title", ID: "", color: getColorValue("dark_header_title_color", "#fff") },
        { tensukien: "menu", class: ".nts-color-menu-text,.nts-color-menu-text.nav-link-icon>i,.nts-color-menu-text.nav-link>span>i,.nts-color-menu-text.dropdown-item>span,.nts-color-menu-text.dropdown-item>span>i,.nts-color-menu-text.dropdown-item>i,.nts-sidebar-collapse,.li-sidebar_collapse::before", ID: "", color: getColorValue("dark_menu_color", "#ffffff") },
        { tensukien: "khac-bg", class: ".nts-khac,.main-footer", ID: "", color: getColorValue("dark_khac_bg_color", "#182433") },
        { tensukien: "khac", class: ".nts-color-khac", ID: "", color: getColorValue("dark_khac_color", "#F76707") }

    ];
}

function saveColors() {
    const lightColors = addJsonLight();
    const darkColors = addJsonDark();

    localStorage.setItem('lightColors', JSON.stringify(lightColors));
    localStorage.setItem('darkColors', JSON.stringify(darkColors));

    // document.getElementById('MauSacJSON_Sang').value = JSON.stringify(lightColors);
    // document.getElementById('MauSacJSON_Toi').value = JSON.stringify(darkColors);

}

function resetColors() {
        const defaultLightColors = {
        them: '#F76707',
        luu: '#4265B6',
        dong: '#ffffff',
        in: '#F76707',
        xuat: '#146654',
        header: '#F76707',
        headerTitle: '#ffffff',
        menuBg: '#FFFFFF',
        menu: '#000000',
        khacBg: '#F76707',
        khac: '#F76707'
    };
    const defaultDarkColors = {
        them: '#f76707',
        luu: '#f76d23',
        dong: '#ffffff',
        in: '#f76707',
        xuat: '#2fb344',
        header: '#1b1b3a',
        headerTitle: '#fff',
        menuBg: '#182433',
        menu: '#ffffff',
        khacBg: '#182433',
        khac: '#182433'
    };
    localStorage.setItem('lightColors', JSON.stringify(defaultLightColors));
    localStorage.setItem('darkColors', JSON.stringify(defaultDarkColors));
    loadColors();
    LuuThongTin_ThietLapMau();
}


function loadColors() {
    const savedLightColors = JSON.parse(localStorage.getItem('lightColors') || '{}');
    const savedDarkColors = JSON.parse(localStorage.getItem('darkColors') || '{}');
    const lightColorInputs = [
        { id: 'light_them_color', value: savedLightColors.them || '#F76707' },
        { id: 'light_luu_color', value: savedLightColors.luu || '#4265B6' },
        { id: 'light_dong_color', value: savedLightColors.dong || '#ffffff' },
        { id: 'light_in_color', value: savedLightColors.in || '#F76707' },
        { id: 'light_xuat_color', value: savedLightColors.xuat || '#146654' },
        { id: 'light_header_color', value: savedLightColors.header || '#F76707' },
        { id: 'light_header_title_color', value: savedLightColors.headerTitle || '#ffffff' },
        { id: 'light_menu_bg_color', value: savedLightColors.menuBg || '#ffffff' },
        { id: 'light_menu_color', value: savedLightColors.menu || '#000000' },
        { id: 'light_khac_color', value: savedLightColors.khac || '#F76707' },
        { id: 'light_khac_bg_color', value: savedLightColors.khacBg || '#F76707' }
    ];
    const darkColorInputs = [
        { id: 'dark_them_color', value: savedDarkColors.them || '#f76707' },
        { id: 'dark_luu_color', value: savedDarkColors.luu || '#f76d23' },
        { id: 'dark_dong_color', value: savedDarkColors.dong || '#ffffff' },
        { id: 'dark_in_color', value: savedDarkColors.in || '#f76707' },
        { id: 'dark_xuat_color', value: savedDarkColors.xuat || '#2fb344' },
        { id: 'dark_header_color', value: savedDarkColors.header || '#1b1b3a' },
        { id: 'dark_header_title_color', value: savedDarkColors.headerTitle || '#fff' },
        { id: 'dark_menu_bg_color', value: savedDarkColors.menuBg || '#182433' },
        { id: 'dark_menu_color', value: savedDarkColors.menu || '#ffffff' },
        { id: 'dark_khac_color', value: savedDarkColors.khac || '#182433' },
        { id: 'dark_khac_bg_color', value: savedDarkColors.khacBg || '#182433' }
    ];


    lightColorInputs.forEach(({ id, value }) => {
        const input = document.getElementById(id);
        input.value = value;
        updateColorValue(id, `${id}_value`);
    });
    darkColorInputs.forEach(({ id, value }) => {
        const input = document.getElementById(id);
        input.value = value;
        updateColorValue(id, `${id}_value`);
    });
}

 function loadColorsFromDB_MauHT() {
     const payload = {
        UserID: userID
    };

    var result =  NTS.getAjaxAPI("GET", window.Laravel.thietlaphethong.GetThietLapMauHeThong, payload);
    if (!result.Err && result.Result && result.Result.length > 0) {
        let data = result.Result[0];
        let lightColors = Array.isArray(data.MauSacJSON_Sang) ? data.MauSacJSON_Sang : JSON.parse(data.MauSacJSON_Sang || '[]');
        let darkColors = Array.isArray(data.MauSacJSON_Toi) ? data.MauSacJSON_Toi : JSON.parse(data.MauSacJSON_Toi || '[]');
        $('#ThietLapMauHeThongID').val(data.id);
        // Ánh xạ tensukien với ID input cho Light Mode
        const lightColorMap = {
            'them': 'light_them_color',
            'luu': 'light_luu_color',
            'dong': 'light_dong_color',
            'in': 'light_in_color',
            'xuat': 'light_xuat_color',
            'header': 'light_header_color',
            'header-text-color': 'light_header_title_color',
            'menu-bg': 'light_menu_bg_color',
            'menu': 'light_menu_color',
            'khac-bg': 'light_khac_bg_color',
            'khac': 'light_khac_color',
        };

        // Gán màu cho Light Mode
        lightColors.forEach(item => {
            const inputId = lightColorMap[item.tensukien];
            if (inputId && item.color) {
                $(`#${inputId}`).val(item.color);
                $(`#${inputId}_value`).text(item.color.toUpperCase());
            }
        });

        // Gán màu cho Dark Mode (nếu có input tương ứng)
        const darkColorMap = {
            'them': 'dark_them_color',
            'luu': 'dark_luu_color',
            'dong': 'dark_dong_color',
            'in': 'dark_in_color',
            'xuat': 'dark_xuat_color',
            'header': 'dark_header_color',
            'header-text-color': 'dark_header_title_color',
            'menu-bg': 'dark_menu_bg_color',
            'menu': 'dark_menu_color',
            'khac-bg': 'dark_khac_bg_color',
            'khac': 'dark_khac_color',
        };

        darkColors.forEach(item => {
            const inputId = darkColorMap[item.tensukien];
            if (inputId && item.color) {
                $(`#${inputId}`).val(item.color);
                $(`#${inputId}_value`).text(item.color.toUpperCase());
            }
        });

        // Lưu vào localStorage để đồng bộ
        localStorage.setItem('lightColors', JSON.stringify(lightColors));
        localStorage.setItem('darkColors', JSON.stringify(darkColors));

        // Gán vào input ẩn
        mausacsang = JSON.stringify(lightColors);
        mausactoi = JSON.stringify(darkColors);

    }
}

function setupDragAndDrop() {
    const colorRows = document.querySelectorAll('.color-row');
    colorRows.forEach(row => {
        row.addEventListener('dragstart', () => {
            row.classList.add('dragging');
        });
        row.addEventListener('dragend', () => {
            row.classList.remove('dragging');
        });
    });

    document.querySelectorAll('.color-group').forEach(group => {
        group.addEventListener('dragover', e => {
            e.preventDefault();
            const afterElement = getDragAfterElement(group, e.clientY);
            const dragging = document.querySelector('.dragging');
            if (afterElement == null) {
                group.appendChild(dragging);
            } else {
                group.insertBefore(dragging, afterElement);
            }
        });
    });
}

function getDragAfterElement(container, y) {
    const draggableElements = [...container.querySelectorAll('.color-row:not(.dragging)')];
    return draggableElements.reduce((closest, child) => {
        const box = child.getBoundingClientRect();
        const offset = y - box.top - box.height / 2;
        if (offset < 0 && offset > closest.offset) {
            return { offset: offset, element: child };
        } else {
            return closest;
        }
    }, { offset: Number.NEGATIVE_INFINITY }).element;
}
 function LuuThongTin_ThietLapMau() {
    const payload = {
        MauSacJSON_Sang: JSON.stringify(addJsonLight()),
        MauSacJSON_Toi: JSON.stringify(addJsonDark()),
        UserID: userID
    };
    var result =  NTS.getAjaxAPI(
        "POST",
        window.Laravel.thietlaphethong.LuuThongTinThietLapMauHeThong,
        payload
    );

    if (!result.Err) {
        //NTS.thanhcong(result.Msg);
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
}

function btnLuuDuLieu_ThietLapMau() {
    saveColors();
    LuuThongTin_ThietLapMau();
}

///////// PHÍM TẮT /////////
var hotKey = 0; // 1 thêm
$(document).on("keydown", function (e) {
    switch (e.keyCode) {
        case 120:
            if (hotKey == 0) $("#btnLuuVaDong").trigger("click");
            e.preventDefault();
            break;
    }
});

async function XoaDuLieu_ThietLapMau(id) {
    var result = await NTS.getAjaxAPIAsync(
        "POST",
        window.Laravel.thietlaphethong.XoaThietLapMauHeThong,
        { ma: id }
    );
    if (!result.err) {
         const defaultLightColors = {
            them: '#F76707',
            luu: '#4265B6',
            dong: '#ffffff',
            in: '#F76707',
            xuat: '#146654',
            header: '#F76707',
            headerTitle: '#ffffff',
            menuBg: '#FFFFFF',
            menu: '#000000',
            khacBg: '#F76707',
            khac: '#F76707',
        };
        const defaultDarkColors = {
            them: '#f76707',
            luu: '#f76d23',
            dong: '#ffffff',
            in: '#f76707',
            xuat: '#2fb344',
            header: '#1b1b3a',
            headerTitle: '#fff',
            menuBg: '#182433',
            menu: '#ffffff',
            khacBg: '#182433',
            khac: '#182433',
        };
        localStorage.setItem('lightColors', JSON.stringify(defaultLightColors));
        localStorage.setItem('darkColors', JSON.stringify(defaultDarkColors));
        loadColors();
        //NTS.thanhcong("Thiết lập màu mặc định thành công!");
        return false;
    } else {
        result.canhbao ? NTS.canhbao(result.msg) : NTS.loi(result.msg);
    }
}

$(document).on("click", "#btnReset", function () {
    var ID =  $("#ThietLapMauHeThongID").val();
    $("#DonViCapTren").value('');
    $("#DonViBaoCao").value('');
    $("#KyThay").value('');
    $("#ChucDanhNguoiKy").value('');
    $("#NguoiKT").value('');
    $("#ChucDanhNguoiKT").value('');
    $("#NguoiLap").value('');
    $("#ChucDanhNguoiLap").value('');
    $("#DiaDanh").value('');
    $("#DangNgayBaoCao").value('');
    $("#NgayLapBaoCao1").value('');
    $("#NgayLapBaoCao2").value('');
    $("#TinhID").value('');
    $("#XaID").value('');
    $("#ThonID").value('');
    $("#NguoiKy").value('');
    LuuThongTin();
    if(ID == ""){
        resetColors();
    }else{
        XoaDuLieu_ThietLapMau(ID);
    }
});

$(document).on('click','#btnLuuVaDong', function(){
    LuuThongTin_ThietLapMau();
    LuuThongTin();
    return false;
});


///////// PHÍM TẮT /////////
var hotKey = 0; // 1 thêm
$(document).on("keydown", function (e) {
    switch (e.keyCode) {
        case 120:
            if (hotKey == 0) $("#btnLuuVaDong").trigger("click");
            e.preventDefault();
            break;
    }
});
