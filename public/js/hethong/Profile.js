const avatarInput = document.getElementById("avatarInput");
const avatarPreview = document.getElementById("avatarPreview");
//#region Combos
let commonComboConfig = {
    columns: 2,
    indexValue: 0,
    indexText: 1, // assuming your result rows are [id, code, name]
    indexText1: 2, // assuming your result rows are [id, code, name]
    textShowTatCa: "-Chọn-",
    showTatCa: true,
};

let loadNhanVienCombo = async () => {
    return loadDataCombos([
        {
            name: "#NhanVienID",
            ajaxUrl: window.Laravel.comboNhanVien,
            ...commonComboConfig,
        },
    ]);
};
let loadGioiTinhCombo = async () => {
    return loadDataCombos([
        {
            name: "#GioiTinh",
            ajaxUrl: window.Laravel.comboGioiTinh,
            ...commonComboConfig,
            textShowTatCa: "-Chọn-",
        },
    ]);
};
//#endregion

//#region Main
document.addEventListener("DOMContentLoaded", async function () {
    //#region load ban đầu
    loadGioiTinhCombo();
    loadNhanVienCombo();
    loadThongTin();
    //#endregion

    // Lấy tất cả các nút toggle
    const toggles = document.querySelectorAll(".btn-toggle-visibility");

    toggles.forEach((btn) => {
        btn.addEventListener("click", function () {
            // tìm input liền kề trong cùng input-group
            const input =
                this.closest(".input-group").querySelector(".password-input");
            if (!input) return;

            if (input.type === "password") {
                input.type = "text";
                this.firstElementChild.classList.remove("fa-eye");
                this.firstElementChild.classList.add("fa-eye-slash");
            } else {
                input.type = "password";
                this.firstElementChild.classList.remove("fa-eye-slash");
                this.firstElementChild.classList.add("fa-eye");
            }
        });
    });

    //#region Lưu thông tin

    avatarInput.addEventListener("change", (e) => {
        const file = e.target.files[0];
        if (!file) return;

        uploadFile(file);
    });

    $("#NhanVienID").on("change", async (e) => {
        let nhanVienData = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.getNhanVienById,
            {
                id: $("#NhanVienID").val(),
            }
        );

        console.log(nhanVienData);

        if (nhanVienData.Err) {
            return NTS.loi(
                nhanVienData.Msg || "Lấy thông tin nhân viên thất bại"
            );
        }

        const r = nhanVienData.Result || {};

        // Helper to format ISO → dd/mm/yyyy
        function formatDate(iso) {
            if (!iso) return "";
            const d = new Date(iso);
            const dd = String(d.getDate()).padStart(2, "0");
            const mm = String(d.getMonth() + 1).padStart(2, "0");
            const yyyy = d.getFullYear();
            return `${dd}/${mm}/${yyyy}`;
        }

        // 1) Họ và tên
        // $("#HoVaTen").val([r.ho, r.ten].filter((x) => x).join(" "));
        $("#HoVaTen").val(r.tenNhanVien);

        // 2) Ngày sinh (if your API returned r.ngaySinh)
        $("#NgaySinh").val(formatDate(r.ngaySinh));

        // 3) Giới tính (assumes your <select id="GioiTinh"> is already populated)
        // and that the API returns a nested object r.gioi_tinh._id
        $("#GioiTinh")
            .val(r.gioiTinhID || "")
            .trigger("change");

        // 4) CMND/CCCD
        $("#CCCD").val(r.cmnd || "");

        // 5) Số điện thoại
        $("#SoDienThoai").val(r.soDienThoai || "");

        // 6) Địa chỉ
        $("#DiaChi").val(r.diaChi || "");

        // 7) Email
        $("#Email").val(r.email || "");
    });

    $("#btnLuu").on("click", async function (e) {
        e.preventDefault();
        if ($("#HoVaTen").val() == "") {
            NTS.canhbao("Họ và tên không được để trống!");
            return false;
        }

        const cccd = $("#CCCD").val();
        const cccdRegex = /^(\d{9}|\d{12})$/;

        if (cccd && !cccdRegex.test(cccd)) {
            NTS.canhbao("CCCD/CMND phải gồm đúng 9 hoặc 12 chữ số");
            return false;
        }
        let r = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.maTuTangUrl,
            {}
        );
        const payload = {
            HoVaTen: $("#HoVaTen").val(),
            NgaySinh: $("#NgaySinh").val(), // dd/mm/yyyy
            GioiTinh: $("#GioiTinh").val(),
            CCCD: $("#CCCD").val(),
            SoDienThoai: $("#SoDienThoai").val(),
            DiaChi: $("#DiaChi").val(),
            Email: $("#Email").val(),
            NhanVienID: $("#NhanVienID").val(),
            MaNhanVien: r.SoChungTu || null,
        };

        const res = await NTS.getAjaxAPIAsync(
            "POST",
            window.Laravel.luuThongTin,
            payload
        );

        if (res.Err) {
            // nếu có lỗi validation
            if (res.Errors) {
                // hiển thị từng lỗi, hoặc đơn giản alert
                NTS.loi(Object.values(res.Errors).flat().join("\n"));
            } else {
                NTS.loi(res.Msg || "Lưu thất bại");
            }
            return;
        }

        const nhanVienID = res?.Result?.user?.NhanVienID ?? null;
        const area = document.getElementById("ChonNV_Area");
        if (area) {
            area.style.display = nhanVienID ? "none" : "";
        }

        NTS.thanhcong("Thông tin cá nhân đã được cập nhật!");
    });
    //#endregion

    //#region Reset password
    const form = document.getElementById("passwordChangeForm");
    const submitBtn = form.querySelector('button[type="submit"]');
    form.addEventListener("submit", async function (e) {
        e.preventDefault();

        // reset thông báo lỗi
        form.querySelectorAll(".invalid-feedback").forEach((el) => el.remove());
        form.querySelectorAll(".is-invalid").forEach((el) =>
            el.classList.remove("is-invalid")
        );

        // lấy dữ liệu form
        const formData = new FormData(form);

        // disable nút submit
        submitBtn.disabled = true;
        const originalText = submitBtn.textContent;
        submitBtn.textContent = "Đang xử lý...";

        try {
            const data = await NTS.getAjaxAPIAsync(
                "POST",
                form.action,
                formData
            );

            // hiển thị lỗi validation
            if (data.Err) {
                if (data.Msg) {
                    NTS.thongtin(data.Msg);
                }
            } else {
                // thành công
                NTS.thanhcong(data.Msg || "Đổi mật khẩu thành công");

                // reset form
                form.reset();

                document.getElementById("logout-form").submit();
            }
        } catch (err) {
            let errMsg = err.responseJSON;
            if (errMsg && Object.keys(errMsg.errors).length > 0) {
                form.querySelectorAll(".invalid-feedback").forEach((el) =>
                    el.remove()
                );
                form.querySelectorAll(".is-invalid").forEach((el) =>
                    el.classList.remove("is-invalid")
                );

                for (const [field, messages] of Object.entries(errMsg.errors)) {
                    const input = form.querySelector(`[name="${field}"]`);
                    if (input) {
                        // highlight input
                        input.classList.add("is-invalid");

                        // tạo feedback div
                        const feedback = document.createElement("div");
                        feedback.className = "invalid-feedback";
                        feedback.textContent = messages.join(" ");

                        // gắn dưới input
                        input.parentNode.appendChild(feedback);
                    }
                }
            } else NTS.loi("Không thể kết nối đến server.");
        } finally {
            // restore nút submit
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    });
    //#endregion
});

//#endregion

//#region Funcs
function switchTab(tab) {
    const infoBtn = document.getElementById("tab-info");
    const pwdBtn = document.getElementById("tab-password");
    const infoContent = document.getElementById("content-info");
    const pwdContent = document.getElementById("content-password");

    if (tab === "info") {
        infoBtn.classList.replace("text-gray-600", "text-gray-900");
        infoBtn.classList.add("bg-gray-100", "border-l-4", "border-blue-600");
        pwdBtn.classList.replace("text-gray-900", "text-gray-600");
        pwdBtn.classList.remove("bg-gray-100", "border-l-4", "border-blue-600");
        infoContent.classList.remove("hidden");
        pwdContent.classList.add("hidden");
    } else {
        pwdBtn.classList.replace("text-gray-600", "text-gray-900");
        pwdBtn.classList.add("bg-gray-100", "border-l-4", "border-blue-600");
        infoBtn.classList.replace("text-gray-900", "text-gray-600");
        infoBtn.classList.remove(
            "bg-gray-100",
            "border-l-4",
            "border-blue-600"
        );
        pwdContent.classList.remove("hidden");
        infoContent.classList.add("hidden");
    }
}

async function uploadFile(file) {
    const formData = new FormData();
    formData.append("file", file);

    NTS.getAjaxAPIAsync("POST", window.Laravel.uploadFile, formData)
        .then((res) => {
            if (res.err) {
                NTS.loi(res.msg);
                return Promise.reject("Upload error");
            }
            const Path = res.url[0];
            const fullUrl = new URL(Path, window.location.origin).href;

            return NTS.getAjaxAPIAsync("POST", window.Laravel.updateAvatar, {
                avatarUrl: fullUrl,
            }).then((saveRes) => {
                if (saveRes.Err) {
                    NTS.loi(saveRes.Msg || "Không thể cập nhật avatar.");
                    return Promise.reject("Update avatar error");
                }

                // Cap nhat preview
                const preview = document.getElementById("avatarPreview");
                let newURL = new URL(
                    saveRes.Result?.avatar,
                    window.location.origin
                ).href;
                if (preview) {
                    preview.src = newURL;
                    NTS.thanhcong("Cập nhật avatar thành công.");
                }
                $("#avt_layout").prop("src", newURL);
            });
        })
        .catch((err) => {
            console.error("Lỗi update avatar:", err);
            NTS.loi(err);
        });
}

async function loadThongTin() {
    const res = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.getUserInfo,
        {}
    );
    if (res.Err) {
        return NTS.loi(res.Msg || "Không thể tải thông tin người dùng.");
    }
    const u = res; // since getInfo returns the raw user object

    // Format an ISO date string → dd/mm/yyyy
    function formatDate(iso) {
        if (!iso) return "";
        const d = new Date(iso);
        const dd = String(d.getDate()).padStart(2, "0");
        const mm = String(d.getMonth() + 1).padStart(2, "0");
        const yyyy = d.getFullYear();
        return `${dd}/${mm}/${yyyy}`;
    }

    // 1) Avatar
    if (u.avatar) {
        document.getElementById("avatarPreview").src = u.avatar;
    }

    // 2) Họ và tên
    $("#HoVaTen").val(u.name || "");

    // 3) Ngày sinh
    $("#NgaySinh").val(formatDate(u.ngaySinh));

    // 4) Giới tính (select)
    $("#GioiTinh")
        .val(u.gioiTinhID || "")
        .trigger("change");

    // 5) CMND/CCCD
    $("#CCCD").val(u.cmnd || "");

    // 6) Số điện thoại
    $("#SoDienThoai").val(u.soDienThoai || "");

    // 7) Địa chỉ
    $("#DiaChi").val(u.diaChi || "");

    // 8) Email
    $("#Email").val(u.email || "");

    // if (u.NhanVienID) {
    //     $("#NhanVienID").val(u.NhanVienID);
    //     $("#ChonNV_Area").hide();
    // }
}

//#endregion
