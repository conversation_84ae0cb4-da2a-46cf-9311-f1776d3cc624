@php
    // Mode: 'multi' (default) or 'single'
    $mode = $mode ?? 'multi';
    $showPreview = $showPreview ?? true;

    // Nếu không được t<PERSON>yền vào, mặc định hiển thị các định dạng file
    $ChapNhanFile = $ChapNhanFile ?? [
        'jpg',
        'jpeg',
        'png',
        'pdf',
        'doc',
        'docx',
        'rar',
        'zip'
    ];
    $accept = '.' . implode(',.', $ChapNhanFile);


    // Unique uploader ID
    $id = $id ?? 'dinhKemFile';
@endphp

<div class="d-inline-block w-auto file-uploader" data-uploader-id="{{ $id }}" data-mode="{{ $mode }}"
    data-show-preview="{{ $showPreview ? 'true' : 'false' }}">
    <div class="d-flex flex-wrap align-items-start gap-3">
        <div id="{{ $id }}_dropArea"
            class="drop-area d-flex align-items-center p-3 rounded shadow-sm justify-content-center"
            style="background: #f5f8fa; gap: 16px; border: 1px dashed #b3c0ce; cursor: pointer; transition: box-shadow 0.2s;">
            <div class="text-center d-flex flex-column align-items-center" style="min-width:50px;">
                <i class="fa fa-cloud-upload" style="font-size: 36px; color: #5a8dee;"></i>
            </div>
            <div class="flex-grow-1">
                <div style="font-size: 15px; color: #444; font-weight: 500;">
                    Chọn {{ $mode === 'multi' ? 'các file' : '1 file' }} hoặc kéo thả vào đây
                </div>
                <div id="{{ $id }}_fileHelp" style="font-size: 13px; color: #888;">
                    <span class="fw-bold">Hỗ trợ:</span>
                    {{ collect($ChapNhanFile)->take(3)->implode(', ') }}@if(count($ChapNhanFile) > 3)…@endif. Tối đa
                    10MB
                </div>
            </div>
            <div class="d-flex flex-column gap-2">
                <button id="{{ $id }}_btnSelect" type="button"
                    class="btn btn-sm d-flex align-items-center justify-content-center mb-1"
                    style="min-width:110px; background-color: var(--nts-bg-blue); color:var(--nts-text-primary);">
                    <i class="fa fa-upload me-2"></i> Chọn file
                </button>
                <button id="{{ $id }}_btnClear" type="button"
                    class="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center d-none" style="width:auto;
                    ">
                    <i class="fa fa-trash-o me-2"></i> Xóa tất cả
                </button>
            </div>
        </div>
        <!-- Inline file list -->
        <div id="{{ $id }}_list" class="file-list d-flex flex-wrap gap-2"></div>
    </div>

    <input type="hidden" id="{{ $id }}" name="{{ $id }}" value="">
    <input type="file" id="{{ $id }}_input" accept="{{ $accept }}" @if($mode === 'multi') multiple @endif
        style="display:none;">
</div>
@once
    @push('styles')
        <style>
            .drop-area.dragover,
            .drop-area.dragover * {
                background: #e3f2fd !important;
                border-color: #5a8dee !important;
                transition: background 0.2s, border 0.2s;
            }

            .file-preview .btn-close {
                color: #e53935 !important;
                opacity: 1 !important;
                z-index: 2;
                padding: 0;
                transition: background 0.15s, color 0.15s, border 0.15s;
            }

            .file-preview .btn-close:hover,
            .file-preview .btn-close:focus {
                color: #c62828 !important;
                outline: none;
            }

            .file-list {
                display: contents;
            }

            .file-preview {
                flex: 0 0 auto;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .file-preview img.img-thumbnail {
                width: 60px;
                height: 60px;
                object-fit: cover;
            }

            .file-preview .file-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
                line-height: 1;
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            window.fileUploaderInstances = window.fileUploaderInstances || {};

            class FileUploader {
                constructor(id, opts = {}) {
                    this.id = id;
                    this.dropArea = document.getElementById(id + '_dropArea');
                    this.input = document.getElementById(id + '_input');
                    this.list = document.getElementById(id + '_list');
                    this.hidden = document.getElementById(id);
                    this.mode = opts.mode || this.dropArea.closest('.file-uploader').dataset.mode;
                    this.showPreview = this.dropArea.closest('.file-uploader').dataset.showPreview === 'true';


                    this.input.multiple = (this.mode === 'multi');

                    this.uploadUrl = opts.uploadUrl;
                    this.deleteUrl = opts.deleteUrl;
                    this.urls = [];
                    this.files = []; // <-- Add this
                    this._bindEvents();
                    if (!this.showPreview) this.list.classList.add('d-none');
                }

                _bindEvents() {
                    document.getElementById(this.id + '_btnSelect')
                        .addEventListener('click', () => this.input.click());

                    this.input.addEventListener('change', e => this._onFiles(e.target.files));

                    document.getElementById(this.id + '_btnClear')
                        .addEventListener('click', () => this.clear());

                    ['this.inputdragenter', 'dragover', 'dragleave', 'drop'].forEach(evt => {
                        this.dropArea.addEventListener(evt, e => {
                            e.preventDefault();
                            e.stopPropagation();
                            if (evt === 'drop') {
                                this._onFiles(e.dataTransfer.files);
                            }
                            if (evt === 'dragenter' || evt === 'dragover') {
                                this.dropArea.classList.add('dragover');
                            } else {
                                this.dropArea.classList.remove('dragover');
                            }
                        });
                    });
                }

                async _onFiles(files) {
                    const fileArray = Array.isArray(files) ? files : Array.from(files);

                    if (this.mode === 'single') {
                        // Single mode: only upload the first file immediately
                        const [f] = fileArray;
                        this.urls = [];
                        this.files = [];
                        try {
                            const form = new FormData();
                            form.append('file', f);
                            const res = await NTS.getAjaxAPIAsync('POST', this.uploadUrl, form);
                            if (!res.success) {
                                NTS.loi(`Tải lên thất bại: ${f.name}`);
                                return;
                            }
                            NTS.thanhcong('Đính kèm file thành công');
                            const url = Array.isArray(res.url) ? res.url[0] : res.url;
                            this.urls.push(url);
                            this.files.push(f);
                            this._renderPreviews();
                            this._emitComplete();
                        } catch (err) {
                            NTS.loi(`Lỗi khi tải lên: ${err.message}`);
                        }
                        return;
                    }

                    // Multi mode: batch all uploads in parallel
                    const uploads = fileArray.map(f => {
                        const form = new FormData();
                        form.append('file', f);
                        return NTS.getAjaxAPIAsync('POST', this.uploadUrl, form)
                            .then(res => ({ res, f }))
                            .catch(error => ({ error, f }));
                    });

                    let results;
                    try {
                        results = await Promise.all(uploads);
                    } catch (e) {
                        // should never happen because we catch each upload above
                        NTS.loi(`Lỗi chung khi tải lên: ${e.message}`);
                        return;
                    }

                    // collect successes/failures
                    const newUrls = [];
                    const newFiles = [];
                    let anyFail = false;

                    for (const { res, error, f } of results) {
                        if (error || (res && !res.success)) {
                            anyFail = true;
                            console.error('Upload error', error || res);
                        } else {
                            const url = Array.isArray(res.url) ? res.url[0] : res.url;
                            newUrls.push(url);
                            newFiles.push(f);
                        }
                    }

                    // merge into instance state
                    this.urls.push(...newUrls);
                    this.files.push(...newFiles);
                    this._renderPreviews();

                    // one toast only
                    if (anyFail) {
                        NTS.loi('Một số file không thể tải lên');
                    } else {
                        NTS.thanhcong('Tất cả file đã được tải lên thành công');
                    }

                    this._emitComplete();
                }

                // helper to fire the upload‑complete event
                _emitComplete() {
                    this.dropArea.dispatchEvent(new CustomEvent('fileUploaderUploadComplete', {
                        detail: { id: this.id, urls: this.urls, files: this.files },
                        bubbles: true,
                    }));
                }

                _renderPreviews() {
                    this.list.innerHTML = '';
                    this.urls.forEach(url => {
                        const fn = url.split('/').pop();
                        const div = document.createElement('div');
                        div.className = 'file-preview position-relative d-inline-block text-center me-2 mb-2';
                        const btn =
                            `<button type="button" class="${this.id}_btn-close btn-close position-absolute top-0 end-0 m-0" aria-label="Delete"></button>`;
                        const ext = fn.split('.').pop().toLowerCase();
                        const thumb = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext) ?
                            `<img src="${url}" class="img-thumbnail">` :
                            `<div class="file-icon bg-secondary text-white rounded d-flex align-items-center justify-content-center mb-1"><i class="fa fa-file"></i></div>`;
                        div.innerHTML = btn + thumb +
                            `<a href="${url}" target="_blank" class="d-block small text-truncate" style="max-width:60px;">${fn}</a>`;
                        div.querySelector(`.${this.id}_btn-close`).addEventListener('click', () => this._remove(url,
                            div));
                        this.list.append(div);
                    });
                    this.hidden.value = this.urls.join('|');
                    this._toggleClearBtn();
                }

                async _remove(url, el) {
                    try {
                        await NTS.getAjaxAPIAsync('DELETE', this.deleteUrl, {
                            urls: [url]
                        });
                    } catch (err) {
                        console.error('Delete error', err);
                    }
                    const index = this.urls.indexOf(url);
                    if (index > -1) {
                        this.urls.splice(index, 1);
                        this.files.splice(index, 1); // Remove corresponding file
                    }
                    this.hidden.value = this.urls.join('|');
                    el.remove();
                    this._toggleClearBtn();

                    const event = new CustomEvent('fileUploaderUploadComplete', {
                        detail: { id: this.id, urls: this.urls, files: this.files },
                        bubbles: true,
                        cancelable: false,
                    });
                    this.dropArea.dispatchEvent(event);
                }

                clear() {
                    this.urls = [];
                    this.input.value = '';
                    this.list.innerHTML = '';
                    this.hidden.value = '';
                    // this._toggleClearBtn();
                }

                _toggleClearBtn() {
                    const btn = document.getElementById(this.id + '_btnClear');
                    if (this.urls.length > 0 && this.mode === 'multi') {
                        btn.classList.remove('d-none');
                    } else {
                        btn.classList.add('d-none');
                    }
                }
            }

            document.addEventListener('DOMContentLoaded', () => {
                document.querySelectorAll('.file-uploader').forEach(el => {
                    const id = el.dataset.uploaderId;
                    const mode = el.dataset.mode;
                    window.fileUploaderInstances[id] = new FileUploader(id, {
                        uploadUrl: '/api/dungchung/files/upload',
                        deleteUrl: '/api/dungchung/files/delete-multiple',
                        mode: mode
                    });
                });
            });

            function resetFileInputV2(id = 'dinhKemFile') {
                const inst = window.fileUploaderInstances[id];
                if (inst) inst.clear();
            }
        </script>
    @endpush
@endonce