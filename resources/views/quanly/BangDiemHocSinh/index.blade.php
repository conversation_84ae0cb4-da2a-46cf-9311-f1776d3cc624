@extends('layouts.layouts')

{{-- tiêu đề trang --}}
@section('title', 'Bang điểm học sinh')

{{-- các file css cần thiết --}}
@push('scripts')
    <link rel="stylesheet" href="{{ asset('css/quanly/hosototnghiep.css') }}">
@endpush
@section('content')
    @php
        $filters = [
            // Từ ngày / Đến ngày
            '<div class="row mb-3">
            <div class="col-md-6">
                <label class="form-label" for="TuNgay_Loc">Từ ngày</label>
                <div class="input-group input-group-sm">

                    <input type="text" class="form-control input-sm date-picker" id="TuNgay_Loc" placeholder="dd/MM/yyyy"
                        autocomplete="off">
                </div>
            </div>
            <div class="col-md-6">
                <label class="form-label" for="DenNgay_Loc">Đ<PERSON><PERSON> ngày</label>
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control input-sm date-picker" id="DenNgay_Loc" placeholder="dd/MM/yyyy"
                        autocomplete="off">
                </div>
            </div>
        </div>',

            // Cấp học (dropdown)
            '<div class="row mb-3">
            <div class="col-md-12">
                <label class="form-label" for="CapHoc_Loc">Cấp học</label>
                <select class="form-select input-sm" id="CapHoc_Loc">
                    <option value="">-- Tất cả --</option>
                    <!-- JS sẽ populate options động -->
                </select>
            </div>
        </div>',

            // Trạng thái (dropdown)
            '<div class="row mb-3">
            <div class="col-md-12">
                <label class="form-label" for="TrangThai_Loc">Trạng thái</label>
                <select class="form-select input-sm" id="TrangThai_Loc">
                    <option value="">-- Tất cả --</option>
                    <!-- JS sẽ populate options động -->
                </select>
            </div>
        </div>'
        ];

    @endphp
    @include('partials.filter-panel', [
            'filters' => $filters,
            'actions' => null,
            'showTimKiemNangCao' => true,
            'showBulkActions' => false,
            'themmoi_dropdown' => true,
            'themmoi_content' => [
                // 1) Simple “Thêm mới” with font‑awesome icon
        // 1) “Thêm mới”
                '<a id="btnThemMoi" class="dropdown-item textsize-item" href="javascript:void(0);" style="
                                                                                          display: flex;
                                                                                          align-items: center;
                                                                                          white-space: nowrap;
                                                                                        ">
            <span style="
                                                                                          width: 1.25em;
                                                                                          text-align: center;
                                                                                          flex-shrink: 0;
                                                                                        ">
                <i class="fa fa-plus text-info"></i>
            </span>
            <span style="margin-left: 0.5em;">
                Thêm mới
            </span>
        </a>',

                '<a id="btnThemMoiOCR" class="dropdown-item textsize-item" href="javascript:void(0);" style="
                                                                                          display: flex;
                                                                                          align-items: center;
                                                                                          white-space: nowrap;
                                                                                        ">
            <span style="
                                                                                          width: 1.25em;
                                                                                          text-align: center;
                                                                                          flex-shrink: 0;
                                                                                        ">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" style="width: 1em; height: 1em;">
                    <path
                        d="M3 1C2.46957 1 1.96086 1.21071 1.58579 1.58579C1.21071 1.96086 1 2.46957 1 3V7.5H3V3H8V1H3ZM1 17V11.5H3V17H8V19H3C2.46957 19 1.96086 18.7893 1.58579 18.4142C1.21071 18.0391 1 17.5304 1 17ZM12 17V19H17C17.5304 19 18.0391 18.7893 18.4142 18.4142C18.7893 18.0391 19 17.5304 19 17V11.5H17V17H12ZM17 7.5H19V3C19 2.46957 18.7893 1.96086 18.4142 1.58579C18.0391 1.21071 17.5304 1 17 1H12V3H17V7.5Z"
                        fill="#F76707" />
                    <path d="M6 5H14V6H6V5ZM5 8H15V9H5V8ZM6 11H14V12H6V11ZM5 14H15V15H5V14Z" fill="#F76707" />
                </svg>
            </span>
            <span style="margin-left: 0.5em;">
                Trích xuất bảng điểm<br> tự động (OCR)
            </span>
        </a>',
            ],
    ])



    <div class="row" style="margin-top: 4px; padding-left: 0;">
        <div class="col-md-12 px-0">
            <div id="Grid1"></div>
        </div>
    </div>

    <!-- MODAL THÊM MỚI -->
    <div id="mdThemMoi" class="full-screen-panel" style="display:none;">
        <div class="panel-content" style="background-color:#DADFE5 !important;">
            <div class="modal-content d-flex flex-column" style="border-radius:0; height:100vh;">
                <!-- HEADER -->
                <div class="modal-header modal-title font-weight-bold"
                    style="border-radius: 0 !important; color:gray !important; text-transform:uppercase">
                    <h5 class="modal-title text-nts-success fw-bold" id="lblTieuDeMultiStep">
                        THÊM MỚI BẢNG ĐIỂM HỌC SINH
                    </h5>
                    <button type="button" class="btn-close my-0 mx-3 d-flex align-items-center justify-content-center"
                        data-bs-dismiss="modal" aria-label="Close"
                        style="width:32px; height:32px; padding:0; font-size:1.2rem; color:gray !important; font-weight:bold;">
                        <i class="fa fa-times" aria-hidden="true"></i>
                    </button>
                </div>

                <!-- BODY -->
                <div class="modal-body flex-fill px-2" style="background-color:inherit !important;">
                    <div class="row p-1" style="height:100%">

                        <!-- MAIN 10-COL PANEL -->
                        <div class="col-md-10" style="height:100%" id="step-content">
                            <input type="hidden" id="HoSoTotNghiepID" value="" />

                            <!-- STEP 1 -->
                            <div id="step-1-content" class="box-shadow-primary border border-primary"
                                style="padding:10px; padding-top:0; border-radius:4px; height:100%; background-color:#fff !important;">
                                <div class="row mt-2 box-hdsd"
                                    style="background-color:white; border-radius:8px; padding:10px; padding-top:0; height:100%;">

                                    <!-- IMAGE -->
                                    <div class="col-md-5 d-flex flex-column justify-content-end h-100">
                                        <img alt="" src="{{ asset('img/step1_BangDiem.png') }}?v={{ time() }}"
                                            style="width:100%" />
                                    </div>

                                    <!-- FORM -->
                                    <div class="col-md-7 d-flex flex-column" style="height:100%">
                                        <div class="mb-3 p-3 border-start border-4 border-warning bg-light d-flex rounded-2 my-4"
                                            style="box-shadow:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      0 -2px 4px rgba(0,0,0,0.04),
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      2px 0 4px rgba(0,0,0,0.04),
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      0 4px 6px rgba(0,0,0,0.2); !important">
                                            <div class="d-flex align-items-center justify-content-center me-3">
                                                <i class="fa fa-info-circle fa-2x text-info" style="flex-shrink:0;"></i>
                                            </div>
                                            <div>
                                                <span class="mb-1 text-info fw-bold">Nhập các thông tin chung</span>
                                                <p class="mb-0 text-info">
                                                    Nhập các thông tin chung như: Số văn bản, ngày ký, ngày người ký,…
                                                    sau đó đính kèm văn bản gốc (nếu có).
                                                    <br>
                                                    <strong>Lưu ý:</strong> Các dấu <span class="text-danger">(*)</span> là
                                                    trường bắt buộc phải nhập.
                                                </p>
                                            </div>
                                        </div>

                                        <fieldset class="KhungVien flex-grow-1" id="fieldThongTinChung">
                                            <legend>Thông tin chung</legend>
                                            <form action="#" id="formThongTinChung">
                                                <div class="row mb-2">
                                                    <div class="col-md-4">
                                                        <label for="SoVanBan">Số văn bản</label>
                                                        <input type="text" class="form-control" id="SoVanBan"
                                                            autocomplete="off" required>
                                                    </div>

                                                </div>
                                                <div class="row mb-2">
                                                    <div class="col-md-4">
                                                        <label for="NgayKy">Ngày ký</label>
                                                        <input type="text" class="form-control date-picker" id="NgayKy"
                                                            name="NgayKy" autocomplete="off" data-date-format="dd/mm/yyyy"
                                                            placeholder="dd/MM/yyyy">
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label for="NguoiKy">Người ký</label>
                                                        <input type="text" class="form-control" id="NguoiKy"
                                                            autocomplete="off">
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label for="ChucVu">Chức vụ</label>
                                                        <select class="form-control" id="ChucVu">
                                                            <option>Trực tiếp</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="row mb-2">
                                                    <div class="col-md-8">
                                                        <label for="KyThi">Kỳ thi</label>
                                                        <select class="form-control" id="KyThi" required></select>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label for="HinhThucDaoTao">Hình thức đào tạo</label>
                                                        <select class="form-control" id="HinhThucDaoTao"></select>
                                                    </div>
                                                </div>

                                                <div class="row mb-2">
                                                    <div class="col-md-8">
                                                        <label for="HoiDong">Hội đồng</label>
                                                        <select class="form-control" id="HoiDong" required></select>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label for="NamHoc">Năm thi</label>
                                                        <select class="form-control" id="NamHoc" required></select>
                                                    </div>
                                                </div>

                                                <div class="row mb-2">
                                                    <div class="col-md-12">
                                                        <label for="CoQuanBanHanh">Cơ quan ban hành</label>
                                                        <input type="text" class="form-control" id="CoQuanBanHanh"
                                                            autocomplete="off">
                                                    </div>
                                                </div>

                                                <div class="row mb-2">
                                                    <div class="col-md-12">
                                                        <label for="TrichYeu">Trích yếu</label>
                                                        <textarea class="form-control" id="TrichYeu" rows="2"></textarea>
                                                    </div>
                                                </div>

                                                <div class="row mb-2">
                                                    <div class="col-md-12">
                                                        @include('partials.dinh-kem', ['id' => 'dinhKemQD'])
                                                    </div>
                                                </div>
                                            </form>
                                        </fieldset>

                                        <div class="row justify-content-end">
                                            <div class="col-auto">
                                                <a href="#" id="btnTiepTuc" class="btn nts-color-luu btn-luuvadong">
                                                    <img src="{{ asset('img/next-step.svg') }}?v={{ time() }}" />
                                                    &ensp;Tiếp tục (F9)
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- STEP 2 -->
                            <div id="step-2-content" class="border border-primary p-2 flex-grow-1"
                                style="display:none; background-color:#fff !important;">
                                <!-- 0) Card ABOVE the tabs -->
                                <div class="card card-luoi shadow-sm mb-3">
                                    <div class="card-body profile-user-box mb-2" style="padding-bottom:0; padding-top:6px;">
                                        <div class="row" id="contentCapNhatVB">
                                            <!-- populated via JS -->
                                        </div>
                                    </div>
                                </div>

                                <!-- 1) Nav tabs -->
                                <ul class="nav nav-tabs" id="step2Tabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="tab-ds-tab" data-bs-toggle="tab"
                                            data-bs-target="#tab-ds" type="button" role="tab" aria-controls="tab-ds"
                                            aria-selected="true">
                                            <i class="fa fa-users"></i>&nbsp;Danh sách học sinh
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="tab-lichsu-tab" data-bs-toggle="tab"
                                            data-bs-target="#tab-lichsu" type="button" role="tab" aria-controls="tab-lichsu"
                                            aria-selected="false"
                                            onclick="getNhatKyThaoTac('NhatKyContainer_us','bang_diems', selectedId);">
                                            <i class="fa fa-clock-o"></i>&nbsp;Lịch sử thao tác
                                        </button>
                                    </li>
                                </ul>

                                <!-- 2) Tab panes -->
                                <div class="tab-content mt-3" id="step2TabsContent">
                                    <!-- Danh sách HS -->
                                    <div class="tab-pane fade show active" id="tab-ds" role="tabpanel"
                                        aria-labelledby="tab-ds-tab">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div class="input-icon" style="max-width:340px;">
                                                <input type="text" class="form-control" placeholder="Nội dung tìm kiếm …"
                                                    id="searchContent" autocomplete="off">
                                                <span class="input-icon-addon"><i class="fa fa-search"></i></span>
                                            </div>
                                            <div class="btn-group ms-2">
                                                <button type="button" class="btn btn-warning dropdown-toggle"
                                                    id="dropdownThemMoiHS" data-bs-toggle="dropdown">
                                                    <i class="fa fa-plus"></i>&nbsp;Thêm mới&nbsp;&nbsp;<i
                                                        class="fa fa-caret-down"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownThemMoiHS">
                                                    <li><a class="dropdown-item" href="#" id="btnNhapNhieuHS">
                                                            <i class="fa fa-list-ul me-2 text-nts-primary"></i>Chọn học sinh
                                                        </a></li>
                                                    <li><a class="dropdown-item" href="#" id="btnNhanExcelHS">
                                                            <i class="fa fa-file-excel-o me-2 text-nts-primary"></i>Nhập
                                                            excel
                                                        </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div id="tabGridHocSinh" class="mb-3"></div>
                                    </div>

                                    <!-- Lịch sử thao tác -->
                                    <div class="tab-pane fade" id="tab-lichsu" role="tabpanel"
                                        aria-labelledby="tab-lichsu-tab">
                                        <div class="p-3">
                                            <div id="lichSuThaoTacContent">
                                                @include('partials.nhatkythaotac', [])
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Footer buttons -->
                                    <div class="d-flex justify-content-end mt-3 gap-2">
                                        <button id="btnQuayLaiBuoc1" class="btn nts-color-canhbao">
                                            <img src="/img/icon_QuayLai.svg" style="height:20px;" />&nbsp;Quay lại (F8)
                                        </button>
                                        <button id="btnKetThuc" class="btn nts-color-luu">
                                            <img src="/img/icon_KetThuc.svg" style="height:20px;" />&nbsp;Kết thúc (F9)
                                        </button>
                                    </div>
                                </div>


                            </div>
                            <!-- / .col-md-10 -->

                            <!-- SIDEBAR INSIDE SAME ROW -->
                            <div class="col-md-2 d-flex flex-column mr-2" style="height:100%">
                                <div class="row flex-fill">
                                    <div class="box-shadow-primary border border-primary"
                                        style="padding: 10px; border-radius: 4px; height: 100%; background-color:#fff !important;">
                                        <div class="row mt-2 box-hdsd"
                                            style="background-color: white; border-radius: 8px; padding: 10px;">
                                            <ul class="steps steps-counter steps-vertical" style="height:auto">
                                                <h3 class="text-nts-primary"
                                                    style="margin-bottom:0px;text-align:center; font-weight:bold; font-size:1.25rem">
                                                    CÁC
                                                    BƯỚC
                                                    THỰC HIỆN</h3>
                                                <li class="step-item" id="sidebar-step-1">
                                                    <div class="step-title font-weight-bold m-0">Thông tin chung</div>
                                                    <div class="step-desc">Cập nhật các thông tin như: Số quyết định, ngày
                                                        ký, người ký, hội đồng công nhận tốt nghiệp, trường,...</div>
                                                    <img class="d-block mx-auto mt-2"
                                                        src="{{ asset('img/Nen.png') }}?v={{ time() }}"
                                                        style="max-width: 43%;">
                                                </li>
                                                <li class="step-item" id="sidebar-step-2">
                                                    <div class="step-title m-0 font-weight-bold">Nhập danh sách học
                                                        sinh/sinh
                                                        viên</div>
                                                    <div class="step-desc">Nhập danh sách học sinh được công nhận tốt
                                                        nghiệp và kết quả tốt nghiệp</div>
                                                    <img class="d-block mx-auto mt-2"
                                                        src="{{ asset('img/Nen2.png') }}?v={{ time() }}"
                                                        style="max-width: 43%;">
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- / .col-md-2 -->

                        </div>
                        <!-- / .row -->
                    </div>
                    <!-- / .modal-body -->
                </div>
                <!-- / .modal-content -->
            </div>
            <!-- / .panel-content -->
        </div>
        <!-- / #mdThemMoi -->

        <div id="mdChiTietQD" class="full-screen-panel" style="display:none;">
            <div class="panel-content" style="background-color:#DADFE5 !important;">
                <div class="modal-dialog modal-content d-flex flex-column" style="border-radius:0; height:100vh;">
                    <!-- Header -->
                    <div class="modal-header modal-title font-weight-bold"
                        style="border-radius:0 !important; color:gray !important; text-transform:uppercase">
                        <h5 class="modal-title text-nts-success fw-bold">XEM CHI TIẾT THÔNG TIN BẢNG ĐIỂM HỌC SINH</h5>
                        <button type="button" class="btn-close  my-0 mx-3 d-flex align-items-center justify-content-center"
                            onclick="$('#mdChiTietQD').hide();" aria-label="Close"
                            style="width:32px; height:32px; padding:0; font-size:1.2rem; font-weight:bold;">
                            <i class="fa fa-times text-nts-success" aria-hidden="true"></i>
                        </button>
                    </div>

                    <!-- Body (re-used step-2 style) -->
                    <div class="modal-body flex-fill px-2" style="background-color:inherit !important;">
                        <div id="step2ChiTiet" class="border border-primary p-2 flex-grow-1"
                            style="background-color:#fff !important; height:100%; display:flex; flex-direction:column;">


                            <!-- 0) Profile Card -->
                            <div class="row align-items-stretch gx-3">
                                <!-- main detail pane -->
                                <div class="col-12 d-flex">
                                    <div class="card card-luoi shadow-sm mb-3 flex-fill d-flex flex-column">
                                        <div class="card-body profile-user-box mb-2 flex-fill">
                                            <div class="row h-100" id="contentCapNhatVBChiTiet">
                                                <!-- injected detail HTML -->
                                            </div>
                                        </div>
                                    </div>
                                </div>


                            </div>


                            <!-- 1) Nav tabs -->
                            <ul class="nav nav-tabs" id="detailTabsChiTiet" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="detail-ds-tab-chi-tiet" data-bs-toggle="tab"
                                        data-bs-target="#detail-ds-chi-tiet" type="button" role="tab"
                                        aria-controls="detail-ds-chi-tiet" aria-selected="true">
                                        <i class="fa fa-users"></i>&nbsp;Danh sách học sinh
                                    </button>
                                </li>

                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="detail-lichsu-tab-chi-tiet" data-bs-toggle="tab"
                                        data-bs-target="#detail-lichsu-chi-tiet" type="button" role="tab"
                                        aria-controls="detail-lichsu-chi-tiet" aria-selected="false"
                                        onclick="getNhatKyThaoTac('NhatKy_XemChiTiet','bang_diems', selectedId);">
                                        <i class="fa fa-clock-o"></i>&nbsp;Lịch sử thao tác
                                    </button>
                                </li>
                            </ul>

                            <!-- 2) Tab panes -->
                            <div class="tab-content mt-3 flex-fill" id="detailTabsContentChiTiet" style="overflow:auto;">
                                <!-- Tab 1: Grid -->
                                <div class="tab-pane fade show active" id="detail-ds-chi-tiet" role="tabpanel"
                                    aria-labelledby="detail-ds-tab-chi-tiet">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div class="input-icon" style="max-width:340px;">
                                            <input type="text" class="form-control" placeholder="Nội dung tìm kiếm…"
                                                id="searchContentDetail" autocomplete="off">
                                            <span class="input-icon-addon"><i class="fa fa-search"></i></span>
                                        </div>
                                    </div>
                                    <div id="tabGridHocSinhChiTiet" class="mb-3" style="height:60vh;"></div>
                                </div>

                                <!-- Tab 2: History -->
                                <div class="tab-pane fade" id="detail-lichsu-chi-tiet" role="tabpanel"
                                    aria-labelledby="detail-lichsu-tab-chi-tiet">
                                    <div class="p-3 ">
                                        <div id="NhatKy_XemChiTiet" style="overflow-y:scroll">
                                            @include('partials.nhatkythaotac', ['containerId' => 'NhatKy_XemChiTiet'])
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal them moi hoc sinh -->
        <div class="modal fade" id="mdThemMoiHocSinh" tabindex="-1" aria-modal="true" role="dialog"
            data-bs-backdrop="static">
            <div class="modal-dialog modal-md modal-dialog-centered" role="document">
                <div class="modal-content p-0 rounded-3 overflow-hidden">
                    <!-- Header -->
                    <div class="modal-header py-2 px-3" style="background:#fd7e14; color:#fff;">
                        <h5 class="modal-title fw-bold text-uppercase" style="font-size:1.15rem;">Thêm mới học sinh/sinh
                            viên
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                    </div>
                    <!-- Body -->
                    <div class="modal-body pb-1 pt-3 px-3">
                        <!-- Học sinh info -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label id="labelNguoiTiepNhan" for="hocSinhID" class="form-label">
                                    Học sinh/sinh viên<span class="text-danger">(*)</span>
                                </label>
                                <div class="position-relative">
                                    <!-- Custom Select -->
                                    <div class="d-flex align-items-center border rounded p-1"
                                        style="background-color: #eee; cursor: pointer;" id="customSelect">
                                        <div class="flex-grow-1" id="selectedOption">
                                            Chọn học sinh
                                        </div>
                                        <a href="#" class="text-nts-primary text-decoration-none me-2">Xem chi tiết</a>
                                        <button class="btn btn-sm text-white"
                                            style="border-radius: 5px; background-color: var(--main-color)"
                                            id="btnDropdownHS">
                                            <i class="fas fa-ellipsis-h"></i>
                                        </button>
                                    </div>

                                    <!-- Dropdown Options -->
                                    <ul class="list-group position-absolute w-100 d-none" id="customDropdown"
                                        style="z-index: 2000; top: 100%; left: 0; background-color: white; border: 1px solid #ced4da; border-radius: 5px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); max-height: 150px; overflow-y: auto;">
                                    </ul>

                                </div>
                                <!-- Hidden native select -->
                                <select id="hocSinhID" class="d-none">

                                </select>
                            </div>
                        </div>


                        <!-- Trường -->
                        <div class="mb-2">
                            <label class="form-label mb-1 text-dark" style="">Trường</label>
                            <input id="tenTruong" type="text" class="form-control" required disabled
                                value="Trung học phổ thông NTSOFT" />
                        </div>

                        <!-- 3 columns: Rèn luyện, Học tập, Tốt nghiệp, Ưu tiên -->
                        <div class="row g-2">
                            <div class="col-md-6">
                                <label class="form-label mb-1" for="ketQuaRenLuyen" style="">Kết quả rèn
                                    luyện</label>
                                <select class="form-select" id="ketQuaRenLuyen">

                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label mb-1" for="ketQuaHocTap" style="">Kết quả học
                                    tập</label>
                                <select class="form-select" id="ketQuaHocTap">

                                </select>
                            </div>

                        </div>
                        <div class="row g-2 mt-2">
                            <div class="col-md-6">
                                <label class="form-label mb-1" for="chkUuTien" style="">Thuộc diện ưu
                                    tiên</label>
                                <select class="form-select" id="chkUuTien">

                                </select>
                            </div>

                            <div class="col-md-6">
                                <label class="form-label mb-1" for="ketQuaTotNghiep" style="">Kết quả tốt
                                    nghiệp <span class="text-danger">*</span></label>
                                <select class="form-select" id="ketQuaTotNghiep">

                                </select>
                            </div>
                        </div>
                        <!-- Ghi chú -->
                        <div class="mb-2 mt-2">
                            <label class="form-label fw-bold" for="ghiChu" style="">Ghi chú</label>
                            <textarea class="form-control" id="ghiChu" rows="4"></textarea>
                        </div>
                    </div>
                    <!-- Footer -->
                    <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                        <div>
                            <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                                <i class="fa fa-times"></i>&nbsp;Đóng (F4)
                            </button>
                            <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnLuuVaDong">
                                <i class="fa fa-save"></i>&nbsp;Lưu và đóng (F9)
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @include('partials.modalChonHocSinh', [
            'tenChucNang' => 'bangdiemhocsinh',
        ])


        <div class="modal fade" id="mdBangDiemHocSinh" tabindex="-1" aria-modal="true" role="dialog"
            data-bs-backdrop="static">
            <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
                <div class="modal-content p-0 rounded-3 overflow-hidden">
                    <!-- Modal Header -->
                    <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                        <h5 class="modal-title fw-bold text-uppercase" id="tieuDe_mdBangDiemHocSinh">Ban hành quyết định
                            công
                            nhận tốt nghiệp
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                    </div>
                    <!-- Modal Body -->
                    <div class="modal-body px-4 py-3">
                        <div class="row">
                            <!-- Illustration -->
                            <div class="col-md-5 d-flex align-items-center justify-content-center mb-2 mb-md-0">
                                <img src="/img/Logo-note2.jpeg" alt="Quyết định" class="img-fluid" style="max-width:350px;">
                            </div>
                            <!-- Right Side: Content -->
                            <div class="col-md-7">
                                <!-- Alert/Instruction -->
                                <div class="border border-warning rounded mb-3 px-3 py-2"
                                    style="background:#fff7e6;font-size:15px;">
                                    <span id="alertMessage">
                                        Bạn đang thực hiện ban hành quyết định công nhận tốt nghiệp tại quyết định số:
                                        <b>01/2024/QĐ-SGD&ĐT</b> ngày ký <b>25/12/2024</b> của <b>Sở giáo dục và đào tạo
                                            Tỉnh
                                            NTSOFT</b> về việc công nhận học sinh sinh viên tốt nghiệp THPT năm học 2024.
                                        Vui
                                        lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>“Ban hành”</b> để thực
                                        hiện
                                        thao tác ban hành cho quyết định.
                                    </span>
                                </div>
                                <!-- Fieldset -->
                                <fieldset class="KhungVien border rounded-3 p-3 mb-1">
                                    <legend id="fieldsetLegend">
                                        Thông tin ban hành
                                    </legend>
                                    <form>
                                        <div class="row g-2">
                                            <div class="col-12 col-sm-6">
                                                <label class="form-label  mb-1" for="ngayBanHanh" style="">Ngày
                                                    ban hành <span class="text-danger">(*)</span></label>
                                                <div class="input-group input-group-sm">
                                                    <input type="text" class="form-control date-picker" id="ngayBanHanh"
                                                        name="NgayKy" autocomplete="off" data-date-format="dd/mm/yyyy"
                                                        placeholder="dd/MM/yyyy" required>

                                                </div>
                                            </div>
                                            <div class="col-12 col-sm-6"></div>
                                            <div class="col-12 col-sm-6">
                                                <label class="form-label  mb-1" for="nguoiBanHanh" style="">Người
                                                    ban hành</label>
                                                <select class="form-select" id="nguoiBanHanh">
                                                    <option>-Chọn-</option>
                                                </select>
                                            </div>
                                            <div class="col-12 col-sm-6">
                                                <label class="form-label  mb-1" for="chucVuBanHanh" style="">Chức
                                                    vụ</label>
                                                <select class="form-select" id="chucVuBanHanh">
                                                    <option>-Chọn-</option>
                                                </select>
                                            </div>
                                            <div class="col-12">
                                                <label class="form-label mb-1" for="noiDungBanHanh" style="">Nội
                                                    dung ban hành</label>
                                                <div id="noiDungBanHanh" class="form-control" contenteditable="true"
                                                    style=" min-height: 120px;  text-align: justify;"></div>
                                            </div>
                                        </div>
                                    </form>
                                </fieldset>
                            </div>
                        </div>
                    </div>
                    <!-- Modal Footer -->
                    <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                        <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                            <i class="fa fa-times"></i>&nbsp;Đóng (F4)
                        </button>
                        <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnBanHanh">
                            <i class="fa fa-check"></i> Ban hành (F9)
                        </button>
                    </div>
                </div>
            </div>
        </div>

        @include('partials.viewHocSinh')
        @include('partials.mdXemDinhKem')

        @include('partials.nhapExcelModal', [
            'id' => 'mdNhanExcel',
            'title' => 'Nhận excel bảng điểm học sinh',
        ])

        @include('partials.nhapExcelModal', [
            'id' => 'mdNhanExcel',
            'title' => 'Nhận excel học sinh tốt nghiệp',
        ])

        <div id="mdTrichXuatOCR" class="full-screen-panel" style="display:none;">
            <div class="panel-content" style="background-color:#DADFE5 !important;">
                <div class="modal-dialog modal-content d-flex flex-column" style="border-radius:0; height:100vh;">
                    <!-- Header -->
                    <div class="modal-header modal-title font-weight-bold"
                        style="border-radius:0 !important; color:gray !important; text-transform:uppercase">
                        <h5 class="modal-title text-nts-success fw-bold">TRÍCH XUẤT BẢNG ĐIỂM TỰ ĐỘNG (OCR)</h5>
                        <button type="button" class="btn-close my-0 mx-3 d-flex align-items-center justify-content-center"
                            onclick="$('#mdTrichXuatOCR').hide();" aria-label="Close"
                            style="width:32px; height:32px; padding:0; font-size:1.2rem; color:gray !important; font-weight:bold;">
                            <i class="fa fa-times" aria-hidden="true"></i>
                        </button>
                    </div>

                    <!-- Body (re-used step-2 style) -->
                    <div class="modal-body flex-fill px-2" style="background-color:inherit !important;">
                        <div id="bodyOCR" class="border border-primary p-2 flex-grow-1"
                            style="background-color:#fff !important; height:100%; display:flex; flex-direction:column;">
                            <div id="fieldSelectorContainer" style="display: none;z-index: 9999;min-width:200px;">
                                <select class="form-control" id="fieldSelector" style="width:200px">
                                    <option></option> <!-- placeholder -->
                                </select>
                            </div>
                            <div class="row gx-3 h-100">

                                <!-- Left column: Uploader + Image Preview -->
                                <div class="col-12 col-md-6 d-flex flex-column" id="col-1-ocr">
                                    <fieldset class=" KhungVien pb-3">
                                        <legend>Chọn hình ảnh</legend>
                                        <div class="row">
                                            <div class="col-12 mb-3 d-flex align-items-center">
                                                <div class="me-3">
                                                    @include('partials.dinh-kem-multiple', [
                                                        'id' => 'dinhKemOCR',
                                                        'ChapNhanFile' => ['jpg', 'jpeg', 'png'],
                                                        'mode' => 'multi',
                                                        'showPreview' => true
                                                    ])
                                                </div>

                                                <!-- Read‑mode: line vs paragraph -->
                                                <div class="d-flex flex-column d-none">
                                                    <span class="me-2 fw-bold">Chế độ đọc:</span>
                                                    <div class="btn-group btn-group-lg" role="group"
                                                        aria-label="Chế độ đọc">
                                                        <button id="btnReadLine" type="button"
                                                            class="btn btn-outline-primary" title="Đọc từng dòng">
                                                            <i class="fa fa-align-left"></i>&ensp;Dòng
                                                        </button>
                                                        <button id="btnReadParagraph" type="button"
                                                            class="btn btn-outline-primary" title="Đọc theo đoạn">
                                                            <i class="fa fa-align-justify"></i>&ensp;Đoạn
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-7">
                                                <div id="ocrToolbar" class="mb-3">
                                                    <div class="btn-group btn-group-lg" role="group"
                                                        aria-label="Chọn vùng đọc">
                                                        <button id="btnDraw" class="btn btn-outline-primary"
                                                            title="Vẽ hộp"><i class="fa fa-pencil"></i></button>
                                                        <button id="btnSelect" class="btn btn-outline-secondary"
                                                            title="Chọn/Di chuyển hộp"><i
                                                                class="fa fa-mouse-pointer"></i></button>
                                                        <button id="btnCopy" class="btn btn-outline-info" title="Copy hộp"><i
                                                                class="fa fa-copy"></i></button>
                                                        <button id="btnPaste" class="btn btn-outline-success" title="Dán hộp"><i
                                                                class="fa fa-paste"></i></button>
                                                        <button id="btnDelete" class="btn btn-outline-danger" title="Xoá hộp"><i
                                                                class="fa fa-minus-circle"></i></button>
                                                        <button id="btnDeleteAll" class="btn btn-outline-danger"
                                                            title="Xoá tất cả hộp"><i class="fa fa-trash"></i></button>

                                                        <button id="btnZoomIn"  class="btn btn-outline-secondary" title="Phóng to hình"><i class="fa fa-search-plus"></i></button>
                                                        <button id="btnZoomOut" class="btn btn-outline-secondary" title="Thu nhỏ hình"><i class="fa fa-search-minus"></i></button>


                                                        <button type="button" id="btnTrichXuatOCR"
                                                            class="btn nts-color-luu d-flex align-items-center justify-content-start me-3"
                                                            style="min-width:110px;">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                                viewBox="0 0 20 20" fill="none">
                                                                <path
                                                                    d="M3 1C2.46957 1 1.96086 1.21071 1.58579 1.58579C1.21071 1.96086 1 2.46957 1 3V7.5H3V3H8V1H3ZM1 17V11.5H3V17H8V19H3C2.46957 19 1.96086 18.7893 1.58579 18.4142C1.21071 18.0391 1 17.5304 1 17ZM12 17V19H17C17.5304 19 18.0391 18.7893 18.4142 18.4142C18.7893 18.0391 19 17.5304 19 17V11.5H17V17H12ZM17 7.5H19V3C19 2.46957 18.7893 1.96086 18.4142 1.58579C18.0391 1.21071 17.5304 1 17 1H12V3H17V7.5Z"
                                                                    fill="#FFF" />
                                                                <path
                                                                    d="M6 5H14V6H6V5ZM5 8H15V9H5V8ZM6 11H14V12H6V11ZM5 14H15V15H5V14Z"
                                                                    fill="#FFF" />
                                                            </svg>
                                                            <span class="ms-2">Trích xuất dữ <br>liệu (OCR)</span>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-5 ml-2" id="imagePagination">
                                                <label for="pageSelect" class="form-label me-2 mb-0">
                                                    Chọn trang:
                                                </label>
                                                <select id="pageSelect" class="form-select w-75">
                                                    <option value="0">Trang 0</option>
                                                </select>
                                            </div>   
                                        </div>
                                    </fieldset>

                                    <!-- Placeholder for image preview -->
                                    <div id="ocrImagePreview" class="KhungVien flex-grow-1 mt-3 p-0 d-flex flex-column align-items-start justify-content-start"
                                        style="background-color:#f5f8fa;">
                                    </div>
                                </div>

                                <!-- Right column: Thông tin chung -->
                            <div class="col-12 col-md-6 d-flex flex-column">
                                    <fieldset class="KhungVien flex-grow-1 overflow-auto"
                                        style="background-color:#FFFBE7 !important;">
                                        <legend>Thông tin bảng điểm</legend>
                                        <div id="chiTietOCR" class="p-3">
                                            <div class="d-flex align-items-center mb-3">
                                                <h5 class="mb-0 me-3" style="font-size:13px">Thông tin chung</h5>
                                                <hr class="flex-grow-1 my-0"style="color:#4265B6" />
                                            </div>
                                            <form class="mt-2">
                                                <div class="row">
                                                    <div class="form-group col-6 col-lg-4">
                                                        <label for="SoQuyetDinh_OCR">Số văn bản</label>
                                                        <input type="text" class="form-control" id="SoQuyetDinh_OCR"
                                                            required />
                                                    </div>
                                                    <div class="form-group col-6 col-lg-4">
                                                        <label for="NgayKy_OCR">Ngày ký</label>
                                                        <input type="text" class="form-control date-picker" id="NgayKy_OCR"
                                                            placeholder="dd/mm/yyyy" />
                                                    </div>
                                                    <div class="form-group col-6 col-lg-4">
                                                        <label for="NguoiKy_OCR">Người ký</label>
                                                        <input type="text" class="form-control" id="NguoiKy_OCR" />
                                                    </div>
                                                    <div class="form-group col-6 col-lg-4">
                                                        <label for="ChucVu_OCR">Chức vụ</label>
                                                        <select class="form-control" id="ChucVu_OCR">
                                                        </select>
                                                    </div>
                                                    <div class="form-group col-6 col-lg-4">
                                                        <label for="KyThi_OCR">Kỳ thi </label>
                                                        <select class="form-control" id="KyThi_OCR" required readonly>
                                                        </select>
                                                    </div>
                                                    <div class="form-group col-6 col-lg-4">
                                                        <label for="HinhThuc">Hình thức đào tạo</label>
                                                        <select class="form-control" id="HinhThucDaoTao_OCR">
                                                        </select>
                                                    </div>
                                                    </div>
                                                    <div class="row mt-3">
                                                        <div class="form-group col-6 col-lg-4">
                                                            <label for="NamHoc_OCR">Năm</label>
                                                            <select class="form-control" id="NamHoc_OCR">
                                                            </select>
                                                        </div>
                                                        <div class="form-group col-6 col-lg-4">
                                                            <label for="HoiDong_OCR">Hội đồng</label>
                                                            <select class="form-control" id="HoiDong_OCR" required>
                                                            </select>
                                                        </div>
                                                        <div class="form-group col-12 col-lg-4">
                                                            <label for="CoQuanBanHanh_OCR">Cơ quan ban hành</label>
                                                            <input type="text" class="form-control" id="CoQuanBanHanh_OCR" />
                                                        </div>
                                                    </div>
                                                    <div class="row mt-3">
                                                        <div class="form-group col-12">
                                                            <label for="TrichYeu_OCR">Trích yếu</label>
                                                            <input type="text" class="form-control" id="TrichYeu_OCR" />
                                                        </div>
                                                    </div>
                                            </form>

                                               <div class="row mt-3">
                                                    

                                                    <div class="col-12 flex-grow-1 overflow-auto">
                                                        <div class="d-flex align-items-center mb-3">
                                                            <h5 class="mb-0 me-3" style="font-size:13px">Danh sách học sinh</h5>
                                                            <hr class="flex-grow-1 my-0" style="color:#4265B6"/>
                                                        </div>
                                                        <div class="col-12 mb-2 mt-2">
                                                        <div class="btn-group" role="group" aria-label="OCR row tools">
                                                        <button
                                                            id="btnMergeRows"
                                                            type="button"
                                                            class="btn btn-outline-primary btn-sm"
                                                            title="Hợp nhất cả hai dòng thành một, nối các ô không trống"
                                                        >
                                                            <i class="fa fa-compress"></i>&ensp;Hợp nhất dòng
                                                        </button>
                                                        <button
                                                            id="btnMergeMissingData"
                                                            type="button"
                                                            class="btn btn-outline-secondary btn-sm"
                                                            title="Điền vào các ô trống bằng giá trị tương ứng từ dòng kia"
                                                        >
                                                            <i class="fa fa-arrows-h"></i>&ensp;Điền dữ liệu thiếu
                                                        </button>
                                                        </div>
                                                    </div>
                                                        <div id="tabGridHocSinhChiTietOCR" class="w-100"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>

                                         <!-- Danh sách học sinh -->


                                    </div>
                                </div>
                                <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                                    <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2"
                                        data-bs-dismiss="modal">
                                        <i class="fa fa-times"></i>&nbsp;Đóng (F4)
                                    </button>
                                    <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnLuuVaDongOCR">
                                        <i clas s="fa fa-save"></i>&nbsp;Lưu và đóng (F9)
                                    </button>
                                </div>
                            </div>

                        </div>
                    </div>


                 </div>
            </div>
        </div>

@endsection
@push('styles')
<style>
    .hr, hr{
        opacity: 1 !important;
    }

    #mdBangDiemHocSinh .modal-dialog{
        max-width: 900px;
    }
</style>
@endpush

@push('scripts')
    <script>
    window.Laravel = window.Laravel || {};
    window.Laravel = {
        ...window.Laravel,
        // other URLs you may already have...
        maTuTangUrl: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'SoQD_HoSoTotNghiep', 'bangDuLieu' => 'quyet_dinhs', 'cotDuLieu' => 'SoBangDiem']) }}`,
        getListToChuc: `{{{ route('dungchung.danhmuc.comboCapHoc') }}}`,
        getListKyThi: `{{{ route('dungchung.danhmuc.comboKyThi') }}}`,
        getListDonVi: `{{{ route('dungchung.danhmuc.comboDonVi') }}}`,
        getListHTDT: `{{{ route('dungchung.danhmuc.comboHTDT') }}}`,
        getListHoiDong: `{{{ route('dungchung.danhmuc.comboHoiDong') }}}`,
        getListChucVu: "{{ route('dungchung.danhmuc.comboChucVu') }}",
        getListHocSinh: "{{ route('dungchung.danhmuc.comboHocSinh') }}",
        getListNhanvien: "{{ route('dungchung.danhmuc.comboNhanVien') }}",
        getListXepLoai: "{{ route('dungchung.danhmuc.comboXepLoai') }}",
        getListDienUuTien: "{{ route('dungchung.danhmuc.comboDienUuTien') }}",
        getListNamHoc: "{{ route('dungchung.danhmuc.comboNamHoc') }}",
        linkAnhHoSo: "{{asset('img/anhhoso.png')}}",
        //CRUD
        luuThongTin: "{{ route('bangdiemhocsinh.luuthongtin') }}",
        luuThongTinOCR: "{{ route('bangdiemhocsinh.luuThongTinOCR') }}",
        luuThongTinHocSinh: "{{ route('bangdiemhocsinh.luuthongtinHocSinhTN')}}",
        luuThongTinBanHanh: "{{ route('bangdiemhocsinh.luuthongtinBanHanh', ['bangdiemid' => 'BangDiemID']) }}",
        luuThongTinThuHoi: "{{ route('bangdiemhocsinh.luuthongtinThuHoi', ['bangdiemid' => 'BangDiemID']) }}",

        getListQD: "{{ route('bangdiemhocsinh.getall') }}",
        getListHSByQD: function(BangDiemId) {
            return "{{ route('bangdiemhocsinh.listHocSinhByBangDiem', ['bangdiemid' => 'BangDiemID']) }}"
                .replace('BangDiemID', BangDiemId);
        },
        loadDuLieuSua: "{{ route('bangdiemhocsinh.loaddulieusua') }}",
        xoa: "{{ route('bangdiemhocsinh.xoa') }}",
        getListTruongHoc: "{{ route('quanly.dungchung.getDanhSachTruongHocDemSoLuong') }}",
        getTruongDuLieu: "{{ route('quanly.dungchung.getTruongDuLieuListTheoModel') }}",

        getListHS: function(donviId_Truong) {
            return "{{ route('quanly.dungchung.getHSByTruongHocDemSoLuong', ['donviId_TruongHoc' => 'DONVI_ID_PLACEHOLDER']) }}"
                .replace('DONVI_ID_PLACEHOLDER', donviId_Truong);
        },
        getHSByID: "{{ route('bangdiemhocsinh.getHocSinhTNById', ['id' => 'ID']) }}",
        xoaHSTN: "{{ route('bangdiemhocsinh.xoaHocSinhTN')}}",
        getMauExcel: "{{ route('bangdiemhocsinh.getMauExcel')}}",
        uploadDocUrl: "{{ route('api.files.upload') }}",
        loadTenSheetUrl: "{{ route('bangdiemhocsinh.loadSheetNames') }}",
        checkExcel: "{{ route('bangdiemhocsinh.checkExcel') }}",
        loadExcel: "{{ route('bangdiemhocsinh.loadExcel') }}",
        importExcel: "{{ route('bangdiemhocsinh.importExcel') }}",

        getSvg: "{{ route('quanly.dungchung.bangDiemMainGridSvg') }}",
        icons: "{{ asset('js/dungchung/svg-icons.js')}}",

        getMonThi: "{{ route('bangdiemhocsinh.listMonThiByBangDiem') }}",
        getMonThiFromKyThi: "{{ route('bangdiemhocsinh.listMonThiByKyThi') }}",
        scanOCR: "{{ route('quanly.dungchung.ocrInternal') }}",
    };
    $(document).on("click", ".btnTaiFileMauNhanExcel", function() {
        // simply open the template URL in a new tab to trigger download
        window.open(Laravel.getMauExcel + '?bangDiemId=' + selectedId, '_blank');
    });
    </script>
    <script src="{{ asset('js/dungchung/svg-icons.js') }}?v={{ time() }}">
    </script>
    <script src="{{ asset('js/dungchung/tabulator-configs.js') }}?v={{ time() }}">
    </script>
    <script src="{{ asset('js/quanly/bangdiemhocsinh/BangDiemHocSinh.js') }}?v={{ time() }}">
    </script>
    <script src="{{ asset('js/quanly/bangdiemhocsinh/banhanh.js') }}?v={{ time() }}">
    </script>
    <script src="{{ asset('js/quanly/bangdiemhocsinh/nhanexcel.js') }}?v={{ time() }}">
    </script>
    <script src="{{ asset('js/quanly/bangdiemhocsinh/trichxuatocr.js') }}?v={{ time() }}">
    </script>
@endpush
