@extends('layouts.layouts')

@section('content')
    <style id="LuoiDScss">
        #GridMainDS .avatar {
            width: 60px !important;
            height: 60px !important;
            font-size: 25px !important;
            border-radius: 50%;
        }

        #GridMainDS .tabulator-tableholder .tabulator-table .tabulator-row .tabulator-cell {
            height: auto !important;
        }

        #GridMainDS .tabulator-row {
            border-bottom: 0px !important;
        }

        .row-girdmain {
            margin-bottom: 0px !important;
        }

        .tabulator-cell .dropdown-menu-end {
            position: fixed !important;
        }

        #GridMainDS .alert {
            --tblr-alert-padding-x: 1rem;
            --tblr-alert-padding-y: 0.3rem;
            --tblr-alert-margin-bottom: 0rem;
        }
    </style>
    <style type="text/css">
        .line {
            flex-grow: 2;
            border-bottom: 1px solid #dadcde;
            margin: 0 10px;
            border-color: #07a607;
        }

        .label-text {
            color: #07a607;
        }

        .form-check .form-check-input {
            margin-left: unset !important;
        }

        .steps-vertical .step-item:before {
            top: var(--tblr-steps-dot-offset);
            left: -8px !important;
            transform: translate(0, 0);
            width: 35px;
            height: 35px;
        }

        #gridDonVi .tabulator-footer {
            display: none !important;
        }

        #gridDonVi .tabulator-data-tree-control {
            display: none !important;
        }

        .modal {
            z-index: 1202 !important;
        }

        .card {
            margin-bottom: 4px !important;
        }

        /* Child modals higher z-index */
        #mdThemMoiHocSinh.modal,
        #mdThemMoiHocSinh.modal .modal-dialog,
        #mdChonHocSinh.modal,
        #mdChonHocSinh.modal .modal-dialog {
            z-index: 1500 !important;
        }

        .modal-backdrop {
            z-index: 1201 !important;
        }

        .full-screen-panel {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.5);
            /* dark semi-transparent overlay */
            z-index: 1200;
            /* above other content */
            overflow-y: auto;
            /* scroll if content too tall */
            display: none;
            /* hidden by default */
        }

        .panel-content {
            background: white;
            border-radius: 0;
            padding-inline: 1rem;
            height: auto;
            overflow-y: auto;
        }

        #customSelect {
            position: relative;
            /* Make sure parent is positioned */
        }

        #customDropdown.show {
            display: block;
        }

        #mdXemThongTin .modal-dialog {
            min-width: 80% !important;
        }
        #mdBanHanhQuyetDinhHuyVanBang .modal-dialog {
            max-width: 900px;
        }
        .card-title
        {
            margin-bottom: 0px;
        }
        .card-title_tongquan{
            margin: 6px 0;
        }
        .hr_trenluoi{
            border: 1px solid var(--nts-bg-secondary);
            margin: 2px 0;
            opacity: 0.6 !important;
        }
        #btnChonTepVB{
            background-color: var(--nts-bg-white);
            color: #fff;
            padding: 8px 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            border: 1px solid #0F91D2;
            border-radius: 4px;
            color: #0F91D2;
        }

         #btnChonTepVB:hover{
            background: #0F91D2;
            color: #ffffff;

         }

        #drop-area{
            border: 1px dashed #9999;
            border-radius: 10px;
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        #mdThemMoi .modal-dialog{
            max-width: 60%;
        }

        span.select2-dropdown.select2-dropdown--above,
        span.select2-dropdown.select2-dropdown--below {
            min-width: 400px;
        }
    </style>
    <div id="DivMain">
        <div id="DivTimKiem" class="mb-2">
            <div class="row flex-row-reverse">
                <div class="col-md-8">
                    <div class="text-end">
                        {{-- <div class="btn-group" style="height:40px">
                                <a id="btnAnHienTQ" style="margin-top:4px;padding:4px;cursor:pointer; color:#41d81e;" class=""><i id="iconAnHien" class="fa fa-eye-slash" aria-hidden="true"></i><span id="textAnHien"> Ẩn trang tổng quan</span></a>
                            </div> --}}
                        <button style="" class="btn position-relative" id="">
                            <select class="form-control" id="CbSapXep" tabindex="0"
                                style="padding: 0.05rem 0.55rem !important;">
                                <option value="NgayKy">Ngày quyết định</option>
                            </select>&ensp;&ensp;
                            <span id="BtnSapXepTangGiam">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="icon icon-tabler icon-tabler-sort-ascending-letters" width="24"
                                    height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                    <path d="M15 10v-5c0 -1.38 .62 -2 2 -2s2 .62 2 2v5m0 -3h-4" />
                                    <path d="M19 21h-4l4 -7h-4" />
                                    <path d="M4 15l3 3l3 -3" />
                                    <path d="M7 6v12" />
                                </svg>
                            </span>
                        </button>
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check height-button-icon" name="btn-layout" id="btn-layout-1"
                                autocomplete="off" checked="">
                            <label style="margin-bottom: 0px" for="btn-layout-1" class="btn btn-icon">
                                <svg class="icon icon-tabler icon-tabler-list" width="20" height="20"
                                    viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M9 6l11 0"></path>
                                    <path d="M9 12l11 0"></path>
                                    <path d="M9 18l11 0"></path>
                                    <path d="M5 6l0 .01"></path>
                                    <path d="M5 12l0 .01"></path>
                                    <path d="M5 18l0 .01"></path>
                                </svg>
                            </label>
                            <input type="radio" class="btn-check height-button-icon" name="btn-layout" id="btn-layout-2"
                                autocomplete="off">
                            <label style="margin-bottom: 0px" for="btn-layout-2" class="btn btn-icon">
                                <svg class="icon icon-tabler icon-tabler-list-details" width="20" height="20"
                                    viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M13 5h8"></path>
                                    <path d="M13 9h5"></path>
                                    <path d="M13 15h8"></path>
                                    <path d="M13 19h5"></path>
                                    <path d="M3 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z">
                                    </path>
                                    <path d="M3 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z">
                                    </path>
                                </svg>
                            </label>
                        </div>
                        {{-- <button type="button" class="nts-color-them btn btn-primary" id="grbtnThemMoi" style="margin-right: -4px;border-bottom-right-radius: 0px;    border-top-right-radius: 0px;">
                            <i class="fa fa-plus"></i>&ensp;Thêm mới(F2)
                        </button> --}}

                        <div class="dropdown btn-group" style="">
                            <button class="btn nts-color-them btn-white dropdown-toggle-hide-arrow" type="button"  id="" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="border-bottom-right-radius: 4px;border-top-right-radius: 4px;">
                                <i class="fa fa-plus"></i>&ensp;Thêm mới(F2)<div style="border-right: 1px solid #fff; height:150%; margin: 0 6px;"></div><i class="fa fa-angle-down" aria-hidden="true"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-end w-auto" style="font-weight:400; min-width: 216px;">
                                <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" id="btnThemMoiTheoDYC">
                                    <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <g clip-path="url(#clip0_18443_11212)">
                                        <path d="M3.23077 0C1.45385 0 0 1.45385 0 3.23077V16.9615C0 18.7385 1.45385 20.1923 3.23077 20.1923H12.1154C12.4385 20.1923 12.6808 20.1164 12.9231 20.1164C11.7923 19.8741 10.8279 19.3038 10.0202 18.5769H3.23077C2.34231 18.5769 1.61538 17.85 1.61538 16.9615V3.23077C1.61538 2.34231 2.34231 1.61538 3.23077 1.61538H8.32973C8.89511 1.77692 8.88461 2.50385 8.88461 3.23077V5.65385C8.88461 6.13846 9.20769 6.46154 9.69231 6.46154H12.1154C12.9231 6.46154 13.7308 6.46154 13.7308 7.26923V8.07692H14.1346C14.5385 8.07692 14.9423 8.148 15.3462 8.22877V6.46154C15.3462 5.57308 14.5482 4.75569 13.1751 3.38181C12.9328 3.22027 12.7615 2.98846 12.5192 2.82692C12.3577 2.58462 12.1251 2.41258 11.9635 2.17027C10.5913 0.798 9.77308 0 8.88461 0H3.23077ZM14.1346 9.69231C11.7115 9.69231 9.69231 11.7115 9.69231 14.1346C9.69231 16.5577 11.7115 18.5769 14.1346 18.5769C15.1628 18.5769 16.1013 18.1965 16.8606 17.5923L17.1134 17.8452C17.0123 17.995 16.967 18.1755 16.9855 18.3553C17.0039 18.5351 17.0849 18.7028 17.2143 18.8289L19.2336 20.8482C19.5567 21.1712 20.0211 21.1712 20.3442 20.8482L20.748 20.4443C21.0711 20.1212 21.0711 19.6318 20.748 19.3087L18.7288 17.2895C18.6104 17.1656 18.4534 17.0856 18.2836 17.0627C18.1138 17.0398 17.9412 17.0753 17.7943 17.1635L17.5673 16.9107C18.1917 16.145 18.5769 15.1822 18.5769 14.1346C18.5769 11.7115 16.5577 9.69231 14.1346 9.69231ZM14.1346 10.9038C15.9115 10.9038 17.3654 12.3577 17.3654 14.1346C17.3654 15.9115 15.9115 17.3654 14.1346 17.3654C12.3577 17.3654 10.9038 15.9115 10.9038 14.1346C10.9038 12.3577 12.3577 10.9038 14.1346 10.9038Z" fill="#2B7DBC"/>
                                        </g>
                                        <defs>
                                        <clipPath id="clip0_18443_11212">
                                        <rect width="21" height="21" fill="white"/>
                                        </clipPath>
                                        </defs>
                                    </svg>&ensp;Thêm theo đơn yêu cầu
                                </a>
                                <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" id="btnThemMoi">
                                    <svg width="20" height="17" viewBox="0 0 20 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M13.9792 2.75972L17.1111 5.75523C17.2431 5.88142 17.2431 6.08732 17.1111 6.21352L9.52778 13.4665L6.30556 13.8086C5.875 13.855 5.51042 13.5063 5.55903 13.0945L5.91667 10.0127L13.5 2.75972C13.6319 2.63352 13.8472 2.63352 13.9792 2.75972ZM19.6042 1.99922L17.9097 0.37859C17.3819 -0.126197 16.5243 -0.126197 15.9931 0.37859L14.7639 1.55421C14.6319 1.68041 14.6319 1.88631 14.7639 2.0125L17.8958 5.00801C18.0278 5.13421 18.2431 5.13421 18.375 5.00801L19.6042 3.83239C20.1319 3.32428 20.1319 2.504 19.6042 1.99922ZM13.3333 11.4938V14.8746H2.22222V4.24751H10.2014C10.3125 4.24751 10.4167 4.20434 10.4965 4.13128L11.8854 2.80289C12.1493 2.5505 11.9618 2.12209 11.5903 2.12209H1.66667C0.746528 2.12209 0 2.8361 0 3.71616V15.4059C0 16.286 0.746528 17 1.66667 17H13.8889C14.809 17 15.5556 16.286 15.5556 15.4059V10.1655C15.5556 9.81012 15.1076 9.63411 14.8438 9.88318L13.4549 11.2116C13.3785 11.2879 13.3333 11.3876 13.3333 11.4938Z" fill="#F76707"/>
                                    </svg>&ensp;Thêm không theo đơn yêu cầu
                                </a>

                            </div>
                        </div>
                        {{-- <div class="btn-group">
                        </div> --}}
                    </div>
                </div>
                <div class="col-md-4">
                    <div style="display: flex; align-items: center" class="DivTimKiem">
                        <div class="input-icon">
                            <input type="text" value="" class="form-control nts-border-secondary" placeholder="Nội dung tìm kiếm"
                                id="SearchKey" autocomplete="off"
                                style="border-top-right-radius: 0px; border-bottom-right-radius: 0px; ">
                            <span class="input-icon-addon nts-secondary">
                                <i class="fa fa-search"></i>
                            </span>
                        </div>
                        <button id="TimKiemNangCao" class="btn btn-primary height-button-icon" type="button"
                            style="border-top-left-radius: 0px;border-bottom-left-radius:0px;">
                            <span class="fa fa-sliders" aria-hidden="true"> </span>
                        </button>
                    </div>
                    <div id="KhungTimKiem" class="bg-white mt-1 px-4 py-2 border rounded shadow position-absolute"
                        style="z-index: 1; width:380px ; display:none">
                        <div class="row">
                            <div class="col text-primary fw-bold nts-secondary">
                                <i class="fa fa-search"></i> Tìm kiếm nâng cao
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="TuNgay_Loc">Từ ngày</label>
                                <input type="text" class="form-control date-picker" id="TuNgay_Loc"
                                    autocomplete="off" data-date-format="dd/mm/yyyy">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="DenNgay_Loc">Đến ngày</label>
                                <input type="text" class="form-control date-picker" id="DenNgay_Loc"
                                    autocomplete="off" data-date-format="dd/mm/yyyy">
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label" for="LoaiVBCCID_Loc">Loại văn bằng, chứng chỉ</label>
                                <select class="form-control input-sm" id="LoaiVBCCID_Loc" tabindex="0">
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label" for="TrangThaiID_Loc">Trạng thái</label>
                                <select class="form-control input-sm" id="TrangThaiID_Loc" tabindex="0">
                                </select>
                            </div>
                        </div>
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group" style="text-align:right">
                                    <button type="button" id="DongTimKiem" class="btn btn-outline-danger nts-color-dong"><i
                                            class="fa fa-times"></i>&ensp;Đóng</button>&ensp;
                                    <button type="button" id="TimKiem" class="btn btn-success ms-auto nts-color-timkiem"><i
                                            class="fa fa-search"></i>&ensp;Tìm kiếm</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="DivDanhSach">
            <div class="row">
                <div class="col-md-12">
                    <div id="GridMainDS">
                    </div>
                </div>
            </div>
        </div>
        <div id="DivLuoi" style="display:none">
            <div class="row">
                <div class="col-md-12">
                    <div id="GridMainLuoi" class="GridData">
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Modal thêm/sửa --}}
    <div class="modal modal-blur fade" id="mdThemMoi" tabindex="-1" aria-modal="true" role="dialog" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header nts-bg-header-primary">
                    <h5 class="modal-title" id="tieuDeModal">Thêm mới quyết định thu hồi/hủy bỏ văn bằng, chứng chỉ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <input type="hidden" id="QuyetDinhHuyVanBangID" />

                    <fieldset class="KhungVien mt-2">
                        <legend class="bg-nts-header">Thông tin quyết định</legend>

                        <div class="row">
                            <div class="col-lg-4 mb-3">
                                <label class="form-label" for="SoQuyetDinh">Số quyết định</label>
                                <input type="text" class="form-control" id="SoQuyetDinh" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-4 mb-3">
                                <label class="form-label" for="NgayKy">Ngày ký</label>
                                <input type="text" class="form-control date-picker" placeholder="dd/MM/yyyy" id="NgayKy" autocomplete="off" data-date-format="dd/mm/yyyy">
                            </div>
                            <div class="col-lg-4 mb-3">
                                <label class="form-label" for="NguoiKyID">Người ký</label>
                                <select class="form-select" id="NguoiKyID"></select>
                            </div>
                            <div class="col-lg-4 mb-3">
                                <label class="form-label" for="ChucVuID">Chức vụ</label>
                                <select class="form-select" id="ChucVuID"></select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-12 mb-3">
                                <label class="form-label" for="HocSinhID">Học sinh</label>
                                <div class="btn-group" style="width: 100%;">
                                    <input type="text" class="form-control " id="TenHocSinh" disabled>
                                    <input type="hidden" class="form-control" id="HocSinhID" required>
                                    <button type="button" class="btn nts-bg-secondary active" id="btnChonHocSinh">
                                        <i class="blue fa fa-ellipsis-h"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-4 mb-3">
                                <label class="form-label" for="SoHieu">Số hiệu</label>
                                <input type="text" class="form-control" id="SoHieu" required>
                            </div>
                            <div class="col-lg-4 mb-3">
                                <label class="form-label" for="NgayCap">Ngày cấp</label>
                                <input type="text" class="form-control date-picker" placeholder="dd/MM/yyyy" id="NgayCap" autocomplete="off" data-date-format="dd/mm/yyyy">
                            </div>
                            <div class="col-lg-4 mb-3">
                                <label class="form-label" for="SoVaoSo">Số vào sổ</label>
                                <input type="text" class="form-control" id="SoVaoSo">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-8 mb-3">
                                <label class="form-label" for="LyDoThuHoi">Lý do thu hồi/hủy bỏ</label>
                                <select class="form-select" id="LyDoThuHoi">
                                </select>
                            </div>
                            <div class="col-lg-4 mb-3">
                                <label class="form-label" for="HinhThucXuLy">Hình thức xử lý</label>
                                <select class="form-select" id="HinhThucXuLy">
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-12 mb-3">
                                <label class="form-label" for="CoQuanBanHanh">Cơ quan ban hành</label>
                                <input type="text" class="form-control" id="CoQuanBanHanh">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-12 mb-3">
                                <label class="form-label" for="TrichYeu">Trích yếu</label>
                                <input type="text" class="form-control" id="TrichYeu">
                            </div>
                        </div>

                        <!-- Đính kèm -->
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label" style="margin-bottom: 8px;">Đính kèm</label>
                                <div id="drop-area" class="drop-area">
                                    <div id="divButtonUpload_"
                                        style="display: flex; align-items: center; margin-right: 10px;">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="37" height="37" viewBox="0 0 37 37" fill="none">
                                            <g clip-path="url(#clip0_18641_4088)">
                                                <path d="M24.7918 24.599L18.6928 18.5L12.5938 24.599" stroke="black" stroke-opacity="0.4" stroke-width="1.52475" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M18.6934 18.5V32.2228" stroke="black" stroke-opacity="0.4" stroke-width="1.52475" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M31.4871 28.2434C32.9743 27.4327 34.1491 26.1498 34.8262 24.5972C35.5032 23.0446 35.6439 21.3107 35.2261 19.6693C34.8084 18.0278 33.8558 16.5722 32.5189 15.5323C31.182 14.4923 29.5368 13.9271 27.843 13.926H25.9218C25.4603 12.1409 24.6001 10.4836 23.4059 9.07882C22.2117 7.674 20.7145 6.55819 19.027 5.81526C17.3395 5.07234 15.5055 4.72163 13.663 4.78952C11.8204 4.85741 10.0172 5.34212 8.38894 6.20721C6.76067 7.0723 5.3497 8.29526 4.26211 9.78415C3.17452 11.273 2.43861 12.9891 2.10971 14.8034C1.7808 16.6176 1.86746 18.4828 2.36317 20.2587C2.85888 22.0346 3.75074 23.6751 4.9717 25.0567" stroke="black" stroke-opacity="0.4" stroke-width="1.52475" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M24.7918 24.599L18.6928 18.5L12.5938 24.599" stroke="black" stroke-opacity="0.4" stroke-width="1.52475" stroke-linecap="round" stroke-linejoin="round"/>
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_18641_4088">
                                                <rect width="36.5941" height="36.5941" fill="white" transform="translate(0.396484 0.203125)"/>
                                                </clipPath>
                                            </defs>
                                        </svg>
                                    </div>
                                    <div style="flex-grow: 1;">
                                        <p style="margin-bottom: 4px; font-size: 12px; color: #000000;">Chọn 1 file hoặc kéo thả vào đây
                                        </p>
                                        <p style="margin: 0; font-size: 12px; color: #999;">JPG, PNG or PDF, file size không lớn hơn
                                            10MB</p>
                                    </div>
                                    <button id="btnChonTepVB" type="button">
                                        <i class="fa fa-upload" style="margin-right: 5px;"></i>Chọn file
                                    </button>
                                </div>
                                <div id="list-file" style="margin-top:4px"></div>
                                <input type="hidden" id="txtDuongDanFileVB" value="">
                                <input type="file" id="fileVB" class="d-none" accept=".jpg,.png,.pdf" />
                            </div>
                        </div>
                    </fieldset>
                </div>

                <div class="modal-footer">
                    <div class="col-md-6"></div>
                    <div class="col-md-6" style="text-align: right;">
                        <a href="#" class="nts-color-dong btn btn-outline-danger" data-bs-dismiss="modal">
                            <i class="fa fa-close"></i>&nbsp;Đóng (F4)
                        </a>
                        <a href="#" id="btnLuuVaDong" class="btn btn-success ms-auto nts-color-luu">
                            <i class="fa fa-save"></i>&ensp;Lưu và đóng (F9)
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal modal-blur fade" id="mdThemMoiTheoDYC" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <input type="hidden" id="DonYeuCauHuyVBCCID" />
        <div class="modal-dialog modal-fullscreen modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header" style="background: #DADFE5;color: #667382;padding-top: 10px !important;">
                    <h5 class="modal-title" id="tieuDeModalDonYCHuyVBCC"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="color: #667382 !important;"></button>
                </div>
                <div class="modal-body" style="background: #DADFE5;padding-top: 0 !important;">
                    <fieldset class="KhungVien mt-2">
                        <legend class="bg-nts-header">Danh sách đơn yêu cầu thu hồi/hủy bỏ văn bằng, chứng chỉ</legend>
                        <div class="row">
                            <div class="col-md-12">
                                <div id="GridDSDonYeuCau" class="GridData">
                                </div>
                            </div>
                        </div>
                    </div>
                    </fieldset>
                </div>
            </div>
        </div>
    </div>
    {{-- End Modal thêm/sửa --}}

    @include('partials.modalChonHocSinh', [
            'tenChucNang' => 'quyetdinhhuyvanbang',
        ])

    {{-- Xem thông tin --}}
    <div class="modal modal-blur fade" id="mdXemThongTin"  tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tieuDeModalXemTTQD" style=" color: #ffffff; ">Xem chi tiết quyết định</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                <div class="col-sm-12">
                    <div class="card">
                        <div class="card-header" style=" margin-left: 11px; ">
                            {{-- <ul class="nav nav-tabs" id="detailTabsChiTiet" role="tablist" style=" margin-bottom: -17px; ">
                                <li class="nav-item" role="presentation">
                                    <button style=" color: #fd7e14; " class="nav-link active" id="detail-ds-tab-chi-tiet" data-bs-toggle="tab" data-bs-target="#detail-ds-chi-tiet" type="button" role="tab" aria-controls="detail-ds-chi-tiet" aria-selected="true">
                                        <i class="fa fa-users"></i>&nbsp;Thông tin quyết định
                                    </button>
                                </li>

                                <li class="nav-item" role="presentation" >
                                    <button style=" color: #fd7e14; "  class="nav-link" id="detail-lichsu-tab-chi-tiet" data-bs-toggle="tab" data-bs-target="#detail-lichsu-chi-tiet" type="button" role="tab" aria-controls="detail-lichsu-chi-tiet" aria-selected="false" onclick="getNhatKyThaoTac('NhatKy_XemChiTiet','quyet_dinh_huy_van_bangs', selectedId);" tabindex="-1">
                                        <i class="fa fa-clock-o"></i>&nbsp;Lịch sử thao tác
                                    </button>
                                </li>
                            </ul> --}}
                            <ul class="nav nav-tabs card-header-tabs" data-bs-toggle="tabs" role="tablist" style="z-index: 10;">
                            <li class="nav-item" role="presentation">
                                <a href="#detail-ds-chi-tiet" class="nav-link active" data-bs-toggle="tab" aria-selected="true" role="tab"><!-- Download SVG icon from http://tabler.io/icons/icon/home -->
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                        <path d="M14.1581 3.5256L8.15812 1.5256C8.05548 1.49138 7.94452 1.49138 7.84188 1.5256L1.84187 3.5256C1.74232 3.55879 1.65572 3.62246 1.59436 3.7076C1.53301 3.79274 1.49999 3.89503 1.5 3.99998V8.99998C1.5 9.13258 1.55268 9.25976 1.64645 9.35353C1.74021 9.4473 1.86739 9.49998 2 9.49998C2.13261 9.49998 2.25979 9.4473 2.35355 9.35353C2.44732 9.25976 2.5 9.13258 2.5 8.99998V4.69373L4.59937 5.3931C4.0416 6.29423 3.86423 7.37988 4.10621 8.41167C4.34819 9.44346 4.98974 10.337 5.89 10.8962C4.765 11.3375 3.7925 12.1356 3.08125 13.2269C3.04426 13.2818 3.01857 13.3436 3.00566 13.4086C2.99276 13.4736 2.9929 13.5405 3.00609 13.6054C3.01927 13.6704 3.04523 13.7321 3.08246 13.7869C3.11969 13.8417 3.16744 13.8886 3.22294 13.9248C3.27844 13.961 3.34058 13.9858 3.40576 13.9977C3.47093 14.0097 3.53783 14.0086 3.60257 13.9945C3.66732 13.9804 3.72861 13.9535 3.78289 13.9155C3.83717 13.8775 3.88335 13.8291 3.91875 13.7731C4.86063 12.3281 6.34812 11.5 8 11.5C9.65187 11.5 11.1394 12.3281 12.0813 13.7731C12.1546 13.8821 12.2679 13.9579 12.3966 13.984C12.5254 14.0101 12.6592 13.9845 12.7692 13.9128C12.8793 13.841 12.9566 13.7288 12.9846 13.6005C13.0126 13.4721 12.9889 13.3379 12.9187 13.2269C12.2075 12.1356 11.2312 11.3375 10.11 10.8962C11.0094 10.3371 11.6503 9.44406 11.8923 8.41303C12.1342 7.38199 11.9574 6.29709 11.4006 5.39623L14.1581 4.47748C14.2577 4.44431 14.3443 4.38064 14.4057 4.2955C14.4671 4.21036 14.5001 4.10806 14.5001 4.0031C14.5001 3.89814 14.4671 3.79584 14.4057 3.7107C14.3443 3.62556 14.2577 3.5619 14.1581 3.52873V3.5256ZM11 7.49998C11.0001 7.97426 10.8878 8.44182 10.6723 8.86429C10.4567 9.28676 10.1441 9.6521 9.75996 9.93034C9.37586 10.2086 8.93127 10.3918 8.46266 10.4649C7.99405 10.5381 7.51477 10.4991 7.06416 10.3511C6.61354 10.2032 6.20442 9.95049 5.87037 9.6138C5.53632 9.27712 5.28685 8.86603 5.14244 8.41427C4.99803 7.9625 4.96279 7.48293 5.03961 7.01491C5.11644 6.54689 5.30313 6.10375 5.58437 5.72185L7.84188 6.47185C7.94452 6.50607 8.05548 6.50607 8.15812 6.47185L10.4156 5.72185C10.7955 6.23687 11.0003 6.86004 11 7.49998Z" fill="#F76707"></path>
                                    </svg>&ensp;Thông tin quyết định</a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a href="#detail-lichsu-chi-tiet" onclick="getNhatKyThaoTac('NhatKy_XemChiTiet','quyet_dinh_huy_van_bangs', selectedId);" class="nav-link" data-bs-toggle="tab" aria-selected="false" tabindex="-1" role="tab"><!-- Download SVG icon from http://tabler.io/icons/icon/user -->
                                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                        <path d="M8.21083 13C9.34447 13 10.2635 12.081 10.2635 10.9473C10.2635 9.81371 9.34447 8.89471 8.21083 8.89471C7.0772 8.89471 6.1582 9.81371 6.1582 10.9473C6.1582 12.081 7.0772 13 8.21083 13Z" stroke="#F76707" stroke-width="1.02632"></path>
                                        <path d="M6.23612 10.92L5.52454 11.6316L4.51328 12.6011C4.2916 12.8139 4.18076 12.9199 4.14244 13.0102C4.09936 13.1063 4.09372 13.215 4.12663 13.315C4.15954 13.415 4.22863 13.4991 4.32033 13.5508C4.40381 13.598 4.55502 13.6124 4.85607 13.6425C5.02576 13.6589 5.11128 13.6671 5.18244 13.6924C5.25976 13.7193 5.33037 13.7625 5.38941 13.8192C5.44846 13.8759 5.49456 13.9447 5.52454 14.0208C5.55123 14.0892 5.56012 14.1707 5.57723 14.3342C5.60802 14.6229 5.62376 14.7673 5.67302 14.8474C5.78523 15.03 6.02265 15.1019 6.23681 15.0184C6.33055 14.9808 6.44139 14.8747 6.66307 14.6626L8.21144 13.1779L9.75981 14.6626C9.98149 14.8747 10.0923 14.9808 10.1861 15.0184C10.4002 15.1019 10.6377 15.03 10.7499 14.8474C10.7991 14.7673 10.8149 14.6229 10.8457 14.3342C10.8628 14.1707 10.8717 14.0892 10.8983 14.0208C10.9283 13.9447 10.9744 13.8759 11.0335 13.8192C11.0925 13.7625 11.1631 13.7193 11.2404 13.6924C11.3123 13.6671 11.3971 13.6589 11.5668 13.6425C11.8679 13.613 12.0191 13.598 12.1025 13.5508C12.1943 13.4991 12.2633 13.415 12.2963 13.315C12.3292 13.215 12.3235 13.1063 12.2804 13.0102C12.2421 12.9199 12.1313 12.8139 11.9096 12.6011L10.8977 11.6316L10.2641 10.9973" stroke="#F76707" stroke-width="1.02632"></path>
                                        <path d="M11.8512 12.313C13.2005 12.2987 13.9531 12.2138 14.4519 11.7144C15.0534 11.1136 15.0534 10.1455 15.0534 8.21051V5.47367C15.0534 3.53872 15.0534 2.57057 14.4519 1.96983C13.8512 1.36841 12.883 1.36841 10.9481 1.36841H5.4744C3.53946 1.36841 2.5713 1.36841 1.97056 1.96983C1.36914 2.57057 1.36914 3.53872 1.36914 5.47367V8.21051C1.36914 10.1455 1.36914 11.1136 1.97056 11.7144C2.49604 12.2405 3.30204 12.3062 4.79019 12.3144" stroke="#F76707" stroke-width="1.02632"></path>
                                        <path d="M6.15944 4.10522H10.2647M4.79102 6.49996H11.6331" stroke="#F76707" stroke-width="1.02632" stroke-linecap="round"></path>
                                    </svg>&ensp;Nhật ký thao tác</a>
                            </li>
                        </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content">
                                <div class="tab-pane active show px-3" id="detail-ds-chi-tiet" role="tabpanel" aria-labelledby="detail-ds-tab-chi-tiet">
                                    <div class="row">
                                        <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Số quyết định: <b id="txtSoQuyetDinh"></b></p>
                                        </div>
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Ngày ký: <b id="txtNgayKy"></b></p>
                                        </div>
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Người ký: <b id="txtNguoiKy"></b></p>
                                        </div>
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Chức vụ người ký: <b id="txtChuVuNguoiKy"></b></p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Cơ quan ban hành: <b id="txtCoQuanBanHanh"></b></p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Lý do hủy/thu hồi: <b id="txtLyDoHuy"></b></p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Trích yếu: <b id="txtTrichYeu"></b></p>
                                        </div>
                                    </div>
                                    <div class="row">
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Học sinh: <b id="txtHocSinh"></b></p>
                                        </div>
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Ngày sinh: <b id="txtNgaySinh"></b></p>
                                        </div>
                                        <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Trường: <b id="txtTruong"></b></p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Số hiệu cũ: <b id="txtSoHieuCu"></b></p>
                                        </div>
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Số vào sổ cũ: <b id="txtSoVaoSoCu"></b></p>
                                        </div>

                                    </div>
                                    <div class="row">
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Ngày ban hành: <b id="txtNgayBanHanh"></b></p>
                                        </div>
                                            <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Người ban hành: <b id="txtNguoiBanHanh"></b></p>
                                        </div> <div class="col-sm-3" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Chức vụ người ban hành: <b id="txtChucVuNguoiBanHanh"></b></p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12" style=" padding: 4px; ">
                                                <p class="fs-big my-1">Nội dung ban hành: <b id="txtNoiDungBanHanh"></b></p>
                                        </div>

                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12" id="fileAttachmentList" style=" padding: 4px; ">
                                        </div>
                                    </div>

                                </div>
                                    <div class="tab-pane fade" id="detail-lichsu-chi-tiet" role="tabpanel" aria-labelledby="detail-lichsu-tab-chi-tiet">
                                        <div class="p-3 ">
                                            <div id="NhatKy_XemChiTiet">
                                                @include('partials.nhatkythaotac', ['containerId' => 'NhatKy_XemChiTiet'])
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                </div>

            </div>
                </div>
            </div>

        </div>
    </div>

    {{-- Xem thông tin --}}
    <div class="modal modal-blur fade" id="mdXemThongTinNhatKy_DonYeuCauHuyVBCC"  tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-top" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tieuDeModalXemTTDonYeuCauHuyVBCC" style=" color: #ffffff; ">Xem chi tiết nhật ký thao tác</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="p-3 ">
                                <div id="NhatKy_XemChiTietDonYC">
                                    @include('partials.nhatkythaotac', ['containerId' => 'NhatKy_XemChiTietDonYC'])
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('layouts.XemDSDinhKem')

    {{-- Modal ban hành quyết định thu hồi/hủy bỏ văn bằng, chứng chỉ --}}
    <div class="modal fade" id="mdBanHanhQuyetDinhHuyVanBang" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
            <div class="modal-content p-0 rounded-3 overflow-hidden">
                <!-- Modal Header -->
                <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                    <h5 class="modal-title fw-bold text-uppercase" id="tieuDe_mdBanHanhQuyetDinhHuyVanBang">Ban hành quyết định
                        thu hồi/hủy bỏ văn bằng, chứng chỉ
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <!-- Modal Body -->
                <div class="modal-body px-4 py-3">
                    <div class="row">
                        <!-- Illustration -->
                        <div class="col-md-5 d-flex align-items-center justify-content-center mb-2 mb-md-0">
                            <img src="/img/Logo-note2.jpeg" alt="Quyết định" class="img-fluid" style="max-width:350px;">
                        </div>
                        <!-- Right Side: Content -->
                        <div class="col-md-7">
                            <!-- Alert/Instruction -->
                            <div class="border border-warning rounded mb-3 px-3 py-2"
                                style="background:#fff7e6;font-size:15px;">
                                <span id="alertMessage">
                                    Bạn đang thực hiện ban hành quyết định thu hồi/hủy bỏ văn bằng, chứng chỉ số:
                                    <b>01/2024/QĐ-SGD&ĐT</b> ngày lập <b>25/12/2024</b> của <b>Sở giáo dục và đào tạo
                                        Tỉnh NTSOFT.</b> Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>“Ban hành”</b> để thực hiện thao tác ban hành cho quyết định.
                                </span>
                            </div>
                            <!-- Fieldset -->
                            <fieldset class="KhungVien border rounded-3 p-3 mb-1">
                                <legend class="float-none w" id="fieldsetLegend">
                                    Thông tin ban hành
                                </legend>
                                <form>
                                    <div class="row g-2">
                                        <div class="col-12 col-sm-6">
                                            <label class="form-label mb-1" for="ngayBanHanh" style="">Ngày ban hành
                                                <span class="text-danger">(*)</span>
                                            </label>
                                            <div class="input-group input-group-sm">
                                                <input type="text" class="form-control date-picker" id="ngayBanHanh"
                                                    name="NgayKy" autocomplete="off" data-date-format="dd/mm/yyyy"
                                                    placeholder="dd/MM/yyyy" required>
                                            </div>
                                        </div>
                                        <div class="col-12 col-sm-6"></div>
                                        <div class="col-12 col-sm-6">
                                            <label class="form-label  mb-1" for="nguoiBanHanh" style="">Người ban
                                                hành</label>
                                            <select class="form-select" id="nguoiBanHanh">
                                            </select>
                                        </div>
                                        <div class="col-12 col-sm-6">
                                            <label class="form-label  mb-1" for="chucVuBanHanh" style="">Chức
                                                vụ</label>
                                            <select class="form-select" id="chucVuBanHanh">
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <label class="form-label mb-1" for="noiDungBanHanh" style="">Nội dung
                                                ban hành</label>
                                            <div id="noiDungBanHanh" class="form-control" contenteditable="true"
                                                style=" min-height: 120px;"></div>
                                        </div>
                                    </div>
                                </form>
                            </fieldset>
                        </div>
                    </div>
                </div>
                <!-- Modal Footer -->
                <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                    <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                        <i class="fa fa-times"></i>&nbsp;Đóng (F4)
                    </button>
                    <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnBanHanh">
                        <i class="fa fa-check"></i>&nbsp;Ban hành (F9)
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-dialog modal-xl modal-dialog-centered" id="mdThuHoiQuyetDinhHuyVanBang" role="document">
        <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
            <div class="modal-content p-0 rounded-3 overflow-hidden">
                <!-- Modal Header -->
                <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                    <h5 class="modal-title fw-bold text-uppercase" id="tieuDe_mdThuHoiQuyetDinhHuyVanBang">Thu hồi ban hành quyết định
                        thu hồi/hủy bỏ văn bằng, chứng chỉ
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <!-- Modal Body -->
                <div class="modal-body px-4 py-3">
                    <div class="row">
                        <!-- Illustration -->
                        <div class="col-md-5 d-flex align-items-center justify-content-center mb-2 mb-md-0">
                            <img src="/img/Logo-note2.jpeg" alt="Quyết định" class="img-fluid" style="max-width:350px;">
                        </div>
                        <!-- Right Side: Content -->
                        <div class="col-md-7">
                            <!-- Alert/Instruction -->
                            <div class="border border-warning rounded mb-3 px-3 py-2"
                                style="background:#fff7e6;font-size:15px;">
                                <span id="alertMessage">
                                    Bạn đang thực hiện thu hồi ban hành quyết định thu hồi/hủy bỏ văn bằng, chứng chỉ số:
                                    <b>01/2024/QĐ-SGD&ĐT</b> ngày lập <b>25/12/2024</b> của <b>Sở giáo dục và đào tạo
                                        Tỉnh NTSOFT.</b> Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>“Ban hành”</b> để thực hiện thao tác ban hành cho quyết định.
                                </span>
                            </div>
                            <!-- Fieldset -->
                            <fieldset class="KhungVien border rounded-3 p-3 mb-1">
                                <legend class="float-none w" id="fieldsetLegend">
                                    Thông tin ban hành
                                </legend>
                                <form>
                                    <div class="row g-2">
                                        <div class="col-12 col-sm-6">
                                            <label class="form-label mb-1" for="ngayThuHoi" style="">Ngày thu hồi
                                                <span class="text-danger">(*)</span>
                                            </label>
                                            <div class="input-group input-group-sm">
                                                <input type="text" class="form-control date-picker" id="ngayThuHoi"
                                                    name="NgayKy" autocomplete="off" data-date-format="dd/mm/yyyy"
                                                    placeholder="dd/MM/yyyy" required>
                                            </div>
                                        </div>
                                        <div class="col-12 col-sm-6"></div>
                                        <div class="col-12 col-sm-6">
                                            <label class="form-label  mb-1" for="nguoiThuHoi" style="">Người ban
                                                hành</label>
                                            <select class="form-select" id="nguoiThuHoi">
                                            </select>
                                        </div>
                                        <div class="col-12 col-sm-6">
                                            <label class="form-label  mb-1" for="chucVuThuHoi" style="">Chức
                                                vụ</label>
                                            <select class="form-select" id="chucVuThuHoi">
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <label class="form-label mb-1" for="noiDungThuHoi" style="">Nội dung
                                                thu hồi</label>
                                            <div id="noiDungThuHoi" class="form-control" contenteditable="true"
                                                style=" min-height: 120px;"></div>
                                        </div>
                                    </div>
                                </form>
                            </fieldset>
                        </div>
                    </div>
                </div>
                <!-- Modal Footer -->
                <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                    <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                        <i class="fa fa-times"></i>&nbsp;Đóng (F4)
                    </button>
                    <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnThuHoi">
                        <i class="fa fa-check"></i>&nbsp;Thu hồi (F9)
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        window.Laravel = window.Laravel || {};
        window.Laravel.local = {
            imgLuoi: `{{ asset('img/anhhoso.png') }}`,
            linkAnhDonXin: "{{asset('img/DonXinCapPhoiBang.png')}}",
            GetComboLoaiPhoi: "{{ route('thongkesdphoi.getAllPhoiVBCC') }}",
            GetComBoTrangThai:"{{ route('quyetdinhhuyvanbang.gettrangthai') }}",
            listChucVuUrl: "{{ route('dungchung.danhmuc.comboChucVu') }}",
            GetComBoNhanVien: "{{ route('dungchung.danhmuc.comboNhanVien') }}",
            getListLyDo: `{{{ route('huyphoivbcc.getListLyDo') }}}`,
            loadHinhThucXuLy: `{{ route('quyetdinhhuyvanbang.loadHinhThucXuLy') }}`,
            GetYeuCauHuyVBCC:"{{ route('quyetdinhhuyvanbang.getYeuCauHuyVBCC') }}",
            GetDonYeuCauByID:"{{ route('quyetdinhhuyvanbang.getDonYeuCauByID') }}",
            GetDanhSachTruongHoc_DemSoLuong:"{{ route('quyetdinhhuyvanbang.getDanhSachTruongHoc_DemSoLuong') }}",
            getListTruongHoc: "{{ route('quanly.dungchung.getDanhSachTruongHoc') }}",
            getListHS: function(donviId_Truong) {
                    return "{{ route('quanly.dungchung.getHSByTruongHoc', ['donviId_TruongHoc' => 'DONVI_ID_PLACEHOLDER']) }}"
                        .replace('DONVI_ID_PLACEHOLDER', donviId_Truong );
            },
            uploadFile: "{{ route('api.files.upload') }}",
            BanHanhQD:"{{ route('quyetdinhhuyvanbang.BanHanhQD') }}",
            ThuHoiQD:"{{ route('quyetdinhhuyvanbang.ThuHoiQD') }}",
        };
    </script>
    <script src="{{ asset('js/dungchung/svg-icons.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/dungchung/tabulator-configs.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/quanly/QuyetDinhHuyVanBang/QuyetDinhHuyVanBang.js') }}?v={{ time() }}"></script>
@endpush
