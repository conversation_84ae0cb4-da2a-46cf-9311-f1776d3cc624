@extends('layouts.layouts')

@section('content')
    <style type="text/css">
        .fs-big {
            white-space: break-spaces;
        }

        #mdChonHocSinh .input-icon {
            width: 100% !important;
        }

        #lblTieuDeMultiStep {
            font-weight: bold !important;
        }

        #KhungTimKiem .fw-bold {
            color: #f76707 !important;
        }

        .list-item {
            border-radius: 6px;
            box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
        }

        #GridMainDS.tabulator {
            border: 1px solid #ffffff00 !important;
            background-color: #ffffff00 !important;
        }

        #GridMainDS.tabulator .tabulator-tableholder {
            background-color: #ffffff00 !important;
        }

        #GridMainDS.tabulator .tabulator-tableholder .tabulator-table {
            background-color: #ffffff00 !important;
        }

        #GridMainDS .tabulator-row.tabulator-selected {
            background-color: #ffffff00 !important;
        }

        .TieuDeLabel span {
            font-weight: bold;
        }

        #GridMainDS .tabulator-row {
            background-color: #ffffff00 !important;
            border: unset !important;
        }

        #GridMainDS .tabulator-row>.tabulator-cell {
            border-right: 0px solid #ffffff00 !important;
            padding: 2px 4px 2px 2px !important;
        }

        #GridMainDS.tabulator .tabulator-footer {
            border: 1px solid #dedede !important;
            padding: 0px !important;
        }

        .tabulator-row.tabulator-selectable:hover>.tabulator-cell .show-or-hide {
            display: block !important;
            background: white;
            padding: 5px 13px;
        }
    </style>
    <style id="LuoiDScss">
        .span-trangthai {
            margin-top: 8px !important;
            width: 80%;
            text-align: center;
            padding: 2px 10px;
            --tblr-alert-color: #ffffff00;
            --tblr-alert-bg: var(--tblr-surface);
            border: var(--tblr-border-width) var(--tblr-border-style) rgb(255 255 255 / 0%);
            border-left: .25rem var(--tblr-border-style) var(--tblr-alert-color);
            box-shadow: rgba(24, 36, 51, .04) 0 2px 4px 0;
            color: white;
        }

        #GridMainDS .avatar {
            width: 60px !important;
            height: 60px !important;
            font-size: 25px !important;
            border-radius: 50%;
        }

        #GridMainDS .tabulator-tableholder .tabulator-table .tabulator-row .tabulator-cell {
            height: auto !important;
        }

        #GridMainDS .tabulator-row {
            border-bottom: 0px !important;
        }

        .row-girdmain {
            margin-bottom: 0px !important;
        }

        .tabulator-cell .dropdown-menu-end {
            position: fixed !important;
        }

        #GridMainDS .alert {
            --tblr-alert-padding-x: 1rem;
            --tblr-alert-padding-y: 0.3rem;
            --tblr-alert-margin-bottom: 0rem;
        }
    </style>
    <style type="text/css">
        #mdGiaoSoGoc .modal-dialog {
            min-width: 60% !important;
        }

        .line {
            flex-grow: 2;
            border-bottom: 1px solid #dadcde;
            margin: 0 10px;
            border-color: #07a607;
        }

        .label-text {
            color: #07a607;
        }

        .form-check .form-check-input {
            margin-left: unset !important;
        }

        .steps-vertical .step-item:before {
            top: var(--tblr-steps-dot-offset);
            left: -8px !important;
            transform: translate(0, 0);
            width: 35px;
            height: 35px;
        }

        #gridDonVi .tabulator-footer {
            display: none !important;
        }

        #gridDonVi .tabulator-data-tree-control {
            display: none !important;
        }

        .modal {
            z-index: 1202 !important;
        }

        .card {
            margin-bottom: 4px !important;
        }

        /* Child modals higher z-index */
        #mdThemMoiHocSinh.modal,
        #mdThemMoiHocSinh.modal .modal-dialog,
        #mdChonHocSinh.modal,
        #mdChonHocSinh.modal .modal-dialog {
            z-index: 1500 !important;
        }

        .modal-backdrop {
            z-index: 1201 !important;
        }

        .full-screen-panel {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.5);
            /* dark semi-transparent overlay */
            z-index: 1200;
            /* above other content */
            overflow-y: auto;
            /* scroll if content too tall */
            display: none;
            /* hidden by default */
        }

        .panel-content {
            background: white;
            border-radius: 0;
            padding-inline: 1rem;
            height: auto;
            overflow-y: auto;
        }

        #customSelect {
            position: relative;
            /* Make sure parent is positioned */
        }

        #customDropdown.show {
            display: block;
        }

        #mdXemThongTin .modal-dialog {
            min-width: 80% !important;
        }

        .tabulator .tabulator-cell.tabulator-editable {
            padding: 7px 7px !important;
            outline: 1px solid #ddd;
            outline-offset: -6px;
            /* pull the outline 6px _inside_ the box */
            transition: box-shadow 0.2s;
        }

        /* hover state */
        .tabulator .tabulator-cell.tabulator-editable:hover {
            box-shadow: inset 0 0 0 1px #bbb;
        }

        /* focus state (when the editor is open) */
        .tabulator .tabulator-cell.tabulator-editable:focus-within {
            box-shadow: inset 0 0 0 1px #999;
        }
    </style>
    <div id="DivMain">
        <div id="DivTimKiem" class="mb-2">
            <div class="row flex-row-reverse">
                <div class="col-md-8">
                    <div class="text-end">
                        <button style="" class="btn position-relative" id="">
                            <select class="form-control" id="CbSapXep" tabindex="0"
                                style="padding: 0.05rem 0.55rem !important;">
                                <option value="NamTotNghiep">Năm tốt nghiệp</option>
                                <option value="SoSoGoc">Số sổ gốc</option>
                                <option value="NgayKy">Ngày ký</option>
                                <option value="CoQuanBanHanh">Cơ quan ban hành</option>
                            </select>&ensp;&ensp;
                            <span id="BtnSapXepTangGiam">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="icon icon-tabler icon-tabler-sort-ascending-letters" width="24"
                                    height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                    <path d="M15 10v-5c0 -1.38 .62 -2 2 -2s2 .62 2 2v5m0 -3h-4" />
                                    <path d="M19 21h-4l4 -7h-4" />
                                    <path d="M4 15l3 3l3 -3" />
                                    <path d="M7 6v12" />
                                </svg>
                            </span>
                        </button>
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check height-button-icon" name="btn-layout" id="btn-layout-1"
                                autocomplete="off" checked="">
                            <label style="margin-bottom: 0px" for="btn-layout-1" class="btn btn-icon">
                                <svg class="icon icon-tabler icon-tabler-list" width="20" height="20"
                                    viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M9 6l11 0"></path>
                                    <path d="M9 12l11 0"></path>
                                    <path d="M9 18l11 0"></path>
                                    <path d="M5 6l0 .01"></path>
                                    <path d="M5 12l0 .01"></path>
                                    <path d="M5 18l0 .01"></path>
                                </svg>
                            </label>
                            <input type="radio" class="btn-check height-button-icon" name="btn-layout" id="btn-layout-2"
                                autocomplete="off">
                            <label style="margin-bottom: 0px" for="btn-layout-2" class="btn btn-icon">
                                <svg class="icon icon-tabler icon-tabler-list-details" width="20" height="20"
                                    viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M13 5h8"></path>
                                    <path d="M13 9h5"></path>
                                    <path d="M13 15h8"></path>
                                    <path d="M13 19h5"></path>
                                    <path d="M3 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z">
                                    </path>
                                    <path d="M3 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z">
                                    </path>
                                </svg>
                            </label>
                        </div>
                        <div class="btn-group ms-2">
                            <button type="button" class="btn nts-color-them dropdown-toggle show" id="dropdownThemMoi"
                                data-bs-toggle="dropdown" aria-expanded="true">
                                <i class="fa fa-plus"></i>&ensp;Thêm mới&ensp;<i class="fa fa-caret-down"></i>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownThemMoi" data-popper-placement="bottom-start"
                                style="width: 240px !important;
                                        position: absolute;
                                        inset: 0px auto auto -83px;
                                        margin: 0px;
                                        transform: translate3d(0px, 31.2px, 0px);">
                                <li style="padding: 0px 0px 0 0px !important;">
                                    <a class="dropdown-item" href="#" id="btnThemMoi">
                                        <i class="fa fa-plus text-primary me-2"></i>Thêm mới(F2)
                                    </a>
                                </li>
                                <li style="padding: 0px 0px 0 0px !important;">
                                    <a class="dropdown-item" href="#" id="btnTrichXuatOCR">
                                        <svg class="me-2" xmlns="http://www.w3.org/2000/svg" width="13" height="13"
                                            viewBox="0 0 24 24" fill="none">
                                            <path
                                                d="M3.59922 1.2002C2.9627 1.2002 2.35225 1.45305 1.90216 1.90314C1.45208 2.35323 1.19922 2.96368 1.19922 3.6002V9.0002H3.59922V3.6002H9.59922V1.2002H3.59922ZM1.19922 20.4002V13.8002H3.59922V20.4002H9.59922V22.8002H3.59922C2.9627 22.8002 2.35225 22.5473 1.90216 22.0973C1.45208 21.6472 1.19922 21.0367 1.19922 20.4002ZM14.3992 20.4002V22.8002H20.3992C21.0357 22.8002 21.6462 22.5473 22.0963 22.0973C22.5464 21.6472 22.7992 21.0367 22.7992 20.4002V13.8002H20.3992V20.4002H14.3992ZM20.3992 9.0002H22.7992V3.6002C22.7992 2.96368 22.5464 2.35323 22.0963 1.90314C21.6462 1.45305 21.0357 1.2002 20.3992 1.2002H14.3992V3.6002H20.3992V9.0002Z"
                                                fill="#F76707" />
                                            <path
                                                d="M7.2 6H16.8V7.2H7.2V6ZM6 9.6H18V10.8H6V9.6ZM7.2 13.2H16.8V14.4H7.2V13.2ZM6 16.8H18V18H6V16.8Z"
                                                fill="#F76707" />
                                        </svg>Trích xuất sổ gốc tự động
                                        (OCR)
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="btn-group">
                            <div class="dropdown d-inline">
                                <button class="btn btn-primary dropdown-toggle-hide-arrow height-button-icon"
                                    type="button" id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true"
                                    aria-expanded="false">
                                    <i class="blue fa fa-ellipsis-h"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-lg-end w-auto">
                                    <a class="nts-color-in dropdown-item textsize-item" href="javascript:void(0);"
                                        id="btnPrint" onclick="previewExportv1('pdf',GridMainLuoi);return false;"><i
                                            class="text-warning fa fa-print iconsize-item"></i>&ensp;In</a>
                                    <a class="nts-color-excel dropdown-item textsize-item" href="javascript:void(0);"
                                        id="btnExport" onclick="previewExportv1('excel',GridMainLuoi);return false;"><i
                                            class="text-success fa fa-file-excel-o iconsize-item"></i>&ensp;Xuất
                                        Excel</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div style="display: flex; align-items: center" class="DivTimKiem">
                        <div class="input-icon">
                            <input type="text" value="" class="form-control nts-border-secondary"
                                placeholder="Nội dung tìm kiếm ..." id="SearchKey" autocomplete="off"
                                style="border-top-right-radius: 0px; border-bottom-right-radius: 0px; ">
                            <span class="input-icon-addon">
                                <i class="fa fa-search"></i>
                            </span>
                        </div>
                        <button id="TimKiemNangCao" class="btn btn-primary height-button-icon" type="button"
                            style="border-top-left-radius: 0px;border-bottom-left-radius:0px;">
                            <span class="fa fa-sliders" aria-hidden="true"> </span>
                        </button>
                    </div>
                    <div id="KhungTimKiem" class="bg-white mt-1 px-4 py-2 border rounded shadow position-absolute"
                        style="z-index: 99; width:425px ; display:none">
                        <div class="row">
                            <div class="col text-primary fw-bold">
                                <i class="fa fa-search"></i> Tìm kiếm nâng cao
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="QuyetDinhID_Loc">Quyết định</label>
                                <select class="form-control input-sm" id="QuyetDinhID_Loc" tabindex="0">
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="NamTotNghiep_Loc">Năm tốt nghiệp</label>
                                <select class="form-control input-sm" id="NamTotNghiep_Loc" tabindex="0">
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label" for="CapHocID_Loc">Cấp học</label>
                                <select class="form-control input-sm" id="CapHocID_Loc" tabindex="0">
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label" for="TrangThai_Loc">Trạng thái</label>
                                <select class="form-control input-sm" id="TrangThai_Loc" tabindex="0">
                                    <option value="" selected>(Tất cả)</option>
                                    <option value="37">Chờ giao</option>
                                    <option value="38">Đã xuất sổ gốc</option>
                                </select>
                            </div>
                        </div>
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group" style="text-align:right">
                                    <button type="button" id="DongTimKiem" class="btn btn-outline-danger"><i
                                            class="fa fa-times"></i>&ensp;Đóng</button>&ensp;
                                    <button type="button" id="TimKiem" class="btn btn-success ms-auto"><i
                                            class="fa fa-search"></i>&ensp;Tìm kiếm</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="DivDanhSach">
            <div class="row">
                <div class="col-md-12">
                    <div id="GridMainDS">
                    </div>
                </div>
            </div>
        </div>
        <div id="DivLuoi" style="display:none">
            <div class="row">
                <div class="col-md-12">
                    <div id="GridMainLuoi" class="GridData">

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- MODAL THÊM MỚI -->
    <div id="mdThemMoi" class="full-screen-panel" style="display:none;">
        <div class="panel-content">
            <div class="modal-content d-flex flex-colum" style="border-radius:0; height:100vh;">
                <div class="modal-header modal-title font-weight-bold"
                    style="border-radius: 0 !important; background-color : white !important; color:gray !important; text-transform:uppercase">
                    <h5 class="modal-title nts-text-backround" id="lblTieuDeMultiStep">THÊM MỚI SỔ GỐC CẤP BẢN SAO VĂN
                        BẰNG, CHỨNG CHỈ</h5>
                    <button type="button"
                        class="btn-close modal-close my-0 mx-3 d-flex align-items-center justify-content-center"
                        data-bs-dismiss="modal" aria-label="Close"
                        style="width:32px; height:32px; padding:0; font-size:1.2rem; color:gray !important; font-weight:bold;">
                        x
                    </button>
                </div>

                <div class="modal-body  flex-fill" style="background:white;">
                    <div class="row p-1" style="height:100%">
                        <div class="col-md-10" style="height:100%" id="step-content">
                            <input type="hidden" id="HoSoTotNghiepID" value="" />
                            <div id="step-1-content" class="box-shadow-primary border border-primary"
                                style="padding: 10px; border-radius: 4px; height: 100%;">
                                <div class="row mt-2 box-hdsd"
                                    style="background-color: white; border-radius: 8px; padding: 10px; height:100%;">
                                    <div class="col-md-4"
                                        style="display: flex; align-content: stretch; justify-content: flex-start; flex-wrap: wrap; align-items: center;">
                                        <img alt="" src="{{ asset('img/ThemMoiHoSo.jpg') }}?v={{ time() }}"
                                            style="width:100%" />
                                    </div>
                                    <div class="col-md-8 d-flex flex-column" style="height:100%">
                                        <fieldset class="KhungVien flex-grow-1 d-flex flex-column"
                                            id="fieldThongTinChung">
                                            <legend>Thông tin chung</legend>
                                            <div class="row mb-2">
                                                <div class="col-md-4">
                                                    <label for="KyThiID">Số sổ gốc</label>
                                                    <input type="text" class="form-control" id="SoSoGoc">
                                                </div>
                                            </div>
                                            <div class="row mb-2">
                                                <div class="col-md-4">
                                                    <label for="NgayKy">Ngày ký</label>
                                                    <input type="text" class="form-control date-picker" id="NgayKy"
                                                        autocomplete="off" data-date-format="dd/mm/yyyy">
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="NhanVienID_NguoiKy">Người ký</label>
                                                    <select class="form-control input-sm" id="NhanVienID_NguoiKy"
                                                        tabindex="0">
                                                    </select>
                                                    {{-- <input type="text" class="form-control" id="NguoiKy"> --}}
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="ChucVuID">Chức vụ</label>
                                                    <select class="form-control input-sm" id="ChucVuID_NguoiKy"
                                                        tabindex="0">
                                                    </select>
                                                </div>
                                            </div>
                                            <!-- SDQ, NguoiKy, ChucVu -->
                                            <div class="row mb-2">
                                                <div class="col-md-8">
                                                    <label for="QuyetDinhID">Quyết định công nhận tốt nghiệp</label>
                                                    <select class="form-control input-sm" id="QuyetDinhID" tabindex="0"
                                                        required>
                                                    </select>
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="HinhThucDaoTaoID">Hình thức đào tạo</label>
                                                    <select class="form-control input-sm" id="HinhThucDaoTaoID"
                                                        tabindex="0">
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="row mb-2">
                                                <div class="col-md-8">
                                                    <input type="hidden" id="DonViID_TruongHoc" />
                                                    <label class="form-label validation" for="DonViID_TruongHoc">Trường
                                                        học</label>
                                                    <div class="form-group" style="width: 100%;">
                                                        <div class="row">
                                                            <div class="col-md-12">
                                                                <div class="form-group">
                                                                    <div class="d-flex align-items-center">
                                                                        <input class="form-control input-sm"
                                                                            style="border-top-right-radius: 0px !important;
                                                                                    border-bottom-right-radius: 0px !important;"
                                                                            id="TenTruongHoc" disabled></input>
                                                                        <button
                                                                            class="btn btn-sm btn-primary nts-color-luu"
                                                                            id="btnChonDonViCha"
                                                                            style="border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important; height: 30px;">
                                                                            <i class="blue fa fa-ellipsis-h"></i>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="NamTotNghiep">Năm tốt nghiệp</label>
                                                    <select class="form-control input-sm" id="NamTotNghiep"
                                                        tabindex="0" required>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="row mb-2">
                                                <div class="col-md-12">
                                                    <label for="CoQuanBanHanh">Cơ quan ban hành</label>
                                                    <textarea class="form-control" id="CoQuanBanHanh" rows="1"></textarea>
                                                </div>
                                            </div>
                                            <div class="row mb-2">
                                                <div class="col-md-12">
                                                    <label for="TrichYeu">Trích yếu</label>
                                                    <textarea class="form-control" id="TrichYeu" rows="2"></textarea>
                                                </div>
                                            </div>
                                            <div class="row mb-2">
                                                <div class="col-md-12">
                                                    @include('partials.dinh-kem-v2', [
                                                        'id' => 'SoGocBanSao',
                                                    ])
                                                </div>
                                            </div>

                                        </fieldset>
                                        <div class="row justify-content-end">
                                            <div class="col-auto">
                                                <a href="#" id="btnTiepTuc" class="btn btn-success btn-luuvadong">
                                                    <img
                                                        src="{{ asset('img/next-step.svg') }}?v={{ time() }}" />&ensp;Tiếp
                                                    tục
                                                    (F9)
                                                </a>
                                            </div>
                                        </div>

                                    </div> <!-- /col-md-9 content area -->
                                </div>
                            </div>

                            <!-- STEP 2 CONTENT -->
                            <div id="step-2-content" class="border border-primary p-2" style="display:none">

                                <div class="row my-2">
                                    <div class="d-flex flex-column">
                                        <!-- Alert -->
                                        <div class="row" style="margin-left: 4px;margin-right: 4px;"">
                                            <div class="card">
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-12 col-xs-6 col-sm-2 text-center"
                                                            style="margin: auto;">
                                                            <div class="profile-picture">
                                                                <img style="object-fit:unset"
                                                                    src="{{ asset('img/img1.png') }}" alt=""
                                                                    class="img-thumbnail rounded lazy">
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-10">
                                                            <div class="row">
                                                                <div class="col-sm-12">
                                                                    <div class="row">
                                                                        <div class="col-md-3">
                                                                            <p class="fs-big my-1">Số sổ gốc: <b
                                                                                    id="lblSoSoGoc_SG"></b></p>
                                                                        </div>
                                                                        <div class="col-md-3">
                                                                            <p class="fs-big my-1">Ngày ký: <b
                                                                                    id="lblNgayKy_SG"></b>
                                                                            </p>
                                                                        </div>
                                                                        <div class="col-md-3">
                                                                            <p class="fs-big my-1">Người ký: <b
                                                                                    id="lblNguoiKy_SG"></b></p>
                                                                        </div>
                                                                        <div class="col-md-3">
                                                                            <p class="fs-big my-1">Chức vụ: <b
                                                                                    id="lblChucVu_SG"></b>
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <hr class="nts-color-them  hr-custom">
                                                            <div class="row">
                                                                <div class="col-sm-12">
                                                                    <div class="row">
                                                                        <div class="col-md-12">
                                                                            <p class="fs-big my-1">Trích yếu/nội dung: <b
                                                                                    id="lblTrichYeu_SG"></b>
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-md-6">
                                                                            <p class="fs-big my-1">Cơ quan ban hành: <b
                                                                                    id="lblCoQuanBanHanh_SG"></b></p>
                                                                        </div>
                                                                        <div class="col-md-6">
                                                                            <p class="fs-big my-1">Trường: <b
                                                                                    id="lblTruong_SG"></b>
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-md-6">
                                                                            <p class="fs-big my-1">Kỳ thi: <b
                                                                                    id="lblKyThi_SG"></b>
                                                                            </p>
                                                                        </div>
                                                                        <div class="col-md-3">
                                                                            <p class="fs-big my-1">Cấp học: <b
                                                                                    id="lblCapHoc_SG"></b>
                                                                            </p>
                                                                        </div>
                                                                        <div class="col-md-3">
                                                                            <p class="fs-big my-1" id="lblDinhKem_SG">Đính
                                                                                kèm: </p>
                                                                        </div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-md-12">
                                                                            <p class="fs-big my-1">Ghi chú: <b
                                                                                    id="lblGhiChu_SG"></b>
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" style="margin-left: 4px;margin-right: 4px;">
                                            <div class="card">
                                                <div class="card-header">
                                                    <ul class="nav nav-tabs card-header-tabs" data-bs-toggle="tabs"
                                                        role="tablist">
                                                        <li class="nav-item" role="presentation">
                                                            <a href="#tabs-home-7" class="nav-link active"
                                                                data-bs-toggle="tab" aria-selected="true"
                                                                role="tab"><!-- Download SVG icon from http://tabler.io/icons/icon/home -->
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="16"
                                                                    height="16" viewBox="0 0 16 16" fill="none">
                                                                    <path
                                                                        d="M7.84169 12.6316C8.97533 12.6316 9.89433 11.7126 9.89433 10.579C9.89433 9.44536 8.97533 8.52637 7.84169 8.52637C6.70806 8.52637 5.78906 9.44536 5.78906 10.579C5.78906 11.7126 6.70806 12.6316 7.84169 12.6316Z"
                                                                        stroke="#F76707" stroke-width="1.02632" />
                                                                    <path
                                                                        d="M5.86698 10.5518L5.1554 11.2633L4.14414 12.2329C3.92246 12.4457 3.81161 12.5517 3.7733 12.642C3.73022 12.7381 3.72458 12.8468 3.75749 12.9468C3.7904 13.0468 3.85948 13.1309 3.95119 13.1825C4.03467 13.2298 4.18588 13.2441 4.48693 13.2742C4.65662 13.2907 4.74214 13.2989 4.8133 13.3242C4.89062 13.351 4.96123 13.3943 5.02027 13.451C5.07932 13.5077 5.12542 13.5764 5.1554 13.6526C5.18209 13.721 5.19098 13.8024 5.20809 13.966C5.23888 14.2547 5.25461 14.3991 5.30388 14.4791C5.41609 14.6618 5.65351 14.7337 5.86767 14.6502C5.9614 14.6125 6.07225 14.5065 6.29393 14.2944L7.8423 12.8097L9.39067 14.2944C9.61235 14.5065 9.72319 14.6125 9.81693 14.6502C10.0311 14.7337 10.2685 14.6618 10.3807 14.4791C10.43 14.3991 10.4457 14.2547 10.4765 13.966C10.4936 13.8024 10.5025 13.721 10.5292 13.6526C10.5592 13.5764 10.6053 13.5077 10.6643 13.451C10.7234 13.3943 10.794 13.351 10.8713 13.3242C10.9431 13.2989 11.028 13.2907 11.1977 13.2742C11.4987 13.2448 11.6499 13.2298 11.7334 13.1825C11.8251 13.1309 11.8942 13.0468 11.9271 12.9468C11.96 12.8468 11.9544 12.7381 11.9113 12.642C11.873 12.5517 11.7621 12.4457 11.5405 12.2329L10.5285 11.2633L9.89493 10.6291"
                                                                        stroke="#F76707" stroke-width="1.02632" />
                                                                    <path
                                                                        d="M11.4821 11.9446C12.8314 11.9303 13.584 11.8454 14.0828 11.3459C14.6842 10.7452 14.6842 9.77705 14.6842 7.84211V5.10526C14.6842 3.17032 14.6842 2.20216 14.0828 1.60142C13.4821 1 12.5139 1 10.5789 1H5.10526C3.17032 1 2.20216 1 1.60142 1.60142C1 2.20216 1 3.17032 1 5.10526V7.84211C1 9.77705 1 10.7452 1.60142 11.3459C2.12689 11.8721 2.93289 11.9378 4.42105 11.946"
                                                                        stroke="#F76707" stroke-width="1.02632" />
                                                                    <path d="M5.7903 3.73682H9.89556M4.42188 6.13155H11.264"
                                                                        stroke="#F76707" stroke-width="1.02632"
                                                                        stroke-linecap="round" />
                                                                </svg>&ensp;Danh sách học sinh</a>
                                                        </li>
                                                        <li class="nav-item" role="presentation">
                                                            <a href="#tabs-profile-7" class="nav-link"
                                                                data-bs-toggle="tab" aria-selected="false" tabindex="-1"
                                                                role="tab"><!-- Download SVG icon from http://tabler.io/icons/icon/user -->
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="18"
                                                                    height="18" viewBox="0 0 18 18" fill="none">
                                                                    <path
                                                                        d="M9.52498 2.68457C7.8918 2.68457 6.32552 3.33335 5.17069 4.48818C4.01586 5.64301 3.36708 7.20929 3.36708 8.84247H1.31445L3.97603 11.504L4.02393 11.5998L6.78814 8.84247H4.73551C4.73551 6.19457 6.87708 4.05299 9.52498 4.05299C12.1729 4.05299 14.3145 6.19457 14.3145 8.84247C14.3145 11.4904 12.1729 13.6319 9.52498 13.6319C8.20445 13.6319 7.00709 13.0914 6.14498 12.2225L5.1734 13.194C5.74354 13.7675 6.42163 14.2224 7.16851 14.5325C7.9154 14.8425 8.7163 15.0015 9.52498 15.0004C11.1582 15.0004 12.7244 14.3516 13.8793 13.1968C15.0341 12.0419 15.6829 10.4756 15.6829 8.84247C15.6829 7.20929 15.0341 5.64301 13.8793 4.48818C12.7244 3.33335 11.1582 2.68457 9.52498 2.68457ZM8.84077 6.10562V9.52668L11.7487 11.2509L12.2755 10.3751L9.86708 8.9451V6.10562H8.84077Z"
                                                                        fill="#4265B6" />
                                                                </svg>&ensp;Nhật ký thao tác</a>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class="card-body">
                                                    <div class="tab-content">
                                                        <div class="tab-pane active show" id="tabs-home-7"
                                                            role="tabpanel">
                                                            <!-- Search and Add -->
                                                            <div
                                                                class="d-flex justify-content-between align-items-center mb-2">
                                                                <div class="col-md-4">
                                                                    <input type="text" id="searchContent"
                                                                        class="form-control"
                                                                        placeholder="Nội dung tìm kiếm"
                                                                        style="max-width:400px;">
                                                                </div>
                                                                <div class="col-md-1" style="margin-left: 4px">
                                                                    <div class="ktXem">
                                                                        Đã chọn <b id="txtSoRow">0</b>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-2">
                                                                    <div class="ktXem">
                                                                        <span id="spBoChon"
                                                                            style="color:white;cursor: pointer;"
                                                                            onclick="BoChon();">
                                                                            <b>Bỏ chọn</b>
                                                                        </span>
                                                                        <div class="btn-group" style="">
                                                                            <div class="dropdown d-inline">
                                                                                <button
                                                                                    class="btn btn-white dropdown-toggle-hide-arrow"
                                                                                    type="button"
                                                                                    style="font-size:18px;height: 33.4px;"
                                                                                    id="growthReportId"
                                                                                    data-bs-toggle="dropdown"
                                                                                    aria-haspopup="true"
                                                                                    aria-expanded="false">
                                                                                    <i style=""
                                                                                        class="blue fa fa-ellipsis-h text-nts-primary"></i>
                                                                                </button>
                                                                                <div class="dropdown-menu dropdown-menu-lg-end w-auto"
                                                                                    style="width: 230px !important;">
                                                                                    <a class="dropdown-item textsize-item d-none"
                                                                                        onclick="SuaGridTab1();"
                                                                                        href="javascript:void(0);"><svg
                                                                                            xmlns="http://www.w3.org/2000/svg"
                                                                                            width="24" height="24"
                                                                                            viewBox="0 0 24 24"
                                                                                            fill="none">
                                                                                            <path
                                                                                                d="M14.06 9.02L14.98 9.94L5.92 19H5V18.08L14.06 9.02ZM17.66 3C17.41 3 17.15 3.1 16.96 3.29L15.13 5.12L18.88 8.87L20.71 7.04C20.8027 6.94749 20.8762 6.8376 20.9264 6.71662C20.9766 6.59565 21.0024 6.46597 21.0024 6.335C21.0024 6.20403 20.9766 6.07435 20.9264 5.95338C20.8762 5.8324 20.8027 5.72251 20.71 5.63L18.37 3.29C18.17 3.09 17.92 3 17.66 3ZM14.06 6.19L3 17.25V21H6.75L17.81 9.94L14.06 6.19Z"
                                                                                                fill="#4265B6" />
                                                                                        </svg>  Cập nhật số vào sổ cấp
                                                                                        bằng</a>
                                                                                    <a class="dropdown-item textsize-item"
                                                                                        onclick="XoaGridTab1();"
                                                                                        href="javascript:void(0);"><i
                                                                                            class="fa fa-trash-o text-danger"
                                                                                            aria-hidden="true"></i>&ensp;Xóa
                                                                                        dữ
                                                                                        liệu</a>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-5 text-end">
                                                                    <div class="btn-group ms-2">
                                                                        <button type="button"
                                                                            class="btn btn-warning dropdown-toggle"
                                                                            id="dropdownThemMoiHS"
                                                                            data-bs-toggle="dropdown"
                                                                            aria-expanded="false">
                                                                            <i class="fa fa-plus"></i>&nbsp;Thêm
                                                                            mới&nbsp;&nbsp;<i class="fa fa-caret-down"></i>
                                                                        </button>
                                                                        <ul class="dropdown-menu"
                                                                            aria-labelledby="dropdownThemMoiHS">
                                                                            <li>
                                                                                <a class="dropdown-item" href="#"
                                                                                    id="btnDropdownHS">
                                                                                    {{-- id="btnThemMoiHS"> --}}
                                                                                    <i
                                                                                        class="fa fa-plus text-primary me-2 "></i>Chọn
                                                                                    học sinh
                                                                                </a>
                                                                            </li>
                                                                            <li class="d-none">
                                                                                <a class="dropdown-item" href="#"
                                                                                    id="btnNhanExcelHS">
                                                                                    <i
                                                                                        class="fa fa-file-excel-o me-2 text-nts-primary"></i>Nhập
                                                                                    excel
                                                                                </a>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                            <!-- Tabulator Grid -->
                                                            <div class="row">
                                                                <div class="col-md-12">
                                                                    <div id="GridCT" class="GridData">

                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="tab-pane" id="tabs-profile-7" role="tabpanel">
                                                            <h4>Nhât ký thao tác</h4>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Footer Buttons -->
                                        <div class="d-flex justify-content-end mt-3 gap-2">
                                            <button id="btnQuayLaiBuoc1"
                                                class="btn btn-warning d-flex align-items-center">
                                                <i class="fa fa-reply-all" aria-hidden="true"></i>&nbsp;Quay lại (F8)
                                            </button>
                                            <button id="btnKetThuc" class="btn btn-success d-flex align-items-center">
                                                <i class="fa fa-fast-forward" aria-hidden="true"></i>&nbsp;Kết thúc (F9)
                                            </button>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- SIDEBAR (always visible) -->
                        <div class="col-md-2" style="height:100%">
                            <div class="row flex-fill">
                                <div class="box-shadow-primary border border-primary"
                                    style="padding: 10px; border-radius: 4px; height: 50%;">
                                    <div class="row mt-2 box-hdsd"
                                        style="background-color: white; border-radius: 8px; padding: 10px;">
                                        <ul class="steps steps-counter steps-vertical" style="height:auto">
                                            <h3 class="text-nts-primary"
                                                style="margin-bottom:0px;text-align:center; font-weight:bold; font-size:1.25rem">
                                                CÁC
                                                BƯỚC
                                                THỰC HIỆN</h3>
                                            <li class="step-item" id="sidebar-step-1">
                                                <div class="text-primary font-weight-bold h4 m-0">Thông tin chung
                                                </div>
                                                <div class="text-primary">Cập nhật các thông tin như: Số sổ gốc, ngày ký,
                                                    người ký, quyết định công nhận tốt nghiệp, học sinh trường,...</div>
                                                <img src="{{ asset('img/Nen.png') }}?v={{ time() }}"
                                                    style="max-width: 43%;">
                                            </li>
                                            <li class="step-item" id="sidebar-step-2">
                                                <div class="text-dark h4 m-0 font-weight-bold">Nhập danh sách học sinh
                                                </div>
                                                <div class="text-dark">Nhập danh sách học sinh được vào sổ gốc cấp văn
                                                    bằng, chứng chỉ.
                                                </div>
                                                <img src="{{ asset('img/Nen2.png') }}?v={{ time() }}"
                                                    style="max-width: 43%;">
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="row flex-fill" style="margin-top: 4px">
                                <div class="card border rounded-3 p-3 border-primary"
                                    style="max-width: 320px; border-color: #fd7e14;">
                                    <h5 class="text-center fw-bold mb-3 text-primary">Thống kê nhanh xếp<br>xếp loại
                                        tốt
                                        nghiệp</h5>
                                    <div id="TKXepLoai">
                                        {{-- <div class="d-flex align-items-center mb-2">
                                            <div class="px-3 py-1 text-white fw-bold rounded me-2"
                                                style="background:#e50000; min-width: 60px; text-align:center;"
                                                id="spanXuatSac">
                                                0/0</div>
                                            <div class="fw-bold">Xuất sắc</div>
                                        </div>

                                        <div class="d-flex align-items-center mb-2">
                                            <div class="px-3 py-1 text-white fw-bold rounded me-2"
                                                style="background:#fd7e14; min-width: 60px; text-align:center;"
                                                id="spanGioi">0/0
                                            </div>
                                            <div class="fw-bold">Giỏi</div>
                                        </div>

                                        <div class="d-flex align-items-center mb-2">
                                            <div class="px-3 py-1 text-white fw-bold rounded me-2"
                                                style="background:#1e90ff; min-width: 60px; text-align:center;"
                                                id="spanKha">0/0
                                            </div>
                                            <div class="fw-bold">Khá</div>
                                        </div>

                                        <div class="d-flex align-items-center">
                                            <div class="px-3 py-1 text-white fw-bold rounded me-2"
                                                style="background:#66c3e9; min-width: 60px; text-align:center;"
                                                id="spanTrungBinh">0/0</div>
                                            <div class="fw-bold">Trung bình</div>
                                        </div> --}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- MODAL CHỌN HỌC SINH -->
    <div class="modal fade" id="mdChonHocSinh" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
            <div class="modal-content p-0 rounded-3 overflow-hidden">
                <!-- Header -->
                <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                    <h5 class="modal-title fw-bold text-uppercase" style="font-size:1.05rem;">Chọn học sinh/sinh viên</h5>
                    <button type="button" class="btn-close btn-close-white close-mdChonHocSinh" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <!-- Body -->
                <div class="modal-body py-2 px-3">
                    <div class="row g-2">
                        <!-- Left: Danh sách trường học -->
                        <div class="col-12 col-md-3">
                            <fieldset class="KhungVien">
                                <legend><span>Danh sách trường học</span></legend>
                                <!-- Search -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="input-icon mb-2">
                                            <input type="text" value="" class="form-control"
                                                placeholder="Tìm kiếm ..." id="timKiemTH" autocomplete="off">
                                            <span class="input-icon-addon">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24"
                                                    height="24" viewBox="0 0 24 24" stroke-width="2"
                                                    stroke="currentColor" fill="none" stroke-linecap="round"
                                                    stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                    <circle cx="10" cy="10" r="7"></circle>
                                                    <line x1="21" y1="21" x2="15" y2="15">
                                                    </line>
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <!-- School list (replace with Tabulator or custom HTML as needed) -->
                                <div class="row">
                                    <div class="px-2 pb-2" style="min-height:360px;max-height:420px;overflow:auto;">
                                        <div id="GridTruongHoc"></div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                        <!-- Right: Danh sách học sinh -->
                        <div class="col-12 col-md-9">
                            <fieldset class="KhungVien">
                                <legend><span>Danh sách học sinh</span></legend>
                                <!-- Search -->
                                <div class="row">
                                    <div class=" col-md-12">
                                        <div class="input-icon mb-2" style="width:40% !important">
                                            <input type="text" value="" class="form-control"
                                                placeholder="Tìm kiếm ..." id="timKiemHS" autocomplete="off">
                                            <span class="input-icon-addon">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24"
                                                    height="24" viewBox="0 0 24 24" stroke-width="2"
                                                    stroke="currentColor" fill="none" stroke-linecap="round"
                                                    stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                    <circle cx="10" cy="10" r="7"></circle>
                                                    <line x1="21" y1="21" x2="15" y2="15">
                                                    </line>
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <!-- Student grid placeholder -->
                                    <div id="tabGridHocSinhChon" class="px-2"
                                        style="min-height:360px;max-height:420px;overflow:auto;border-radius:6px;">
                                        <div id="GridChonHocSinh">

                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                </div>
                <!-- Footer -->
                <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                    <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2 close-mdChonHocSinh"
                        data-bs-dismiss="modal">
                        <i class="fa fa-times"></i>&ensp;Đóng (F4)
                    </button>
                    <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnChonVaDong">
                        <i class="fa fa-check"></i>&ensp;Chọn và đóng (F9)
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- MODAL THÊM MỚI HỌC SINH XX-->
    <div class="modal fade" id="mdThemMoiHocSinh" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content p-0 rounded-3 overflow-hidden">
                <!-- Header -->
                <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                    <h5 class="modal-title fw-bold text-uppercase" style="font-size:1.15rem;"
                        id="lblTieuDemdThemMoiHocSinh">Thêm mới học sinh/sinh viên
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <!-- Body -->
                <div class="modal-body pb-1 pt-3 px-3">
                    <!-- Học sinh info -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label id="labelNguoiTiepNhan" for="HocSinhID_CT" class="form-label">
                                Học sinh/sinh viên<span class="text-danger">(*)</span>
                            </label>
                            <div class="position-relative">
                                <!-- Custom Select -->
                                <div class="d-flex align-items-center border rounded p-1"
                                    style="background-color: #eee; cursor: pointer;" id="customSelect">
                                    <div class="flex-grow-1" id="txtTenHocSinh"></div>
                                    <a href="#" class="text-nts-primary text-decoration-none me-2 d-none">Xem chi
                                        tiết</a>
                                    <button class="btn btn-sm text-white"
                                        style="border-radius: 5px; background-color: var(--main-color)"
                                        id="btnDropdownHS">
                                        <i class="fas fa-ellipsis-h"></i>
                                    </button>
                                </div>

                                <!-- Dropdown Options -->
                                <ul class="list-group position-absolute w-100 d-none" id="customDropdown"
                                    style="z-index: 2000; top: 100%; left: 0; background-color: white; border: 1px solid #ced4da; border-radius: 5px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); max-height: 150px; overflow-y: auto;">
                                </ul>

                            </div>
                            <!-- Hidden native select -->
                            <input type="hidden" id="HocSinhID_CT" required />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-8">
                            <div class="mb-3">
                                <label class="form-label" for="txtDonViID_CT">Trường</label>
                                <input type="text" class="form-control" id="txtDonViID_CT" disabled>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="mb-3">
                            <label class="form-label" for="XepLoaiID_CT">Kết quả xếp loại</label>
                            <select class="form-control" id="XepLoaiID_CT" tabindex="0" disabled>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-4">
                            <div class="mb-3">
                                <label class="form-label" for="DiemThi_CT">Điểm thi</label>
                                <input type="text" class="form-control" id="DiemThi_CT" required>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="mb-3">
                                <label class="form-label" for="SoHieuVanBang_CT">Số hiệu văn bằng</label>
                                <input type="text" class="form-control" id="SoHieuVanBang_CT" required>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="mb-3">
                                <label class="form-label" for="SoVaoSoGoc_CT">Số vào sổ gốc</label>
                                <input type="text" class="form-control" id="SoVaoSoGoc_CT" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="mb-3">
                                <label class="form-label" for="TrichYeu_CT">Ghi chú</label>
                                <textarea class="form-control" id="TrichYeu_CT" rows="1"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Footer -->
                <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                    <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                        <i class="fa fa-times"></i>&ensp;Đóng
                    </button>
                    <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnLuuVaDongCT">
                        <i class="fa fa-save"></i>&ensp;Lưu và đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal modal-blur fade" id="mdXemThongTin" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tieuDeModalXemThongTin">Xem thông tin sổ gốc</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <fieldset class="KhungVien">
                        <legend>Thông tin chung</legend>
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="mb-1">
                                    <label class="">Quyết định: <b id="lblQuyetDinh_Xem"></b></label>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="mb-1">
                                    <label class="">Năm tốt nghiệp: <b id="lblNamTotNghiep"></b></label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="mb-1">
                                    <label class="">Học sinh trường: <b id="lblTenDonVi_Xem"></b></label>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="mb-1">
                                    <label class="">Hình thức học: <b id="lblTenHinhThuc_Xem"></b></label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="mb-1">
                                    <label class="">Kỳ thi: <b id="lblTenKyThi_Xem"></b></label>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="mb-1">
                                    <label class="">Khóa thi: <b id="lblTenKhoaThi_Xem"></b></label>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                    <fieldset class="KhungVien">
                        <legend>Danh sách học sinh, sinh viên</legend>
                        <div class="row flex-row-reverse">
                            <div class="col-md-8">
                            </div>
                            <div class="col-md-4">
                                <div class="input-icon mb-2">
                                    <input type="text" value="" class="form-control"
                                        placeholder="Nội dung tìm kiếm ..." id="timKiem_Xem" autocomplete="off">
                                    <span class="input-icon-addon">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24"
                                            height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                            fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                            <path d="M21 21l-6 -6"></path>
                                        </svg>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div id="GridXemCT"></div>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="modal-footer">
                    <div style="display: contents; align-items: center; justify-content: space-between">
                        <div class="col-md-6" style="display: flex; align-items: center">
                        </div>
                        <div class="col-md-6">
                            <div style="float:right;text-align: right">
                                <a href="#" class="nts-color-dong btn btn-outline-danger" data-bs-dismiss="modal">
                                    <i class="fa fa-close"></i>&nbsp;Đóng
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="mdUpdateSoGocCT" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content p-0 rounded-3 overflow-hidden">
                <!-- Modal Header -->
                <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                    <h5 class="modal-title fw-bold text-uppercase" id="tieuDe_mdUpdateSoGocCT">
                        Cập nhật số vào sổ cấp bằng
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>

                <!-- Modal Body -->
                <div class="modal-body px-4 py-3">
                    <div class="row">
                        <!-- Cột Trái: Hình ảnh -->
                        <div class="col-md-5 d-flex align-items-center justify-content-center mb-2 mb-md-0">
                            <img src="/img/Logo-note2.jpeg" alt="Minh họa cập nhật" class="img-fluid"
                                style="max-width:350px;">
                        </div>

                        <!-- Cột Phải: Nội dung -->
                        <div class="col-md-7">
                            <!-- Thông báo -->
                            <div class="alert border rounded mb-3 px-3 py-2"
                                style="background: #f5f9ff; font-size: 15px;">
                                <i class="fa fa-info-circle text-primary me-1"></i>
                                Bạn đang thực hiện thao tác cập nhật đồng loạt số vào sổ cấp bằng của số sổ gốc:
                                <strong class="text-uppercase text-primary" id="lblSoSoGoc_UD">...</strong>
                                ngày ký: <strong id="lblNgayKy_UD">...</strong> người ký:
                                <strong id="lblNguoiKy_UD">...</strong> của
                                <strong id="lblTruong_UD">...</strong> về việc
                                <strong id="lblTrichYeu_UD">...</strong>.
                                Để thực hiện thao tác vui lòng nhập đầy đủ thông tin bên dưới và bấm vào nút <strong>“Cập
                                    nhật”</strong>
                            </div>

                            <!-- Thông tin cập nhật -->
                            <fieldset class="border border-warning rounded-3 p-3 mb-2 bg-white">
                                <legend class="w-auto px-2" style="font-size:16px;">
                                    <i class="fa fa-edit me-1 text-warning"></i> Thông tin số vào sổ
                                </legend>
                                <div class="text-warning mb-2" style="font-size: 14px;">
                                    <i class="fa fa-exclamation-circle me-1"></i>
                                    <strong>Xác nhận cập nhật:</strong> Hệ thống sẽ cập nhật đồng loạt số vào sổ cấp bằng
                                    cho
                                    <strong id="lblSoDong_UD">... dòng đã chọn</strong>.
                                    Vui lòng nhập số bắt đầu và kiểm tra kỹ trước khi xác nhận.
                                    <strong class="text-danger">Thao tác này không thể hoàn tác!</strong>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label mb-1 fw-semibold" for="KyTuDauTien">Phần ký tự
                                            đầu</label>
                                        <input type="text" class="form-control form-control-sm" id="KyTuDauTien"
                                            placeholder="VD: 02PT/PT-2013/" disabled>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label mb-1 fw-semibold" for="SoTuTang">Phần tự tăng</label>
                                        <input type="text" class="form-control form-control-sm" id="SoTuTang"
                                            placeholder="VD: 0023" required>
                                    </div>
                                </div>
                                <div class="small fst-italic text-muted">
                                    <i class="fa fa-lightbulb me-1 text-primary"></i>
                                    Ví dụ sau khi cập nhật: Dòng đầu tiên sẽ có số vào sổ là
                                    <strong class="text-primary">02PT/PT-2013/0023</strong>,
                                    dòng thứ hai là
                                    <strong class="text-primary">02PT/PT-2013/0024</strong>, ...
                                </div>
                            </fieldset>
                        </div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                    <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                        <i class="fa fa-times me-1"></i> Đóng (F4)
                    </button>
                    <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnUpdateSoGocCT">
                        <i class="fa fa-save me-1"></i> Cập nhật (F9)
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="mdGiaoSoGoc" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content p-0 rounded-3 overflow-hidden">
                <!-- Modal Header -->
                <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                    <h5 class="modal-title fw-bold text-uppercase" id="tieuDe_mdGiaoSoGoc">Giao sổ gốc cấp bằng
                        tốt nghiệp
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <!-- Modal Body -->
                <div class="modal-body px-4 py-3">
                    <div class="row">
                        <!-- Illustration -->
                        <div class="col-md-5 d-flex align-items-center justify-content-center mb-2 mb-md-0">
                            <img src="/img/Logo-note2.jpeg" alt="Quyết định" class="img-fluid" style="max-width:350px;">
                        </div>
                        <!-- Right Side: Content -->
                        <div class="col-md-7">
                            <!-- Alert/Instruction -->
                            <div class="border border-warning rounded mb-3 px-3 py-2"
                                style="background:#fff7e6;font-size:15px;">
                                <span id="alertMessage_G">

                                </span>
                            </div>
                            <!-- Fieldset -->
                            <fieldset class="KhungVien border rounded-3 p-3 mb-1">
                                <legend class="float-none w" id="fieldsetLegend_G">
                                    Thông tin
                                </legend>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="NgayGiao_G" id="md_NgayGiao">Ngày giao</label>
                                        <input type="text" class="form-control date-picker" id="NgayGiao_G"
                                            autocomplete="off" data-date-format="dd/mm/yyyy" readonly>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="NhanVienID_G" id="md_NhanVien">Người giao</label>
                                        <select class="form-select" id="NhanVienID_G" required></select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="ChucVuID_G" id="md_ChucVu">Chức vụ</label>
                                        <select class="form-select" id="ChucVuID_G" required></select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <label class="form-label mb-1" for="NoiDungGiao_G" id="md_NoiDungGiao"
                                            style="">Nội dung
                                            giao</label>
                                        <div id="NoiDungGiao_G" class="form-control" contenteditable="true"
                                            style="min-height: 120px;"></div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                </div>
                <!-- Modal Footer -->
                <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                    <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                        <i class="fa fa-times"></i> Đóng (F4)
                    </button>
                    <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnGiaoSoGoc">
                        <i class="fa fa-check"></i> Giao sổ (F9)
                    </button>
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" id="SoGocID" />
    <input type="hidden" id="TrangThaiGiao" />
    <input type="hidden" id="SoGocCTID" />
    @include('layouts.ChonDonVi')
    @include('layouts.ModalPrint_Full')
    @include('layouts.XemDSDinhKem')
@endsection

@push('scripts')
    <script>
        window.Laravel = window.Laravel || {}; // 👈 Bảo vệ trước khi gán
        window.Laravel.local = {
            imgLuoi: `{{ asset('img/img1.png') }}`,
            getListKyThi: `{{ route('dungchung.danhmuc.comboKyThi') }}`,
            getListKhoaThi: `{{ route('dungchung.danhmuc.comboKhoaThi') }}`,
            getListQuyetDinh: `{{ route('dungchung.danhmuc.comboQuyetDinh') }}`,
            getListNam: `{{ route('dungchung.comboNam') }}`,
            GetListDonViCoHocSinh: `{{ route('sogocbansao.GetListDonViCoHocSinh') }}`,
            GetListHocSinhByDonVi: `{{ route('sogocbansao.GetListHocSinhByDonVi') }}`,
            getListXepLoai: "{{ route('dungchung.danhmuc.comboXepLoai') }}",
            luuthongtinct: "{{ route('sogocbansao.luuthongtinct') }}",
            getallct: "{{ route('sogocbansao.getallct') }}",
            xoact: "{{ route('sogocbansao.xoact') }}",
            LayMaTuTang: "{{ route('sogocbansao.LayMaTuTang') }}",
            loaddulieusuact: "{{ route('sogocbansao.loaddulieusuact') }}",
            Update_GiaoThuHoi: "{{ route('sogocbansao.Update_GiaoThuHoi') }}",
            quyetdinhtotnghiep: "{{ route('quyetdinhtotnghiep.loaddulieusua') }}",
            Update_SoVaoSoGocBanSao: `{{ route('sogocbansao.Update_SoVaoSoGocBanSao') }}`,
            XuatExcelSoGoc: `{{ route('sogocbansao.XuatExcelSoGoc') }}`,
            Update_OneColum: `{{ route('dungchung.Update_OneColum') }}`,
            maTuTangUrlSoVaoSoGoc: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'SoVaoSoGocBanSao', 'bangDuLieu' => 'so_goc_c_t_s', 'cotDuLieu' => 'SoVaoSoGocBanSao']) }}`,
            //maTuTangUrlSoHieuVanBang: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'SoHieuVanBang', 'bangDuLieu' => 'so_goc_c_t_s', 'cotDuLieu' => 'SoHieuVanBang']) }}`,
            LayThongTinThongKeXepLoai: `{{ route('sogocbansao.LayThongTinThongKeXepLoai') }}`,
            getListNhanvien: "{{ route('dungchung.danhmuc.comboNhanVien') }}",
        };
    </script>
    <script src="{{ asset('js/quanly/SoGocBanSao.js') }}?v={{ time() }}"></script>
@endpush
