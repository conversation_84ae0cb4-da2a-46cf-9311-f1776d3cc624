@extends('layouts.layouts')

@section('content')
    <style id="DinhKemcss">

    </style>
    <style id="LuoiDScss">
        #KhungTimKiem .fw-bold {
    color: #f76707 !important;
}
        #GridMainDS .avatar {
            width: 60px !important;
            height: 60px !important;
            font-size: 25px !important;
            border-radius: 50%;
        }

        #GridMainDS .tabulator-tableholder .tabulator-table .tabulator-row .tabulator-cell {
            height: auto !important;
        }

        #GridMainDS .tabulator-row {
            border-bottom: 0px !important;
        }

        .row-girdmain {
            margin-bottom: 0px !important;
        }

        .tabulator-cell .dropdown-menu-end {
            position: fixed !important;
        }

        #GridMainDS .alert {
            --tblr-alert-padding-x: 1rem;
            --tblr-alert-padding-y: 0.3rem;
            --tblr-alert-margin-bottom: 0rem;
        }

.nts-table {
    border-collapse: collapse;
    color: #DBDBE1;
    font-family: <PERSON>ibri;
    font-size: 12px;
    font-weight: 400;
    font-style: none;
    text-align: left;
    vertical-align: bottom;
    min-width: 100%;
    margin: 0 auto;
    /* height: 300px !important; */
}
.nts-header-tr {
    background: #f0f5f1;
}
.nts-table-tr {
    height: 35px;
}
.nts-table-tile {
    border-top: thin solid #DBDBE1;
    border-bottom: thin solid #DBDBE1;
    border-left: thin solid #DBDBE1;
    border-right: thin solid #DBDBE1;
    font-family: var(--tblr-font-sans-serif);
    font-weight: 700;
    text-align: center;
    vertical-align: middle;
    font-size: 14px;
}
.nts-table-td {
    border-top: thin solid #DBDBE1;
    border-bottom: thin solid #DBDBE1;
    border-left: thin solid #DBDBE1;
    border-right: thin solid #DBDBE1;
    font-family: var(--tblr-font-sans-serif);
    vertical-align: middle;
    position: relative;
}

.select2-container .select2-selection--single .select2-selection__rendered {
    display: block;
    padding-left: 8px;
    padding-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
}
    </style>
    <style id="Modalcss">
        .modal-footer {
            flex-wrap: unset;
            padding-left: 20px;
        }

        .form-label {
            font-weight: unset;
        }

        #mdThemMoi .modal-dialog {
            min-width: 60% !important;

        }

        .apexcharts-text {
            fill: #ffffff !important;
        }
    </style>

    <div id="DivMain">
        <div id="DivThongKe" class="row" style="margin-bottom:4px">
            <div class="col-md-4 d-flex">
                <div class="card w-100">
                    <div class="card-body d-flex align-items-center justify-content-center">
                        <div id="XepLoaiChart" style="height: 100px; width: 100%;"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-8" style="padding-left:unset; display: flex;">
                <div class="card w-100">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <h3 style="color:darkgreen" class="card-title"><i class="fa fa-th-list"
                                        aria-hidden="true"></i> Tổng số: <b><u><b id="txtTongPhoi">0</b></u> <b> phôi bằng</b> trong
                                        đó:</b></h3>
                            </div>
                        </div>
                        <hr style="margin-top:4px">
                        <div class="row">
                            <div class="col-md-6" style="border-right: 1px solid #07a607">
                                <h3 style="color: #2FB344" class="card-title">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17"
                                        fill="none">
                                        <path
                                            d="M14.5303 3.6184L8.3724 1.56577C8.26706 1.53065 8.15317 1.53065 8.04783 1.56577L1.88993 3.6184C1.78775 3.65246 1.69888 3.71781 1.63591 3.80519C1.57294 3.89257 1.53905 3.99755 1.53906 4.10526V9.23684C1.53906 9.37293 1.59313 9.50346 1.68936 9.59969C1.7856 9.69593 1.91612 9.74999 2.05222 9.74999C2.18832 9.74999 2.31884 9.69593 2.41508 9.59969C2.51131 9.50346 2.56538 9.37293 2.56538 9.23684V4.81726L4.72 5.53504C4.14755 6.45988 3.96551 7.5741 4.21386 8.63304C4.46221 9.69199 5.12064 10.6091 6.04459 11.183C4.88998 11.6358 3.89189 12.455 3.16192 13.5749C3.12396 13.6314 3.09759 13.6948 3.08435 13.7615C3.07111 13.8282 3.07125 13.8969 3.08478 13.9635C3.09832 14.0302 3.12496 14.0934 3.16317 14.1497C3.20137 14.206 3.25038 14.2541 3.30734 14.2912C3.3643 14.3284 3.42808 14.3538 3.49497 14.3661C3.56186 14.3784 3.63052 14.3773 3.69697 14.3628C3.76341 14.3483 3.82632 14.3208 3.88203 14.2818C3.93773 14.2427 3.98513 14.1931 4.02146 14.1356C4.98813 12.6525 6.51477 11.8026 8.21012 11.8026C9.90546 11.8026 11.4321 12.6525 12.3988 14.1356C12.474 14.2474 12.5903 14.3252 12.7224 14.352C12.8546 14.3788 12.9919 14.3526 13.1049 14.2789C13.2178 14.2053 13.2972 14.0901 13.3259 13.9584C13.3546 13.8267 13.3303 13.6889 13.2583 13.5749C12.5283 12.455 11.5264 11.6358 10.3756 11.183C11.2987 10.6091 11.9565 9.69261 12.2048 8.63444C12.4531 7.57627 12.2717 6.46282 11.7002 5.53825L14.5303 4.59532C14.6325 4.56128 14.7214 4.49594 14.7844 4.40856C14.8474 4.32117 14.8813 4.21618 14.8813 4.10846C14.8813 4.00074 14.8474 3.89575 14.7844 3.80837C14.7214 3.72099 14.6325 3.65565 14.5303 3.6216V3.6184ZM11.2891 7.69736C11.2892 8.18413 11.1739 8.66399 10.9527 9.09758C10.7315 9.53117 10.4106 9.90613 10.0164 10.1917C9.62218 10.4772 9.16589 10.6653 8.68495 10.7403C8.20401 10.8154 7.71212 10.7754 7.24964 10.6235C6.78717 10.4717 6.36728 10.2124 6.02444 9.86682C5.6816 9.52127 5.42557 9.09937 5.27736 8.63571C5.12915 8.17206 5.09298 7.67987 5.17182 7.19953C5.25067 6.71919 5.44227 6.26439 5.73092 5.87244L8.04783 6.64218C8.15317 6.6773 8.26706 6.6773 8.3724 6.64218L10.6893 5.87244C11.0791 6.40102 11.2893 7.04059 11.2891 7.69736ZM8.21012 5.61715L3.67508 4.10526L8.21012 2.59336L12.7451 4.10526L8.21012 5.61715Z"
                                            fill="#D63939" />
                                    </svg>
                                    Đã sử dụng: <b><u><b id="txtPhoiDaSuDung">0</b></u></b>
                                </h3>
                                <h3 style="color: #F76707" class="card-title">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="17" viewBox="0 0 20 17"
                                        fill="none">
                                        <g clip-path="url(#clip0_17947_41925)">
                                            <path
                                                d="M11.8433 0.886737L9.79911 0L7.75496 0.886737L5.47933 1.17616L4.44807 2.90037L2.81055 4.25511L3.18533 6.1579L2.81055 8.06068L4.44807 9.41542L5.47933 11.1396L7.75496 11.4291L9.79911 12.3158L11.8433 11.4291L14.1189 11.1396L15.1501 9.41542L16.7877 8.06068L16.4129 6.1579L16.7877 4.25511L15.1501 2.90037L14.1189 1.17616L11.8433 0.886737ZM13.3081 2.11113L14.1458 3.51205L15.4759 4.61226L15.1722 6.1579L15.4759 7.70353L14.1458 8.80374L13.3081 10.2047L11.4587 10.4397L9.79911 11.1602L8.13954 10.4397L6.29013 10.2047L5.45239 8.80374L4.12228 7.70353L4.42725 6.1579L4.12106 4.61226L5.45239 3.51205L6.29013 2.11113L8.13954 1.87611L9.79911 1.15563L11.4599 1.87611L13.3081 2.11113Z"
                                                fill="#9E24E9" />
                                            <path
                                                d="M4.90039 12.1044V16.4211L9.79949 15.3947L14.6986 16.4211V12.1044L12.227 12.4184L9.79949 13.4714L7.37199 12.4184L4.90039 12.1044Z"
                                                fill="#9E24E9" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_17947_41925">
                                                <rect width="19.5964" height="16.4211" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    Chưa sử dụng: <b><u><b id="txtPhoiChuaSuDung">0</b>
                                        </u></b>
                                </h3>
                                <h3 style="color: #EA1818" class="card-title">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="17" viewBox="0 0 20 17"
                                        fill="none">
                                        <g clip-path="url(#clip0_17947_41925)">
                                            <path
                                                d="M11.8433 0.886737L9.79911 0L7.75496 0.886737L5.47933 1.17616L4.44807 2.90037L2.81055 4.25511L3.18533 6.1579L2.81055 8.06068L4.44807 9.41542L5.47933 11.1396L7.75496 11.4291L9.79911 12.3158L11.8433 11.4291L14.1189 11.1396L15.1501 9.41542L16.7877 8.06068L16.4129 6.1579L16.7877 4.25511L15.1501 2.90037L14.1189 1.17616L11.8433 0.886737ZM13.3081 2.11113L14.1458 3.51205L15.4759 4.61226L15.1722 6.1579L15.4759 7.70353L14.1458 8.80374L13.3081 10.2047L11.4587 10.4397L9.79911 11.1602L8.13954 10.4397L6.29013 10.2047L5.45239 8.80374L4.12228 7.70353L4.42725 6.1579L4.12106 4.61226L5.45239 3.51205L6.29013 2.11113L8.13954 1.87611L9.79911 1.15563L11.4599 1.87611L13.3081 2.11113Z"
                                                fill="#9E24E9" />
                                            <path
                                                d="M4.90039 12.1044V16.4211L9.79949 15.3947L14.6986 16.4211V12.1044L12.227 12.4184L9.79949 13.4714L7.37199 12.4184L4.90039 12.1044Z"
                                                fill="#9E24E9" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_17947_41925">
                                                <rect width="19.5964" height="16.4211" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    Hủy bỏ: <b><u><b id="txtPhoiHuyBo">0</b></u></b>
                                </h3>
                            </div>
                            <div class="col-md-6" id="LisdSoLuongPhoi">
                                <h3 style="color: #7AA802" class="card-title">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17"
                                        fill="none">
                                        <path
                                            d="M2.73756 2.05261C1.97809 2.05261 1.36914 2.66156 1.36914 3.42103V10.2631C1.36914 10.6261 1.51331 10.9741 1.76994 11.2308C2.02657 11.4874 2.37463 11.6316 2.73756 11.6316H8.21125V15.0526L10.2639 13L12.3165 15.0526V11.6316H13.6849C14.0479 11.6316 14.3959 11.4874 14.6525 11.2308C14.9092 10.9741 15.0534 10.6261 15.0534 10.2631V3.42103C15.0534 3.05811 14.9092 2.71004 14.6525 2.45341C14.3959 2.19678 14.0479 2.05261 13.6849 2.05261H2.73756ZM8.21125 3.42103L10.2639 4.78945L12.3165 3.42103V5.81577L14.3691 6.84209L12.3165 7.8684V10.2631L10.2639 8.89472L8.21125 10.2631V7.8684L6.15861 6.84209L8.21125 5.81577V3.42103ZM2.73756 3.42103H6.15861V4.78945H2.73756V3.42103ZM2.73756 6.15788H4.79019V7.5263H2.73756V6.15788ZM2.73756 8.89472H6.15861V10.2631H2.73756V8.89472Z"
                                            fill="#7AA802" />
                                    </svg> Phôi THCS: <b><u><b id="txtDaCapBang">0</b></u></b>
                                </h3>
                                <h3 style="color: #F78B2D" class="card-title">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17"
                                        fill="none">
                                        <path
                                            d="M8.21083 13C9.34447 13 10.2635 12.081 10.2635 10.9473C10.2635 9.81371 9.34447 8.89471 8.21083 8.89471C7.0772 8.89471 6.1582 9.81371 6.1582 10.9473C6.1582 12.081 7.0772 13 8.21083 13Z"
                                            stroke="#F78B2D" stroke-width="1.02632" />
                                        <path
                                            d="M6.23612 10.92L5.52454 11.6316L4.51328 12.6011C4.2916 12.8139 4.18076 12.9199 4.14244 13.0102C4.09936 13.1063 4.09372 13.215 4.12663 13.315C4.15954 13.415 4.22863 13.4991 4.32033 13.5508C4.40381 13.598 4.55502 13.6124 4.85607 13.6425C5.02576 13.6589 5.11128 13.6671 5.18244 13.6924C5.25976 13.7193 5.33037 13.7625 5.38941 13.8192C5.44846 13.8759 5.49456 13.9447 5.52454 14.0208C5.55123 14.0892 5.56012 14.1707 5.57723 14.3342C5.60802 14.6229 5.62376 14.7673 5.67302 14.8474C5.78523 15.03 6.02265 15.1019 6.23681 15.0184C6.33055 14.9808 6.44139 14.8747 6.66307 14.6626L8.21144 13.1779L9.75981 14.6626C9.98149 14.8747 10.0923 14.9808 10.1861 15.0184C10.4002 15.1019 10.6377 15.03 10.7499 14.8474C10.7991 14.7673 10.8149 14.6229 10.8457 14.3342C10.8628 14.1707 10.8717 14.0892 10.8983 14.0208C10.9283 13.9447 10.9744 13.8759 11.0335 13.8192C11.0925 13.7625 11.1631 13.7193 11.2404 13.6924C11.3123 13.6671 11.3971 13.6589 11.5668 13.6425C11.8679 13.613 12.0191 13.598 12.1025 13.5508C12.1943 13.4991 12.2633 13.415 12.2963 13.315C12.3292 13.215 12.3235 13.1063 12.2804 13.0102C12.2421 12.9199 12.1313 12.8139 11.9096 12.6011L10.8977 11.6316L10.2641 10.9973"
                                            stroke="#F78B2D" stroke-width="1.02632" />
                                        <path
                                            d="M11.8512 12.313C13.2005 12.2987 13.9531 12.2138 14.4519 11.7144C15.0534 11.1136 15.0534 10.1455 15.0534 8.21051V5.47367C15.0534 3.53872 15.0534 2.57057 14.4519 1.96983C13.8512 1.36841 12.883 1.36841 10.9481 1.36841H5.4744C3.53946 1.36841 2.5713 1.36841 1.97056 1.96983C1.36914 2.57057 1.36914 3.53872 1.36914 5.47367V8.21051C1.36914 10.1455 1.36914 11.1136 1.97056 11.7144C2.49604 12.2405 3.30204 12.3062 4.79019 12.3144"
                                            stroke="#F78B2D" stroke-width="1.02632" />
                                        <path d="M6.15944 4.10522H10.2647M4.79102 6.49996H11.6331" stroke="#F78B2D"
                                            stroke-width="1.02632" stroke-linecap="round" />
                                    </svg> Phôi THPT: <b><u><b id="txtChuaCapBang">0</b></u></b>
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="DivTimKiem" class="mb-2">
            <div class="row flex-row-reverse">
                <div class="col-md-8">
                    <div class="text-end">
                         <div class="btn-group" style="height:28px">
                        <a id="btnAnHienTQ" style="color: #41d81e; margin-top: 0px; padding: 4px; cursor:pointer"><i id="iconAnHien" class="fa fa-eye-slash" aria-hidden="true"></i><span id="textAnHien"> Ẩn
                                trang tổng quan</span></a>
                    </div>
                        <button style="" class="btn position-relative" id="">
                            <select class="form-control" id="CbSapXep" tabindex="0"
                                style="padding: 0.05rem 0.55rem !important;">
                                <option value="MaPhieu">Mã phiếu</option>
                                <option value="NgayNhap">Ngày nhập</option>
                            </select>&ensp;&ensp;
                            <span id="BtnSapXepTangGiam">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="icon icon-tabler icon-tabler-sort-ascending-letters" width="24"
                                    height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                    <path d="M15 10v-5c0 -1.38 .62 -2 2 -2s2 .62 2 2v5m0 -3h-4" />
                                    <path d="M19 21h-4l4 -7h-4" />
                                    <path d="M4 15l3 3l3 -3" />
                                    <path d="M7 6v12" />
                                </svg>
                            </span>
                        </button>
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check height-button-icon" name="btn-layout" id="btn-layout-1"
                                autocomplete="off" checked="">
                            <label style="margin-bottom: 0px" for="btn-layout-1" class="btn btn-icon">
                                <svg class="icon icon-tabler icon-tabler-list" width="20" height="20"
                                    viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M9 6l11 0"></path>
                                    <path d="M9 12l11 0"></path>
                                    <path d="M9 18l11 0"></path>
                                    <path d="M5 6l0 .01"></path>
                                    <path d="M5 12l0 .01"></path>
                                    <path d="M5 18l0 .01"></path>
                                </svg>
                            </label>
                            <input type="radio" class="btn-check height-button-icon" name="btn-layout" id="btn-layout-2"
                                autocomplete="off">
                            <label style="margin-bottom: 0px" for="btn-layout-2" class="btn btn-icon">
                                <svg class="icon icon-tabler icon-tabler-list-details" width="20" height="20"
                                    viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M13 5h8"></path>
                                    <path d="M13 9h5"></path>
                                    <path d="M13 15h8"></path>
                                    <path d="M13 19h5"></path>
                                    <path d="M3 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z">
                                    </path>
                                    <path d="M3 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z">
                                    </path>
                                </svg>
                            </label>
                        </div>
                        <button type="button" class="nts-color-them btn btn-primary" id="btnThemMoi"><i
                                class="fa fa-plus"></i>&ensp;Thêm mới
                            (F2)</button>
                        <div class="btn-group">
                            <div class="dropdown d-inline">
                                <button class="btn btn-primary dropdown-toggle-hide-arrow height-button-icon" type="button"
                                    id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true"
                                    aria-expanded="false">
                                    <i class="blue fa fa-ellipsis-h"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-lg-end w-auto">
                                    <a class="nts-color-in dropdown-item textsize-item" href="javascript:void(0);"
                                        id="btnPrint" onclick="previewExportv1('pdf',GridMainLuoi);return false;"><i
                                            class="text-warning fa fa-print iconsize-item"></i>&ensp; In</a>
                                    <a class="nts-color-excel dropdown-item textsize-item" href="javascript:void(0);"
                                        id="btnExport" onclick="previewExportv1('excel',GridMainLuoi);return false;"><i
                                            class="text-success fa fa-file-excel-o iconsize-item"></i>&ensp; Xuất
                                        Excel</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div style="display: flex; align-items: center" class="DivTimKiem">
                        <div class="input-icon">
                            <input type="text" value="" class="form-control"
                                placeholder="Nội dung tìm kiếm ..." id="SearchKey" autocomplete="off"
                                style="border-top-right-radius: 0px; border-bottom-right-radius: 0px; ">
                            <span class="input-icon-addon">
                                <i class="fa fa-search"></i>
                            </span>
                        </div>
                        <button id="TimKiemNangCao" class="btn btn-primary height-button-icon" type="button"
                            style="border-top-left-radius: 0px;border-bottom-left-radius:0px;">
                            <span class="fa fa-sliders" aria-hidden="true"> </span>
                        </button>
                    </div>
                    <div id="KhungTimKiem" class="bg-white mt-1 px-4 py-2 border rounded shadow position-absolute"
                        style="z-index: 99; width:380px ; display:none">
                        <div class="row">
                            <div class="col text-primary fw-bold">
                                <i class="fa fa-search"></i> Tìm kiếm nâng cao
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="TuNgay_Loc">Từ ngày</label>
                                <input type="text" class="form-control date-picker" id="TuNgay_Loc"
                                    autocomplete="off" data-date-format="dd/mm/yyyy">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="DenNgay_Loc">Đến ngày</label>
                                <input type="text" class="form-control date-picker" id="DenNgay_Loc"
                                    autocomplete="off" data-date-format="dd/mm/yyyy">
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6" style="display:none">
                                <label class="form-label" for="CapHocID_Loc">Cấp học</label>
                                <select class="form-control input-sm" id="CapHocID_Loc" tabindex="0">
                                </select>
                            </div>
                            <div class="col-md-12">
                                <label class="form-label" for="LoaiPhoiVanBangChungChiID_Loc">Loại văn bằng, chứng chỉ</label>
                                <select class="form-control input-sm" id="LoaiPhoiVanBangChungChiID_Loc" tabindex="0">
                                </select>
                            </div>
                        </div>

                          <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label" for="CapHocID_Loc">Đơn vị nhận</label>
                                <select class="form-control input-sm" id="DonViNhanID_Loc" tabindex="0">
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label" for="CapHocID_Loc">Trạng thái</label>
                                <select class="form-control input-sm" id="TrangThai_Loc" tabindex="0">
                                    <option value="">(Tất cả)</option>
                                    <option value="0">Chưa duyệt</option>
                                    <option value="32">Đã duyệt</option>
                                    <option value="33">Thu hồi phê duyệt</option>
                                </select>
                            </div>
                        </div>
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group" style="text-align:right">
                                    <button type="button" id="DongTimKiem" class="btn btn-outline-danger"><i
                                            class="fa fa-times"></i>&ensp;Đóng</button>&ensp;
                                    <button type="button" id="TimKiem" class="btn nts-color-them ms-auto"><i
                                            class="fa fa-search"></i>&ensp;Tìm kiếm</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="DivDanhSach">
            <div class="row">
                <div class="col-md-12">
                    <div id="GridMainDS">
                    </div>
                </div>
            </div>
        </div>
        <div id="DivLuoi" style="display:none">
            <div class="row">
                <div class="col-md-12">
                    <div id="GridMainLuoi" class="GridData">

                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Create Modal --}}
    <div class="modal modal-blur fade" id="mdThemMoi" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document" style="">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tieuDeModal"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <fieldset class="KhungVien">
                        <legend>Thông tin nhập phiếu</legend>
                        <div class="row" style="margin: 5px;">
                            <div class="col-md-12" style="padding-left: 10px">
                                <div class="row">
                                    <div class="col-lg-3">
                                        <div class="mb-1">
                                            <label class="form-label" for="MaPhieu">Mã phiếu</label>
                                            <input type="text" class="form-control" id="MaPhieu" required>
                                        </div>
                                    </div>
                                      <div class="col-lg-3">
                                        <div class="mb-1">
                                            <label class="form-label" for="NgayNhap">Ngày nhập</label>
                                            <input type="text" class="form-control date-picker" id="NgayNhap"
                                                autocomplete="off" data-date-format="dd/mm/yyyy" required>
                                        </div>
                                    </div>
                                         <div class="col-lg-3">
                                        <div class="mb-1">
                                            <label class="form-label" for="NhanVienID_Nhap">Người nhập</label>
                                            <select class="form-control" id="NhanVienID_Nhap" tabindex="0">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="mb-1">
                                            <label class="form-label" for="ChucVuID">Chức vụ</label>
                                             <select class="form-control" id="ChucVuID" tabindex="0">
                                            </select>
                                        </div>
                                    </div>
                                       <div class="col-lg-4" style="display: none;">
                                        <div class="mb-1">
                                            <label class="form-label" for="CapHocID">Cấp học</label>
                                            <select class="form-control" id="CapHocID" tabindex="0" >
                                            </select>
                                        </div>
                                    </div>
                                </div>



                                
                                <div class="row">
                                    <div class="col-lg-12">
                                            <div class="d-flex align-items-center mb-2" style="gap: 6px;">
                                            <label style="font-size: 1rem; margin: 0;">Phôi văn bằng, chứng chỉ </label>
                                            <i class="fa fa-plus-circle green" onclick="ThemDieuKienXepLoai(); return false;" style="color: #56bf35;font-size: 18px "></i>
                                            <hr class="flex-grow-1" style="background-color: #4265B6; height: 2px; border: none; margin: 0 0 0 12px;">
                                        </div>

                                    </div>
                                    

                                     <div class="col-lg-12">
                                    <table cellpadding="0" cellspacing="0" class="nts-table">
                                        <colgroup>
                                            <col style="width: 10%">
                                            <col style="width: 30%">
                                            <col style="width: 15%">
                                            <col style="width: 15%">
                                             <col style="width: 15%">
                                              <col style="width: 15%">
                                        </colgroup>
                                        <thead style="margin-bottom:4px">
                                            <tr class="nts-table-tr nts-header-tr">
                                                <td class="nts-table-tile" rowspan="1" colspan="0"><label class="nts-table-tile-font-size"><i class="fa fa-ellipsis-h nts-color-NTS-16a085"></i></label></td>
                                                <td class="nts-table-tile" rowspan="1" colspan="0"><label class="nts-table-tile-font-size">Tên phôi văn bằng, chứng chỉ</label></td>
                                                <td class="nts-table-tile" rowspan="1" colspan="0"><label class="nts-table-tile-font-size">Đơn vị tính</label></td>
                                                <td class="nts-table-tile" rowspan="1" colspan="0"><label class="nts-table-tile-font-size">Số lượng đề nghị</label></td>
                                                <td class="nts-table-tile" rowspan="1" colspan="0"><label class="nts-table-tile-font-size">Số hiệu từ</label></td>
                                                <td class="nts-table-tile" rowspan="1" colspan="0"><label class="nts-table-tile-font-size">Số hiệu đến</label></td>
                                            </tr>
                                        </thead>
                                        <tbody id="tbodyKetQuaLoaiVBCC"> 
                                        </tbody>
                                    </table>
                                        <div class="mb-1" style="display: none;">
                                            <label class="form-label" for="LoaiPhoiVanBangChungChiID">Loại văn bằng, chứng chỉ</label>
                                            <select class="form-control" id="LoaiPhoiVanBangChungChiID" tabindex="0">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" for="DonViID_Cap">Đơn vị cấp</label>
                                             <select class="form-control" id="DonViID_Cap" tabindex="0"  menu-width="150%"></select>
                                        </div>
                                    </div>
                                     <div class="col-lg-8">
                                        <div class="mb-1">
                                            <label class="form-label" for="DonViID_Nhap">Đơn vị nhận</label>
                                                <select class="form-control" id="DonViID_Nhap" tabindex="0" required></select>
                                        </div>
                                    </div>
                                </div>
                                  <div class="row">
                               
                                    <div class="col-lg-12">
                                        <div class="mb-1">
                                            <label class="form-label" for="DonViID_YeuCau">Đơn vị yêu cầu cấp phát phôi bằng</label>
                                                 <select class="form-control" id="DonViID_YeuCau" tabindex="0" ></select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="display: none;">
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" for="SoLuongNhap">Số lượng</label>
                                            <input type="number" class="form-control " id="SoLuongNhap"style="text-align: end"
                                                >
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" for="SoHieuTu">Số hiệu từ</label>
                                            <input type="text" class="form-control " id="SoHieuTu"
                                                >
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="mb-1">
                                            <label class="form-label" for="SoHieuDen">Số hiệu đến</label>
                                            <input type="text" class="form-control " id="SoHieuDen"
                                                >
                                        </div>
                                    </div>
                                </div>
                              
                                <div class="row">
                               
                                    <div class="col-lg-12">
                                        <div class="mb-1">
                                            <label class="form-label" for="SoHieuDen">Ghi chú</label>
                                              <textarea class="form-control" id="GhiChu" rows="4"></textarea>
                                        </div>
                                    </div>
                                </div>
                            
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <label class="form-label mb-2">Đính kèm</label>
                                        <div id="drop-area"
                                            class="drop-area d-flex align-items-center p-3 rounded shadow-sm"
                                            style="background: #f5f8fa; gap: 16px; border: 1px dashed #b3c0ce; cursor: pointer; transition: box-shadow 0.2s;">
                                            <div class="me-3 text-center d-flex flex-column align-items-center"
                                                style="min-width:50px;">
                                                <i class="fa fa-cloud-upload"
                                                    style="font-size: 36px; color: #5a8dee;"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div style="font-size: 15px; color: #444; font-weight: 500;">Kéo thả
                                                    file vào đây hoặc chọn file từ thiết bị</div>
                                                <div style="font-size: 13px; color: #888;">Hỗ trợ: pdf, doc, docx, xlsx,
                                                    xls. Tối đa 10MB</div>
                                            </div>
                                            <div class="d-flex flex-column gap-2">
                                                <button id="btnChonTepVB_us" type="button"
                                                    class="btn btn-primary btn-sm d-flex align-items-center justify-content-center mb-1"
                                                    style="min-width:110px;">
                                                    <i class="fa fa-upload me-2"></i> Chọn file
                                                </button>
                                                <button id="btnXoaHetTepVB_us" type="button"
                                                    class="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center"
                                                    style="min-width:110px;">
                                                    <i class="fa fa-trash-o me-2"></i> Xóa tất cả
                                                </button>
                                            </div>
                                        </div>
                                        <input type="hidden" id="txtDuongDanFileVB" value="">
                                        <input type="file" id="fileVB_us" style="display: none;"
                                            accept=".pdf,.doc,.docx,.xlsx,.xls" multiple>
                                        <div id="list-file-v-b" class="mt-2"></div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="form-group">
                                        <label class="col-md-2 control-label no-padding-right"></label>
                                        <div class="col-md-10" id="list-file">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="modal-footer">
                    <div style="display: contents; align-items: center; justify-content: space-between">
                        <div class="col-md-6" style="display: flex; align-items: center">
                        </div>
                        <div class="col-md-6">
                            <div style="float:right;text-align: right">
                                <a href="#" class="nts-color-dong btn btn-outline-danger" data-bs-dismiss="modal">
                                    <i class="fa fa-close"></i>&nbsp;Đóng (F4)
                                </a>
                                <a href="#" id="btnLuuVaDong" class="nts-color-luu btn btn-success ms-auto">
                                    <i class="fa fa-save"></i>&ensp;Lưu và đóng (F9)
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Create Modal --}}
<div class="modal fade" id="mdQPheDuyet" tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document" style=" width: 52%; ">
        <div class="modal-content p-0 rounded-3 overflow-hidden">
            <!-- Modal Header -->
            <div class="modal-header py-2 px-3" style="background:#fd7e14;color:#fff;">
                <h5 class="modal-title fw-bold text-uppercase" id="tieuDe_mdQPheDuyet">Phê duyệt tiếp nhập kho phôi bằng
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <!-- Modal Body -->
            <div class="modal-body px-4 py-3">
                <div class="row">
                    <!-- Illustration -->
                    <div class="col-md-5 d-flex align-items-center justify-content-center mb-2 mb-md-0">
                        <img src="/img/Logo-note2.jpeg" alt="Quyết định" class="img-fluid" style="max-width:350px;">
                    </div>
                    <!-- Right Side: Content -->
                    <div class="col-md-7">
                        <!-- Alert/Instruction -->
                        <div class="border border-warning rounded mb-3 px-3 py-2"
                            style="background:#fff7e6;font-size:15px;">
                            <span id="alertMessage">
                                Bạn đang thực hiện phê duyệt nhận phôi văn bằng, chứng chỉ cho <b id="DonViNhan"></b>
                            </span>
                            <span id="alertMessage">
                                Bạn đang thực hiện phê duyệt tiếp nhận phôi văn bằng, chứng chỉ số: <b id="SoDeNghiPD"></b> ngày lập <b id="NgayDeNghiPD"></b> của <b id="DonViDeNghiPD"></b>. 
                                Vui lòng điền đầy đủ thông tin bên dưới và nhấn vào nút <b>"Phê duyệt"</b> để thực hiện thao tác phê duyệt tiếp nhận phôi văn bằng, chứng chỉ.
                            </span>
                        </div>
                        <!-- Fieldset -->
                        <fieldset class="KhungVien border rounded-3 p-3 mb-1">
                            <legend class="float-none w" id="fieldsetLegend">
                                Thông tin phê duyệt
                            </legend>
                            <form>
                                <div class="row g-2">
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label mb-1" for="NgayPheDuyet" style="">Ngày phê duyệt
                                            <span class="text-danger">(*)</span></label>
                                        <div class="input-group input-group-sm">
                                            <input type="text" class="form-control date-picker" id="NgayPheDuyet"
                                                name="NgayKy" autocomplete="off" data-date-format="dd/mm/yyyy"
                                                placeholder="dd/MM/yyyy" required>

                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6"></div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label  mb-1" for="NhanVienID_PheDuyet" style="">Người phê duyệt</label>
                                        <select class="form-select" id="NhanVienID_PheDuyet">
                                            <option>-Chọn-</option>
                                        </select>
                                    </div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label  mb-1" for="ChucVuID_PheDuyet" style="">Chức
                                            vụ</label>
                                        <select class="form-select" id="ChucVuID_PheDuyet">
                                            <option>-Chọn-</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label mb-1" for="NoiDung_PheDuyet" style="">Nội dung phê duyệt</label>
                                        <div id="NoiDung_PheDuyet" class="form-control" contenteditable="true"
                                            style=" min-height: 120px;"></div>
                                    </div>
                                </div>
                            </form>
                        </fieldset>
                    </div>
                </div>
            </div>
            <!-- Modal Footer -->
            <div class="modal-footer justify-content-end py-2 px-3" style="background:#f4f4f4;">
                <button type="button" class="btn btn-outline-danger fw-bold px-3 me-2" data-bs-dismiss="modal">
                    <i class="fa fa-times"></i>&nbsp; Đóng (F4)
                </button>
                <button type="button" class="btn btn-warning text-white fw-bold px-3" id="btnBanHanh">
                    <i class="fa fa-check"></i>&nbsp; Phê duyệt (F9)
                </button>
            </div>
        </div>
    </div>
</div>
    <input type="hidden" id="TiepNhanPhoiVBCCID" />
@endsection
@include('layouts.XemDSDinhKem')
@push('scripts')
    <script>
        window.Laravel = window.Laravel || {}; // 👈 Bảo vệ trước khi gán
        window.Laravel.local = {
            uploadFile: "{{ route('api.files.uploadFileTemp') }}",
            imgLuoi: `{{ asset('img/imgBienBanKho.png') }}`,
            maTuTangUrl: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'SoHieuVanBang', 'bangDuLieu' => 'tiep_nhan_phoi_v_b_c_c_s', 'cotDuLieu' => 'SoHieuDen']) }}`,
            soPhieuTuTangUrl: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'MaPhieuNhapKhoPhoiBang', 'bangDuLieu' => 'tiep_nhan_phoi_v_b_c_c_s', 'cotDuLieu' => 'MaPhieu']) }}`,
            comboNhanVien: "{{ route('dungchung.danhmuc.comboNhanVien') }}",
            getcaphoc: "{{ route('caphoc.ldb.getcaphoc') }}",
            getloaiphoivanbangchungchi: "{{ route('loaiphoivanbangchungchi.getloaiphoivanbangchungchi') }}",
            getMauVanBangChungChiByLoai: "{{ route('tiepnhanphoivbcc.getMauVanBangChungChiByLoai') }}",
            getListDonVi: `{{{ route('dungchung.danhmuc.comboDonVi') }}}`,
            listChucVuUrl: "{{ route('dungchung.danhmuc.comboChucVu') }}",
            getAlldonxincapphoibang: "{{ route('tiepnhanphoivbcc.getAlldonxincapphoibang') }}",
            getAllthongkevanbangdasudung: "{{ route('tiepnhanphoivbcc.getAllthongkevanbangdasudung') }}",
            getListDonViTinh: `{{{ route('dungchung.danhmuc.comboDonViTinh') }}}`,
        };
    </script>
    <script src="{{ asset('js/quanly/TiepNhanPhoiVBCC.js') }}?v={{ time() }}"></script>
@endpush
