@extends('layouts.layouts')

{{-- tiêu đề trang --}}
@section('title', '<PERSON><PERSON> sách hội đồng')

@section('content')
    <style>
        .card-header {
            background-color: #b9bdba !important;
        }

        #hoiDongTabs .nav-item>.nav-link.active {
            background-color: #fff !important;
        }

        .modal-xl .modal-body .card-body {
            overflow-y: auto;
        }
    </style>
    <div class="row flex-row-reverse">
        <div class="col-md-8">
            <div class="text-end">
                <button type="button" class="btn btn-primary nts-color-them" id="btnThemMoi"><i
                        class="fa fa-plus"></i>&ensp;Thêm mới
                    (F2)</button>
                <div class="btn-group">
                    <div class="dropdown d-inline">
                        <button class="nts-color-them btn btn-primary dropdown-toggle-hide-arrow height-button-icon"
                            type="button" id="growthReportId" data-bs-toggle="dropdown" aria-haspopup="true"
                            aria-expanded="false">
                            <i class="blue fa fa-ellipsis-h"></i>
                        </button>
                        <div class="dropdown-menu dropdown-menu-lg-end w-auto nts-border-secondary">
                            <a class="nts-color-in btntienich dropdown-item textsize-item" href="javascript:void(0);"
                                id="btnPrint" onclick="previewExportv1('pdf',table);return false;"><i
                                    class="text-warning fa fa-print iconsize-item"></i>&ensp; In</a>
                            <a class="nts-color-excel btntienich dropdown-item textsize-item" href="javascript:void(0);"
                                id="btnExport" onclick="previewExportv1('excel',table);return false;"><i
                                    class="text-success fa fa-file-excel-o iconsize-item"></i>&ensp; Xuất
                                Excel</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="input-icon mb-2">
                <input type="text" value="" class="form-control nts-border-secondary" placeholder="Nội dung tìm kiếm"
                    id="timKiem" autocomplete="off">
                <span class="input-icon-addon">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon nts-secondary" width="24" height="24"
                        viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <circle cx="10" cy="10" r="7"></circle>
                        <line x1="21" y1="21" x2="15" y2="15"></line>
                    </svg>
                </span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div id="Grid1"></div>
        </div>
    </div>


    <!-- #region MODALS -->
    {{-- Modal thêm/sửa --}}
    <div class="modal modal-blur fade" id="mdThemMoi" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header nts-bg-header-primary">
                    <h5 class="modal-title" id="tieuDeModal"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="card">
                        <div class="card-header">
                            <ul id="hoiDongTabs" class="nav nav-tabs card-header-tabs" data-bs-toggle="tabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a href="#tab-thongtin" class="nav-link active" data-bs-toggle="tab" role="tab"
                                        aria-controls="tab-thongtin" aria-selected="true" id="tab-thongtin-btn">
                                        <i class="fa fa-info-circle me-1"></i>
                                        Thông tin hội đồng
                                    </a>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <a href="#tab-thanhphan" class="nav-link" data-bs-toggle="tab" role="tab"
                                        aria-controls="tab-thanhphan" aria-selected="false" id="tab-thanhphan-btn">
                                        <i class="fa fa-users me-1"></i>
                                        Thông tin thành viên
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <!-- Tab panes -->
                            <div class="tab-content pt-3">
                                <!-- Thông tin tab -->
                                <div class="tab-pane active show" id="tab-thongtin" role="tabpanel"
                                    aria-labelledby="tab-thongtin-btn">
                                    <!-- ... All your content for Thông tin hội đồng stays unchanged ... -->
                                    <form id="formHoiDong">
                                        <div class="row mb-3">
                                            <div class="col-12">
                                                <label for="tenHoiDong" class="form-label">Tên hội đồng </label>
                                                <input type="text" class="form-control" id="tenHoiDong" name="tenHoiDong"
                                                    required>
                                            </div>

                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="soQD" class="form-label">Số quyết định thành lập</label>
                                                <input type="text" class="form-control" id="soQD" name="soQD" required>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="ngayKy" class="form-label">Ngày ký</label>
                                                <input type="text" class="form-control date-picker" id="ngayKy"
                                                    name="ngayKy" autocomplete="off" data-date-format="dd/mm/yyyy"
                                                    placeholder="dd/MM/yyyy">
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="trichYeu" class="form-label">Trích yếu</label>
                                            <textarea class="form-control" id="trichYeu" name="trichYeu"
                                                rows="3"></textarea>
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-12">
                                                <label for="coQuanBanHanh" class="form-label">Cơ quan ban hành</label>
                                                <input type="text" class="form-control" id="coQuanBanHanh"
                                                    name="coQuanBanHanh">
                                            </div>

                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="nguoiKy" class="form-label">Người ký</label>
                                                <input type="text" class="form-control" id="nguoiKy" name="nguoiKy">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="chucDanhNguoiKy" class="form-label">Chức danh người ký</label>
                                                <input type="text" class="form-control" id="chucDanhNguoiKy"
                                                    name="chucDanhNguoiKy">
                                            </div>

                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-12">
                                                @include('partials.dinh-kem', [
                                                    'id' => 'dinhKem'
                                                ])

                                            </div>

                                        </div>

                                        <div class="row mb-3">
                                            <div class="form-group">
                                                <label class="col-md-2 control-label no-padding-right"></label>
                                                <div class="col-md-10" id="list-file">
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>

                                <!-- Thành phần tab -->
                                <div class="tab-pane" id="tab-thanhphan" role="tabpanel" aria-labelledby="tab-thanhphan-btn"
                                    style="max-height:550px">
                                    <!-- ... All your content for Thành phần hội đồng stays unchanged ... -->
                                    <div class="d-flex justify-content-between mb-2">
                                        <div class="input-icon mb-2" style="max-width: 50%;">
                                            <input type="text" value="" class="form-control" placeholder="Tìm kiếm ..."
                                                id="searchThanhVien" autocomplete="off">
                                            <span class="input-icon-addon">
                                                <!-- Tabler uses Lucide icons by default, but you can keep the SVG here if needed -->
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24"
                                                    viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                                    stroke-linecap="round" stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                    <circle cx="10" cy="10" r="7"></circle>
                                                    <line x1="21" y1="21" x2="15" y2="15"></line>
                                                </svg>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <div id="grid_thanhphan"></div>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>

                </div>
                <div class="modal-footer">
                    <div style="display: contents; align-items: center; justify-content: space-between">
                        <div class="col-md-6" style="display: flex; align-items: center">
                            <label style="margin-bottom: unset" class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="trangThai">
                                <label class="form-check-label" for="trangThai">Ngưng hoạt động</label>
                            </label>
                        </div>
                        <div class="col-md-6">
                            <div style="float:right;text-align: right">
                                <a href="#" class="btn nts-color-dong btn-outline-danger" data-bs-dismiss="modal">
                                    <i class="fa fa-close"></i>&nbsp;Đóng (F4)
                                </a>
                                <a href="#" id="btnLuuVaDong" class="btn btn-success ms-auto nts-color-luu">
                                    <i class="fa fa-save"></i>&ensp;Lưu và đóng (F9)
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('partials.nhapExcelModal', [
        'id' => 'mdNhanExcel',
        'title' => 'Nhận excel nhân viên',
    ])

@endsection
@push('scripts')
    <script>
          window.Laravel = window.Laravel || {};
          window.Laravel = {
                ...window.Laravel,
                hoiDongIndex: "{{ route('hoidong.index') }}",
                hoiDongGetAll: "{{ route('hoidong.getall') }}",
                hoiDongGetAllCT: "{{ route('hoidong.getAllCT') }}",
                hoiDongGetAllNamXet: "{{ route('hoidong.getAllNamXet') }}",
                hoiDongLoad: "{{ route('hoidong.loaddulieusua') }}",
                hoiDongLoadCT: "{{ route('hoidong.loadCT') }}",
                hoiDongExists: "{{ route('hoidong.exists') }}",
                hoiDongSave: "{{ route('hoidong.luuthongtin') }}",
                hoiDongSaveCT: "{{ route('hoidong.saveCT') }}",
                hoiDongDelete: "{{ route('hoidong.xoa') }}",
                hoiDongDeletePost: "{{ route('hoidong.xoa.post') }}",
            hoiDongDeleteCT: "{{ route('hoidong.deleteCT') }}",
            hoiDongChucVus: "{{ route('dungchung.danhmuc.comboChucVu') }}",
            hoiDongAutoComplete: "{{ route('hoidong.autocompleteChucVu') }}",
            uploadFile: "{{ route('api.files.upload') }}",
            maTuTangUrl: `{{ route(
            'dungchung.lay-ma-tutang',
            [
                'kyhieuLoaiPhieu' => 'HoiDong_SoQDThanhlap',
                'bangDuLieu' => 'hoi_dongs',
                'cotDuLieu' => 'SoQDThanhLap'
            ]
        ) }}`,
        };
    </script>
    <script src="{{ asset('js/danhmuc/hoidong.js') }}?v={{ time() }}"></script>
@endpush