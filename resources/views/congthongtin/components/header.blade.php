<!-- Font Awesome CDN -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<!-- Skip Link for Accessibility -->
{{-- <a href="#main-content" class="skip-link">Bỏ qua đến nội dung chính</a> --}}

<header id="header" class="header seosona-header">
    <div class="header-wrapper">
        <!-- Top Bar -->
        <div class="header-top">
            <div class="container">
                <div class="flex-row">
                    <!-- Contact Info -->
                    <div class="flex-col flex-left">
                        <ul class="nav nav-left">
                            @if (isset($thietLapWebsite) && $thietLapWebsite)
                                <li>
                                    <a
                                        href="tel:{{ str_replace(['(', ')', ' ', '-'], '', $thietLapWebsite->SoDienThoai ?? '') }}">
                                        <i class="fas fa-phone"></i>
                                        {{ $thietLapWebsite->SoDienThoai ?? '(0270) 3 843 058' }}
                                    </a>
                                </li>
                                <li>
                                    <a href="mailto:{{ $thietLapWebsite->Email ?? '<EMAIL>' }}">
                                        <i class="fas fa-envelope"></i>
                                        {{ $thietLapWebsite->Email ?? '<EMAIL>' }}
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <i class="fas fa-map-marker-alt"></i>
                                        {{ $thietLapWebsite->DiaChi ?? 'Số H25 đường Phan Văn Đáng, Phường 8, Thành phố Vĩnh Long, Tỉnh Vĩnh Long' }}
                                    </a>
                                </li>
                            @else
                                <li>
                                    <a href="tel:02703843058">
                                        <i class="fas fa-phone"></i>
                                        (0270) 3 843 058
                                    </a>
                                </li>
                                <li>
                                    <a href="mailto:<EMAIL>">
                                        <i class="fas fa-envelope"></i>
                                        <EMAIL>
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <i class="fas fa-map-marker-alt"></i>
                                        Số H25 đường Phan Văn Đáng, Phường 8, Thành phố Vĩnh Long, Tỉnh Vĩnh Long
                                    </a>
                                </li>
                            @endif
                        </ul>
                    </div>

                    <!-- Social Icons -->
                    <div class="flex-col flex-right">
                        <div class="social-icons">
                            @if (isset($thietLapWebsite) && $thietLapWebsite)
                                @if ($thietLapWebsite->Facebook && $thietLapWebsite->Facebook !== '#')
                                    <a href="{{ $thietLapWebsite->Facebook }}" aria-label="Facebook" target="_blank">
                                        <i class="fab fa-facebook-f"></i>
                                    </a>
                                @endif
                                @if ($thietLapWebsite->Youtube && $thietLapWebsite->Youtube !== '#')
                                    <a href="{{ $thietLapWebsite->Youtube }}" aria-label="Youtube" target="_blank">
                                        <i class="fab fa-youtube"></i>
                                    </a>
                                @endif
                                @if ($thietLapWebsite->Website && $thietLapWebsite->Website !== '#')
                                    <a href="{{ $thietLapWebsite->Website }}" aria-label="Website" target="_blank">
                                        <i class="fas fa-globe"></i>
                                    </a>
                                @endif
                                @if ($thietLapWebsite->Email)
                                    <a href="mailto:{{ $thietLapWebsite->Email }}" aria-label="Email">
                                        <i class="fas fa-envelope"></i>
                                    </a>
                                @endif
                            @else
                                <a href="#" aria-label="Facebook">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="#" aria-label="Instagram">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                <a href="#" aria-label="Twitter">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" aria-label="Email">
                                    <i class="fas fa-envelope"></i>
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Header -->
        <div class="header-main">
            <div class="header-inner flex-row container" role="navigation">
                <!-- Logo -->
                <div id="logo" class="flex-col logo editable-component" data-component-type="logo">
                    <a href="{{ url('/') }}" title="{{ $thietLapWebsite->TenDonVi ?? 'Website' }}" rel="home">
                        @php
                            $headerLogoUrl = $thietLapWebsite->LogoUrl ?? asset('assets/congthongtin/Seosona-Logo.png');
                            // Add timestamp to prevent cache if it's not a default asset
                            if (!str_contains($headerLogoUrl, 'assets/congthongtin/Seosona-Logo.png')) {
                                $headerLogoUrl .= '?v=' . time();
                            }
                        @endphp
                        <img src="{{ $headerLogoUrl }}" class="header-logo"
                            alt="{{ $thietLapWebsite->TenDonVi ?? 'Logo' }}"
                            data-debug-logo-url="{{ $thietLapWebsite->LogoUrl ?? 'null' }}">
                    </a>
                </div>

                <!-- Navigation Menu -->
                <div class="flex-col hide-for-medium flex-center flex-grow">
                    <ul class="header-nav header-nav-main nav nav-center">
                        <li class="menu-item {{ request()->is('/') ? 'active' : '' }}">
                            <a href="{{ url('/') }}" class="nav-top-link">Trang chủ</a>
                        </li>
                        <li class="menu-item has-dropdown">
                            <a href="/gioi-thieu" class="nav-top-link" aria-expanded="false" aria-haspopup="menu">
                                Giới thiệu
                            </a>
                        </li>
                        <li class="menu-item has-dropdown {{ request()->is('tin-tuc*') ? 'active' : '' }}">
                            <a href="{{ route('news.index') }}" class="nav-top-link" aria-expanded="false"
                                aria-haspopup="menu">
                                Tin tức <i class="fas fa-chevron-down"></i>
                            </a>
                            <ul class="sub-menu nav-dropdown">

                                @if (isset($loaiTinTucsMenu) && $loaiTinTucsMenu->count() > 0)
                                    @foreach ($loaiTinTucsMenu as $loaiTinTuc)
                                        <li>
                                            <a href="{{ route('news.index', ['loai' => $loaiTinTuc->DinhDanh]) }}">
                                                {{ $loaiTinTuc->TenLoaiTinTuc }}
                                            </a>
                                        </li>
                                    @endforeach
                                @else
                                    {{-- Fallback nếu không có dữ liệu --}}
                                    <li><a href="{{ route('news.index') }}">Tất cả tin tức</a></li>
                                @endif
                            </ul>
                        </li>
                        <li class="menu-item has-dropdown">
                            <a href="/tra-cuu" class="nav-top-link" aria-expanded="false" aria-haspopup="menu">
                                Tra cứu
                            </a>
                        </li>
                        <li class="menu-item has-dropdown">
                            <a href="/lien-he" class="nav-top-link" aria-expanded="false" aria-haspopup="menu">
                                Liên hệ
                            </a>
                        </li>

                    </ul>
                </div>
                <div class="flex-col hide-for-medium flex-right">
                    @auth
                        <a href="{{ url('/quanly/tongquan') }}" class="nav-top-link login-btn">
                            <img src="{{ asset('img/logo.png') }}" alt="Logo" class="login-icon">
                            Đăng nhập
                        </a>
                    @else
                        <a href="{{ route('login') }}" class="nav-top-link login-btn">
                            <img src="{{ asset('img/logo.png') }}" alt="Logo" class="login-icon">
                            Đăng nhập
                        </a>
                    @endauth
                </div>

                <!-- Mobile Menu Button -->
                <div class="flex-col show-for-medium flex-right">
                    <ul class="mobile-nav nav nav-right">
                        <li class="nav-icon has-icon">
                            <div class="header-button">
                                <a href="#" data-open="#main-menu" data-pos="right"
                                    class="icon primary button circle is-small" aria-label="Menu"
                                    aria-controls="main-menu" aria-expanded="false">
                                    <i class="fas fa-bars"></i>
                                </a>
                            </div>
                        </li>
                    </ul>
                </div>

            </div>

        </div>

        <div class="header-bg-container fill">
            <div class="header-bg-image fill"></div>
            <div class="header-bg-color fill"></div>
        </div>
    </div>
</header>

<!-- Mobile Menu Overlay -->
<div class="mobile-menu-overlay"></div>

<!-- Mobile Menu -->
<div id="main-menu" class="mobile-menu">
    <div class="mobile-menu-header">
        <div class="mobile-menu-logo">
            @php
                $mobileLogoUrl = $thietLapWebsite->LogoUrl ?? asset('assets/congthongtin/Seosona-Logo.png');
                // Add timestamp to prevent cache if it's not a default asset
                if (!str_contains($mobileLogoUrl, 'assets/congthongtin/Seosona-Logo.png')) {
                    $mobileLogoUrl .= '?v=' . time();
                }
            @endphp
            <img src="{{ $mobileLogoUrl }}" alt="{{ $thietLapWebsite->TenDonVi ?? 'Logo' }}" style="max-height: 35px;">
        </div>
        <button class="mobile-menu-close" aria-label="Đóng menu">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <nav class="mobile-menu-content">
        <ul class="mobile-menu-nav">
            <li>
                <a href="{{ url('/') }}" class="{{ request()->is('/') ? 'active' : '' }}">
                    <i class="fas fa-home"></i>
                    Trang chủ
                </a>
            </li>
            <li>
                <a href="/gioi-thieu">
                    <i class="fas fa-info-circle"></i>
                    Giới thiệu
                </a>
            </li>
            <li class="has-dropdown">
                <a href="#" class="{{ request()->is('tin-tuc*') ? 'active' : '' }}" data-dropdown-toggle>
                    <i class="fas fa-newspaper"></i>
                    Tin tức
                </a>
                <ul class="sub-menu">
                    <li><a href="{{ route('news.index') }}">Tất cả tin tức</a></li>
                    @if (isset($loaiTinTucsMenu) && $loaiTinTucsMenu->count() > 0)
                        @foreach ($loaiTinTucsMenu as $loaiTinTuc)
                            <li>
                                <a href="{{ route('news.index', ['loai' => $loaiTinTuc->DinhDanh]) }}">
                                    {{ $loaiTinTuc->TenLoaiTinTuc }}
                                </a>
                            </li>
                        @endforeach
                    @else
                        <li><a href="{{ route('news.index') }}">Tin tức</a></li>
                    @endif
                </ul>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-search"></i>
                    Tra cứu
                </a>
            </li>
            <li>
                <a href="/lien-he">
                    <i class="fa fa-address-book" aria-hidden="true"></i>
                    Liên hệ
                </a>
            </li>
        </ul>
    </nav>

    <div class="mobile-login">
        @auth
            <a href="{{ url('/quanly/tongquan') }}">
                <img src="{{ asset('img/logo.png') }}" alt="Logo" class="login-icon">
                Đăng nhập
            </a>
        @else
            <a href="{{ route('login') }}">
                <img src="{{ asset('img/logo.png') }}" alt="Logo" class="login-icon">
                Đăng nhập
            </a>
        @endauth
    </div>
</div>

<!-- Seosona Header Styles - Load after Flatsome CSS -->
<link rel="stylesheet" href="{{ asset('css/congthongtin/header.css') }}?v={{ time() }}">
<link rel="stylesheet" href="{{ asset('css/congthongtin/header-edit.css') }}?v={{ time() }}">

<script src="{{ asset('js/congthongtin/header.js') }}"></script>
