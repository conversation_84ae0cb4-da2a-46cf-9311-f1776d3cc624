@extends('congthongtin.layouts.app')

@section('title', 'Cổng thông tin')
@section('description',
    '<PERSON><PERSON> thống Tra cứu Văn bằng chứng chỉ - Nền tảng trực tuyến cho phép người dùng dễ dàng tra cứu
    hợp pháp của văn bằng, chứng chỉ')
@section('keywords', 'tra cứu văn bằng, chứng chỉ, gi<PERSON><PERSON> dụ<PERSON>, xá<PERSON> thực')

@push('styles')
    <link rel="stylesheet" href="{{ asset('css/congthongtin/index-layout.css') }}">
    <link rel="stylesheet" href="{{ asset('css/congthongtin/edit-mode.css') }}">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
    </style>
@endpush

@section('content')
    {{-- Section 1: Hero --}}
    <div class="section-1">
        <div class="hero-container">
            <div class="hero-content">
                @if (isset($components['section-1']) && $components['section-1']->count() > 0)
                    @foreach ($components['section-1'] as $component)
                        @if ($component->TenComponent == 'hero-left')
                            <div class="hero-left editable-component" data-component-id="{{ $component->_id }}">
                                @if ($component->MauHTML)
                                    {!! $component->MauHTML !!}
                                @else
                                    <div class="play-button">
                                        <div class="play-icon">
                                            <i class="fas fa-search" style="color:white"></i>
                                        </div>
                                    </div>
                                    <h1 class="hero-title">
                                        @if ($component->NoiDungText)
                                            @php $content = json_decode($component->NoiDungText, true); @endphp
                                            {{ $content['title'] ?? 'Hệ thống Tra cứu Văn bằng chứng chỉ' }}
                                        @else
                                            Hệ thống Tra cứu <br>Văn bằng chứng chỉ
                                        @endif
                                    </h1>
                                    <p class="hero-description">
                                        @if ($component->NoiDungText)
                                            {{ $content['description'] ?? 'Là nền tảng trực tuyến cho phép người dùng dễ dàng tra cứu hợp pháp của văn bằng, chứng chỉ do các cơ sở giáo dục cấp. Hệ thống đảm bảo dữ liệu được cập nhật liên tục, bảo mật và minh bạch.' }}
                                        @else
                                            Là nền tảng trực tuyến cho phép người dùng dễ dàng tra cứu hợp pháp của văn
                                            bằng, chứng chỉ do
                                            các cơ sở giáo dục cấp. Hệ thống đảm bảo dữ liệu được cập nhật liên tục, bảo mật
                                            và minh bạch.
                                        @endif
                                    </p>
                                @endif
                            </div>
                        @elseif($component->TenComponent == 'hero-right')
                            <div class="hero-right editable-component" data-component-id="{{ $component->_id }}">
                                @if ($component->MauHTML)
                                    {!! $component->MauHTML !!}
                                @else
                                    <div class="hero-image">
                                        @if ($component->NoiDungText)
                                            @php $content = json_decode($component->NoiDungText, true); @endphp
                                            <img src="{{ $content['imageUrl'] ?? asset('assets/congthongtin/1.png') }}"
                                                alt="{{ $content['title'] ?? 'Google Marketing Illustration' }}">
                                        @else
                                            <img src="{{ asset('assets/congthongtin/1.png') }}"
                                                alt="Google Marketing Illustration">
                                        @endif
                                    </div>
                                @endif
                            </div>
                        @endif
                    @endforeach
                @else
                    {{-- Default content if no components in database --}}
                    <div class="hero-left editable-component">
                        <div class="play-button">
                            <div class="play-icon">
                                <i class="fas fa-search" style="color:white"></i>
                            </div>
                        </div>
                        <h1 class="hero-title">
                            Hệ thống Tra cứu <br>Văn bằng chứng chỉ
                        </h1>
                        <p class="hero-description">
                            Là nền tảng trực tuyến cho phép người dùng dễ dàng tra cứu hợp pháp của văn bằng, chứng chỉ do
                            các cơ sở giáo dục cấp. Hệ thống đảm bảo dữ liệu được cập nhật liên tục, bảo mật và minh bạch.
                        </p>
                    </div>
                    <div class="hero-right editable-component">
                        <div class="hero-image">
                            <img src="{{ asset('assets/congthongtin/1.png') }}" alt="Google Marketing Illustration">
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
    {{-- Section 2: Services --}}
    <div class="section-2">
        <div class="services-container">
            <div class="services-grid">
                @if (isset($components['section-2']) && $components['section-2']->count() > 0)
                    @foreach ($components['section-2'] as $component)
                        <div class="service-card {{ $component->TenComponent }} @if ($component->TenComponent == 'card-1' || $component->TenComponent == 'card-3') flip-card @endif editable-component"
                            data-component-id="{{ $component->_id }}">
                            @if ($component->MauHTML)
                                {!! $component->MauHTML !!}
                            @else
                                @if ($component->TenComponent == 'card-1')
                                    <div class="flip-card-inner">
                                        <div class="flip-card-front">
                                            <div class="service-icon">
                                                @if ($component->NoiDungText)
                                                    @php $content = json_decode($component->NoiDungText, true); @endphp
                                                    <img src="{{ $content['imageUrl'] ?? asset('assets/congthongtin/s2-1.png') }}"
                                                        alt="{{ $content['title'] ?? 'Conversion Rate Optimization' }}">
                                                @else
                                                    <img src="{{ asset('assets/congthongtin/s2-1.png') }}"
                                                        alt="Conversion Rate Optimization">
                                                @endif
                                            </div>
                                            <h3 class="service-title">
                                                @if ($component->NoiDungText)
                                                    {{ $content['title'] ?? 'Tra cứu văn bằng, chứng chỉ trực tuyến' }}
                                                @else
                                                    Tra cứu văn bằng, chứng chỉ trực tuyến
                                                @endif
                                            </h3>
                                            <p class="service-description">
                                                @if ($component->NoiDungText)
                                                    {{ $content['description'] ?? 'Người dùng dễ dàng tra cứu thông tin văn bằng, chứng chỉ mọi lúc, mọi nơi chỉ với số hiệu hoặc thông tin cá nhân cơ bản. Kết quả trả về nhanh chóng, phục vụ đa dạng nhu cầu xác minh.' }}
                                                @else
                                                    Người dùng dễ dàng tra cứu thông tin văn bằng, chứng chỉ mọi lúc, mọi
                                                    nơi chỉ với số hiệu hoặc thông tin cá nhân cơ bản. Kết quả trả về nhanh
                                                    chóng, phục vụ đa dạng nhu cầu xác minh.
                                                @endif
                                            </p>
                                        </div>
                                        <div class="flip-card-back">
                                            <div class="service-icon">
                                                @if ($component->NoiDungText)
                                                    <img src="{{ $content['imageUrl'] ?? asset('assets/congthongtin/s2-1.png') }}"
                                                        alt="{{ $content['title'] ?? 'Conversion Rate Optimization' }}">
                                                @else
                                                    <img src="{{ asset('assets/congthongtin/s2-1.png') }}"
                                                        alt="Conversion Rate Optimization">
                                                @endif
                                            </div>
                                            <h3 class="service-title">
                                                @if ($component->NoiDungText)
                                                    {{ $content['title'] ?? 'Tra cứu văn bằng, chứng chỉ trực tuyến' }}
                                                @else
                                                    Tra cứu văn bằng, chứng chỉ trực tuyến
                                                @endif
                                            </h3>
                                            <p class="service-description">
                                                @if ($component->NoiDungText)
                                                    {{ $content['description'] ?? 'Người dùng dễ dàng tra cứu thông tin văn bằng, chứng chỉ mọi lúc, mọi nơi chỉ với số hiệu hoặc thông tin cá nhân cơ bản. Kết quả trả về nhanh chóng, phục vụ đa dạng nhu cầu xác minh.' }}
                                                @else
                                                    Người dùng dễ dàng tra cứu thông tin văn bằng, chứng chỉ mọi lúc, mọi
                                                    nơi chỉ với số hiệu hoặc thông tin cá nhân cơ bản. Kết quả trả về nhanh
                                                    chóng, phục vụ đa dạng nhu cầu xác minh.
                                                @endif
                                            </p>
                                        </div>
                                    </div>
                                @elseif($component->TenComponent == 'card-2')
                                    <div class="wave-background">
                                        <img src="{{ asset('assets/congthongtin/s2-wave.svg') }}" alt="Wave Animation"
                                            class="wave-svg">
                                    </div>
                                    <div class="service-icon">
                                        @if ($component->NoiDungText)
                                            @php $content = json_decode($component->NoiDungText, true); @endphp
                                            <img src="{{ $content['imageUrl'] ?? asset('assets/congthongtin/s2-2.png') }}"
                                                alt="{{ $content['title'] ?? 'Online Reputation Management' }}">
                                        @else
                                            <img src="{{ asset('assets/congthongtin/s2-2.png') }}"
                                                alt="Online Reputation Management">
                                        @endif
                                    </div>
                                    <h3 class="service-title">
                                        @if ($component->NoiDungText)
                                            @php $content = json_decode($component->NoiDungText, true); @endphp
                                            {{ $content['title'] ?? 'Cập nhật dữ liệu liên tục từ các đơn vị đào tạo' }}
                                        @else
                                            Cập nhật dữ liệu liên tục từ các đơn vị đào tạo
                                        @endif
                                    </h3>
                                    <p class="service-description">
                                        @if ($component->NoiDungText)
                                            {{ $content['description'] ?? 'Hệ thống tự động đồng bộ, cập nhật thông tin từ các trường và cơ quan giáo dục trên toàn quốc, đảm bảo dữ liệu luôn đầy đủ, mới nhất và chính xác.' }}
                                        @else
                                            Hệ thống tự động đồng bộ, cập nhật thông tin từ các trường và cơ quan giáo dục
                                            trên toàn quốc, đảm bảo dữ liệu luôn đầy đủ, mới nhất và chính xác.
                                        @endif
                                    </p>
                                @elseif($component->TenComponent == 'card-3')
                                    <div class="flip-card-inner">
                                        <div class="flip-card-front">
                                            <div class="service-icon">
                                                @if ($component->NoiDungText)
                                                    @php $content = json_decode($component->NoiDungText, true); @endphp
                                                    <img src="{{ $content['imageUrl'] ?? asset('assets/congthongtin/s2-3.png') }}"
                                                        alt="{{ $content['title'] ?? 'Online Accounting' }}">
                                                @else
                                                    <img src="{{ asset('assets/congthongtin/s2-3.png') }}"
                                                        alt="Online Accounting">
                                                @endif
                                            </div>
                                            <h3 class="service-title">
                                                @if ($component->NoiDungText)
                                                    @php $content = json_decode($component->NoiDungText, true); @endphp
                                                    {{ $content['title'] ?? 'Dữ liệu minh bạch, bảo mật và cập nhật liên tục' }}
                                                @else
                                                    Dữ liệu minh bạch, bảo mật và cập nhật liên tục
                                                @endif
                                            </h3>
                                            <p class="service-description">
                                                @if ($component->NoiDungText)
                                                    {{ $content['description'] ?? 'Thông tin văn bằng được thu thập từ nguồn chính thống, lưu trữ an toàn, bảo mật, đảm bảo minh bạch và đáng tin cậy cho cá nhân, tổ chức và công tác quản lý.' }}
                                                @else
                                                    Thông tin văn bằng được thu thập từ nguồn chính thống, lưu trữ an toàn,
                                                    bảo mật, đảm bảo minh bạch và đáng tin cậy cho cá nhân, tổ chức và công
                                                    tác quản lý.
                                                @endif
                                            </p>
                                        </div>
                                        <div class="flip-card-back">
                                            <div class="service-icon">
                                                @if ($component->NoiDungText)
                                                    <img src="{{ $content['imageUrl'] ?? asset('assets/congthongtin/s2-3.png') }}"
                                                        alt="{{ $content['title'] ?? 'Online Accounting' }}">
                                                @else
                                                    <img src="{{ asset('assets/congthongtin/s2-3.png') }}"
                                                        alt="Online Accounting">
                                                @endif
                                            </div>
                                            <h3 class="service-title">
                                                @if ($component->NoiDungText)
                                                    {{ $content['title'] ?? 'Dữ liệu minh bạch, bảo mật và cập nhật liên tục' }}
                                                @else
                                                    Dữ liệu minh bạch, bảo mật và cập nhật liên tục
                                                @endif
                                            </h3>
                                            <p class="service-description">
                                                @if ($component->NoiDungText)
                                                    {{ $content['description'] ?? 'Thông tin văn bằng được thu thập từ nguồn chính thống, lưu trữ an toàn, bảo mật, đảm bảo minh bạch và đáng tin cậy cho cá nhân, tổ chức và công tác quản lý.' }}
                                                @else
                                                    Thông tin văn bằng được thu thập từ nguồn chính thống, lưu trữ an toàn,
                                                    bảo mật, đảm bảo minh bạch và đáng tin cậy cho cá nhân, tổ chức và công
                                                    tác quản lý.
                                                @endif
                                            </p>
                                        </div>
                                    </div>
                                @endif
                            @endif
                        </div>
                    @endforeach
                @else
                    {{-- Default cards if no components in database --}}
                    <div class="service-card card-1 flip-card editable-component">
                        <div class="flip-card-inner">
                            <!-- Front side -->
                            <div class="flip-card-front">
                                <div class="service-icon">
                                    <img src="{{ asset('assets/congthongtin/s2-1.png') }}"
                                        alt="Conversion Rate Optimization">
                                </div>
                                <h3 class="service-title">Tra cứu văn bằng, chứng chỉ trực tuyến</h3>
                                <p class="service-description">
                                    Người dùng dễ dàng tra cứu thông tin văn bằng, chứng chỉ mọi lúc, mọi nơi chỉ với số
                                    hiệu hoặc thông tin cá nhân cơ bản. Kết quả trả về nhanh chóng, phục vụ đa dạng nhu cầu
                                    xác minh.
                                </p>
                            </div>
                            <!-- Back side -->
                            <div class="flip-card-back">
                                <div class="service-icon">
                                    <img src="{{ asset('assets/congthongtin/s2-1.png') }}"
                                        alt="Conversion Rate Optimization">
                                </div>
                                <h3 class="service-title">Tra cứu văn bằng, chứng chỉ trực tuyến</h3>
                                <p class="service-description">
                                    Người dùng dễ dàng tra cứu thông tin văn bằng, chứng chỉ mọi lúc, mọi nơi chỉ với số
                                    hiệu
                                    hoặc thông tin cá nhân cơ bản. Kết quả trả về nhanh chóng, phục vụ đa dạng nhu cầu
                                    xác
                                    minh.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="service-card card-2 editable-component">
                        <div class="wave-background">
                            <img src="{{ asset('assets/congthongtin/s2-wave.svg') }}" alt="Wave Animation"
                                class="wave-svg">
                        </div>
                        <div class="service-icon">
                            <img src="{{ asset('assets/congthongtin/s2-2.png') }}" alt="Online Reputation Management">
                        </div>
                        <h3 class="service-title">Cập nhật dữ liệu liên tục từ các đơn vị đào tạo</h3>
                        <p class="service-description">
                            Hệ thống tự động đồng bộ, cập nhật thông tin từ các trường và cơ quan giáo dục trên toàn quốc,
                            đảm bảo dữ liệu luôn đầy đủ, mới nhất và chính xác.
                        </p>
                    </div>

                    <div class="service-card card-3 flip-card editable-component">
                        <div class="flip-card-inner">
                            <div class="flip-card-front">
                                <div class="service-icon">
                                    <img src="{{ asset('assets/congthongtin/s2-3.png') }}" alt="Online Accounting">
                                </div>
                                <h3 class="service-title">Dữ liệu minh bạch, bảo mật và cập nhật liên tục</h3>
                                <p class="service-description">
                                    Thông tin văn bằng được thu thập từ nguồn chính thống, lưu trữ an toàn, bảo mật, đảm bảo
                                    minh bạch và đáng tin cậy cho cá nhân, tổ chức và công tác quản lý.
                                </p>
                            </div>
                            <div class="flip-card-back">
                                <div class="service-icon">
                                    <img src="{{ asset('assets/congthongtin/s2-3.png') }}" alt="Online Accounting">
                                </div>
                                <h3 class="service-title">Dữ liệu minh bạch, bảo mật và cập nhật liên tục</h3>
                                <p class="service-description">
                                    Thông tin văn bằng được thu thập từ nguồn chính thống, lưu trữ an toàn, bảo mật, đảm bảo
                                    minh bạch và đáng tin cậy cho cá nhân, tổ chức và công tác quản lý.
                                </p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    {{-- Section 3: Marketing --}}
    <div class="section-3">
        <div class="marketing-container">
            <div class="marketing-content">
                @if (isset($components['section-3']) && $components['section-3']->count() > 0)
                    @foreach ($components['section-3'] as $component)
                        @if ($component->TenComponent == 'marketing-left')
                            <div class="marketing-left editable-component" data-component-id="{{ $component->_id }}">
                                @if ($component->MauHTML)
                                    {!! $component->MauHTML !!}
                                @else
                                    <div class="marketing-image">
                                        @if ($component->NoiDungText)
                                            @php $content = json_decode($component->NoiDungText, true); @endphp
                                            <img src="{{ $content['imageUrl'] ?? asset('assets/congthongtin/s3-2.png') }}"
                                                alt="{{ $content['title'] ?? 'Website Development Illustration' }}">
                                        @else
                                            <img src="{{ asset('assets/congthongtin/s3-2.png') }}"
                                                alt="Website Development Illustration">
                                        @endif
                                    </div>
                                @endif
                            </div>
                        @elseif($component->TenComponent == 'marketing-right')
                            <div class="marketing-right editable-component" data-component-id="{{ $component->_id }}">
                                @if ($component->MauHTML)
                                    {!! $component->MauHTML !!}
                                @else
                                    <div class="marketing-text">
                                        <h2 class="marketing-title">
                                            @if ($component->NoiDungText)
                                                @php $content = json_decode($component->NoiDungText, true); @endphp
                                                {{ $content['title'] ?? 'Hiệu quả mang lại' }}
                                            @else
                                                Hiệu quả mang lại
                                            @endif
                                        </h2>
                                        <p class="marketing-description">
                                            @if ($component->NoiDungText)
                                                {{ $content['description'] ?? 'Phần mềm được xây dựng với mục tiêu quản lý thông tin học sinh, theo dõi quá trình đào tạo, đánh giá kết quả học tập cho đến việc cấp phát văn bằng, chứng chỉ. Hệ thống không chỉ giúp nhà trường, cơ sở đào tạo và cơ quan quản lý giáo dục kiểm soát, lưu trữ dữ liệu đồng bộ, chính xác mà còn tạo điều kiện cho người dân, doanh nghiệp dễ dàng tra cứu, xác thực thông tin văn bằng, chứng chỉ mọi lúc, mọi nơi. Qua đó, phần mềm góp phần nâng cao hiệu quả quản lý giáo dục, tăng tính minh bạch, phòng chống gian lận và đảm bảo quyền lợi cho người học.' }}
                                            @else
                                                Phần mềm được xây dựng với mục tiêu quản lý thông tin học sinh, theo dõi quá
                                                trình đào tạo, đánh giá kết quả học tập cho đến việc cấp phát văn bằng,
                                                chứng chỉ. Hệ thống không chỉ giúp nhà trường, cơ sở đào tạo và cơ quan quản
                                                lý giáo dục kiểm soát, lưu trữ dữ liệu đồng bộ, chính xác mà còn tạo điều
                                                kiện cho người dân, doanh nghiệp dễ dàng tra cứu, xác thực thông tin văn
                                                bằng, chứng chỉ mọi lúc, mọi nơi. Qua đó, phần mềm góp phần nâng cao hiệu
                                                quả quản lý giáo dục, tăng tính minh bạch, phòng chống gian lận và đảm bảo
                                                quyền lợi cho người học.
                                            @endif
                                        </p>
                                    </div>
                                @endif
                            </div>
                        @endif
                    @endforeach
                @else
                    {{-- Default components if no data in database --}}
                    <div class="marketing-left editable-component">
                        <div class="marketing-image">
                            <img src="{{ asset('assets/congthongtin/s3-2.png') }}"
                                alt="Website Development Illustration">
                        </div>
                    </div>
                    <div class="marketing-right editable-component">
                        <div class="marketing-text">
                            <h2 class="marketing-title">Hiệu quả mang lại</h2>
                            <p class="marketing-description">
                                Phần mềm được xây dựng với mục tiêu quản lý thông tin học sinh, theo dõi quá trình đào tạo,
                                đánh giá kết quả học tập cho đến việc cấp phát văn bằng, chứng chỉ. Hệ thống không chỉ giúp
                                nhà trường, cơ sở đào tạo và cơ quan quản lý giáo dục kiểm soát, lưu trữ dữ liệu đồng bộ,
                                chính xác mà còn tạo điều kiện cho người dân, doanh nghiệp dễ dàng tra cứu, xác thực thông
                                tin văn bằng, chứng chỉ mọi lúc, mọi nơi. Qua đó, phần mềm góp phần nâng cao hiệu quả quản
                                lý giáo dục, tăng tính minh bạch, phòng chống gian lận và đảm bảo quyền lợi cho người học.
                            </p>
                            <p class="marketing-contact">
                                Hãy liên hệ với chúng tôi để sử dụng dịch vụ của chúng tôi ngay bây giờ.
                            </p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    @include('congthongtin.components.news-events')
@endsection

@push('scripts')
    <script src="{{ asset('js/congthongtin/flip-cards.js') }}"></script>
    <script src="{{ asset('js/congthongtin/scroll-animations.js') }}"></script>
    <script src="{{ asset('js/congthongtin/search-button.js') }}"></script>
    <script src="{{ asset('js/congthongtin/floating-edit-button.js') }}"></script>
    <script src="{{ asset('js/congthongtin/drag-drop-components.js') }}"></script>
    <script src="{{ asset('js/congthongtin/edit-sidebar.js') }}"></script>

    <script>
        // Pass website settings to JavaScript
        @if (isset($thietLapWebsite))
            window.thietLapWebsite = {
                ck_QuanTriNgayTrenWebsite: {{ $thietLapWebsite->ck_QuanTriNgayTrenWebsite ? 'true' : 'false' }},
                ck_CoNutLenDauTrang: {{ $thietLapWebsite->ck_CoNutLenDauTrang ? 'true' : 'false' }},
                ck_KhoaBaiDang: {{ $thietLapWebsite->ck_KhoaBaiDang ? 'true' : 'false' }}
            };
        @endif
    </script>
@endpush
