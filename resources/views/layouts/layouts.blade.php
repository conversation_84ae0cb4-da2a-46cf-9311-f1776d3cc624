<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>
        {{ isset($thietLapWebsite) && $thietLapWebsite->TieuDeWebsite ? $thietLapWebsite->TieuDeWebsite : 'PHẦN MỀM QUẢN LÝ VĂN BẢN CHỨNG CHỈ - QLVBCC' }}
    </title>
    <meta name="description" content="PHẦN MỀM QUẢN LÝ VĂN BẢN CHỨNG TỪ - QLVBCT" />
    <meta name="theme-color" content="#007bff">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome Icons -->
    <link href="{{ asset('js/libs/fontawesome/css/all.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('js/libs/jquery/css/jquery-ui.custom.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('js/libs/jquery/css/jquery-ui.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('js/libs/font-awesome/4.7.0/css/font-awesome.css') }}" rel="stylesheet" />
    <link href="{{ asset('js/libs/boxicons-2.1.4/css/boxicons.css') }}" rel="stylesheet" />
    <link href="{{ asset('js/libs/select2/select2.min.css') }}" rel="stylesheet" />
    <!-- Theme style -->
    <link href="{{ asset('js/libs/toastr/toastr.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('css/tabler/demo.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('css/tabler/tabler-vendors.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('css/tabler/tabler.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('js/libs/datepicker/bootstrap-datepicker3.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('js/libs/datepicker/bootstrap-datetimepicker.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('js/libs/daterangepicker/daterangepicker.css') }}" rel="stylesheet" />
    <link href="{{ asset('js/libs/confirm/dist/jquery-confirm.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('js/libs/tabulator/dist/css/tabulator.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('js/libs/tabulator/dist/css/tabulator_custom.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('css/tabler/style-custom.css') }}" rel="stylesheet" />
    {{--
    <link href="{{ asset('css/tabler/tabler.css') }}" rel="stylesheet" /> --}}
    <script src="{{ asset('js/libs/jquery/jquery-3.6.3.min.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/tabulator/dist/js/tabulator.min.js') }}?v={{ time() }}"></script>

    <link href="{{ asset('js/libs/dropzone/dist/dropzone.css') }}" rel="stylesheet" />
    <script src="{{ asset('js/libs/dropzone/dist/dropzone-min.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/dungchung/NTSPlugin.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/dungchung/permission.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/dungchung/customs.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/apexcharts/dist/apexcharts.js') }}?v={{ time() }}"></script>
    <!-- Popper.js (đúng bản) -->
    <script src="{{ asset('https://unpkg.com/@popperjs/core@2/dist/umd/popper.min.js') }}?v={{ time() }}"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link href="{{ asset('css/tabler/stylecss_custom2.css') }}" rel="stylesheet" />
    <!-- TinyMCE Editor -->
    <script src="{{ asset('js/libs/tinymce/tinymce.min.js') }}?v={{ time() }}"></script>
    @stack('styles')


    <link rel="icon"
        href="{{ isset($thietLapWebsite) && $thietLapWebsite->FaviconUrl ? $thietLapWebsite->FaviconUrl : asset('favicon.ico') }}"
        type="image/x-icon" />
</head>

<body class="hold-transition layout-footer-fixed layout-fixed">
    {{-- @Html.AntiForgeryToken() --}}
    <div class="page">
        @include('layouts.header')
        @include('layouts.leftbar')
        <div class="page-wrapper" style="background: var(--nts-bg-maunen) !important;">
            <div class="page-body">
                <div class="container-xl">
                    <div class="collapse navbar-collapse" id="navbar-menu"
                        style="display: block !important; margin-bottom:10px;">
                        <div style="font-weight: bold; font-size: 16px !important;" class="nts-text-backround"
                            id="TieuDeTrang">
                            {{ Str::upper($TieuDeTrang) ?? '' }}
                        </div>
                    </div>
                    <div id="Loadding"> </div>
                    @yield('content')
                </div>
            </div>
            @include('layouts.footer')
        </div>
    </div>
    {{-- THEME --}}
    <div class="settings d-none">
        <a href="#" class="btn btn-floating btn-icon btn-primary" data-bs-toggle="offcanvas" style="width:4px"
            data-bs-target="#offcanvasSettings" aria-controls="offcanvasSettings" aria-label="Theme Builder">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="icon icon-1">
                <path d="M3 21v-4a4 4 0 1 1 4 4h-4"></path>
                <path d="M21 3a16 16 0 0 0 -12.8 10.2"></path>
                <path d="M21 3a16 16 0 0 1 -10.2 12.8"></path>
                <path d="M10.6 9a9 9 0 0 1 4.4 4.4"></path>
            </svg>
        </a>
        <form class="offcanvas offcanvas-start offcanvas-narrow" tabindex="-1" id="offcanvasSettings" aria-modal="true"
            role="dialog">
            <div class="offcanvas-header">
                <h2 class="offcanvas-title">SETTING</h2>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body d-flex flex-column">
                <div class="mt-auto space-y">
                    <button type="button" class="btn w-100" id="reset-changes">
                        <!-- Download SVG icon from http://tabler.io/icons/icon/rotate -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="icon icon-1">
                            <path d="M19.95 11a8 8 0 1 0 -.5 4m.5 5v-5h-5"></path>
                        </svg>
                        Reset changes
                    </button>
                    <a href="#" class="btn btn-primary w-100" data-bs-dismiss="offcanvas">
                        <!-- Download SVG icon from http://tabler.io/icons/icon/settings -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="icon icon-1">
                            <path
                                d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z">
                            </path>
                            <path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0"></path>
                        </svg>
                        Save settings
                    </a>
                </div>
            </div>
        </form>
    </div>
    <!-- jQuery -->
    <script src="{{ asset('js/libs/select2/select2.full.min.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/jquery/jquery-ui.min.js') }}?v={{ time() }}"></script>
    <!-- Bootstrap 4 -->
    <script src="{{ asset('js/libs/bootstrap/dist/js/bootstrap.bundle.min.js') }}?v={{ time() }}"></script>

    <script src="{{ asset('js/libs/datepicker/bootstrap-datepicker.min.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/moment/moment.min.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/datepicker/bootstrap-datetimepicker.min.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/daterangepicker/daterangepicker.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/toastr/toastr.min.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/dungchung/NTSPlugin.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/dungchung/NTSLibrary.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/dungchung/NTSValidate.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/confirm/js/jquery-confirm.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/libs/confirm/dist/jquery-confirm.min.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/dungchung/svg-icons.js') }}?v={{ time() }}"></script>
    <script>
        const loadingImgUrl = "{{ asset('img/loading.gif') }}";
        const file_us = "{{ asset('img/file.png') }}";
    </script>
    <script src="{{ asset('js/dungchung/Header.js') }}?v={{ time() }}"></script>

    {{-- Export Preview Modal --}}
    <div class="modal fade" id="exportPreviewModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exportPreviewTitle">Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="previewContent"></div>
                <div class="modal-footer">
                    <div style="float:right;text-align: right">
                        <a href="#" class="nts-color-dong btn btn-outline-danger" data-bs-dismiss="modal">
                            <i class="fa fa-close"></i>&nbsp;Đóng
                        </a>
                        <a href="#" id="confirmDownloadBtn" class="nts-color-luu btn btn-success ms-auto">
                            <i class="fa fa-save"></i>&ensp;Tải về
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(function() {

                setupDragAndDrop();
                // Lấy thông tin người dùng trước khi load màu
                NTS.getAjaxAPIAsync("GET", Laravel.layouts.getProfileInfo, {})
                    .then(userInfo => {
                        const currentUserID = userInfo.id;
                        const payload2 = {
                            UserID: currentUserID
                        };
                        // Gọi API lấy thiết lập màu
                        const result = NTS.getAjaxAPI("GET", window.Laravel.layouts.getThietLapMauHeThong,
                            payload2);
                        if (!result.Err && result.Result && result.Result.length > 0) {
                            const data = result.Result[0];
                            if (String(data.UserID) === String(currentUserID)) {
                                loadColorsFromDB(currentUserID);
                            }
                        }
                    })
                    .catch(err => {});
            });

            function toggleTheme() {
                document.body.classList.toggle('dark-mode');
                const isDark = document.body.classList.contains('dark-mode');
                localStorage.setItem('theme', isDark ? 'dark' : 'light');
                updateThemeIcons();

                // Gọi lại loadColorsFromDB để cập nhật màu
                NTS.getAjaxAPIAsync("GET", Laravel.layouts.getProfileInfo, {})
                    .then(userInfo => {
                        const currentUserID = userInfo.id;
                        loadColorsFromDB(currentUserID); // Cập nhật màu ngay khi toggle
                    })
                    .catch(err => {});
            }

            function setupDragAndDrop() {
                const colorRows = document.querySelectorAll('.color-row');
                colorRows.forEach(row => {
                    row.addEventListener('dragstart', () => {
                        row.classList.add('dragging');
                    });
                    row.addEventListener('dragend', () => {
                        row.classList.remove('dragging');
                    });
                });

                document.querySelectorAll('.color-group').forEach(group => {
                    group.addEventListener('dragover', e => {
                        e.preventDefault();
                        const afterElement = getDragAfterElement(group, e.clientY);
                        const dragging = document.querySelector('.dragging');
                        if (afterElement == null) {
                            group.appendChild(dragging);
                        } else {
                            group.insertBefore(dragging, afterElement);
                        }
                    });
                });
            }

            function getDragAfterElement(container, y) {
                const draggableElements = [...container.querySelectorAll('.color-row:not(.dragging)')];
                return draggableElements.reduce((closest, child) => {
                    const box = child.getBoundingClientRect();
                    const offset = y - box.top - box.height / 2;
                    if (offset < 0 && offset > closest.offset) {
                        return {
                            offset: offset,
                            element: child
                        };
                    } else {
                        return closest;
                    }
                }, {
                    offset: Number.NEGATIVE_INFINITY
                }).element;
            }

            function loadColorsFromDB(userID) {
                const payload = {
                    UserID: userID
                };
                var result = NTS.getAjaxAPI("GET", window.Laravel.layouts.getThietLapMauHeThong, payload);
                if (!result.Err && result.Result && result.Result.length > 0) {
                    let data = result.Result[0];
                    let lightColors = Array.isArray(data.MauSacJSON_Sang) ? data.MauSacJSON_Sang : JSON.parse(data
                        .MauSacJSON_Sang || '[]');
                    let darkColors = Array.isArray(data.MauSacJSON_Toi) ? data.MauSacJSON_Toi : JSON.parse(data
                        .MauSacJSON_Toi || '[]');

                    // Ánh xạ tensukien với ID input
                    const lightColorMap = {
                        'them': '.nts-color-them',
                        'luu': '.nts-color-luu',
                        'dong': '.nts-color-dong',
                        'in': '.nts-in,.fa.fa-print',
                        'xuat': '.nts-xuat,.fa.fa-file-excel-o',
                        'header': '.nts-bg-header-primary',
                        'header-text-color': '.nts-bg-header-primary>h5#tieuDeModal',
                        'menu': '.nts-color-menu-text,.nts-color-menu-text.nav-link-icon>i,.nts-color-menu-text.nav-link>span>i,.nts-color-menu-text.dropdown-item>span,.nts-color-menu-text.dropdown-item>span>i,.nts-color-menu-text.dropdown-item>i,.nts-sidebar-collapse',
                        'menu-bg': '.nts-color-menu-bg,.nts-color-menu,.flyout-menu.full-height-from-header',
                        'khac-bg': '.nts-khac',
                        'khac': '.nts-color-khac',
                        'sidebar-collapse': '.li-sidebar_collapse' // Thêm key riêng cho .li-sidebar_collapse
                    };

                    const darkColorMap = {
                        'them': '.nts-color-them',
                        'luu': '.nts-color-luu',
                        'dong': '.nts-color-dong',
                        'in': '.nts-in,.fa.fa-print',
                        'xuat': '.nts-xuat,.fa.fa-file-excel-o',
                        'header': '.nts-bg-header-primary',
                        'header-text-color': '.nts-bg-header-primary>h5#tieuDeModal',
                        'menu': '.nts-color-menu-text,.nts-color-menu-text.nav-link-icon>i,.nts-color-menu-text.nav-link>span>i,.nts-color-menu-text.dropdown-item>span,.nts-color-menu-text.dropdown-item>span>i,.nts-color-menu-text.dropdown-item>i,.nts-sidebar-collapse',
                        'menu-bg': '.nts-color-menu-bg,.nts-color-menu,.flyout-menu.full-height-from-header',
                        'khac-bg': '.nts-khac',
                        'khac': '.nts-color-khac',
                        'sidebar-collapse': '.li-sidebar_collapse' // Thêm key riêng cho .li-sidebar_collapse
                    };

                    // Kiểm tra chế độ giao diện
                    $(document).ready(function() {
                        const isDarkMode = $('body').hasClass('dark-mode');

                        const backgroundKeys = ['them', 'luu', 'dong', 'header', 'menu-bg',
                            'khac-bg'
                        ]; // Thêm 'sidebar-collapse'
                        const dualKeys = [];

                        const colorsToApply = isDarkMode ? darkColors : lightColors;
                        const colorMap = isDarkMode ? darkColorMap : lightColorMap;

                        colorsToApply.forEach(item => {
                            const inputClass = colorMap[item.tensukien];
                            if (inputClass && item.color) {
                                const classes = inputClass.split(',').map(cls => cls
                                    .trim()); // Tách và loại bỏ khoảng trắng thừa
                                classes.forEach(cls => {
                                    if (cls && !cls.includes('::before')) { // Bỏ qua pseudo-elements
                                        const $elements = $(cls);
                                        $elements.each(function() {
                                            $(this).removeAttr(
                                                'style'); // Xóa inline style hiện tại
                                            let currentStyle = $(this).attr('style') || '';

                                            if (dualKeys.includes(item.tensukien)) {
                                                currentStyle =
                                                    `${currentStyle} background-color: ${item.color} !important; color: ${item.color} !important;`;
                                            } else if (backgroundKeys.includes(item
                                                    .tensukien)) {
                                                currentStyle =
                                                    `${currentStyle} background-color: ${item.color} !important;`;
                                                // if (cls === '.li-sidebar_collapse') {
                                                //     $(this).addClass('custom-color'); // Thêm class để áp dụng màu cho ::before
                                                // }
                                            } else {
                                                currentStyle =
                                                    `${currentStyle} color: ${item.color} !important;`;
                                            }

                                            $(this).attr('style', currentStyle);
                                            console.log(`Applied style to ${cls}:`,
                                                currentStyle);
                                        });
                                    }
                                });
                            }
                        });
                    });

                    // Lưu vào localStorage để đồng bộ
                    localStorage.setItem('lightColors', JSON.stringify(lightColors));
                    localStorage.setItem('darkColors', JSON.stringify(darkColors));
                }
            }

            // Gắn theme lúc load trang
            document.addEventListener('DOMContentLoaded', () => {
                if (localStorage.getItem('theme') === 'dark') {
                    document.body.classList.add('dark-mode');
                }
                updateThemeIcons(); // <- đừng quên cái này
            });

            function updateThemeIcons() {
                const isDark = document.body.classList.contains('dark-mode');
                const darkIcon = document.getElementById('toggle-dark');
                const lightIcon = document.getElementById('toggle-light');

                if (darkIcon && lightIcon) {
                    darkIcon.style.display = isDark ? 'none' : 'inline-block';
                    lightIcon.style.display = isDark ? 'inline-block' : 'none';
                }
            }

            window.Laravel = window.Laravel || {}; // 👈 Bảo vệ trước khi gán
            window.Laravel.layouts = {
                exportExcelUrl: "{{ url('export/excel_v2') }}",
                exportPdfUrl: "{{ url('export/pdf_v2') }}",
                uploadUrl: "{{ route('api.files.upload') }}",
                luuDangSDUrl: `{{ route('dungchung.luu-dang-sd') }}`,
                KiemTraXoa: `{{ route('dungchung.kiem-tra-xoa-v1') }}`,
                GetDSDiaBanHC_Tinh: "{{ route('diabanhanhchinh.db.getdiabanhc_tinh') }}",
                GetDSDiaBanHC_NoiCap: "{{ route('diabanhanhchinh.db.getdiabanhc_noicap') }}",
                GetDSDanToc: "{{ route('dantoc.ldb.getdantoc') }}",
                GetDSDiaBanHC_ByIDCha: "{{ route('diabanhanhchinh.db.getdiabanhc_byidcha') }}",
                GetDSDonViAll: "{{ route('donvi.dv.getalldonvi') }}",
                GetDSTrangThaiAll: "{{ route('trangthai.lbl.gettrangthai') }}",
                GetDSTonGiao: "{{ route('tongiao.ldb.gettongiao') }}",
                getThietLapMauHeThong: "{{ route('thietlapmauhethong.getdata') }}",
                GetDSCapHoc: "{{ route('caphoc.ldb.getcaphoc') }}",
                getDSNienDo: "{{ route('dungchung.comboNam') }}",
                getListNamHoc: "{{ route('dungchung.danhmuc.comboNamHoc') }}",
                getNienDo: "{{ route('dungchung.niendo') }}",
                getChucVuList: "{{ route('dungchung.danhmuc.comboChucVu') }}",
                getHinhThucList: "{{ route('hinhthucdaotao.ldb.gethinhthucdaotao') }}",
                updateNienDo: "{{ route('hethong.profile.updateNienDo') }}",
                GetDSGioiTinh: "{{ route('dungchung.danhmuc.comboGioiTinh') }}",
                getNhanVienList: "{{ route('dungchung.danhmuc.comboNhanVien') }}",
                getDonViList: "{{ route('dungchung.danhmuc.comboDonVi') }}",
                getListMauVanBangChungChi: `{{ route('dungchung.danhmuc.comboMauVanBangChungChi') }}`,
                //BAO-CAO
                getUserGroupCode: "{{ route('dungchungbaocao.getUserGroupCode') }}",
                GetTuyChonIn: "{{ route('dungchungbaocao.GetTuyChonIn') }}",
                LuuThamSoBaoCao: "{{ route('dungchungbaocao.LuuThamSoBaoCao') }}",
                XuatBaoCao: "{{ route('dungchungbaocao.XuatBaoCao') }}",
                luuThongTinBaoCaoDaLuu: "{{ route('danhsachbaocao.LuuThongTin_BaoCaoDaLuu') }}",
                taoMauMacDinhUser: "{{ route('dungchungbaocao.taoMauMacDinhUser') }}",
                getQuyen: function(permissionKey) {
                    return "{{ route('dungchung.getPermission', ['permissionKey' => '__key__']) }}"
                        .replace('__key__', encodeURIComponent(permissionKey));
                },
                getProfileInfo: "{{ route('hethong.profile.getUser') }}",
                getDonViByID: "{{ route('donvi.getDonViByID') }}",
                getCurrentUserInfo: "{{ route('api.get-current-user-info.getCurrentUserInfo') }}",
            }
        </script>
    @endpush
    @stack('scripts')
</body>

</html>
