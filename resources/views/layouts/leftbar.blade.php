<style>
.nav-link p {
    padding-left: 7px !important;
}

.search-title .text-light {
    color: #7f7777 !important;
}

.nav-link-title {
    text-wrap: wrap;
}

.text-outline {
    text-shadow: -0.3px -0.3px 1px black,
        0.3px -0.3px 1px black,
        -0.3px 0.3px 1px black,
        0.3px 0.3px 1px black;
}
</style>

<!-- Sidebar -->
<aside class="navbar navbar-leftbar navbar-vertical navbar-expand-lg navbar-menu navbar-menu-nts nts-color-menu-bg" data-bs-theme="" >
    <div class="container-fluid leftbar">
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar-menu"
            aria-controls="sidebar-menu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
{{--
        <div class="navbar-brand navbar-brand-autodark" data-label="Tổng quan" style="text-align:left;padding: 10px 10px 0px 10px !important;display: flex; flex-direction: column;">
            <a href="/quanly/tongquan"  style="text-decoration: none; text-align:left; font-weight: 400;" class="brand-link nts-leftbar-menu nts-color-menu-text">
                <i class="fa fa-tachometer" aria-hidden="true" style="font-size: 14px !important;font-weight: 400;"></i>
                <span class="" style="font-weight: 400;font-size: 13px; padding-left:10px;">Tổng quan</span>
            </a>
            <div class="class-hr"></div>
        </div> --}}
        <div class="collapse navbar-collapse" id="sidebar-menu">
            <ul class="navbar-nav">
                @foreach ($menus as $menu)
                @include('layouts.leftbar-item', ['menu' => $menu])
                @endforeach
            </ul>
        </div>
        <div class="sidebar-collapse nts-sidebar-collapse nts-khac" id="sidebar-collapse">
            <i class="icon-double-angle-left fa fa-angle-double-left menu-collapse nts-color-menu nts-color-khac" data-icon1="icon-double-angle-left" data-icon2="icon-double-angle-right"></i>
        </div>
    </div>
</aside>


 <script>

    $(document).ready(function () {
    var $sidebar = $('aside.navbar.navbar-leftbar.navbar-vertical');
    var $collapseButton = $('#sidebar-collapse');
    var $collapseIcon = $collapseButton.find('.menu-collapse');
    var $pageWrapper = $('.page-wrapper');
    var $flyoutMenu = $('.flyout-menu.full-height-from-header');
    var $navLinks = $('#sidebar-menu .nav-link, #sidebar-menu .dropdown-item');
    var $footer = $('.footer.footer-transparent.d-print-none.nts-text-backround');
    var $navlinkSidebar = $('a.brand-link.nts-leftbar-menu.nts-color-menu-text');
    var $li_Sidebar = $('li.nav-item.nav-item-leftbar');
    var $tongquan_Sidebar = $('.navbar-brand.navbar-brand-autodark');
    var $flyouttitle_Sidebar = $('.flyout-title.nts-color-menu-text');

    $collapseButton.on('click', function () {
        $sidebar.toggleClass('collapsed');
        var isCollapsed = $sidebar.hasClass('collapsed');

        if (isCollapsed) {
            $collapseIcon.removeClass('fa-angle-double-left').addClass('fa-angle-double-right');
            $pageWrapper.addClass('sidebar-collapsed');
            $flyoutMenu.addClass('flyout-menu-collapsed');
            $footer.addClass('footer-menu-collapsed');
            $navlinkSidebar.addClass('navlink-sidebar_collapse');
            $li_Sidebar.addClass('li-sidebar_collapse');
            $tongquan_Sidebar.addClass('link-sidebar_tongquan');
            $flyouttitle_Sidebar.addClass('flyout-title_collapse');
             // Gán data-label từ nav-link-title
           $('.li-sidebar_collapse').each(function () {
                // Lấy tên menu từ span.nav-link-title và gán vào data-label
                const labelSpan = $(this).find('.nav-link-title').first();
                if (labelSpan.length > 0) {
                    $(this).attr('data-label', labelSpan.text().trim());
                }

                // Gán sự kiện click: chuyển đến href của <a> bên trong
                $(this).off('click').on('click', function (e) {
                    // Tránh việc click vào dropdown-toggle gây bug
                    if ($(e.target).closest('.dropdown-toggle').length > 0) return;

                    const link = $(this).find('a.nav-link').attr('href');
                    if (link) {
                        window.location.href = link;
                    }
                });
            });


           $('.link-sidebar_tongquan').each(function () {
                 // Click anywhere trong div đều chuyển link
                $(this).css('cursor', 'pointer').off('click').on('click', function (e) {
                    // Nếu click vào link gốc thì để mặc định
                    if ($(e.target).closest('a').length) return;

                    const href = $(this).find('a').attr('href');
                    if (href) {
                        window.location.href = href;
                    }
                });
            });

        } else {
            $collapseIcon.removeClass('fa-angle-double-right').addClass('fa-angle-double-left');
            $pageWrapper.removeClass('sidebar-collapsed');
            $flyoutMenu.removeClass('flyout-menu-collapsed');
            $footer.removeClass('footer-menu-collapsed');
            $navlinkSidebar.removeClass('navlink-sidebar_collapse');
            $li_Sidebar.removeClass('li-sidebar_collapse');
            $tongquan_Sidebar.removeClass('link-sidebar_tongquan');
            $flyouttitle_Sidebar.removeClass('flyout-title_collapse');
        }
    });

    // Xử lý hover cho menu cha có dropdown
    $('#sidebar-menu .nav-item.dropdown').hover(
        function () {
            $(this).find('.flyout-menu').addClass('show');
            if ($sidebar.hasClass('collapsed')) {
                $(this).find('.dropdown-toggle').addClass('hovered');
            }
        },
        function () {
            if ($sidebar.hasClass('collapsed')) {
                $(this).find('.dropdown-toggle').removeClass('hovered');
            }
            // Không ẩn ngay lập tức, chỉ ẩn khi click ra ngoài
        }
    );

    // Ngăn ẩn dropdown khi click vào menu con
    $('.flyout-menu').on('click', function (e) {
        e.stopPropagation();
    });

    // Ẩn dropdown khi click ra ngoài
    $(document).on('click', function (e) {
        if (!$(e.target).closest('.nav-item.dropdown').length) {
            $('.flyout-menu').removeClass('show');
        }
    });

    $(document).on('hide.bs.dropdown', function (e) {
        const $toggle = $(e.target); // thường là <a.dropdown-toggle>
        const $li = $toggle.closest('li');

        // Nếu LI có cả `nav-item-leftbar` + `dropdown` thì mới ngăn
        if ($li.hasClass('nav-item-leftbar') && $li.hasClass('dropdown')) {
            e.preventDefault(); // chỉ ngăn dropdown đặc biệt
        }
        // Ngược lại, để các menu khác tự đóng như bình thường
    });

    //active theo dung url tren menu

const currentUrl = window.location.href;
const url = currentUrl.split('#')[0];

$navLinks.each(function () {
    if (this.href === url) {
        $(this).addClass('active');

        // Truy ngược lên cha đến khi hết
        let $current = $(this).closest('li.dropdown');
        while ($current.length) {
            const $toggle = $current.children('a.dropdown-toggle');
            const $menu = $current.children('ul.dropdown-menu');
            const $flyout = $current.closest('.flyout-menu');

            $toggle.addClass('active');
            $menu.addClass('show');
            $flyout.addClass('show');

            // Tìm cấp cha tiếp theo
            $current = $current.parent().closest('li.dropdown');
        }

        return false; // Thoát vòng lặp sau khi tìm thấy
    }
});

});

 </script>
