@extends('layouts.layouts')

{{-- shorthand section, no @endsection needed --}}
@section('title', '<PERSON><PERSON> sách kiểm tra xóa')
@section('content')
    <!-- Bootstrap & Tabler version of VerticalTabsProfile-withUserInfo, full height -->
    <div class="container-fluid">
        <div class="w-100" style="
                height:30vh;
                background:  linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)),
              url('{{ asset('img/bg1.jpg') }}') center/cover no-repeat;
                display:flex;
                align-items:flex-end;
                justify-content:center;
            ">
            <div class="row align-items-center mb-2 w-100">
                {{-- Ảnh đại diện (3 cols on md+) --}}
                <div class="col-12 col-md-2 d-flex align-items-center justify-content-center position-relative mb-3">
                    <img id="avatarPreview" src="{{ $profilePicture ?? '/public/img/default_avatar.png' }}"
                        alt="Ảnh đại diện" class="rounded-circle"
                        style="width:25vh; height:25vh; object-fit:cover; border:2px solid #ccc;" />

                    <label for="avatarInput" class="position-absolute bottom-0 end-0 bg-white rounded-circle p-2"
                        style="cursor:pointer; transform: translate(-25%, 25%); border:1px solid #ddd;">
                        <i class="fa fa-camera"></i>
                    </label>
                    <input type="file" id="avatarInput" accept="image/*" style="display:none;" />
                </div>

                {{-- Thông tin người dùng (9 cols on md+) --}}
                <div class="col-12 col-md-10 text-white">
                    <div
                        class="d-flex flex-column flex-md-row align-items-start align-items-md-end justify-content-start justify-content-md-between gap-4 mx-4">
                        <ul class="list mb-0" style="color:#fff; font-size:16px; list-style:none; padding:0;">
                            <li class="fw-semibold mb-2" style="font-size:20px;">
                                <i class="fa fa-user"></i>
                                <span id="lblTenDangNhap">{{ $username }}</span>
                            </li>
                            <li class="fw-semibold mb-2" style="font-size:16px;">
                                <i class="ace-icon fa fa-users"></i>
                                Nhóm người dùng:&nbsp;
                                <span id="lbl_NhomNguoiDung" style="white-space:normal;">
                                    Quản trị hệ thống
                                </span>
                            </li>
                            <li class="fw-semibold">
                                <i class="fa fa-map"></i>
                                Đơn vị: <span id="lblTenDonVi">{{ $tenDonVi }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
        <div class="d-flex align-items-center justify-content-center vh-100">
            <div class="card shadow-sm w-100 h-100">
                <div class="row g-0 h-100">
                    <!-- Left Tabs -->
                    <div class="col-2 border-end h-100">
                        <div class="nav flex-column nav-pills py-3 h-100" role="tablist" aria-orientation="vertical">
                            <button class="nav-link active" id="tab-info-btn" data-bs-toggle="pill"
                                data-bs-target="#tab-info" type="button" role="tab" aria-controls="tab-info"
                                aria-selected="true">
                                <i class="fa fa-user me-2"></i>Thông tin cá nhân
                            </button>
                            <button class="nav-link" id="tab-password-btn" data-bs-toggle="pill"
                                data-bs-target="#tab-password" type="button" role="tab" aria-controls="tab-password"
                                aria-selected="false">
                                <i class="fa fa-lock me-2"></i>Đổi mật khẩu
                            </button>
                        </div>
                    </div>
                    <!-- Content Area -->
                    <div class="col-10 h-100">
                        <div class="card-body d-flex flex-column h-100">
                            <div class="tab-content flex-grow-1 overflow-auto">
                                <!-- Edit Profile Tab -->
                                <div class="tab-pane fade show active h-100" id="tab-info" role="tabpanel"
                                    aria-labelledby="tab-info-btn">
                                    <div class="row h-100">
                                        <div class="col-12" style="padding-left:20px">
                                            <fieldset class="KhungVien" id="ChonNV_Area" @if (!$showNhanVien != 1)
                                            style="display:none;" @endif>
                                                <legend><span>Chọn nhân viên</span></legend>
                                                <div class="row mb-2 text-red">
                                                    <div class="col-md-12">
                                                        Tài khoản này chưa có thông tin cá nhân. Vui lòng chọn một nhân viên
                                                        từ danh sách hoặc nhập thông tin cá nhân mới vào các ô bên dưới để
                                                        cập nhật.
                                                    </div>
                                                </div>
                                                <div class="row mb-2">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label class="">Nhân viên</label>
                                                            <select class="form-control" id="NhanVienID"></select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </fieldset>
                                            <fieldset class="KhungVien" style="padding:15px">
                                                <legend><span>Thông tin cá nhân</span></legend>
                                                <div class="row mb-2">
                                                    <div class="col-12">
                                                        <label class="validation" for="HoVaTen"> Họ và tên</label>
                                                        <input type="text" class="form-control input-sm" id="HoVaTen">
                                                    </div>
                                                </div>
                                                <div class="row mb-2">
                                                    <div class="col-md-6">
                                                        <label class="" for="NgaySinh">Ngày sinh</label>
                                                        <div class="input-group">
                                                            <input class="form-control input-sm date-picker" id="NgaySinh"
                                                                type="text" data-date-format="dd/mm/yyyy"
                                                                placeholder="dd/MM/yyyy">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="" for="GioiTinh">Giới tính</label>
                                                        <select class="form-select" id="GioiTinh"></select>
                                                    </div>
                                                </div>
                                                <div class="row mb-2">
                                                    <div class="col-md-6">
                                                        <label class="" for="CCCD">CMND/CCCD</label>
                                                        <input class="form-control input-sm" type="text" id="CCCD">
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="" for="SoDienThoai"> Số điện thoại</label>
                                                        <input class="form-control input-sm" type="text" id="SoDienThoai">
                                                    </div>
                                                </div>
                                                <div class="row mb-2">
                                                    <div class="col-md-12">
                                                        <label class="" for="DiaChi"> Địa chỉ</label>
                                                        <input class="form-control input-sm" type="text" id="DiaChi">
                                                    </div>
                                                </div>
                                                <div class="row mb-2">
                                                    <div class="col-md-12">
                                                        <label class="" for="Email">Email</label>
                                                        <input class="form-control input-sm" type="email" id="Email"
                                                            disabled readonly>
                                                    </div>
                                                </div>
                                                <div class="row mb-2">
                                                    <div class="col-md-3">
                                                        <button href="#" id="btnLuu" class="btn btn-success btn-nts-luu">
                                                            <i class="ace-icon fa fa-floppy-o bigger-110"></i>&nbsp;Lưu
                                                        </button>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                    </div>
                                </div>
                                <!-- Change Password Tab -->
                                <div class="tab-pane fade h-100" id="tab-password" role="tabpanel"
                                    aria-labelledby="tab-password-btn">
                                    <!-- card unchanged -->
                                    <div class="card card-outline-secondary h-100">
                                        <div class="card-header bg-primary text-white text-center">
                                            <h3 class="mb-0">Đổi mật khẩu</h3>
                                        </div>
                                        <div class="card-body overflow-auto">
                                            <form id="passwordChangeForm" method="POST"
                                                action="{{ route('hethong.doimatkhau') }}" autocomplete="off">
                                                <!-- form groups unchanged -->
                                                <div class="form-group mb-3">
                                                    <label for="inputPasswordOld">Mật khẩu hiện tại</label>
                                                    <div class="input-group">
                                                        <input type="password" name="current_password"
                                                            class="form-control password-input" id="inputPasswordOld"
                                                            required>
                                                        <button class="btn btn-outline-secondary btn-toggle-visibility"
                                                            type="button" tabindex="-1"><i class="fa fa-eye"></i></button>
                                                    </div>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label for="inputPasswordNew">Mật khẩu mới</label>
                                                    <div class="input-group">
                                                        <input type="password" name="new_password"
                                                            class="form-control password-input" id="inputPasswordNew"
                                                            required>
                                                        <button class="btn btn-outline-secondary btn-toggle-visibility"
                                                            type="button" tabindex="-1"><i class="fa fa-eye"></i></button>
                                                    </div>
                                                    <span class="form-text small text-muted">Mật khẩu phải dài 8–20 ký tự
                                                        và
                                                        không chứa khoảng trắng.</span>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label for="inputPasswordNewVerify">Xác nhận mật khẩu mới</label>
                                                    <div class="input-group">
                                                        <input type="password" name="new_password_confirmation"
                                                            class="form-control password-input" id="inputPasswordNewVerify"
                                                            required>
                                                        <button class="btn btn-outline-secondary btn-toggle-visibility"
                                                            type="button" tabindex="-1"><i class="fa fa-eye"></i></button>
                                                    </div>
                                                    <span class="form-text small text-muted">Nhập lại mật khẩu mới để xác
                                                        nhận.</span>
                                                </div>
                                                <div class="form-group">
                                                    <button type="submit" class="btn btn-success btn-lg">Xác
                                                        nhận</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


@endsection

@push('scripts')
    <script>
        window.Laravel = window.Laravel || {};
        window.Laravel = {
            ...window.Laravel,
            doimatkhau: "{{ route('hethong.doimatkhau') }}",
            comboNhanVien: "{{ route('dungchung.danhmuc.comboNhanVien') }}",
            comboGioiTinh: "{{ route('dungchung.danhmuc.comboGioiTinh') }}",
            getUserInfo: "{{ route('hethong.profile.getUser') }}",
            getNhanVienById: "{{ route('hethong.profile.getNhanVien') }}",
            uploadFile: "{{ route('api.files.upload') }}",
            updateAvatar: "{{ route('hethong.profile.updateAvatar') }}",
            luuThongTin: "{{ route('hethong.profile.luuThongTin') }}",
            maTuTangUrl: `{{ route('dungchung.lay-ma-tutang', ['kyhieuLoaiPhieu' => 'MaNhanVien', 'bangDuLieu' => 'nhan_viens', 'cotDuLieu' => 'maNhanVien']) }}`,
        };
    </script>
    <script src="{{ asset('js/hethong/Profile.js') }}?v={{ time() }}"></script>
@endpush