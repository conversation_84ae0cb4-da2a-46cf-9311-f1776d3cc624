<?php

namespace App\Http\Controllers\HeThong;

use App\Events\UserPasswordChanged;
use App\Models\DanhMuc\NhanVien;
use App\Services\CurrentUserService;
use App\Services\ThongBao;
use App\Http\Controllers\Controller;
use App\Models\HeThong\ChucNang;
use App\Services\DungChungDb;
use Illuminate\Support\Carbon;
use Hash;
use Illuminate\Http\Request;
use MongoDB\BSON\ObjectId;
use PhpOffice\PhpWord\TemplateProcessor;
use Illuminate\Support\Facades\Auth;
use App\Services\DungChungNoDb;
use App\Models\User;
use Illuminate\Validation\ValidationException;
use Storage;
use Str;
use Validator;// ← Thêm dòng này
use Illuminate\Database\Eloquent\Relations\BelongsTo;


class ProfileController extends Controller
{
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;
    protected CurrentUserService $currentUser;

    public function __construct(DungChungDb $dungChungDb, DungChungNoDb $logService, CurrentUserService $currentUser)
    {
        $this->dungChungDb = $dungChungDb;
        $this->logService = $logService;
        $this->currentUser = $currentUser;
    }

    public function index()
    {
        $name = $this->currentUser->username();
        $profilePicture = $this->currentUser->user()?->avatar ?? '/images/default-avatar.png'; // Default avatar if not set
        $tenDonVi = $this->currentUser->tenDonVi();
        $showNhanVien = $this->currentUser->user()?->NhanVienID != null;
        return view('hethong.profile.index', [
            'username' => $name,
            'tenDonVi' => $tenDonVi,
            'profilePicture' => $profilePicture,
            'showNhanVien' => $showNhanVien
        ]);
    }

    public function donVi(): BelongsTo
    {
        return $this->belongsTo(DonVi::class, 'DonViID', '_id');
    }

    // public function getInfo(){
    //     return $this->currentUser->user();
    // }
    public function getInfo()
    {
        $user = $this->currentUser->user();
        if ($user) {
            $user->load('donVi'); // Nạp mối quan hệ donVi
            $userData = $user->toArray(); // Chuyển đổi user thành mảng
            // Thêm TenDonVi và MaDonVi trực tiếp vào mảng userData
            if ($user->donVi) {
                $userData['TenDonVi'] = $user->donVi->TenDonVi;
                $userData['MaDonVi'] = $user->donVi->MaDonVi;
            }
            return $userData;
        }
        return null; // Trả về null nếu không có user
    }
    // public function luuThongTin(Request $request)
    // {
    //     // 1) Validation
    //     $validator = Validator::make($request->all(), [
    //         'HoVaTen' => ['required', 'string', 'max:255'],
    //         'NgaySinh' => ['nullable', 'date_format:d/m/Y'],
    //         'GioiTinh' => ['nullable', 'string'],
    //         'CCCD' => ['nullable', 'string', 'max:20'],
    //         'SoDienThoai' => ['nullable', 'string', 'max:20'],
    //         'DiaChi' => ['nullable', 'string', 'max:500'],
    //         'Email' => ['nullable', 'email', 'max:255'],
    //     ]);

    //     if ($validator->fails()) {
    //         return response()->json([
    //             'Err' => true,
    //             'Msg' => 'Dữ liệu không hợp lệ.',
    //             'Errors' => $validator->errors(),
    //         ], 422);
    //     }

    //     // 2) Get current user
    //     $user = $this->currentUser->user();
    //     if (!$user) {
    //         return response()->json([
    //             'Err' => true,
    //             'Msg' => 'Chưa đăng nhập.',
    //         ], 401);
    //     }

    //     // 3) Apply updates
    //     $data = $validator->validated();

    //     $user->name = $data['HoVaTen'];
    //     if (!empty($data['NgaySinh'])) {
    //         // convert dd/mm/YYYY → Date
    //         $user->ngaySinh = Carbon::createFromFormat('d/m/Y', $data['NgaySinh'])->startOfDay();
    //     }
    //     $user->gioiTinhID = $data['GioiTinh'] ?? null;
    //     $user->cmnd = $data['CCCD'] ?? null;
    //     $user->soDienThoai = $data['SoDienThoai'] ?? null;
    //     $user->diaChi = $data['DiaChi'] ?? null;
    //     $user->email = $data['Email'] ?? $user->email;
    //     if ($user->NhanVienId == null && $request['NhanVienID'] != null)
    //         $user->NhanVienID = $request['NhanVienID'] ?? null;

    //     $user->save();

    //     // 4) Return result
    //     return response()->json([
    //         'Err' => false,
    //         'Msg' => 'Cập nhật thông tin thành công.',
    //         'Result' => [
    //             'user' => $user->only(['name', 'ngaySinh', 'gioiTinhID', 'cmnd', 'soDienThoai', 'diaChi', 'email', 'NhanVienID']),
    //         ],
    //     ]);
    // }

    public function luuThongTin(Request $request)
    {
        // 1) Validation
        $validator = Validator::make($request->all(), [
            'HoVaTen' => ['required', 'string', 'max:255'],
            'NgaySinh' => ['nullable', 'date_format:d/m/Y'],
            'GioiTinh' => ['nullable', 'string'],
            'CCCD' => ['nullable', 'string', 'max:20'],
            'SoDienThoai' => ['nullable', 'string', 'max:20'],
            'DiaChi' => ['nullable', 'string', 'max:500'],
            'Email' => ['nullable', 'email', 'max:255'],
            'NhanVienID' => ['nullable', 'string'],
            'MaNhanVien' => ['nullable', 'string', 'max:50'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Dữ liệu không hợp lệ.',
                'Errors' => $validator->errors(),
            ], 422);
        }

        // 2) Get current user
        $user = $this->currentUser->user();
        if (!$user) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Chưa đăng nhập.',
            ], 401);
        }

        $data = $validator->validated();
        $inputNhanVienID = $request->input('NhanVienID');
        $userNhanVienID = $user->NhanVienID;

        // 3) Normalize ngày sinh
        $ngaySinh = null;
        if (!empty($data['NgaySinh'])) {
            $ngaySinh = Carbon::createFromFormat('d/m/Y', $data['NgaySinh'])->startOfDay();
        }

        // 4) Check logic các trường hợp

        // Trường hợp 1: Thêm mới nhân viên
        if (empty($userNhanVienID) && empty($inputNhanVienID)) {
            $nv = NhanVien::create([
                'maNhanVien' => $data['MaNhanVien'] ?? null,
                'tenNhanVien' => $data['HoVaTen'],
                'soDienThoai' => $data['SoDienThoai'] ?? null,
                'ngaySinh' => $ngaySinh,
                'gioiTinhID' => $data['GioiTinh'] ?? null,
                'cmnd' => $data['CCCD'] ?? null,
                'diaChi' => $data['DiaChi'] ?? null,
                'avatar' => $user->avatar ?? null,
                'donViID' => $user->DonViID,
                'userID' => new ObjectId($user->_id),
                'ngayThaoTac' => now(),
                'trangThai' => true, // Mặc định là hoạt động
            ]);
            $user->NhanVienID = new ObjectId($nv->_id);
        }

        // Trường hợp 2: User đã có nhân viên → cập nhật song song
        elseif (empty($inputNhanVienID) && !empty($userNhanVienID)) {
            $nv = NhanVien::find($userNhanVienID);
            if ($nv) {
                $nv->fill([
                    'tenNhanVien' => $data['HoVaTen'],
                    'soDienThoai' => $data['SoDienThoai'] ?? null,
                    'ngaySinh' => $ngaySinh,
                    'gioiTinhID' => $data['GioiTinh'] ?? null,
                    'cmnd' => $data['CCCD'] ?? null,
                    'diaChi' => $data['DiaChi'] ?? null,
                ])->save();
            }
        }

        // Trường hợp 3: Gán nhân viên có sẵn vào User
        elseif (!empty($inputNhanVienID) && empty($userNhanVienID)) {
            $user->NhanVienID = new ObjectId($inputNhanVienID);

            $nv = NhanVien::find($inputNhanVienID);
            if ($nv) {
                $nv->fill([
                    'tenNhanVien' => $data['HoVaTen'],
                    'soDienThoai' => $data['SoDienThoai'] ?? null,
                    'ngaySinh' => $ngaySinh,
                    'gioiTinhID' => $data['GioiTinh'] ?? null,
                    'cmnd' => $data['CCCD'] ?? null,
                    'diaChi' => $data['DiaChi'] ?? null,
                ])->save();
            }
        }

        // 5) Cập nhật thông tin user
        // $user->name = $data['HoVaTen'];
        $user->tenNhanVien = $data['HoVaTen'];
        $user->ngaySinh = $ngaySinh;
        $user->gioiTinhID = $data['GioiTinh'] ?? null;
        $user->cmnd = $data['CCCD'] ?? null;
        $user->soDienThoai = $data['SoDienThoai'] ?? null;
        // $user->diaChi = $data['DiaChi'] ?? null;
        $user->email = $data['Email'] ?? $user->email;
        $user->save();

        // 6) Return response
        return response()->json([
            'Err' => false,
            'Msg' => 'Cập nhật thông tin thành công.',
            'Result' => [
                'user' => $user->only([
                    'name', 'ngaySinh', 'gioiTinhID', 'cmnd', 'soDienThoai',
                    'diaChi', 'email', 'NhanVienID',
                ]),
            ],
        ]);
    }

    public function getNhanVienFromID(Request $request)
    {
        // 1) Grab the ID from the request. Adjust the key if you send it under a different name.
        $id = $request->input('id');

        if (!$id) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Thiếu NhanVien ID.',
                'Result' => null,
            ], 400);
        }

        try {
            // 2) Eager-load the 4 related docs and fetch by Mongo _id
            $nhanvien = NhanVien::with(['chucVu', 'phongBan', 'gioiTinh', 'donVi'])
                ->where('_id', $id)
                ->first();

            if (!$nhanvien) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Không tìm thấy nhân viên.',
                    'Result' => null,
                ], 404);
            }

            // 3) Return the full document
            return response()->json([
                'Err' => false,
                'Msg' => '',
                'Result' => $nhanvien,
            ]);
        } catch (\Exception $ex) {
            // log if you have a logger, then...
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi server: ' . $ex->getMessage(),
                'Result' => null,
            ], 500);
        }
    }

    /**
     * POST  /profile/avatar-url
     * Body: { "avatarUrl": "https://…" }
     */
    public function updateAvatarUrl(Request $request)
    {
        // 1) Validate incoming URL
        $validator = Validator::make($request->all(), [
            'avatarUrl' => ['required', 'url', 'max:1000'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Dữ liệu không hợp lệ.',
                'Errors' => $validator->errors(),
            ], 422);
        }

        // 2) Fetch the current User model
        $user = $this->currentUser->user();
        if (!$user) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Chưa đăng nhập.',
            ], 401);
        }
        $oldAvatar = $user->avatar;

        // 3) Setup paths and storage disk
        $donViid = $this->currentUser->donViId();
        $username = $this->currentUser->username();

        $tempPrefix = "uploads/{$donViid}/{$username}/files/temp/";
        $targetDir = "uploads/{$donViid}/{$username}/files/profile";

        $fileDisk = Storage::disk('public');

        // 4) Normalize new avatar URL to relative storage path
        $newAvatarUrl = $validator->validated()['avatarUrl'];

        $relativePath = trim(parse_url($newAvatarUrl, PHP_URL_PATH), '/');
        $relativePath = preg_replace('#^storage/#', '', $relativePath);

        // 5) If file is in temp folder, move to profile folder
        if (Str::startsWith($relativePath, $tempPrefix)) {
            $filename = basename($relativePath);
            $newPath = $targetDir . '/' . $filename;

            $base = pathinfo($filename, PATHINFO_FILENAME);
            $ext = pathinfo($filename, PATHINFO_EXTENSION);
            $finalPath = $newPath;

            // Ensure unique filename in target dir
            while ($fileDisk->exists($finalPath)) {
                $salt = Str::random(6);
                $finalPath = $targetDir . '/' . $base . '_' . $salt . '.' . $ext;
            }

            try {
                if ($fileDisk->exists($relativePath)) {
                    \Log::info("Moving avatar file from {$relativePath} to {$finalPath}");
                    $fileDisk->copy($relativePath, $finalPath);
                    $fileDisk->delete($relativePath);
                    $newAvatarUrl = '/storage/' . $finalPath;
                } else {
                    \Log::warning("Temp avatar file does not exist: {$relativePath}");
                }
            } catch (\Exception $e) {
                \Log::error("Failed to move avatar file: " . $e->getMessage());
                // You can decide to return error here or continue with old path
            }
        }

        // 6) Update & persist user avatar URL
        $user->avatar = $newAvatarUrl;
        $user->save();

        // 7) Delete old avatar file if applicable
        if (
            $oldAvatar
            && !Str::contains($oldAvatar, ['user.png', 'defaultavatar']) // adjust default avatar filenames here
            && Str::startsWith($oldAvatar, [url('/storage/'), '/storage/', 'storage/'])
        ) {
            $storageUrlPrefix = url('/storage') . '/';
            if (Str::startsWith($oldAvatar, $storageUrlPrefix)) {
                $oldRelativePath = Str::replaceFirst($storageUrlPrefix, '', $oldAvatar);
            } else {
                $oldRelativePath = ltrim(Str::replaceFirst('/storage/', '', $oldAvatar), '/');
            }
            if ($fileDisk->exists($oldRelativePath)) {
                \Log::info("Deleting old avatar file: {$oldRelativePath}");
                $fileDisk->delete($oldRelativePath);
            }
        }

        // 8) Return success + updated avatar URL
        return response()->json([
            'Err' => false,
            'Msg' => 'Cập nhật ảnh đại diện thành công.',
            'Result' => [
                'avatar' => $user->avatar,
            ],
        ]);
    }


    public function updateNienDo(Request $request)
    {

        if (!$request->has('NienDo')) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Thiếu tham số NienDo!'
            ]);
        }

        session(['nien_do' => $request->input('NienDo')]);

        return response()->json([
            'Err' => false,
            'canhbao' => false,
            'Msg' => 'Cập nhật niên độ thành công!'
        ]);
    }

}