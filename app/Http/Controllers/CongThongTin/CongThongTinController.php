<?php

namespace App\Http\Controllers\CongThongTin;

use App\Http\Controllers\Controller;
use App\Models\CongThongTin\TinTuc;
use App\Models\CongThongTin\LoaiTinTuc;
use App\Models\CongThongTin\ThietLapWebsite;
use App\Models\CongThongTin\BinhLuan;
use App\Models\HeThong\UserGroup;
use App\Services\JWTVerificationService;
use Illuminate\Http\Request;

class CongThongTinController extends Controller
{
    protected $jwtService;

    public function __construct(JWTVerificationService $jwtService)
    {
        $this->jwtService = $jwtService;
    }

    /**
     * Get menu categories for header and footer
     */
    private function getLoaiTinTucsMenu()
    {
        return LoaiTinTuc::forMenu()->get();
    }

    /**
     * Display the Cổng Thông Tin page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Lấy tin tức nổi bật để hiển thị (lấy nhiều hơn để tránh trùng lặp)
        $tinTucNoiBat = TinTuc::with('loaiTinTuc')
            ->active()
            ->coTheHienThi() // Chỉ lấy tin tức đã được duyệt
            ->noiBat()
            ->orderBy('NgayTao', 'desc')
            ->limit(12)
            ->get();

        // Nếu không có tin nổi bật, lấy tin tức mới nhất
        if ($tinTucNoiBat->isEmpty()) {
            $tinTucNoiBat = TinTuc::with('loaiTinTuc')
                ->active()
                ->coTheHienThi() // Chỉ lấy tin tức đã được duyệt
                ->orderBy('NgayTao', 'desc')
                ->limit(12)
                ->get();
        }

        // Lấy thông tin thiết lập website
        $thietLapWebsite = ThietLapWebsite::first();

        // Lấy loại tin tức cho header và footer
        $loaiTinTucsMenu = $this->getLoaiTinTucsMenu();

        // Kiểm tra quyền admin của user hiện tại
        $isAdmin = false;
        if (auth()->check()) {
            $user = auth()->user();
            if ($user->NhomNguoiDungID) {
                $groupCode = UserGroup::codeById($user->NhomNguoiDungID);
                $isAdmin = $groupCode !== null && strcasecmp($groupCode, 'Admin') === 0;
            }
        }

        // Không cần load components ở đây vì ViewComposer đã load rồi
        return view('congthongtin.index', compact('tinTucNoiBat', 'thietLapWebsite', 'loaiTinTucsMenu', 'isAdmin'));
    }

    /**
     * Display news listing page.
     *
     * @return \Illuminate\View\View
     */
    public function news(Request $request)
    {
        $query = TinTuc::with('loaiTinTuc')->active()->coTheHienThi();

        // Lọc theo loại tin tức nếu có
        if ($request->has('loai') && $request->loai) {
            $loaiTinTuc = LoaiTinTuc::where('DinhDanh', $request->loai)->first();
            if ($loaiTinTuc) {
                $query->where('LoaiTinTucID', $loaiTinTuc->_id);
            }
        }

        // Tìm kiếm theo từ khóa
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('TieuDe', 'like', "%{$search}%")
                    ->orWhere('NoiDungTomTat', 'like', "%{$search}%")
                    ->orWhere('TuKhoa', 'like', "%{$search}%");
            });
        }

        $tinTucs = $query->orderBy('NgayTao', 'desc')->paginate(12);
        $loaiTinTucs = LoaiTinTuc::active()
            ->with(['tinTucs' => function ($query) {
                $query->active()->coTheHienThi();
            }])
            ->get();

        // Lấy thông tin thiết lập website
        $thietLapWebsite = ThietLapWebsite::first();

        // Lấy loại tin tức cho header và footer
        $loaiTinTucsMenu = $this->getLoaiTinTucsMenu();

        return view('congthongtin.news.index', compact('tinTucs', 'loaiTinTucs', 'thietLapWebsite', 'loaiTinTucsMenu'));
    }

    /**
     * Display news detail page.
     *
     * @param string $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        $tinTuc = TinTuc::with('loaiTinTuc')
            ->where('DinhDanh', $slug)
            ->active()
            ->coTheHienThi() // Chỉ hiển thị tin tức đã được duyệt
            ->firstOrFail();

        // Tăng lượt xem
        $tinTuc->incrementViews();

        // Lấy tin tức liên quan (cùng loại, khác bài hiện tại)
        $tinTucLienQuan = TinTuc::with('loaiTinTuc')
            ->where('LoaiTinTucID', $tinTuc->LoaiTinTucID)
            ->where('_id', '!=', $tinTuc->_id)
            ->active()
            ->coTheHienThi() // Chỉ lấy tin tức đã được duyệt
            ->orderBy('NgayTao', 'desc')
            ->limit(12)
            ->get();

        // Lấy thông tin thiết lập website
        $thietLapWebsite = ThietLapWebsite::first();

        // Lấy loại tin tức cho header và footer
        $loaiTinTucsMenu = $this->getLoaiTinTucsMenu();

        return view('congthongtin.news.detail', compact('tinTuc', 'tinTucLienQuan', 'thietLapWebsite', 'loaiTinTucsMenu'));
    }

    /**
     * Store a new comment for a news article.
     *
     * @param Request $request
     * @param string $slug
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeBinhLuan(Request $request, $slug)
    {
        try {
            // Check if verification is enabled
            $thietLapWebsite = ThietLapWebsite::first();
            $isVerificationEnabled = $thietLapWebsite && $thietLapWebsite->ck_XacThucTaiKhoan;

            // Prepare validation rules
            $rules = [
                'g-recaptcha-response' => 'required|captcha',
                'HoVaTen' => 'required|string|max:255',
                'Email' => 'required|email|max:255',
                'BinhLuan' => 'required|string|max:1000',
            ];

            $messages = [
                'g-recaptcha-response.required' => 'Vui lòng xác nhận bạn không phải là robot',
                'g-recaptcha-response.captcha' => 'Vui lòng xác nhận bạn không phải là robot',
                'HoVaTen.required' => 'Vui lòng nhập họ và tên',
                'HoVaTen.max' => 'Họ và tên không được vượt quá 255 ký tự',
                'Email.required' => 'Vui lòng nhập email',
                'Email.email' => 'Email không đúng định dạng',
                'Email.max' => 'Email không được vượt quá 255 ký tự',
                'BinhLuan.required' => 'Vui lòng nhập nội dung bình luận',
                'BinhLuan.max' => 'Bình luận không được vượt quá 1000 ký tự',
            ];

            // Add verification code validation if enabled
            if ($isVerificationEnabled) {
                $rules['verification_code'] = 'required|string|size:6';
                $rules['jwt_token'] = 'required|string';
                $messages['verification_code.required'] = 'Vui lòng nhập mã xác thực.';
                $messages['verification_code.size'] = 'Mã xác thực phải có 6 chữ số.';
                $messages['jwt_token.required'] = 'JWT token là bắt buộc.';
            }

            // Validate input including captcha
            $request->validate($rules, $messages);

            // Verify the verification code if enabled
            if ($isVerificationEnabled) {
                $targetEmail = $request->input('Email'); // Use email from form
                $verificationCode = $request->input('verification_code');
                $jwtToken = $request->input('jwt_token');

                $isCodeValid = $this->jwtService->verifyCode($jwtToken, $targetEmail, $verificationCode);

                if (!$isCodeValid) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Mã xác thực không hợp lệ hoặc đã hết hạn.',
                        'errors' => [
                            'verification_code' => ['Mã xác thực không hợp lệ hoặc đã hết hạn.']
                        ]
                    ], 422);
                }
            }

            // Find the news article
            $tinTuc = TinTuc::where('DinhDanh', $slug)
                ->active()
                ->coTheHienThi()
                ->firstOrFail();

            // Create new comment
            $binhLuan = new BinhLuan([
                'TinTucID' => $tinTuc->_id,
                'TieuDe' => $tinTuc->TieuDe,
                'HoVaTen' => $request->HoVaTen,
                'Email' => $request->Email,
                'BinhLuan' => $request->BinhLuan,
                'NgayTao' => now(),
                'DangSD' => true,
            ]);

            // Thiết lập trạng thái kiểm duyệt dựa trên cài đặt
            $binhLuan->setTrangThaiKiemDuyetKhiTao();
            $binhLuan->save();

            // Xác định thông báo dựa trên cài đặt kiểm duyệt
            $thietLap = \App\Models\CongThongTin\ThietLapWebsite::first();
            $message = 'Bình luận của bạn đã được gửi thành công.';

            if ($thietLap && $thietLap->ck_KiemDuyetBinhLuan) {
                $message = 'Bình luận của bạn đã được gửi và đang chờ kiểm duyệt.';
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => [
                    'id' => $binhLuan->_id,
                    'HoVaTen' => $binhLuan->HoVaTen,
                    'BinhLuan' => $binhLuan->BinhLuan,
                    'NgayTao' => $binhLuan->formatted_ngay_tao,
                    'TrangThaiKiemDuyet' => $binhLuan->TrangThaiKiemDuyet,
                ]
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi gửi bình luận. Vui lòng thử lại.'
            ], 500);
        }
    }

    /**
     * Get comments for a news article.
     *
     * @param Request $request
     * @param string $slug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBinhLuan(Request $request, $slug)
    {
        try {
            // Find the news article
            $tinTuc = TinTuc::where('DinhDanh', $slug)
                ->active()
                ->coTheHienThi()
                ->firstOrFail();

            $page = $request->get('page', 1);
            $perPage = 3;

            // Get comments with pagination using updated scope
            $binhLuans = BinhLuan::where('TinTucID', $tinTuc->_id)
                ->coTheHienThi() // Scope đã được cập nhật để xử lý logic kiểm duyệt
                ->orderBy('NgayTao', 'desc')
                ->skip(($page - 1) * $perPage)
                ->take($perPage)
                ->get();

            // Get total count for pagination
            $totalCount = BinhLuan::where('TinTucID', $tinTuc->_id)
                ->coTheHienThi()
                ->count();

            $hasMore = ($page * $perPage) < $totalCount;

            // Format comments data
            $formattedComments = $binhLuans->map(function ($binhLuan) {
                return [
                    'id' => $binhLuan->_id,
                    'HoVaTen' => $binhLuan->HoVaTen,
                    'BinhLuan' => $binhLuan->BinhLuan,
                    'NgayTao' => $binhLuan->formatted_ngay_tao,
                    'TrangThaiKiemDuyet' => $binhLuan->TrangThaiKiemDuyet,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedComments,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $totalCount,
                    'has_more' => $hasMore,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải bình luận.'
            ], 500);
        }
    }

    /**
     * Toggle lock status of news article
     *
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleLockNews(Request $request, $id)
    {
        try {
            $tinTuc = TinTuc::findOrFail($id);

            $lockStatus = $request->input('lock_status', false);

            $tinTuc->update([
                'ck_KhoaBaiDang' => $lockStatus,
                'updated_at' => now()
            ]);

            $action = $lockStatus ? 'khóa' : 'mở khóa';

            return response()->json([
                'success' => true,
                'message' => "Đã {$action} bài đăng thành công!",
                'lock_status' => $lockStatus
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }
}
