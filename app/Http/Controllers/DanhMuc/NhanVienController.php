<?php

namespace App\Http\Controllers\DanhMuc;

use App\Http\Controllers\Controller;
use App\Models\DanhMuc\ChucVu;
use App\Models\DanhMuc\DonVi;
use App\Models\DanhMuc\GioiTinh;
use App\Models\DanhMuc\PhongBan;
use App\Services\CurrentUserService;
use Illuminate\Http\Request;
use App\Models\DanhMuc\NhanVien;
use MongoDB\BSON\ObjectId;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Arr;
use App\Services\DungChungDb;
use App\Services\DungChungNoDb;
use App\Services\ThongBao;
use Exception;
use Illuminate\Support\Facades\DB;

class NhanVienController extends Controller
{
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;
    protected CurrentUserService $currentUser;

    public function __construct(DungChungDb $dungChungDb, DungChungNoDb $logService, CurrentUserService $currentUser)
    {
        $this->dungChungDb = $dungChungDb;
        $this->logService = $logService;
        $this->currentUser = $currentUser;
    }

    /**
     * Hiển thị trang danh sách cán bộ, công chức, viên chức
     */
    public function index()
    {
        return view('danhmuc.nhanvien.index');
    }

    function isMongoId($id)
    {
        return is_string($id) && preg_match('/^[a-f\d]{24}$/i', $id);
    }

    function safeObjectId($id)
    {
        return $this->isMongoId($id) ? new ObjectId($id) : null;
    }
    public function luuThongTin(Request $request)
    {
        $now = now();
        $userId = auth()->id() ? new ObjectId(auth()->id()) : null;

        $data = $request->only([
            'maNhanVien',
            'tenNhanVien',
            'soDienThoai',
            'diaChi',
            'phongBanID',
            'chucVuID',
            'gioiTinhID',
            'ngaySinh',
            'cmnd',
            'ngayCap',
            'noiCap',
            'trangThai',
        ]);

        if (!empty($data['phongBanID'])) {
            $data['phongBanID'] = new ObjectId($data['phongBanID']);
        }
        if (!empty($data['chucVuID'])) {
            $data['chucVuID'] = new ObjectId($data['chucVuID']);
        }
        if (!empty($data['gioiTinhID'])) {
            $data['gioiTinhID'] = new ObjectId($data['gioiTinhID']);
        }

        $meta = [
            'donViID' => $this->safeObjectId($this->currentUser->donViId()),
            'nguoiCapNhat' => $userId,
            'ngayCapNhat' => $now,
            'ngayThaoTac' => $now,
            'userID' => $userId,
        ];

        if (!empty($data['ngaySinh'])) {
            $data['ngaySinh'] = Carbon::createFromFormat('d/m/Y', $data['ngaySinh'])->toDateString();
        }

        if (!empty($data['ngayCap'])) {
            $data['ngayCap'] = Carbon::createFromFormat('d/m/Y', $data['ngayCap'])->toDateString();
        }

        try {
            if ($request->isMethod('post')) {
                if ($this->dungChungDb->kiemTraTonTai('nhanvien', 'maNhanVien', $data['maNhanVien'])) {
                    return response()->json(['Err' => true, 'Msg' => ThongBao::coLoiXayRa('Mã cán bộ, công chức, viên chức đã tồn tại')]);
                }

                $insert = array_merge($data, [
                    'nguoiTao' => $userId,
                    'ngayTao' => $now,
                ], $meta);

                NhanVien::create($insert);
                $this->logService->ghiLogs($request->all(), 'them', auth()->id(), null);

                return response()->json(['Err' => false, 'Msg' => 'Thêm cán bộ, công chức, viên chức thành công!']);
            }

            if ($request->isMethod('put')) {
                $modelId = $request->input('nhanVienId');
                $nv = NhanVien::findOrFail($modelId);

                $exists = DB::connection('mongodb')
                    ->table('nhanvien')
                    ->where('maNhanVien', $data['maNhanVien'])
                    ->where('_id', '<>', new ObjectId($modelId))
                    ->exists();

                if ($exists) {
                    return response()->json(['Err' => true, 'Msg' => ThongBao::coLoiXayRa('Mã cán bộ, công chức, viên chức đã tồn tại')]);
                }

                $nv->update(array_merge($data, $meta));
                $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);

                return response()->json(['Err' => false, 'Msg' => 'Cập nhật cán bộ, công chức, viên chức thành công!']);
            }

            abort(405);
        } catch (\Throwable $e) {
            $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'Debug' => $e->getMessage(),
            ]);
        }
    }

    public function getAll()
    {
        try {
            $donViId = $this->currentUser->donViId();
            $donViObjID = null;
            try {
                $donViObjID = new ObjectId($donViId);
            } catch (\Exception $e) {
            }

            $rows = NhanVien::with(['phongBan', 'chucVu', 'gioiTinh'])
                ->where(function ($query) use ($donViId, $donViObjID) {
                    $query->where('donViID', $donViId);
                    if ($donViObjID) {
                        $query->orWhere('donViID', $donViObjID);
                    }
                })
                ->orderBy('maNhanVien')
                ->get()
                ->map(function ($nv) {
                    return collect([
                        'id' => $nv->_id,
                        'maNhanVien' => $nv->maNhanVien,
                        'tenNhanVien' => $nv->tenNhanVien,
                        'soDienThoai' => $nv->soDienThoai,
                        'tenChucVu' => $nv->chucVu?->tenChucVu,
                        'tenPhongBan' => $nv->phongBan?->TenPhongBan,
                        'tenGioiTinh' => $nv->gioiTinh?->TenGioiTinh,
                        'ngaySinh' => optional($nv->ngaySinh)->format('d/m/Y'),
                        'ngayBatDauLamViec' => optional($nv->ngayBatDauLamViec)->format('d/m/Y'),
                        'trangThai' => $nv->trangThai,
                    ])->all();
                });

            return response()->json([
                'Err' => false,
                'Result' => $rows,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }



    public function loadDuLieuSua(Request $request)
    {
        try {
            $id = $request->input('id');
            $nv = NhanVien::find($id);

            if (!$nv) {
                return response()->json(['Err' => true, 'Msg' => 'Không tìm thấy cán bộ, công chức, viên chức']);
            }

            return response()->json([
                'Err' => false,
                'Result' => [$nv->toArray()],

            ]);
        } catch (Exception $e) {
            $this->logService->ghiLogs(['id' => $request->input('id')], 'loadDuLieuSua', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json(['Err' => true, 'Msg' => ThongBao::coLoiXayRa($code)]);
        }
    }

    public function xoa(Request $request)
    {
        try {
            $id = $request->input('ma');
            $nv = NhanVien::whereKey($id)->firstOrFail();

            if (!$nv) {
                return response()->json([
                    'err' => true,
                    'msg' => ThongBao::thaoTacThatBai(),
                ]);
            }

            $nv->delete();
            return response()->json(['Err' => false, 'Msg' => ThongBao::xoaThanhCong()]);
        } catch (Exception $e) {
            $this->logService->ghiLogs(['ma' => $request->input('ma')], 'xoa', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json(['Err' => true, 'Msg' => ThongBao::coLoiXayRa($code)]);
        }
    }



    #region Nhận excel
    /**
     * Kiểm tra cấu trúc file, load lên session cho preview
     */


    public function checkExcel(Request $request)
    {
        $path = $request->input('path', '');
        $sheet = $request->input('sheet', '');

        if (!$path || !$sheet) {
            return response()->json(['Err' => true, 'Msg' => 'Missing path or sheet'], 422);
        }

        // Turn URL “/storage/…” into storage/app/public-relative
        $urlPath = parse_url($path, PHP_URL_PATH);
        $urlPath = preg_replace('#^/+/#', '/', $urlPath);
        $relative = Str::after($urlPath, '/storage/');

        if (!Storage::disk('public')->exists($relative)) {
            return response()->json(['Err' => true, 'Msg' => 'File not found on server'], 404);
        }
        $fullPath = Storage::disk('public')->path($relative);

        try {
            // load data‐only
            $reader = IOFactory::createReaderForFile($fullPath);
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($fullPath);

            if (!$spreadsheet->sheetNameExists($sheet)) {
                return response()->json([
                    'Err' => true,
                    'Msg' => "Sheet “{$sheet}” không tồn tại"
                ], 422);
            }

            $ws = $spreadsheet->getSheetByName($sheet);
            $rows = $ws->toArray(null, true, true, false);

            // grab & trim header
            $header = array_map('trim', array_shift($rows));

            // required columns
            $required = [
                'Mã cán bộ, công chức, viên chức',
                'Họ',
                'Tên',
                'Email',
                'Số điện thoại',
                'Phòng ban',
                'Chức vụ',
                'Ngày bắt đầu',
                'Địa chỉ'
            ];
            if ($missing = array_diff($required, $header)) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Thiếu cột: ' . implode(', ', $missing),
                ], 422);
            }

            // combine into assoc rows
            $assoc = [];
            foreach ($rows as $r) {
                if (count($r) === count($header)) {
                    $assoc[] = array_combine($header, $r);
                }
            }

            session(['nhanvien_excel' => $assoc]);

            return response()->json(['Err' => false]);
        } catch (\Throwable $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi đọc file: ' . $e->getMessage(),
            ], 500);
        }
    }



    /**
     * Trả về JSON để Tabulator preview
     */
    public function loadExcel()
    {
        $rows = collect(session('nhanvien_excel', []));

        $data = $rows->map(function (array $r) {
            // raw cell value for “Ngày bắt đầu”
            $val = trim($r['Ngày bắt đầu'] ?? '');

            // convert either Excel serial or d/m/Y text into Y-m-d
            if (is_numeric($val)) {
                try {
                    $dt = Date::excelToDateTimeObject((float) $val);
                } catch (\Throwable $e) {
                    $dt = null;
                }
            } elseif ($val !== '') {
                $dt = Carbon::createFromFormat('d/m/Y', $val);
            } else {
                $dt = null;
            }

            return [
                'MaNhanVien' => $r['Mã cán bộ, công chức, viên chức'] ?? '',
                'Ho' => $r['Họ'] ?? '',
                'Ten' => $r['Tên'] ?? '',
                'Email' => $r['Email'] ?? '',
                'SoDienThoai' => $r['Số điện thoại'] ?? '',
                'PhongBan' => $r['Phòng ban'] ?? '',
                'ChucVu' => $r['Chức vụ'] ?? '',
                'NgayBatDau' => $dt ? $dt->format('Y-m-d') : null,
                'DiaChi' => $r['Địa chỉ'] ?? '',
                'TrangThai' => 'Chờ nhận',
            ];
        });

        return response()->json($data);
    }

    /**
     * Nhập những dòng được chọn
     */
    public function importExcel(Request $request)
    {
        $selected = $request->input('rows', []);
        $all = collect(session('nhanvien_excel', []));

        $out = [];

        foreach ($selected as $idx) {
            // get the Nth row (assoc array) or null
            $r = $all->get($idx);

            if (!$r || !is_array($r)) {
                $out[$idx] = ['Err' => true, 'Msg' => 'Dữ liệu hàng không tồn tại'];
                continue;
            }

            // validate the raw Excel columns
            $v = Validator::make($r, [
                'Mã cán bộ, công chức, viên chức' => 'required|unique:nhan_viens,maNhanVien',
                'Email' => 'required|email',
                // we’ll parse the date manually, so just require it be present
                'Ngày bắt đầu' => 'required',
            ]);

            if ($v->fails()) {
                $out[$idx] = ['Err' => true, 'Msg' => $v->Errors()->first()];
                continue;
            }

            // normalize “Ngày bắt đầu” → Y-m-d
            $rawDate = trim($r['Ngày bắt đầu']);
            if (is_numeric($rawDate)) {
                // Excel serial
                $dt = Date::excelToDateTimeObject((float) $rawDate);
            } else {
                // dd/mm/yyyy text
                try {
                    $dt = Carbon::createFromFormat('d/m/Y', $rawDate);
                } catch (\Throwable $e) {
                    $out[$idx] = ['Err' => true, 'Msg' => 'Định dạng ngày không hợp lệ'];
                    continue;
                }
            }

            // finally: insert into Mongo
            NhanVien::create([
                'maNhanVien' => $r['Mã cán bộ, công chức, viên chức'],
                'ho' => $r['Họ'],
                'ten' => $r['Tên'],
                'email' => $r['Email'],
                'soDienThoai' => Arr::get($r, 'Số điện thoại', ''),
                'phongBan' => Arr::get($r, 'Phòng ban', ''),
                'chucVu' => Arr::get($r, 'Chức vụ', ''),
                'ngayBatDauLamViec' => Carbon::instance($dt)->toDateString(),
                'diaChi' => Arr::get($r, 'Địa chỉ', ''),
                'trangThai' => true,
                'createdBy' => new ObjectId(auth()->id()),
                'createdAt' => now(),
                'updatedBy' => new ObjectId(auth()->id()),
                'updatedAt' => now(),
            ]);

            $out[$idx] = ['Err' => false];
        }

        return response()->json($out);
    }

    public function loadSheetNames(Request $request)
    {
        $path = $request->input('path');
        if (!$path) {
            return response()->json(['Err' => true, 'Msg' => 'Missing file path'], 422);
        }

        // 1) extract just the path part, e.g. "//storage/uploads/…"
        $urlPath = parse_url($path, PHP_URL_PATH);

        // 2) collapse multiple leading slashes: "/storage/uploads/…"
        $urlPath = preg_replace('#^/+/#', '/', $urlPath);

        // 3) grab everything after "/storage/" → "uploads/…"
        $relative = Str::after($urlPath, '/storage/');

        // 4) now check on the public disk
        if (!Storage::disk('public')->exists($relative)) {
            return response()->json(['Err' => true, 'Msg' => 'File not found on disk'], 404);
        }

        $fullPath = Storage::disk('public')->path($relative);

        try {
            // create the best reader for this file
            $reader = IOFactory::createReaderForFile($fullPath);
            // only list sheet names, no data
            $sheetNames = $reader->listWorksheetNames($fullPath);
            $Result = array_map(fn($name) => ['TenSheet' => $name], $sheetNames);

            $payload = [
                'CanhBao' => false,
                'Xem' => false,
                'Them' => false,
                'Sua' => false,
                'Xoa' => false,
                'InAn' => false,
                'Nap' => false,
                'Quyen1' => false,
                'Quyen2' => false,
                'Quyen3' => false,
                'Err' => false,
                'ErrCode' => '',
                'ErrCatch' => '',
                'Result' => $Result,
                'Msg' => '',
                'Logs' => '',
                'Redirect' => false,
                'RedirectUrl' => '',
            ];

            // encode as JSON string
            $json = json_encode($payload);

            // return it as a plain-text response
            return response($json, 200)
                ->header('Content-Type', 'text/html');
        } catch (\Throwable $e) {
            // return the real exception message so we can see why it fails
            return response()->json([
                'Err' => true,
                'Msg' => 'Error reading file: ' . $e->getMessage()
            ], 500);
        }
    }
    public function downloadTemplate()
    {
        return Excel::download(
            new NhanVienTemplateExport,
            'Mau_NhanVien.xlsx'
        );
    }
    #endregion
    #endregion

}
#region NhanVienTemplateExport
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
class NhanVienTemplateExport implements FromCollection, WithHeadings, ShouldAutoSize
{
    /** No data rows, just headings */
    public function collection()
    {
        return collect();
    }

    /** The template’s columns */
    public function headings(): array
    {
        return [
            'Mã cán bộ, công chức, viên chức',
            'Họ',
            'Tên',
            'Email',
            'Số điện thoại',
            'Phòng ban',
            'Chức vụ',
            'Ngày bắt đầu',
            'Địa chỉ',
        ];
    }
    #endregion
}
