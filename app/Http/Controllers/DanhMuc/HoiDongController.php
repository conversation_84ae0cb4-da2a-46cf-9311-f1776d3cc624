<?php

namespace App\Http\Controllers\DanhMuc;

use App\Http\Controllers\Controller;
use App\Models\DanhMuc\ChucVu;
use App\Models\DanhMuc\HoiDong;
use App\Models\DanhMuc\HoiDong_ThanhPhan;
use App\Services\CurrentUserService;
use App\Services\DungChungDb;
use App\Services\DungChungNoDb;
use App\Services\ThongBao;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Storage;
use Str;

class HoiDongController extends Controller
{
    protected DungChungDb $svc;
    protected DungChungNoDb $svcnodb;
    protected CurrentUserService $currentUser;

    public function __construct(DungChungDb $dungChungDb, CurrentUserService $currentUser, DungChungNoDb $svcnodb)
    {
        $this->svc = $dungChungDb;
        $this->currentUser = $currentUser;
        $this->svcnodb = $svcnodb;
    }
    public function index()
    {
        return view('danhmuc.hoidong.index');
    }

    public function getAll()
    {
        $donViId = $this->currentUser->donViId();
        $list = HoiDong::where('DonViID', $donViId)
            // ->where('LoaiHoiDong', 0)
            ->orderBy('NgayThanhLap', 'desc')
            ->get([
                'HoiDongID',
                'TenHoiDong',
                'SoQDThanhLap',
                'NgayKy',
                'CQBanHanh',
                'TrichYeu',
                'DinhKem',
                'NguoiKy',
                'ChucVuNguoiKy',
                'LoaiHoiDong',
                'TrangThai'
            ]);

        return response()->json(['Err' => false, 'Result' => $list]);
    }

    public function getAllCT(Request $request)
    {
        $id = $request->input('HoiDongID');
        $items = HoiDong_ThanhPhan::where('HoiDongID', $id)
            ->get(['_id', 'HoiDongID', 'HoTen', 'ChucVu', 'ChuTich', 'ChucDanh']);

        return response()->json(['Err' => false, 'Result' => $items]);
    }

    public function getAllNamXet()
    {
        $years = collect(range(2000, now()->year))->map(function ($y) {
            return ['Nam' => $y, 'NamXetDuyet' => (string) $y];
        });
        return response()->json($years);
    }

    public function loadDuLieuSua(Request $request)
    {
        $id = $request->input('HoiDongID');
        return response()->json(['Err' => false, 'Result' => HoiDong::find($id)]);
    }

    public function loadDuLieuSuaCT(Request $request)
    {
        $id = $request->input('HoiDong_ThanhPhanID');
        return response()->json(['Err' => false, 'Result' => HoiDong_ThanhPhan::find($id)]);
    }

    public function kiemTraTonTai(Request $request)
    {
        $exists = HoiDong::where($request->tenCot, $request->ma)->exists();
        return response($exists ? 'true' : 'false');
    }

    protected function saveFiles(string $paths): string
    {
        $donViid = $this->currentUser->donViId();
        $username = $this->currentUser->username();
        $disk = Storage::disk('public');

        $permanentBase = "uploads/{$donViid}/{$username}/files/HoiDong";
        $results = [];

        foreach (explode('|', $paths) as $p) {
            $p = trim($p);
            if (!$p) {
                continue;
            }

            // grab only the path portion (e.g. "/storage/uploads/…")
            $urlPath = parse_url($p, PHP_URL_PATH);

            // if it already lives under our permanent folder, keep it
            if (Str::startsWith($urlPath, "/storage/{$permanentBase}/")) {
                $results[] = $urlPath;
                continue;
            }

            // otherwise treat it as a "temp" file under /storage/…
            // strip leading "/" and "storage/" to get the disk key
            $diskKey = preg_replace('#^/storage/#', '', $urlPath);

            // build a new filename in the permanent folder
            $name = pathinfo($diskKey, PATHINFO_FILENAME);
            $ext = pathinfo($diskKey, PATHINFO_EXTENSION);
            $slug = Str::slug($name);
            $newName = "{$slug}-" . time() . ".{$ext}";
            $newKey = "{$permanentBase}/{$newName}";

            // move on the 'public' disk (move = copy + delete original)
            $disk->move($diskKey, $newKey);

            // push the new public URL
            $results[] = '/storage/' . $newKey;
        }

        // re‐implode with '|'
        return implode('|', $results);
    }

    public function luuThongTin(Request $request)
    {
        $donViid = $this->currentUser->donViId();
        $data = $request->all();
        $rawDate = $data['data']['NgayKy'] ?? null;

        if ($rawDate) {
            $data['data']['NgayKy'] = Carbon::createFromFormat('d/m/Y', $rawDate);
        }

        $paths = $this->saveFiles($data['data']['DinhKem'] ?? '');

        $attrs = array_merge($data['data'], [
            'DinhKem' => $paths,
            'DonViID' => $donViid,
            'UserID' => auth()->id(),
        ]);

        // ─── DUPLICATE CHECK ────────────────────────────────────────────

        $soQD = trim($data['data']['SoQDThanhLap'] ?? '');
        $tenHD = trim($data['data']['TenHoiDong'] ?? '');
        $id = $data['HoiDongID'] ?? null;

        if ($soQD !== '') {
            // check SoQDThanhLap
            $dup = HoiDong::where('DonViID', $donViid)
                ->where('SoQDThanhLap', $soQD);
            if ($id) {
                $dup->where('_id', '<>', $id);
            }
            if ($dup->exists()) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Số quyết định thành lập này đã tồn tại trong hệ thống',
                ], 200);
            }
        } elseif ($tenHD !== '') {
            // fallback: check TenHoiDong
            $dup = HoiDong::where('DonViID', $donViid)
                ->where('TenHoiDong', $tenHD);
            if ($id) {
                $dup->where('_id', '<>', $id);
            }
            if ($dup->exists()) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Tên hội đồng này đã tồn tại trong hệ thống',
                ], 200);
            }
        }
        // ────────────────────────────────────────────────────────────────

        $model = HoiDong::updateOrCreate(
            ['_id' => $id],
            $attrs
        );

        return response()->json([
            'Err' => false,
            'Msg' => $id
                ? ThongBao::capNhatThanhCong()
                : ThongBao::themThanhCong(),
            'Result' => $model,
        ]);
    }


    public function luuThongTinCT(Request $request)
    {
        $data = $request->all();
        $model = HoiDong_ThanhPhan::updateOrCreate(
            ['_id' => $data['id'] ?? null],
            $data + ['HoiDongID' => $data['HoiDongID']]
        );
        return response()->json(['Err' => false, 'Result' => $model]);
    }

    public function xoa(Request $request)
    {
        HoiDong::destroy($request->id);
        return response()->json(['Err' => false, 'Msg' => ThongBao::xoaThanhCong()]);
    }

    public function xoaHoiDong_ThanhPhan(Request $request)
    {
        HoiDong_ThanhPhan::destroy($request->id);
        return response()->json(['Err' => false, 'Msg' => ThongBao::xoaThanhCong()]);
    }

    public function getAllChucVu()
    {
        return response()->json(ChucVu::where('TrangThai', 0)->get());
    }

    public function loadDuLieuAutoComplete()
    {
        return response()->json(ChucVu::where('TrangThai', 0)->pluck('TenChucVu'));
    }
}