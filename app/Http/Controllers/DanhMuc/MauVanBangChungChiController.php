<?php

namespace App\Http\Controllers\DanhMuc;

use App\Services\ThongBao;
use MongoDB\BSON\ObjectId;
use Illuminate\Http\Request;
use App\Services\DungChungDb;
use PhpOffice\PhpWord\PhpWord;
use App\Models\DanhMuc\TieuChi;
use App\Services\DungChungNoDb;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Shared\Html;
use App\Http\Controllers\Controller;
use App\Services\CurrentUserService;
use Illuminate\Support\Facades\Auth;
use PhpOffice\PhpWord\SimpleType\Jc;
use App\Services\OfficeConvertService;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\TemplateProcessor;
use PhpOffice\PhpWord\SimpleType\JcTable;
use App\Models\DanhMuc\MauVanBangChungChi;
use App\Models\DanhMuc\MauVanBangChungChiCT;
use App\Models\DanhMuc\LoaiPhoiVanBangChungChi;

class MauVanBangChungChiController extends Controller
{
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;
    protected CurrentUserService $currentUser;

    public function __construct(DungChungDb $dungChungDb, DungChungNoDb $logService, CurrentUserService $currentUser)
    {
        $this->dungChungDb = $dungChungDb;
        $this->logService  = $logService;
        $this->currentUser = $currentUser;
    }


    public function index()
    {
        return view('danhmuc.mauvanbangchungchi.index');
    }
    /**
     * Thêm mới hoặc cập nhật thông tin đia bàn
     */
    public function luuthongtin(Request $request)
    {
        try {
            $MaMauVanBangChungChi = $request->input('MaMauVanBangChungChi');
            $TenMauVanBangChungChi = $request->input('TenMauVanBangChungChi');
            $LoaiVanBangChungChi = $request->input('LoaiVanBangChungChi');
            $MoTa = $request->input('MoTa');
            $CanCuPhapLy = $request->input('CanCuPhapLy');
             $TrangThai = filter_var($request->input('TrangThai'), FILTER_VALIDATE_BOOLEAN);
            $MauVanBangChungChiId = $request->input('MauVanBangChungChiId');
            $userId = auth()->id() ? new ObjectId(auth()->id()) : null;
            $now = now();

            if ($request->isMethod('post')) {
                // duplicate check
                if ($this->dungChungDb->kiemTraTonTai('mau_van_bang_chung_chi_s','MaMauVanBangChungChi',$MaMauVanBangChungChi)) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Mã mẫu văn bằng, chứng chỉ đã tồn tại!'
                    ]);
                }
            // Insert
            MauVanBangChungChi::create([
                    'MaMauVanBangChungChi'    => $MaMauVanBangChungChi,
                    'TenMauVanBangChungChi'    => $TenMauVanBangChungChi,
                    'LoaiVanBangChungChi'    => $LoaiVanBangChungChi,
                    'MoTa'    => $MoTa,
                    'CanCuPhapLy'    => $CanCuPhapLy,
                    'TrangThai'    => $TrangThai,
                    'UserID_ThaoTac'   => $userId,
                    'UserID_CapNhat'   => $userId,
                    'NgayThaoTac'   => $now,
                    'NgayCapNhat'   => $now,
                ]);

                return response()->json([
                    'Err' => false,
                    'Msg' => 'Thêm mẫu văn bằng, chứng chỉ thành công!'
                ]);
            }

            if ($request->isMethod('put') || $request->isMethod('patch')) {
                // Update
                $MauVanBangChungChi = MauVanBangChungChi::find($MauVanBangChungChiId);
                if (!$MauVanBangChungChi) {
                    return response()->json([
                        'Err' => true,
                'canhbao' => true,
                        'Msg' => 'Không tìm thấy mẫu văn bằng, chứng chỉ cần cập nhật!'
                    ]);
                }

                $MauVanBangChungChi->update([
                    'MaMauVanBangChungChi'    => $MaMauVanBangChungChi,
                    'TenMauVanBangChungChi'    => $TenMauVanBangChungChi,
                    'LoaiVanBangChungChi'    => $LoaiVanBangChungChi,
                    'MoTa'    => $MoTa,
                    'CanCuPhapLy'    => $CanCuPhapLy,
                    'TrangThai'    => $TrangThai,
                    'UserID_CapNhat'   => $userId,
                    'NgayCapNhat'   => $now,
                ]);

                return response()->json([
                    'Err' => false,
                    'Msg' => 'Cập nhật mẫu văn bằng, chứng chỉ thành công!'
                ]);
            }

            // Nếu không phải POST hoặc PUT
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Phương thức HTTP không hợp lệ!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }


    /**
     * Lấy toàn bộ đia bàn dưới dạng JSON
     */
    public function getAll()
    {
        try {
            $dsLoaiVanBang = LoaiPhoiVanBangChungChi::pluck('TenLoaiPhoiVanBangChungChi', 'MaLoaiPhoiVanBangChungChi');

            $result = MauVanBangChungChi::orderBy('MaMauVanBangChungChi', 'asc')->get();

            $result->transform(function ($item) use ($dsLoaiVanBang) {
                $maLoai = $item->LoaiVanBangChungChi;
                $item->TenLoaiPhoiVanBangChungChi = $dsLoaiVanBang[$maLoai] ?? 'Không rõ loại VB';
                return $item;
            });

            return response()->json([
                'Err'    => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err'   => true,
                'Msg'   => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Tải dữ liệu cho form sửa
     */
    public function loadDuLieuSua(Request $request)
    {
        $id = $request->input('id');
        $MauVanBangChungChiId = MauVanBangChungChi::find($id);

        if (!$MauVanBangChungChiId) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Không tìm thấy địa bàn!'
            ]);
        }

        return response()->json([
            'Err'    => false,
            'Result' => [[
                'MaMauVanBangChungChi'   => $MauVanBangChungChiId->MaMauVanBangChungChi,
                'TenMauVanBangChungChi'  => $MauVanBangChungChiId->TenMauVanBangChungChi,
                'LoaiVanBangChungChi'  => $MauVanBangChungChiId->LoaiVanBangChungChi,
                'MoTa'             => $MauVanBangChungChiId->MoTa,
                'CanCuPhapLy'             => $MauVanBangChungChiId->CanCuPhapLy,
                'TrangThai'        => $MauVanBangChungChiId->TrangThai,
                'DiaBanID'         => $MauVanBangChungChiId->_id,
            ]]
        ]);
    }

    /**
     * Xóa mẫu văn bằng, chứng chỉ
     */
    public function xoa(Request $request)
    {
        $id = $request->input('ma');
        $model = MauVanBangChungChi::whereKey($id)->firstOrFail();

        if (! $model) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Không tìm thấy mẫu văn bằng, chứng chỉ để xóa!'
            ]);
        }
        // 4) Ok, xóa
        $model->delete();

        return response()->json([
            'Err' => false,
            'Msg' => 'Xóa mẫu văn bằng, chứng chỉ thành công!'
        ]);
    }

    /**
     * Xóa mẫu văn bằng, chứng chỉ
     */
    public function xoact(Request $request)
    {
        $id = $request->input('ma');
        $model = MauVanBangChungChiCT::where('MauVanBangChungChiID', NEW ObjectId($id));

        if (! $model) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Không tìm thấy mẫu văn bằng, chứng chỉ chi tiết để xóa!'
            ]);
        }
        // 4) Ok, xóa
        $model->delete();

        return response()->json([
            'Err' => false,
            'Msg' => 'Xóa mẫu văn bằng, chứng chỉ chi tiết thành công!'
        ]);
    }
    public function getMauVanBangChungChi(Request $request)
    {
        try {
            $query = MauVanBangChungChi::select('_id', 'MaMauVanBangChungChi', 'TenMauVanBangChungChi')->where('TrangThai', true)
            ->orderBy('MaMauVanBangChungChi', 'asc');
            $result = $query->get();

            $payload = [

                'Err'         => false,
                'Result'      => $result,
                'Msg'         => '',
            ];
            $json = json_encode($payload);
            // return it as a plain-text response
            return response($json, 200)
                ->header('Content-Type', 'text/html');

        } catch (\Exception $e) {
            return response()->json([
                'Err'   => true,
                'Msg'   => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    public function GetFontFamily_Combo(Request $request)
    {
        try {
            $fonts = [
                ['name' => 'Times New Roman'],
                ['name' => 'Arial'],
                ['name' => 'Georgia'],
                ['name' => 'Courier'],
            ];

            $payload = [
                'Err'    => false,
                'Result' => $fonts,
                'Msg'    => '',
            ];

            $json = json_encode($payload);

            return response($json, 200)
                ->header('Content-Type', 'text/html');

        } catch (\Exception $e) {
            return response()->json([
                'Err'   => true,
                'Msg'   => 'Không thể lấy danh sách font!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    public function GetFontSize_Combo(Request $request)
    {
        try {
            $fontSizes = [
                ['name' => '8'],
                ['name' => '9'],
                ['name' => '10'],
                ['name' => '11'],
                ['name' => '12'],
                ['name' => '13'],
                ['name' => '14'],
                ['name' => '15'],
                ['name' => '16'],
                ['name' => '17'],
                ['name' => '18'],
                ['name' => '19'],
                ['name' => '20'],
            ];

            $payload = [
                'Err'    => false,
                'Result' => $fontSizes,
                'Msg'    => '',
            ];

            $json = json_encode($payload);

            return response($json, 200)
                ->header('Content-Type', 'text/html');

        } catch (\Exception $e) {
            return response()->json([
                'Err'   => true,
                'Msg'   => 'Không thể lấy danh sách kích thước chữ!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    public function KiemTraMauCT(Request $request)
    {
        $id = $request->input('id');
        try {
            // Đảm bảo _id là ObjectId (nếu cần)
            $objectId = new ObjectId($id);
            $count = MauVanBangChungChiCT::where('MauVanBangChungChiID', $objectId)->count();
            return response()->json([
                'Err' => false,
                'Count' => $count,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi khi đếm!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    public function InsertMauGCNCT(Request $request)
    {
        try {
            $loai = $request->input('loai');
            $mauId = $request->input('id'); // Truyền vào từ client
            // Nếu muốn gom chung cả 2 trường hợp
            if ($loai === 'THCS' || $loai === 'THCS-BS') {
                $loaiArr = ['THCS', 'THCS-BS'];
            } else {
                 $loaiArr = ['THPT', 'THPT-BS'];
            }

            // B1: Lấy danh sách tiêu chí phù hợp
            $tieuChis = TieuChi::whereIn('LoaiTieuChi', $loaiArr)
                ->where('TrangThai', true)
                ->get();

            if ($tieuChis->isEmpty()) {
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Không có dữ liệu để chèn.',
                ]);
            }

            // B2: Map dữ liệu để insert
            $toInsert = $tieuChis->map(function ($tc) use ($mauId) {
                return [
                    'MauVanBangChungChiID' => new ObjectId($mauId),
                    'MaTieuChi'            => $tc->MaTieuChi,
                    'TenTieuChi'           => $tc->TenTieuChi,
                    'StringSQL'            => $tc->StringSQL,
                    'InNghien'             => $tc->InNghien,
                    'InDam'                => $tc->InDam,
                    'TenTieuChiHTML'       => $tc->TenTieuChiHTML,
                    'LoaiTieuChi'          => $tc->LoaiTieuChi,
                    'GachChan'             => $tc->GachChan,
                    'Font'                 => $tc->Font,
                    'FontSize'             => $tc->FontSize,
                    'InHoa'                => $tc->InHoa,
                    'Margin'          => $tc->Margin,
                    'MauSac'               => $tc->MauSac,
                    'TrangThai'            => $tc->TrangThai,
                    'Class'                => $tc->Class,
                    'TextAlign'            => $tc->TextAlign,
                    'Float'                => $tc->Float,
                    'created_at'           => now(),
                    'updated_at'           => now(),
                ];
            })->toArray();

            // B3: Insert hàng loạt
            MauVanBangChungChiCT::insert($toInsert);

            return response()->json([
                'Err' => false,
                'count'=> count($toInsert),
                'Msg' => 'Chèn thành công ' . count($toInsert) . ' dòng.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi khi chèn dữ liệu!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    public function GetAllCT(Request $request)
    {
        $id = $request->input('id');
        try {
            // Đảm bảo _id là ObjectId (nếu cần)
            $objectId = new ObjectId($id);
            $Result = MauVanBangChungChiCT::where('MauVanBangChungChiID', $objectId)->orderBy('MaTieuChi', 'asc')->get();
            return response()->json([
                'Err' => false,
                'Result' => $Result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi khi đếm!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
        /**
     * Thêm mới hoặc cập nhật thông tin đia bàn
     */
    public function LuuThongTinCT(Request $request)
    {
        try {//filter_var($request->input('TrangThai'), FILTER_VALIDATE_BOOLEAN);
            $Font = $request->input('Font');
            $FontSize = $request->input('FontSize');
            $InDam =  filter_var($request->input('InDam'), FILTER_VALIDATE_BOOLEAN);
            $InNghien =  filter_var($request->input('InNghien'), FILTER_VALIDATE_BOOLEAN);
            $GachChan = filter_var($request->input('GachChan'), FILTER_VALIDATE_BOOLEAN);
            $InHoa = filter_var($request->input('InHoa'), FILTER_VALIDATE_BOOLEAN);
            $MauSac = $request->input('MauSac');
            $TextAlign = $request->input('TextAlign');
            $Margin = $request->input('Margin');
            $TenTieuChi = $request->input('TenTieuChi');
            $TenTieuChiHTML = $request->input('TenTieuChiHTML');
            $ID = $request->input('ID');
            if ($request->isMethod('put') || $request->isMethod('patch')) {
                // Update
                $MauVanBangChungChiCT = MauVanBangChungChiCT::find($ID);
                if (!$MauVanBangChungChiCT) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy mẫu văn bằng, chứng chỉ chi tiết cần cập nhật!'
                    ]);
                }

                $MauVanBangChungChiCT->update([
                    'Font'    => $Font,
                    'FontSize'    => $FontSize,
                    'InDam'    => $InDam,
                    'InNghien'    => $InNghien,
                    'GachChan'    => $GachChan,
                    'InHoa'    => $InHoa,
                    'MauSac'    => $MauSac,
                    'TextAlign'    => $TextAlign,
                    'TenTieuChi'    => $TenTieuChi,
                    'TenTieuChiHTML'    => $TenTieuChiHTML,
                    'Margin'    => $Margin,
                ]);

                return response()->json([
                    'Err' => false,
                    'Msg' => 'Cập nhật mẫu văn bằng, chứng chỉ chi tiết thành công!'
                ]);
            }

            // Nếu không phải POST hoặc PUT
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Phương thức HTTP không hợp lệ!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }

    public function getFileWord(Request $request)
    {
        try {
            $html = $request->input('html');
            $name =$this->currentUser->username();
            if ($html=="") {
                return response()->json([
                    'Err' => true,
                    'canhbao' => true,
                    'Msg' => 'Không có dữ liệu!'
                ]);
            }
            $file =  OfficeConvertService::HTMLToPdf($html,$name);

            return response()->json([
                'Err' => false,
                'file' => $file
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' =>true,
                'Msg' => 'Đã xảy ra lỗi xử lý'], 500);
        }
    }

    public function getFileWordV2(Request $request)
{
    // Accept raw JSON: {"parts":[...]}
    $data = $request->getContent();
    $json = json_decode($data, true);
    $rows = $json['parts'] ?? [];

    $username = $this->currentUser->username();

    // Build directory and filenames
    $subDir = "/xuatword/{$username}/mauvanban";
    $folder = public_path($subDir);

    // Ensure directory exists
    if (!is_dir($folder)) {
        mkdir($folder, 0777, true);
    }

    // Delete old files if present
    $docxPath = "{$folder}/BangTotNgiep.docx";
    $pdfPath = "{$folder}/BangTotNgiep.pdf";
    if (file_exists($docxPath)) unlink($docxPath);
    if (file_exists($pdfPath)) unlink($pdfPath);

    // Create the Word file
    $phpWord = new \PhpOffice\PhpWord\PhpWord();
    $sectionStyle = [
        'marginTop'    => 1134,
        'marginBottom' => 1134,
        'marginLeft'   => 1134,
        'marginRight'  => 1134,
    ];
    $section = $phpWord->addSection($sectionStyle);
    $this->renderCertificate($section, $rows);

    $phpWordWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
    $phpWordWriter->save($docxPath);

    // Convert DOCX → PDF
    // Note: relative path from public/ for your static method
    $docxRelative = "{$subDir}/BangTotNgiep.docx";
    $conversionSuccess = OfficeConvertService::DocxToPdfLibreOffice($docxRelative);

    // URLs for response (from web root)
    $baseUrl = url('/');
    $docxUrl = $baseUrl . '/'. ltrim($subDir . '/BangTotNgiep.docx', '/');
    $pdfUrl = $baseUrl . '/'. ltrim($subDir . '/BangTotNgiep.pdf', '/');

    return response()->json([
            'success' => true,
            'docxUrl' => $docxUrl,
            'pdfUrl' => $pdfUrl,
            'message' => $conversionSuccess ? 'OK' : 'PDF conversion failed'
        ]);
    }

    public function marginToSpacing($marginStr) {
        $parts = preg_split('/\s+/', trim($marginStr));
        $top = isset($parts[0]) ? (int) $parts[0] : 0;
        $bottom = isset($parts[2]) ? (int) $parts[2] : 0;
        return [
            'spaceBefore' => $top * 20,
            'spaceAfter' => $bottom * 20,
        ];
    }

    // Render a single row as a normal PHPWord paragraph
    public function renderRow($section, $row) {
        $bool = function($v) {
            return filter_var($v, FILTER_VALIDATE_BOOLEAN);
        };
        $fontStyle = [
            'name' => $row['Font'] ?? 'Times New Roman',
            'size' => (int)($row['FontSize'] ?? 11),
            'bold' => $bool($row['InDam'] ?? false),
            'italic' => $bool($row['InNghien'] ?? false),
            'underline' => $bool($row['GachChan'] ?? false) ? Font::UNDERLINE_SINGLE : Font::UNDERLINE_NONE,
            'allCaps' => $bool($row['InHoa'] ?? false),
            'color' => (!empty($row['MauSac']) && strtolower($row['MauSac']) !== 'null') ? $row['MauSac'] : null,
        ];
        $align = strtolower($row['TextAlign'] ?? 'left');
        $alignment = Jc::LEFT;
        if ($align === 'center') $alignment = Jc::CENTER;
        elseif ($align === 'right') $alignment = Jc::RIGHT;

        $margin = $this->marginToSpacing($row['Margin'] ?? '');

        $paraStyle = [
            'alignment' => $alignment,
            'spaceBefore' => $margin['spaceBefore'],
            'spaceAfter' => $margin['spaceAfter'],
        ];

        $text = $row['TenTieuChi'] ??  '';
        if ($bool($row['InHoa'] ?? false)) {
            $text = mb_strtoupper($text, 'UTF-8');
        }

        $section->addText($text, $fontStyle, $paraStyle);
    }

    // Render a grid row as a table (proportional cell widths)
    public function renderGridRow($section, $rowBuffer, $totalCols)
{

    $tableWidth = 9000;
    $table = $section->addTable([
        'borderSize' => 0,          // No borders
        'cellMargin' => 0,
        'alignment' => JcTable::CENTER,
        'width' => $tableWidth,
    ]);
    $table->addRow();

    // Group cells by Float: left first, then right
    $leftCells = [];
    $rightCells = [];
    foreach ($rowBuffer as $cellData) {
        $float = strtolower($cellData['row']['Float'] ?? 'left');
        if ($float === 'right') {
            $rightCells[] = $cellData;
        } else {
            $leftCells[] = $cellData;
        }
    }
    $orderedCells = array_merge($leftCells, $rightCells);

    foreach ($orderedCells as $cellData) {
        $colSpan = $cellData['col'];
        $cellWidth = intval($tableWidth * $colSpan / 12);
        $cell = $table->addCell($cellWidth, [
            'valign' => 'top',
            'borderSize' => 0,
            'borderTopSize' => 0,
            'borderRightSize' => 0,
            'borderBottomSize' => 0,
            'borderLeftSize' => 0,
            'borderColor' => 'ffffff'

            ]);
        if ($cellData['row'] !== null) {
            $this->renderCellText($cell, $cellData['row']);
        }
    }
}

    // Helper for rendering text inside a table cell
    public function renderCellText($cell, $row) {
        $bool = function($v) {
            return filter_var($v, FILTER_VALIDATE_BOOLEAN);
        };
        $fontStyle = [
            'name' => $row['Font'] ?? 'Times New Roman',
            'size' => (int)($row['FontSize'] ?? 11),
            'bold' => $bool($row['InDam'] ?? false),
            'italic' => $bool($row['InNghien'] ?? false),
            'underline' => $bool($row['GachChan'] ?? false) ? Font::UNDERLINE_SINGLE : Font::UNDERLINE_NONE,
            'allCaps' => $bool($row['InHoa'] ?? false),
            'color' => (!empty($row['MauSac']) && strtolower($row['MauSac']) !== 'null') ? $row['MauSac'] : null,
        ];
        $align = strtolower($row['TextAlign'] ?? 'left');
        $alignment = Jc::LEFT;
        if ($align === 'center') $alignment = Jc::CENTER;
        elseif ($align === 'right') $alignment = Jc::RIGHT;

        $margin = $this->marginToSpacing($row['Margin'] ?? '');

        $paraStyle = [
            'alignment' => $alignment,
            'spaceBefore' => $margin['spaceBefore'],
            'spaceAfter' => $margin['spaceAfter'],
        ];

        $text = $row['TenTieuChi'] ??  '';
        if ($bool($row['InHoa'] ?? false)) {
            $text = mb_strtoupper($text, 'UTF-8');
        }
        $cell->addText($text, $fontStyle, $paraStyle);
    }

    // Bootstrap grid column parser
    public function getGridColumn($classes) {
        if (!is_array($classes)) $classes = [];
        foreach ($classes as $class) {
            if (preg_match('/col-(xs|sm|md|lg|xl)-(\d+)/', $class, $m)) {
                return (int)$m[2];
            } elseif (preg_match('/col-md-(\d+)/', $class, $m)) {
                return (int)$m[1];
            }
        }
        return 12; // fallback
    }

    // The grid-aware certificate renderer!
    public function renderCertificate($section, $rows)
    {

        $rowBuffer = [];
        $colCount = 0;
        $i = 0;
        $n = count($rows);

        while ($i < $n) {
            $row = $rows[$i];
            $col = $this->getGridColumn($row['Classes'] ?? []);
            $float = isset($row['Float']) ? strtolower($row['Float']) : 'left';

            // Flush buffer if adding this cell would exceed 12 columns
            if ($colCount + $col > 12) {
                // Optionally pad before flush
                if ($colCount < 12) {
                    $rowBuffer[] = ['col' => 12 - $colCount, 'row' => null];
                }
                $this->renderGridRow($section, $rowBuffer, $colCount);
                $rowBuffer = [];
                $colCount = 0;
            }

            // Float: right and at row start → pad left
            if ($float == 'right' && empty($rowBuffer) && $col < 12) {
                $pad = 12 - $col;
                $rowBuffer[] = ['col' => $pad, 'row' => null];
                $colCount += $pad;
            }

            // Add cell
            $rowBuffer[] = ['col' => $col, 'row' => $row];
            $colCount += $col;

            // Flush if full row (after adding)
            if ($colCount == 12) {
                $this->renderGridRow($section, $rowBuffer, $colCount);
                $rowBuffer = [];
                $colCount = 0;
            }

            $i++;
        }

        // Flush leftovers (pad if needed)
        if ($rowBuffer) {
            if ($colCount < 12) {
                $rowBuffer[] = ['col' => (12 - $colCount), 'row' => null];
            }
            $this->renderGridRow($section, $rowBuffer, 12);
        }
    }
}
