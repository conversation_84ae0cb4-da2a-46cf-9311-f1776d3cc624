<?php

namespace App\Http\Controllers\QuanLy;

use App\Models\DanhMuc\CapHoc;
use App\Models\DanhMuc\ChucVu;
use App\Models\DanhMuc\HinhThucDaoTao;
use App\Models\DanhMuc\LoaiPhoiVanBangChungChi;
use App\Models\DanhMuc\LoaiVanBangChungChi;
use App\Models\DanhMuc\NhanVien;
use App\Models\DanhMuc\XepLoai;
use App\Models\QuanLy\DoiTuong;
use App\Models\QuanLy\DonXinCapBanSao;
use App\Models\QuanLy\NhatKyThaoTac;
use App\Models\quanly\ThaoTac;
use Str;
use Storage;
use Carbon\Carbon;
use App\Services\ThongBao;
use Illuminate\Http\Request;
use App\Models\DanhMuc\DonVi;
use App\Services\DungChungDb;
use App\Services\DungChungNoDb;
use App\Models\DanhMuc\TrangThai;
use App\Http\Controllers\Controller;
use App\Services\CurrentUserService;
use App\Models\QuanLy\DonYeuCau;
use MongoDB\BSON\ObjectId; // from mongodb/mongodb
use Log;
class DuyetDonXinCapBanSaoController extends Controller
{
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;
    protected CurrentUserService $currentUser;

    public function __construct(DungChungDb $dungChungDb,CurrentUserService $currentUser, DungChungNoDb $logService)
    {
        $this->dungChungDb = $dungChungDb;
        $this->logService  = $logService;
        $this->currentUser  = $currentUser;
    }
    function safeObjectId($id) {
        return is_string($id) && preg_match('/^[a-f\d]{24}$/i', $id) ? new ObjectId($id) : null;
    }

    public function index()
    {
        return view('quanly.duyetdonxincapbansao.index');
    }
    public function luuthongtin(Request $request)
    {
        try {
        $DonXinCapBanSaoID = $this->safeObjectId($request->input('DonXinCapBanSaoID'));
        $NgayLap = $request->filled('NgayLap')? Carbon::createFromFormat('d/m/Y', $request->input('NgayLap')) : null; 
        $NhanVienID_NguoiLap = $this->safeObjectId($request->input('NhanVienID_NguoiLap'));
        $ChucVuID_NguoiLap = $this->safeObjectId($request->input('ChucVuID_NguoiLap'));
        $DoiTuongID =$this->safeObjectId($request->input('DoiTuongID'));
        $LoaiVanBangChungChiID = $this->safeObjectId($request->input('LoaiVanBangChungChiID'));
        $DonViID_Nhan =$this->safeObjectId($request->input('DonViID_Nhan'));
        $NamTotNghiep  =  $request->input('NamTotNghiep');
        $XepLoaiID =$this->safeObjectId($request->input('XepLoaiID'));
        $HinhThucDaoTaoID = $this->safeObjectId($request->input('HinhThucDaoTaoID'));
        $NgayCap = $request->filled('NgayCap')? Carbon::createFromFormat('d/m/Y', $request->input('NgayCap')) : null; 
        $SoHieu =  $request->input('SoHieu');
        $SoVaoSo = $request->input('SoVaoSo');
        $GhiChu =  $request->input('GhiChu');
        $LyDo =  $request->input('LyDo');
        $DinhKem = $request->input('DinhKem') ?? '';
        $NgayThaoTac= now();
        $NgayCapNhat= now();
        $UserID_CapNhat= auth()->id() ? new ObjectId(auth()->id()) : null;
        $UserID_ThaoTac= auth()->id() ? new ObjectId(auth()->id()) : null;
        $DonViID_ThaoTac = $this->safeObjectId($this->currentUser->donViId());
        $SoDonXinCapBanSao =  $request->input('SoDonXinCapBanSao');
        
            if ($request->isMethod('post')) {
                // duplicate check
            $paths = $this->saveFiles($DinhKem); 
            DonXinCapBanSao::create([
                    'NgayLap'=>$NgayLap,
                    'NhanVienID_NguoiLap'=>$NhanVienID_NguoiLap,
                    'ChucVuID_NguoiLap'=>$ChucVuID_NguoiLap,
                    'DoiTuongID'=>$DoiTuongID,
                    'LoaiVanBangChungChiID'=>$LoaiVanBangChungChiID,
                    'NamTotNghiep'=>$NamTotNghiep,
                    'XepLoaiID'=>$XepLoaiID,
                    'HinhThucDaoTaoID'=>$HinhThucDaoTaoID,
                    'NgayCap'=>$NgayCap,
                    'SoHieu'=>$SoHieu,
                    'SoVaoSo'=>$SoVaoSo,
                    'GhiChu'=>$GhiChu,
                    'LyDo'=>$LyDo,
                    'DinhKem'=>$paths,
                    'TrangThai'=>'50',
                    'NgayThaoTac'=>$NgayThaoTac,
                    'NgayCapNhat'=>$NgayCapNhat,
                    'UserID_CapNhat'=>$UserID_CapNhat,
                    'UserID_ThaoTac'=>$UserID_ThaoTac,
                    'DonViID_ThaoTac'=>$DonViID_ThaoTac,
                    'SoDonXinCapBanSao'=>$SoDonXinCapBanSao
                ]);
                $this->logService->ghiLogs($request->all(), 'them', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Thêm dữ liệu thành công!'
                ]);
            }

            if ($request->isMethod('put') || $request->isMethod('patch')) {
                // Update
                $DonXinCapBanSao = DonXinCapBanSao::find($DonXinCapBanSaoID);
                if (!$DonXinCapBanSao) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                    ]);
                }       
                // ✅ Xóa các file đính kèm nếu có
                if (!empty($DonXinCapBanSao->DinhKem)) {
                    $paths = explode('|', $DonXinCapBanSao->DinhKem);
                    foreach ($paths as $path) {
                        // Xử lý path: bỏ /storage/ để về path thật trong public
                        $relativePath = str_replace('/storage/', 'public/', $path);
                        if (Storage::exists($relativePath)) {
                            Storage::delete($relativePath);
                        }
                    }
                }
                $paths = $this->saveFiles($DinhKem);

                $DonXinCapBanSao->update([
                    'NgayLap'=>$NgayLap,
                    'NhanVienID_NguoiLap'=>$NhanVienID_NguoiLap,
                    'ChucVuID_NguoiLap'=>$ChucVuID_NguoiLap,
                    'DoiTuongID'=>$DoiTuongID,
                    'LoaiVanBangChungChiID'=>$LoaiVanBangChungChiID,
                    'NamTotNghiep'=>$NamTotNghiep,
                    'XepLoaiID'=>$XepLoaiID,
                    'HinhThucDaoTaoID'=>$HinhThucDaoTaoID,
                    'NgayCap'=>$NgayCap,
                    'SoHieu'=>$SoHieu,
                    'SoVaoSo'=>$SoVaoSo,
                    'GhiChu'=>$GhiChu,
                    'LyDo'=>$LyDo,
                    'DinhKem'=>$paths,
                    'SoDonXinCapBanSao'=>$SoDonXinCapBanSao
                ]);
                $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Cập nhật dữ liệu thành công!'
                ]);
            }

            // Nếu không phải POST hoặc PUT
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Phương thức HTTP không hợp lệ!'
            ]);

        } catch (\Exception $e) {
            $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    protected function saveFiles(string $paths): string
    {
        $donViid = $this->currentUser->donViId();
        $username = $this->currentUser->username();
        $disk = Storage::disk('public');

        $permanentBase = "uploads/{$donViid}/{$username}/files/donxincapbansao";
        $results = [];

        foreach (explode('|', $paths) as $p) {
            $p = trim($p);
            if (!$p) {
                continue;
            }

            $urlPath = parse_url($p, PHP_URL_PATH);

            if (Str::startsWith($urlPath, "/storage/{$permanentBase}/")) {
                $results[] = $urlPath;
                continue;
            }

            $diskKey = preg_replace('#^/storage/#', '', $urlPath);

            $name = pathinfo($diskKey, PATHINFO_FILENAME);
            $ext = pathinfo($diskKey, PATHINFO_EXTENSION);
            $slug = Str::slug($name);
            $newName = "{$slug}-" . time() . ".{$ext}";
            $newKey = "{$permanentBase}/{$newName}";

            $disk->move($diskKey, $newKey);

            $results[] = '/storage/' . $newKey;
        }

        return implode('|', $results);
    }
    public function getall(Request $request)
    {
        try {
            $query = DonXinCapBanSao::query();
             
            $LoaiPhoiID_Loc = $request->input('LoaiPhoiID_Loc');
            $TrangThai_Loc = $request->input('TrangThai_Loc');
            $UserID_CapNhat = auth()->id() ? new ObjectId(auth()->id()) : null;
            $donViId = $this->safeObjectId($this->currentUser->donViId());

            if ($donViId) {
                $query->where('DonViID_Nhan', $donViId);
            }
            if (!is_null($LoaiPhoiID_Loc) && $LoaiPhoiID_Loc !== '') {
                $query->where('LoaiVanBangChungChiID',  $LoaiPhoiID_Loc); // hoặc (string) nếu là chuỗi
            }
            if (!is_null($TrangThai_Loc) && $TrangThai_Loc !== '') {
                $query->where('TrangThai',  $TrangThai_Loc); // hoặc (string) nếu là chuỗi
            }
            if ($request->filled('TuNgay_Loc')) {
                $tuNgay = Carbon::createFromFormat('d/m/Y', $request->input('TuNgay_Loc'))->startOfDay();
                $query->where('NgayLap', '>=', $tuNgay);
            }
            if ($request->filled('DenNgay_Loc')) {
                $denNgay = Carbon::createFromFormat('d/m/Y', $request->input('DenNgay_Loc'))->endOfDay();
                $query->where('NgayLap', '<=', $denNgay);
            }
            $sortField = $request->input('CbSapXep', 'NgayLap');
            $query->orderBy($sortField, 'asc');
                $result = $query->get();
                $result = $result->map(function ($item) {
                $item->NgayLapConVer = $item->NgayLap? Carbon::parse($item->NgayLap)->format('d/m/Y'): null;
                $item->TenNhanVien_Lap = optional(NhanVien::find($item->NhanVienID_NguoiLap))->tenNhanVien ?? '';
                $item->TenChucVu_Lap = optional(ChucVu::find($item->ChucVuID_NguoiLap))->tenChucVu ?? '';
                $item->TenDonVi_Nhan = optional(DonVi::find($item->DonViID_Nhan))->TenDonVi ?? '';
                $item->TenDonVi_YeuCau = optional(DonVi::find($item->DonViID_ThaoTac))->TenDonVi ?? '';
                $item->HocSinh = optional(DoiTuong::find($item->DoiTuongID))->Hovaten ?? '';
                $item->XepLoai = optional(XepLoai::find($item->XepLoaiID))->TenXepLoai ?? '';
                $item->TenHinhThucDaoTao = optional(HinhThucDaoTao::find($item->HinhThucDaoTaoID))->TenHinhThucDaoTao ?? '';
                $item->NgayCapConVer = $item->NgayCap? Carbon::parse($item->NgayCap)->format('d/m/Y'): null;
                $item->TenTrangThai = optional( TrangThai::where('MaTrangThai', $item->TrangThai)->first() )->TenTrangThai ?? '';
                $item->MauSac = optional( TrangThai::where('MaTrangThai', $item->TrangThai)->first() )->MauSac ?? '';
                $item->TenLoaiVanBang = optional(LoaiPhoiVanBangChungChi::find($item->LoaiVanBangChungChiID))->TenLoaiPhoiVanBangChungChi ?? '';
                $item->NgayPheDuyetConVer = $item->NgayPheDuyet? Carbon::parse($item->NgayPheDuyet)->format('d/m/Y'): null;
                $item->TenNhanVien_PheDuyet = optional(NhanVien::find($item->NhanVienID_PheDuyet))->tenNhanVien ?? '';
                $item->TenChucVu_PheDuyet = optional(ChucVu::find($item->ChucVuID_PheDuyet))->tenChucVu ?? '';
                
                $item->NgayGuiConVer = $item->NgayGui? Carbon::parse($item->NgayGui)->format('d/m/Y'): null;
                $item->TenNhanVien_Gui = optional(NhanVien::find($item->NhanVienID_Gui))->tenNhanVien ?? '';
                $item->TenChucVu_Gui = optional(ChucVu::find($item->ChucVuID_Gui))->tenChucVu ?? '';


                $item->NgayTuChoiConVer = $item->NgayTuChoi? Carbon::parse($item->NgayTuChoi)->format('d/m/Y'): null;
                $item->TenNhanVien_TuChoi = optional(NhanVien::find($item->NhanVienID_TuChoi))->tenNhanVien ?? '';
                $item->TenChucVu_TuChoi = optional(ChucVu::find($item->ChucVuID_TuChoi))->tenChucVu ?? '';

                  $item->NgayThuHoiConVer = $item->NgayThuHoi? Carbon::parse($item->NgayThuHoi)->format('d/m/Y'): null;
                $item->TenNhanVien_ThuHoi = optional(NhanVien::find($item->NhanVienID_ThuHoiPheDuyet))->tenNhanVien ?? '';
                $item->TenChucVu_ThuHoi = optional(ChucVu::find($item->ChucVuID_ThuHoiPheDuyet))->tenChucVu ?? '';
                return $item;
            });
            // Map lại từng dòng để thêm Tên Cha
             return response()->json([
                'Err'    => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err'   => true,
                'Msg'   => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    public function xoa(Request $request)
    {
        try {
            $id = $request->input('ma');
            $model = DonXinCapBanSao::whereKey($id)->firstOrFail();
    
            // ✅ Xóa file đính kèm nếu có
            if (!empty($model->DinhKem)) {
                $paths = [];
    
                // Xử lý chuỗi đường dẫn file (dùng | hoặc , làm dấu ngăn cách)
                if (is_string($model->DinhKem)) {
                    $paths = preg_split('/[|,]/', $model->DinhKem);
                }
    
                foreach ($paths as $path) {
                    $cleanPath = str_replace('\\', '/', trim($path));
    
                    // Loại bỏ '/storage/' => còn lại: uploads/...
                    $relativePath = str_replace('/storage/', '', $cleanPath);
    
                    // Laravel file system path: storage/app/public/...
                    $fullStoragePath = 'public/' . $relativePath;
                    if (Storage::disk('public')->exists($relativePath)) {
                        Storage::disk('public')->delete($relativePath);
                    } else {
                        Log::warning("Không tìm thấy file để xoá: $fullStoragePath");
                    }
                }
            }
    
            // ✅ Xoá dữ liệu gốc
            $model->delete();
    
            return response()->json([
                'Err' => false,
                'Msg' => 'Xóa dữ liệu và file đính kèm thành công!'
            ]);
    
        } catch (\Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
    
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    public function loaddulieusua(Request $request)
    {
        $id = $request->input('id');
        try {
            $query = DonXinCapBanSao::query();
            $UserID_CapNhat = auth()->id() ? new ObjectId(auth()->id()) : null;
            if ($UserID_CapNhat) {
                $query->where('_id', $id);
            }
                $result = $query->get();
            $result = $result->map(function ($item) {

                     $item->NgayLapConVer = $item->NgayLap? Carbon::parse($item->NgayLap)->format('d/m/Y'): null;
                    $item->TenNhanVien_Lap = optional(NhanVien::find($item->NhanVienID_NguoiLap))->tenNhanVien ?? '';
                    $item->TenChucVu_Lap = optional(ChucVu::find($item->ChucVuID_NguoiLap))->tenChucVu ?? '';
                    $item->TenDonVi_Nhan = optional(DonVi::find($item->DonViID_Nhan))->TenDonVi ?? '';
                    $item->TenDonVi_YeuCau = optional(DonVi::find($item->DonViID_ThaoTac))->TenDonVi ?? '';
                    $item->HocSinh = optional(DoiTuong::find($item->DoiTuongID))->Hovaten ?? '';
                    $item->XepLoai = optional(XepLoai::find($item->XepLoaiID))->TenXepLoai ?? '';
                    $item->TenHinhThucDaoTao = optional(HinhThucDaoTao::find($item->HinhThucDaoTaoID))->TenHinhThucDaoTao ?? '';
                    $item->NgayCapConVer = $item->NgayCap? Carbon::parse($item->NgayCap)->format('d/m/Y'): null;
                    $item->TenTrangThai = optional( TrangThai::where('MaTrangThai', $item->TrangThai)->first() )->TenTrangThai ?? '';
                    $item->MauSac = optional( TrangThai::where('MaTrangThai', $item->TrangThai)->first() )->MauSac ?? '';
                    $item->TenLoaiVanBang = optional(LoaiPhoiVanBangChungChi::find($item->LoaiVanBangChungChiID))->TenLoaiPhoiVanBangChungChi ?? '';
                    $item->NgayPheDuyetConVer = $item->NgayPheDuyet? Carbon::parse($item->NgayPheDuyet)->format('d/m/Y'): null;
                    $item->TenNhanVien_PheDuyet = optional(NhanVien::find($item->NhanVienID_PheDuyet))->tenNhanVien ?? '';
                    $item->TenChucVu_PheDuyet = optional(ChucVu::find($item->ChucVuID_PheDuyet))->tenChucVu ?? '';



                    $item->NgayTuChoiConVer = $item->NgayTuChoi? Carbon::parse($item->NgayTuChoi)->format('d/m/Y'): null;
                    $item->TenNhanVien_TuChoi = optional(NhanVien::find($item->NhanVienID_TuChoi))->tenNhanVien ?? '';
                    $item->TenChucVu_TuChoi = optional(ChucVu::find($item->ChucVuID_TuChoi))->tenChucVu ?? '';

                    $item->NgayThuHoiConVer = $item->NgayThuHoi? Carbon::parse($item->NgayThuHoi)->format('d/m/Y'): null;
                    $item->TenNhanVien_ThuHoi = optional(NhanVien::find($item->NhanVienID_ThuHoi))->tenNhanVien ?? '';
                    $item->TenChucVu_ThuHoi = optional(ChucVu::find($item->ChucVuID_ThuHoi))->tenChucVu ?? '';

                    $item->NgayThuHoiPheDuyetConVer = $item->NgayThuHoi? Carbon::parse($item->NgayThuHoiPheDuyet)->format('d/m/Y'): null;
                    $item->TenNhanVien_ThuHoiPheDuyet = optional(NhanVien::find($item->NhanVienID_ThuHoiPheDuyet))->tenNhanVien ?? '';
                    $item->TenChucVu_ThuHoiPheDuyet = optional(ChucVu::find($item->ChucVuID_ThuHoiPheDuyet))->tenChucVu ?? '';

                    $item->NgayGuiConVer = $item->NgayGui? Carbon::parse($item->NgayGui)->format('d/m/Y'): null;
                    $item->TenNhanVien_Gui = optional(NhanVien::find($item->NhanVienID_Gui))->tenNhanVien ?? '';
                    $item->TenChucVu_Gui = optional(ChucVu::find($item->ChucVuID_Gui))->tenChucVu ?? '';
                return $item;
            });
  
            // Map lại từng dòng để thêm Tên Cha
             return response()->json([
                'Err'    => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err'   => true,
                'Msg'   => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }

    public function PheDuyet(Request $request)
    {
            $TiepNhanPhoiVBCC=null;
             $didLogAction = true;  
            $TrangThai= $request->input('TrangThai');
            $DonXinCapBanSaoID = $this->safeObjectId($request->input('DonXinCapBanSaoID'));
            $NhanVienID_PheDuyet= $request->input('NhanVienID_PheDuyet');
            $NgayPheDuyet= $request->filled('NgayPheDuyet')? Carbon::createFromFormat('d/m/Y', $request->input('NgayPheDuyet')) : null; 
            $NoiDung_PheDuyet= $request->input('NoiDung_PheDuyet');
            $ChucVuID_PheDuyet= $this->safeObjectId($request->input('ChucVuID_PheDuyet'));
        try {
            $Loai= $request->input('Loai');
            if ($request->isMethod('post') ) {
                // Update
                $TiepNhanPhoiVBCC = DonXinCapBanSao::find($DonXinCapBanSaoID);
                if (!$TiepNhanPhoiVBCC) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                    ]);
                }
                if($Loai=='Gui'){
                    $TiepNhanPhoiVBCC->update([
                        'NhanVienID_PheDuyet'=>$NhanVienID_PheDuyet
                        ,'NgayPheDuyet'=>$NgayPheDuyet
                        ,'NoiDung_PheDuyet'=>$NoiDung_PheDuyet
                        ,'ChucVuID_PheDuyet'=>$ChucVuID_PheDuyet
                        ,'TrangThai'=>$TrangThai,

                        'NhanVienID_ThuHoiPheDuyet'=>''
                        ,'NgayThuHoiPheDuyet'=>''
                        ,'NoiDung_ThuHoiPheDuyet'=>''
                        ,'ChucVuID_ThuHoiPheDuyet'=>''

                         ,'NhanVienID_TuChoi'=>''
                        ,'NgayTuChoi'=>null
                        ,'NoiDung_TuChoi'=>''
                        ,'ChucVuID_TuChoi'=>''
                    ]);
                }
                 if($Loai=='ThuHoi'){
                     $TiepNhanPhoiVBCC->update([
                        'NhanVienID_ThuHoiPheDuyet'=>$NhanVienID_PheDuyet
                        ,'NgayThuHoiPheDuyet'=>$NgayPheDuyet
                        ,'NoiDung_ThuHoiPheDuyet'=>$NoiDung_PheDuyet
                        ,'ChucVuID_ThuHoiPheDuyet'=>$ChucVuID_PheDuyet
                        ,'TrangThai'=>$TrangThai,


                        'NhanVienID_PheDuyet'=>''
                        ,'NgayPheDuyet'=>''
                        ,'NoiDung_PheDuyet'=>''
                        ,'ChucVuID_PheDuyet'=>''


                        ,'NhanVienID_TuChoi'=>''
                        ,'NgayTuChoi'=>null
                        ,'NoiDung_TuChoi'=>''
                        ,'ChucVuID_TuChoi'=>''
                    ]);
                }

                if($Loai=='TuChoi'){
                     $TiepNhanPhoiVBCC->update([
                        'NhanVienID_TuChoi'=>$NhanVienID_PheDuyet
                        ,'NgayTuChoi'=>$NgayPheDuyet
                        ,'NoiDung_TuChoi'=>$NoiDung_PheDuyet
                        ,'ChucVuID_TuChoi'=>$ChucVuID_PheDuyet
                        ,'TrangThai'=>$TrangThai
                        ,'NhanVienID_PheDuyet'=>''
                        ,'NgayPheDuyet'=>null
                        ,'NoiDung_PheDuyet'=>''
                        ,'ChucVuID_PheDuyet'=>''
                        ,'NhanVienID_ThuHoiPheDuyet'=>''
                        ,'NgayThuHoiPheDuyet'=>null
                        ,'NoiDung_ThuHoiPheDuyet'=>''
                        ,'ChucVuID_ThuHoiPheDuyet'=>''
                        ,'DonViID_Nhan'=>''
                    ]);
                }
                $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Cập nhật dữ liệu thành công!'
                ]);
            }
           
           

            // Nếu không phải POST hoặc PUT
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Phương thức HTTP không hợp lệ!'
            ]);



        } catch (\Exception $e) {
            $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        } finally {
            Log::info('Ghi NhatKyThaoTac: ' . ($TiepNhanPhoiVBCC ? $TiepNhanPhoiVBCC->_id : 'null') . ', didLogAction: ' . ($didLogAction ? 'true' : 'false'));

         
                try {
                    if (!$TiepNhanPhoiVBCC && isset($id)) {
                        $TiepNhanPhoiVBCC = DonXinCapBanSao::find($id);
                    }
                    if (!$TiepNhanPhoiVBCC) {
                        Log::warning('Không tìm thấy đơn để ghi nhật ký thao tác!');
                        return;
                    }
                   
                    $TenTrangThai = TrangThai::where('MaTrangThai', $TrangThai)->value('TenTrangThai');
                    $TenNhanVien = NhanVien::where('_id', $NhanVienID_PheDuyet)->value('tenNhanVien'); 
                    $TenChucVu = ChucVu::where('_id', $ChucVuID_PheDuyet)->value('tenChucVu'); 



                    $duLieuThaoTac = ThaoTac::getFirstByThaoTac('Sửa', (string) $this->currentUser->id());
                    NhatKyThaoTac::luuNhatKyThaoTac(
                        $duLieuThaoTac['MaThaoTac'],
                        $duLieuThaoTac['ThaoTac'],
                        $duLieuThaoTac['MauSac'],
                        $TenTrangThai.' đơn đề nghị cấp bản sao',
                        $TenTrangThai. ' đơn đề nghị cấp bản sao vào ngày '. $request->input('NgayPheDuyet'),
                        (string) $TiepNhanPhoiVBCC->_id,
                        $TiepNhanPhoiVBCC->getTable(),
                        url()->current(),
                        $TenNhanVien ?? '',
                        $TenChucVu ?? '',
                        (string) $this->currentUser->id(),
                        (string) $this->currentUser->donViId()
                    );
                } catch (\Throwable $t) {
                    
                    \Log::error('Ghi NhatKyThaoTac thất bại: ' . $t->getMessage());
                    \Log::error($t->getTraceAsString());
                }
        
        }

        
    }


public function getAllthongkevanbangdasudung(Request $request)
{
    try {
        $UserID = auth()->id() ? new ObjectId(auth()->id()) : null;
        if (!$UserID) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không xác định được người dùng.'
            ]);
        }
        // 1. Phôi đã sử dụng: đếm bản ghi trong SoGocCT
        $DaDuyet = DonXinCapBanSao::where('UserID_ThaoTac', $UserID)
            ->where('TrangThai', '32')
            ->count();
        $BiTuChoi = DonXinCapBanSao::where('UserID_ThaoTac', $UserID)
            ->where('TrangThai', '42')
            ->count();
        $ChoDuyet = DonXinCapBanSao::where('UserID_ThaoTac', $UserID)
            ->where('TrangThai', '40')
            ->count();
        $MauSacDaDuyet = TrangThai::where('MaTrangThai', '32')->value('MauSac'); 

        $MauSacBiTuChoi = TrangThai::where('MaTrangThai', '42')->value('MauSac'); 

        $MauSacChoDuyet = TrangThai::where('MaTrangThai', '40')->value('MauSac'); 


   
        $data = [
            [
                'SoLuongDaDuyet' => $DaDuyet, 
                'SoLuongTuChoi' => $BiTuChoi,
                'SoLuongChoDuyet' => $ChoDuyet, 
                'TongSo' => $ChoDuyet + $BiTuChoi + $DaDuyet, 
                'MauSacDaDuyet' => $MauSacDaDuyet, 
                'MauSacBiTuChoi' => $MauSacBiTuChoi, 
                'MauSacChoDuyet' => $MauSacChoDuyet, 
            ],
        ];

        return response()->json([
            'Err' => false,
            'Result' => $data,
            'Msg' => '',
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'Err' => true,
            'Msg' => 'Không thể lấy thống kê!',
            'debug' => $e->getMessage(),
        ]);
    }
}

    public function getAllLoaiVanBangCC()
    {
        try {
         $result = LoaiVanBangChungChi::select('_id', 'MaLoaiVanBangChungChi', 'TenLoaiVanBangChungChi')
            ->orderBy('MaLoaiVanBangChungChi', 'asc')
            ->get();
        // Map lại từng dòng để thêm Tên Cha
            return response()->json([
                'Err'    => false,
                'Result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err'   => true,
                'Msg'   => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
}