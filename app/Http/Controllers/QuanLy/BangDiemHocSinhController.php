<?php

namespace App\Http\Controllers\QuanLy;
use App\Http\Controllers\Controller;
use App\Http\Resources\BangDiemChiTietResource;
use App\Models\DanhMuc\DonVi;
use App\Models\DanhMuc\HoiDong;
use App\Models\DanhMuc\MonHoc;
use App\Models\DanhMuc\GioiTinh;
use App\Models\DanhMuc\KyThi;
use App\Models\QuanLy\NhatKyThaoTac;
use App\Models\quanly\ThaoTac;
use DB;
use MongoDB\BSON\Regex;
use App\Models\QuanLy\BangDiem;
use App\Models\QuanLy\BangDiem_ChiTiet;
use App\Models\QuanLy\DoiTuong;
use App\Models\QuanLy\HocSinhTN;
use App\Models\QuanLy\QuyetDinh;
use App\Services\CurrentUserService;
use App\Services\DungChungDb;
use App\Services\DungChungNoDb;
use App\Services\ThongBao;
use Carbon\Carbon;
use Date;
use Exception;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Log;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\DataValidation;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Storage;
use Str;
use Validator;
use MongoDB\BSON\ObjectId;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Shared\Date as ExcelDate;
use PhpOffice\PhpSpreadsheet\NamedRange;

class BangDiemHocSinhController extends Controller
{
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;
    protected CurrentUserService $currentUser;

    public function __construct(DungChungDb $dungChungDb, DungChungNoDb $logService, CurrentUserService $currentUser)
    {
        $this->dungChungDb = $dungChungDb;
        $this->currentUser = $currentUser;
        $this->logService = $logService;
    }
    //
    function index()
    {
        $steps = [];

        $stepContents = [];


        $stepOptions = [
            ['style' => 'color:green;'],         // Step 1
            ['icon' => "<i class='ti ti-user'></i>"], // Step 2
            []                                   // Step 3
        ];

        return view('quanly.bangdiemhocsinh.index', compact('steps', 'stepContents', 'stepOptions'));
    }


    /**
     * Lấy tất cả bảng điểm (JSON), hỗ trợ filter tùy chọn:
     * - Từ ngày (NgayKy)
     * - Đến ngày (NgayKy)
     * - Cấp học (CapHocID)
     * - Trạng thái (TrangThai)
     */
    public function getAll(Request $request)
    {
        try {
            // 1) Read filters + pagination params
            $tuNgay = $request->input('tuNgay');
            $denNgay = $request->input('denNgay');
            $capHocID = $request->input('capHocID');
            $trangThai = $request->input('trangThai');

            $page = max(1, (int) $request->input('page', 1));
            $size = max(1, (int) $request->input('size', 50));

            // normalize empty strings → null
            foreach (['tuNgay', 'denNgay', 'capHocID'] as $f) {
                $$f = empty($$f) ? null : $$f;
            }
            $trangThai = ($trangThai === '' || is_null($trangThai)) ? null : $trangThai;

            // 2) Build base Eloquent query
            $query = BangDiem::with([
                'kyThi',
                'chucVu',
                'hinhThucDaoTao',
                'nhanVienBanHanh',
                'chucVuBanHanh',
                'capHoc',
                'TrangThaiLabel'
            ]);
            $items = $query->get();
            if ($capHocID = $request->input('capHocID')) {
                $items = $items->filter(function ($item) use ($capHocID) {
                    return $item->kyThi
                        && (string) $item->kyThi->CapHocID === (string) $capHocID;
                });
            }



            // status filter
            if (!is_null($trangThai)) {
                $items = $items->filter(
                    fn($item) =>
                    ((string) ($item->TrangThai ?? '21')) === (string) $trangThai
                );
            }

            // date filters
            if ($tuNgay) {
                $from = Carbon::createFromFormat('d/m/Y', $tuNgay)->startOfDay();
                $items = $items->filter(function ($item) use ($from) {
                    if (empty($item->NgayKy))
                        return false;
                    try {
                        return Carbon::createFromFormat('d/m/Y', $item->NgayKy)->gte($from);
                    } catch (\Exception $e) {
                        return false;
                    }
                });
            }
            if ($denNgay) {
                $to = Carbon::createFromFormat('d/m/Y', $denNgay)->endOfDay();
                $items = $items->filter(function ($item) use ($to) {
                    if (empty($item->NgayKy))
                        return false;
                    try {
                        return Carbon::createFromFormat('d/m/Y', $item->NgayKy)->lte($to);
                    } catch (\Exception $e) {
                        return false;
                    }
                });
            }

            // 4) Sort by NgayKy desc
            $sorted = $items
                ->sortByDesc(function ($item) {
                    try {
                        return Carbon::createFromFormat('d/m/Y', $item->NgayKy);
                    } catch (\Exception $e) {
                        return null;
                    }
                })
                ->values();

            // 5) Paginate the collection slice
            $total = $sorted->count();
            $lastPage = (int) ceil($total / $size);
            $pageItems = $sorted->forPage($page, $size)->values();

            // 6) Return Tabulator‐friendly JSON
            return response()->json([
                'last_page' => $lastPage,
                'last_row' => $total,
                'data' => $pageItems,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function getHocSinhTNWithDoiTuongByQuyetDinhId($quyetDinhId)
    {
        try {
            $quyetDinhObjectId = new ObjectId($quyetDinhId);

            // 1) select only the cols your resource needs, including _id
            // 2) eager-load exactly the relationships your Resources will serialize
            $hocSinhList = BangDiem_ChiTiet::select(
                '_id',
                'HocSinhID',
                'KyThiID',
                'BangDiemId',
                'SBD',
                'DiemCuoiCap',
                'DiemMonThi',
                'DiemUT',
                'PhongThi',
                'DiaDiemThi',
                'GhiChu'
            )
                // ->whereHas('hocSinh')    // ← this will drop any where hocSinh is null
                ->with([
                    // this will be passed into DoiTuongSimpleResource
                    'hocSinh:id,MaDoiTuong,Hovaten,Ngaysinh,Noisinh,Gioitinh,CCCD,DiaChi,SDT,Email,LopHocID,DonViID,DonViID_Hoc',
                    'hocSinh.danToc:id,TenDanToc',
                    'hocSinh.diaBanTinh:id,TenDiaBan',
                    'hocSinh.donViHoc:id,TenDonVi',
                    'hocSinh.gioiTinh_:TenGioiTinh'
                ])
                ->where('BangDiemId', $quyetDinhObjectId)
                ->get();

            // if you expect an empty list, just return an empty array
            if ($hocSinhList->isEmpty()) {
                return response()->json([
                    'Err' => false,
                    'Result' => [],
                ]);
            }

            // Wrap the collection in your Resource—this will strip away
            // anything not defined in toArray() of your BangDiemChiTietResource
            return response()->json([
                'Err' => false,
                'Result' => BangDiemChiTietResource::collection($hocSinhList),
            ]);
        } catch (Exception $e) {
            \Log::error('Lấy danh sách chi tiết bảng điểm thất bại: ' . $e->getMessage());

            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra khi lấy danh sách chi tiết bảng điểm thất bại.',
                $e
            ], 500);
        }
    }

    public function loadDuLieuSua(Request $request)
    {
        $id = $request->input('id');
        if (!$id) {
            return response()->json([
                'Err' => true,
                'Msg' => 'ID không được để trống',
            ], 400);
        }

        try {
            $quyetDinh = BangDiem::with([
                'hoiDong',
                'kyThi',
                'chucVu',
                'hinhThucDaoTao',
                'capHoc',
                'nhanVienBanHanh',
                'chucVuBanHanh',
            ])
                ->where('_id', $id)
                ->first();

            if (!$quyetDinh) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Không tìm thấy bảng điểm với ID đã cho',
                ], 404);
            }

            return response()->json([
                'Err' => false,
                'Result' => $quyetDinh,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi khi lấy dữ liệu: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function luuThongTin(Request $request)
    {
        $bangDiem = null;
        $msg = '';
        $thaoTac = '';
        $didLogUpdate = false;  // sẽ bật true khi có tạo mới hoặc sửa thực tế

        try {
            // 1) Thông tin user
            $donViId = $this->currentUser->donViId();
            $username = $this->currentUser->username();

            // 2) Thư mục tạm & chính
            $tempPrefix = "uploads/{$donViId}/{$username}/files/";
            $targetDir = "{$tempPrefix}BangDiemHocSinh";
            $disk = Storage::disk('public');

            // 3) Xử lý file uploads (DinhKemQD)
            $fileUrls = $request->input('DinhKemQD', []);
            $newFileUrls = [];
            foreach ($fileUrls as $fileUrl) {
                $relative = ltrim(str_replace('/storage/', '', $fileUrl), '/');
                if (Str::startsWith($relative, $tempPrefix)) {
                    $filename = basename($relative);
                    $base = pathinfo($filename, PATHINFO_FILENAME);
                    $ext = pathinfo($filename, PATHINFO_EXTENSION);
                    $newPath = "{$targetDir}/{$filename}";
                    $finalPath = $newPath;
                    while ($disk->exists($finalPath)) {
                        $finalPath = "{$targetDir}/{$base}_" . Str::random(6) . ".{$ext}";
                    }
                    if ($disk->exists($relative)) {
                        $disk->copy($relative, $finalPath);
                        $disk->delete($relative);
                    }
                    $newFileUrls[] = '/storage/' . $finalPath;
                } else {
                    // đã ở thư mục chính
                    $newFileUrls[] = $fileUrl;
                }
            }

            // 4) Đọc toàn bộ payload
            $data = $request->all();
            $data['DinhKemQD'] = $newFileUrls;

            // 5) Ánh xạ payload → bangDiemData
            $bangDiemData = [];
            $map = [
                'SoVanBan' => 'SoQuyetDinh',
                'NguoiKy' => 'NguoiKy',
                'ChucVu' => 'ChucVuID_NK',
                'NgayKy' => 'NgayKy',
                'CoQuanBanHanh' => 'CoQuanBanHanh',
                'KyThi' => 'KyThiID',
                'HinhThucDaoTao' => 'HinhThucID',
                'HoiDong' => 'HoiDongID',
                'NamHoc' => 'NamHoc',
                'TrichYeu' => 'TrichYeu',
                'CapHoc' => 'CapHocID',
                'KhoaThi' => 'KhoaThiID',
            ];
            foreach ($map as $inputKey => $dbColumn) {
                if ($request->has($inputKey)) {
                    $bangDiemData[$dbColumn] = $request->input($inputKey);
                }
            }

            // 6) Luôn lưu UserID và FileDinhKem
            $bangDiemData['UserID'] = (string) $this->currentUser->id();
            $bangDiemData['DonViID'] = (string) $this->currentUser->donViId();
            $bangDiemData['FileDinhKem'] = $newFileUrls;

            // 7) Parse ngày ký từ dd/mm/YYYY → Carbon
            if (!empty($data['NgayKy'])) {
                try {
                    $bangDiemData['NgayKy'] = Carbon::createFromFormat('d/m/Y', $data['NgayKy']);
                } catch (\Exception $e) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Định dạng ngày không hợp lệ, phải là dd/mm/YYYY',
                    ], 422);
                }
            }

            // 8) Chuyển các khóa ngoại về ObjectId
            foreach (['HoiDongID', 'KyThiID', 'KhoaThiID', 'HinhThucID', 'CapHocID'] as $fk) {
                if (!empty($bangDiemData[$fk])) {
                    $bangDiemData[$fk] = new ObjectId($bangDiemData[$fk]);
                }
            }
            $details = null;

            // 9) Tạo mới hoặc cập nhật
            if ($request->isMethod('post')) {
                $bangDiemData['TrangThai'] = '34';//Chưa phê duyệt
                // CREATE
                $bangDiem = BangDiem::create($bangDiemData);
                $msg = 'Tạo mới bảng điểm thành công';
                $thaoTac = 'Thêm bảng điểm';
                $didLogUpdate = true;
            } elseif ($request->isMethod('put') || $request->isMethod('patch')) {
                // UPDATE
                $bangDiem = BangDiem::where('_id', $data['BangDiemID'])->first();

                $original = $bangDiem->getOriginal();


                if (!$bangDiem) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => ThongBao::coLoiXayRa('Không tìm thấy bảng điểm để cập nhật'),
                    ], 404);
                }

                $thaoTac = 'Cập nhật bảng điểm';
                $bangDiem->update($bangDiemData);

                // 9b) If KyThi changed, remove scores for subjects
                if ($request->boolean('kyThiChanged')) {
                    $bangDiem->load('kyThi');
                    $newMonThiIds = collect($bangDiem->kyThi->MonThi)
                        ->pluck('MonThiID')
                        ->map(fn($id) => (string) $id)
                        ->toArray();
                    // find all detail records for this BangDiem
                    $details = BangDiem_ChiTiet::where('BangDiemId', new ObjectId($bangDiem->_id))->get();
                    foreach ($details as $d) {
                        // filter out any scores whose MonThiID is no longer in the new list
                        $d->DiemMonThi = array_filter(
                            (array) $d->DiemMonThi,
                            fn($score, $monThiId) => in_array($monThiId, $newMonThiIds, true),
                            ARRAY_FILTER_USE_BOTH
                        );
                        $d->DiemCuoiCap = array_filter(
                            (array) $d->DiemCuoiCap,
                            fn($score, $monThiId) => in_array($monThiId, $newMonThiIds, true),
                            ARRAY_FILTER_USE_BOTH
                        );
                        // update the detail’s KyThiID to the new one
                        $d->KyThiID = (string) $bangDiem->KyThiID;
                        $d->save();
                    }
                }

                $msg = 'Cập nhật bảng điểm thành công';

                // kiểm tra thay đổi thực sự
                $rawChanges = $bangDiem->getChanges();

                $actualChanges = [];
                foreach ($rawChanges as $key => $newValue) {
                    $oldValue = $original[$key] ?? null;

                    // skip system fields
                    if (in_array($key, ['updated_at', 'UserID'])) {
                        continue;
                    }

                    // avoid casting arrays to string
                    if (is_array($oldValue) || is_array($newValue)) {
                        continue;
                    }

                    if ((string) $oldValue !== (string) $newValue) {
                        $actualChanges[$key] = $newValue;
                    }
                }

                \Log::info('Changes: ' . json_encode($actualChanges, flags: JSON_PRETTY_PRINT));
                if (($request->isMethod('put') || $request->isMethod('patch')) && count($actualChanges) > 0) {
                    $this->logService->ghiLogs(
                        $bangDiem->toArray(),
                        $thaoTac,
                        $this->currentUser->id(),
                        $this->currentUser->donViId()
                    );
                    $didLogUpdate = true;
                }
            } else {
                return response()->json([
                    'Err' => true,
                    'canhbao' => true,
                    'Msg' => ThongBao::coLoiXayRa('Phương thức HTTP không được hỗ trợ'),
                ], 405);
            }

            // 10) Trả về kết quả
            return response()->json([
                'Err' => false,
                'canhbao' => false,
                'Msg' => $msg,
                'Data' => $bangDiem,
                $details
            ]);
        } catch (\Exception $ex) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => ThongBao::coLoiXayRa($ex->getMessage()),
            ], 500);
        } finally {
            // Ghi nhật ký thao tác nếu có create hoặc update thực sự
            if (isset($bangDiem) && $didLogUpdate) {
                try {
                    $method = $request->isMethod('post') ? 'Thêm' : 'Sửa';
                    $soQD = $data['SoVanBan'] ?? '';
                    $ngayKy = $data['NgayKy'] ?? '';
                    $hoiDongID = $data['HoiDong'] ?? '';
                    $hoidong = HoiDong::find($hoiDongID);
                    $noiDungChiTiet = sprintf(
                        '%s bảng điểm (QĐ: %s, ngày ký %s) của Hội đồng %s',
                        $method,
                        $soQD,
                        $ngayKy,
                        $hoidong ? $hoidong->TenHoiDong : 'không xác định'
                    );

                    $duLieuThaoTac = ThaoTac::getByThaoTac($method, (string) $this->currentUser->id())
                        ->firstOrFail();

                    NhatKyThaoTac::luuNhatKyThaoTac(
                        $duLieuThaoTac['MaThaoTac'],
                        $duLieuThaoTac['ThaoTac'],
                        $duLieuThaoTac['MauSac'],
                        $thaoTac,
                        $noiDungChiTiet,
                        (string) $bangDiem->_id,
                        $bangDiem->getTable(),
                        url()->current(),
                        $duLieuThaoTac['NguoiThaoTac'],
                        $duLieuThaoTac['ChucVu'],
                        (string) $this->currentUser->id(),
                        (string) $this->currentUser->donViId()
                    );
                } catch (\Exception $nkEx) {
                    \Log::error('Ghi NhatKyThaoTac thất bại: ' . $nkEx->getMessage());
                }
            }
        }
    }


    public function luuthongtinHocSinhTN(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'BangDiemId' => 'required|string',
            'hocSinhID' => 'required|string',  // Mongo _id of DoiTuong / HocSinhTN
            'KyThiID' => 'nullable|string',
        ]);


        if ($validator->fails()) {
            return response()->json([
                'Err' => true,
                'Msg' => $validator->errors()->first(),
            ], 422);
        }

        try {
            $data = $validator->validated();

            $quyetDinhObjectId = new ObjectId($data['BangDiemId']);
            $doiTuongObjectId = new ObjectId($data['hocSinhID']);
            $kyThiObjectId = isset($data['KyThiID']) ? new ObjectId($data['KyThiID']) : null;
            // Check if DoiTuong exists
            $doiTuong = DoiTuong::find($doiTuongObjectId);
            if (!$doiTuong) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Không tìm thấy học sinh.',
                    'Result' => [],
                ], 404);
            }

            // Determine if POST (create) or PUT (update)
            //Switch HTTP Method
            switch (strtoupper($request->method())) {
                case 'POST':
                    $bangdiem_ct = new BangDiem_ChiTiet();
                    $bangdiem_ct->_id = new ObjectId(); // new ID for HocSinhTN
                    $bangdiem_ct->SBD = $doiTuong->MaDoiTuong; // new ID for HocSinhTN
                    $bangdiem_ct->HocSinhID = (string) $doiTuongObjectId;
                    $bangdiem_ct->KyThiID = (string) $kyThiObjectId;
                    break;
                case "PUT":
                    $detailId = $request->input('BangDiemCTID');
                    $bangdiem_ct = BangDiem_ChiTiet::find(new ObjectId($detailId));
                    if (!$bangdiem_ct) {
                        return response()->json([
                            'Err' => true,
                            'Msg' => 'Không tìm thấy chi tiết bảng điểm.',
                        ], 404);
                    }
                    // only update the detail fields
                    $bangdiem_ct->DiemMonThi = $request->input('DiemMonThi', []);
                    $bangdiem_ct->DiemCuoiCap = $request->input('DiemCuoiCap', []);
                    $bangdiem_ct->DiemUT = $request->input('DiemUT', []);
                    $bangdiem_ct->SBD = $request->input('SBD', []);
                    $bangdiem_ct->PhongThi = $request->input('PhongThi', []);
                    $bangdiem_ct->DiaDiemThi = $request->input('DiaDiemThi', []);
                    $bangdiem_ct->GhiChu = $request->input('GhiChu', '');
                    break;
                default:
                    return response()->json([
                        'Err' => true,
                        'Msg' => ThongBao::methodKhongHoTro($request->method()),
                    ], 405);
            }

            $bangdiem_ct->BangDiemId = $quyetDinhObjectId;  // save the ObjectId here ONLY

            $bangdiem_ct->save();

            $msg = $request->isMethod('post') ? 'Tạo mới học sinh tốt nghiệp thành công.' : 'Cập nhật thông tin thành công.';

            return response()->json([
                'Err' => false,
                'Msg' => $msg,
                'Result' => $bangdiem_ct,
            ]);
        } catch (Exception $e) {
            \Log::error('Lỗi lưu chi tiết bảng điểm: ' . $e->getMessage());
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra khi lưu thông tin.',
            ], 500);
        } finally {
            $this->logService->ghiLogs(
                $bangdiem_ct->toArray(),
                $request->isMethod('post') ? 'Thêm mới chi tiết bảng điểm học sinh' : 'Cập nhật chi tiết bảng điểm học sinh',
                $this->currentUser->id(),
                $this->currentUser->donViId()
            );

            if (isset($bangdiem_ct) && isset($doiTuong)) {
                try {
                    // 1) Xác định nhãn thao tác: "Thêm mới" hay "Cập nhật"
                    $methodLabel = $request->isMethod('post') ? 'Thêm mới' : 'Cập nhật';

                    // 2) Thông tin học sinh
                    $hoTen = $doiTuong->Hovaten;
                    $cccd = $doiTuong->CCCD;
                    // quan hệ donViHoc trên model DoiTuong
                    $tenTruong = optional($doiTuong->donViHoc)->TenDonVi ?? '';

                    // 3) Xây dựng nội dung
                    $noiDung = sprintf(
                        '%s điểm của học sinh',
                        $methodLabel,
                    );
                    $noiDungChiTiet = sprintf(
                        '%s điểm của học sinh %s, số CMND/CCCD: %s thuộc trường %s (ID: %s)',
                        $methodLabel,
                        $hoTen,
                        $cccd,
                        $tenTruong,
                        (string) $doiTuongObjectId
                    );

                    // 4) Lấy màu và mã thao tác từ collection ThaoTac
                    //    gọi getByThaoTac() với tên gốc "Thêm" / "Sửa"
                    $lookupName = $request->isMethod('post') ? 'Thêm' : 'Sửa';
                    $duLieuThaoTac = ThaoTac::getByThaoTac($lookupName, $this->currentUser->id())
                        ->firstOrFail();

                    // 5) Ghi vào NhatKyThaoTac
                    NhatKyThaoTac::luuNhatKyThaoTac(
                        $duLieuThaoTac['MaThaoTac'],
                        $duLieuThaoTac['ThaoTac'],
                        $duLieuThaoTac['MauSac'],
                        $noiDung,                     // nội dung ngắn
                        $noiDungChiTiet, // chi tiết JSON
                        (string) $bangdiem_ct->BangDiemId,    // ThamChieuID
                        $bangdiem_ct->getTable(),             // BangThamChieu (thường 'bangdiem_chitiet')
                        url()->current(),                     // đường dẫn
                        $duLieuThaoTac['NguoiThaoTac'],
                        $duLieuThaoTac['ChucVu'],
                        (string) $this->currentUser->id(),
                        (string) $this->currentUser->donViId()
                    );
                } catch (\Exception $nkEx) {
                    \Log::error('Ghi NhatKyThaoTac thất bại: ' . $nkEx->getMessage());
                }
            }
        }
    }
    public function luuThongTinOCR(Request $request)
    {
        try {
            // 1) Map incoming fields → BangDiem columns
            $data = $request->all();
            $map = [
                'SoQuyetDinh' => 'SoQuyetDinh',
                'NguoiKy' => 'NguoiKy',
                'ChucVu' => 'ChucVuID_NK',
                'NgayKy' => 'NgayKy',
                'CoQuanBanHanh' => 'CoQuanBanHanh',
                'KyThi' => 'KyThiID',
                'HinhThucDaoTao' => 'HinhThucID',
                'HoiDong' => 'HoiDongID',
                'NamHoc' => 'NamHoc',
                'TrichYeu' => 'TrichYeu',
                'DinhKem' => 'FileDinhKemOCR',
            ];
            $bdData = [];
            foreach ($map as $in => $col) {
                if ($request->filled($in)) {
                    $bdData[$col] = $request->input($in);
                }
            }
            $bdData['UserID'] = (string) $this->currentUser->id();

            // 2) Parse NgayKy
            if (!empty($data['NgayKy'])) {
                try {
                    $bdData['NgayKy'] = Carbon::createFromFormat('d/m/Y', $data['NgayKy']);
                } catch (\Exception $e) {
                    return response()->json([
                        'Err' => true,
                        'Msg' => 'Ngày ký phải ở định dạng dd/mm/YYYY',
                    ], 422);
                }
            }

            // 3) Cast FKs
            foreach (['KyThiID', 'HinhThucID', 'HoiDongID'] as $fk) {
                if (!empty($bdData[$fk])) {
                    $bdData[$fk] = new ObjectId($bdData[$fk]);
                }
            }

            // 4) Create or update BangDiem
            if ($request->isMethod('post')) {
                $bdData['TrangThai'] = '34';
                $bangDiem = BangDiem::create($bdData);
            } else {
                $id = $request->input('BangDiemID');
                $bangDiem = BangDiem::where('_id', $id)->firstOrFail();
                $bangDiem->update($bdData);
            }
            $rawDetails = $request->input('DanhSachHocSinh', []);
            // remove rows with no SBD, no CCCD, no name
            $details = array_filter($rawDetails, function ($r) {
                return !empty(trim($r['SBD'] ?? ''))
                    || !empty(trim($r['CCCD'] ?? ''))
                    || !empty(trim($r['HoVaTen'] ?? ''));
            });

            // 5) Add detail rows (add‑only), but first create DoiTuong
            $details = $request->input('DanhSachHocSinh', []);
            foreach ($details as $row) {
                // build the student payload
                $dtData = [
                    'MaDoiTuong' => $row['SBD'] ?? null,
                    'Hovaten' => $row['HoVaTen'] ?? null,
                    'CCCD' => $row['CCCD'] ?? null,
                    'NgaySinh' => !empty($row['NgaySinh'])
                        ? Carbon::createFromFormat('d/m/Y', $row['NgaySinh'])
                        : null,
                    'Noisinh' => $row['NoiSinh'] ?? null,
                ];
                if (!empty($row['TenTruong'])) {
                    // try to find an existing DonVi by name
                    $donVi = DonVi::firstOrNew(
                        ['TenDonVi' => $row['TenTruong']],
                        // these attributes will only be applied on a NEW record
                        [
                            'MaDonVi' => Str::upper(Str::slug($row['TenTruong'], '')),
                            'TrangThai' => true,
                            'UserID' => (string) $this->currentUser->id(),
                            'NgayThaoTac' => now(),
                        ]
                    );
                    if (!$donVi->exists) {
                        $donVi->save();
                    }
                    $dtData['DonViID_Hoc'] = new ObjectId((string) $donVi->_id);
                }
                if (!empty($row['GioiTinh'])) {
                    $gt = GioiTinh::firstOrNew(
                        ['TenGioiTinh' => $row['GioiTinh']],
                        [
                            'MaGioiTinh' => Str::upper(Str::slug($row['GioiTinh'], '')),
                            'TrangThai' => true,
                            'UserID' => (string) $this->currentUser->id(),
                            'NgayThaoTac' => now(),
                        ]
                    );
                    if (!$gt->exists) {
                        $gt->save();
                    }
                    $dtData['Gioitinh'] = new ObjectId((string) $gt->_id);
                }


                // parse NgaySinh if present…

                // only try to look up if we have a real identifier
                $student = null;
                if (($dtData['CCCD'] != null && !empty($dtData['CCCD'])) || ($dtData['CCCD'] != null && !empty($dtData['MaDoiTuong']))) {
                    $student = DoiTuong::where('CCCD', $dtData['CCCD'])
                        ->orWhere('MaDoiTuong', $dtData['MaDoiTuong'])
                        ->first();
                }

                if ($student) {
                    $student->update($dtData);
                } else {
                    $student = DoiTuong::create($dtData);
                }

                $hocSinhId = $student->_id;

                // now save the detail row as before
                $ct = new BangDiem_ChiTiet();
                $ct->_id = new ObjectId();
                $ct->BangDiemId = new ObjectId((string) $bangDiem->_id);
                $ct->HocSinhID = $hocSinhId;
                $ct->KyThiID = isset($bangDiem['KyThiID']) ? new ObjectId($bangDiem['KyThiID']) : null;
                $ct->DiemMonThi = $row['DiemMonThi'] ?? [];
                $ct->DiemCuoiCap = $row['DiemCuoiCap'] ?? [];
                $ct->GhiChu = $row['GhiChu'] ?? '';
                $ct->save();
            }

            return response()->json([
                'Err' => false,
                'Msg' => $request->isMethod('post')
                    ? 'Tạo mới bảng điểm & chi tiết thành công'
                    : 'Cập nhật bảng điểm & thêm chi tiết thành công',
                'Data' => $bangDiem,
            ]);

        } catch (\Exception $ex) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi khi lưu thông tin OCR: ' . $ex->getMessage(),
            ], 500);
        }
    }
    public function getHocSinhTNById($id)
    {
        try {

            $hocSinhTN = HocSinhTN::with(['hocSinh', 'xepLoaiHocTap', 'xepLoaiRenLuyen', 'xepLoaiTotNghiep'])
                ->where('_id', $id)
                ->first();

            if (!$hocSinhTN) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Không tìm thấy học sinh tốt nghiệp.',
                ], 404);
            }

            return response()->json([
                'Err' => false,
                'Result' => $hocSinhTN,
            ]);
        } catch (Exception $e) {
            \Log::error('Error fetching HocSinhTN by ID: ' . $e->getMessage());
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra khi lấy thông tin học sinh tốt nghiệp.',
            ], 500);
        }
    }
    public function luuthongtinBanHanh(Request $request, $bangdiemid)
    {
        $quyetDinh = null;
        $thaoTac = 'Phê duyệt bảng điểm học sinh';

        try {
            $data = $request->only(['NgayBanHanh', 'NhanVienID', 'ChucVuQL_BH', 'NoiDungBH']);
            $quyetDinh = BangDiem::find($bangdiemid);


            if (!empty($data['NgayBanHanh'])) {
                try {
                    $quyetDinh->NgayBanHanh = Carbon::createFromFormat('d/m/Y', $data['NgayBanHanh'])
                        ->startOfDay();
                    $quyetDinh->TrangThai = '32'; // trạng thái "Ban hành"
                } catch (\Exception $e) {
                    return response()->json([
                        'Err' => true,
                        'Msg' => 'Ngày ban hành không hợp lệ',
                    ], 422);
                }
            } else {
                $quyetDinh->NgayBanHanh = null;
            }
            // Assign other fields
            $quyetDinh->NhanVienID = $data['NhanVienID'] ?? null;
            $quyetDinh->ChucVuQL_BH = $data['ChucVuQL_BH'] ?? null;
            $quyetDinh->NoiDungBH = $data['NoiDungBH'] ?? null;

            // Clear thu hoi fields
            $quyetDinh->NgayThuHoi = null;
            $quyetDinh->NhanVienID_thuhoi = null;
            $quyetDinh->ChucVuQL_thuhoi = null;
            $quyetDinh->NoiDung_thuhoi = null;

            $quyetDinh->save();

            return response()->json([
                'Err' => false,
                'Msg' => 'Lưu thông tin ban hành thành công',
                'Result' => $quyetDinh,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi: ' . $e->getMessage(),
            ], 500);
        } finally {
            if ($quyetDinh !== null) {
                try {
                    $this->logService->ghiLogs(
                        $quyetDinh->toArray(),
                        $thaoTac,
                        $this->currentUser->id(),
                        $this->currentUser->donViId()
                    );
                } catch (\Exception $logEx) {
                    \Log::error('Ghi log thất bại: ' . $logEx->getMessage());
                }

                try {
                    // Common bits
                    $userID = (string) $this->currentUser->id();
                    $donViID = (string) $this->currentUser->donViId();
                    $methodName = 'Ban hành';

                    // 1) Lookup ThaoTac record
                    $dt = ThaoTac::getByThaoTac($methodName, $userID)
                        ->firstOrFail();

                    // 2) Build dynamic nội dung
                    $soQD = $quyetDinh->SoQuyetDinh;
                    $ngayBanHanh = $quyetDinh->NgayBanHanh
                        ? $quyetDinh->NgayBanHanh
                        : '';
                    $noiDung = sprintf(
                        'Ban hành bảng điểm '
                    );
                    $noiDungChiTiet = sprintf(
                        'Ban hành bảng điểm (QĐ: %s, ngày ban hành %s)',
                        $soQD,
                        $ngayBanHanh
                    );
                    $thamChieuID = (string) $quyetDinh->_id;
                    $bangThamChieu = $quyetDinh->getTable();
                    $duongDan = url()->current();

                    // 3) Who & what position
                    $userModel = $this->currentUser->user();
                    $nguoi = optional($userModel)->Hovaten ?? '';
                    $chucVu = optional(optional($userModel)->nhanVien->chucVu)->tenChucVu ?? '';

                    // 4) Write to NhatKyThaoTac
                    $nguoiThaoTac = $dt['NguoiThaoTac'];
                    $chucVu = $dt['ChucVu'];

                    // 4) Ghi NhatKyThaoTac
                    NhatKyThaoTac::luuNhatKyThaoTac(
                        $dt['MaThaoTac'],
                        $dt['ThaoTac'],
                        $dt['MauSac'],
                        $noiDung,
                        $noiDungChiTiet,
                        $thamChieuID,
                        $bangThamChieu,
                        $duongDan,
                        $nguoiThaoTac,
                        $chucVu,
                        $userID,
                        $donViID
                    );
                } catch (\Exception $nkEx) {
                    \Log::error('Ghi NhatKyThaoTac (Ban hành) thất bại: ' . $nkEx->getMessage());
                }
            }
        }
    }

    public function luuthongtinThuHoi(Request $request, $bangdiemid)
    {
        $quyetDinh = null;
        $thaoTac = 'Thu hồi phê duyệt bảng điểm học sinh';

        try {
            $data = $request->only(['NgayThuHoi', 'NhanVienID_thuhoi', 'ChucVuID_thuhoi', 'NoiDung_thuhoi']);
            $quyetDinh = BangDiem::find($bangdiemid);

            if (!$quyetDinh) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Không tìm thấy bảng điểm với ID đã cho',
                ], 404);
            }

            // Parse NgayThuHoi (expecting d/m/Y format)
            if (!empty($data['NgayThuHoi'])) {
                try {
                    $quyetDinh->NgayThuHoi = Carbon::createFromFormat('d/m/Y', $data['NgayThuHoi'])->startOfDay();
                    $quyetDinh->TrangThai = "33"; // Ban hành
                } catch (\Exception $e) {
                    return response()->json([
                        'Err' => true,
                        'Msg' => 'Ngày thu hồi không hợp lệ',
                    ], 422);
                }
            } else {
                $quyetDinh->NgayThuHoi = null;
            }

            // Assign other thu hoi fields
            $quyetDinh->NhanVienID_thuhoi = $data['NhanVienID_thuhoi'] ?? null;
            $quyetDinh->ChucVuQL_thuhoi = $data['ChucVuID_thuhoi'] ?? null;
            $quyetDinh->NoiDung_thuhoi = $data['NoiDung_thuhoi'] ?? null;
            $quyetDinh->save();

            return response()->json([
                'Err' => false,
                'Msg' => 'Lưu thông tin thu hồi thành công',
                'Result' => $quyetDinh,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi: ' . $e->getMessage(),
            ], 500);
        } finally {
            if ($quyetDinh !== null) {
                try {
                    $this->logService->ghiLogs(
                        $quyetDinh->toArray(),
                        $thaoTac,
                        $this->currentUser->id(),
                        $this->currentUser->donViId()
                    );
                } catch (\Exception $logEx) {
                    \Log::error('Ghi log thất bại: ' . $logEx->getMessage());
                }

                try {
                    $userID = (string) $this->currentUser->id();
                    $donViID = (string) $this->currentUser->donViId();
                    $methodName = 'Thu hồi ban hành';

                    // 1) Lookup ThaoTac record
                    $dt = ThaoTac::getByThaoTac($methodName, $userID)
                        ->firstOrFail();

                    // 2) Build dynamic nội dung
                    $soQD = $quyetDinh->SoQuyetDinh;
                    $ngayThuHoi = $quyetDinh->NgayThuHoi
                        ? $quyetDinh->NgayThuHoi->format('d/m/Y')
                        : '';
                    $noiDung = sprintf(
                        'Thu hồi ban hành bảng điểm'
                    );
                    $noiDungChiTiet = sprintf(
                        'Thu hồi ban hành bảng điểm (QĐ: %s, ngày thu hồi %s)',
                        $soQD,
                        $ngayThuHoi
                    );
                    $thamChieuID = (string) $quyetDinh->_id;
                    $bangThamChieu = $quyetDinh->getTable();
                    $duongDan = url()->current();

                    // 3) Who & what position
                    $nguoiThaoTac = $dt['NguoiThaoTac'];
                    $chucVu = $dt['ChucVu'];

                    // 4) Ghi NhatKyThaoTac
                    NhatKyThaoTac::luuNhatKyThaoTac(
                        $dt['MaThaoTac'],
                        $dt['ThaoTac'],
                        $dt['MauSac'],
                        $noiDung,
                        $noiDungChiTiet,
                        $thamChieuID,
                        $bangThamChieu,
                        $duongDan,
                        $nguoiThaoTac,
                        $chucVu,
                        $userID,
                        $donViID
                    );
                } catch (\Exception $nkEx) {
                    \Log::error('Ghi NhatKyThaoTac (Thu hồi) thất bại: ' . $nkEx->getMessage());
                }
            }
        }
    }

    /**
     * Xóa đối tượng
     */
    public function xoa(Request $request)
    {
        $model = null;
        $thaoTac = 'Xóa bảng điểm học sinh';

        try {
            $id = $request->input('ma');
            $model = BangDiem::whereKey($id)->firstOrFail();

            $model->delete();

            return response()->json([
                'Err' => false,
                'Msg' => 'Xóa bảng điểm thành công!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => $e instanceof \Illuminate\Database\Eloquent\ModelNotFoundException
                    ? 'Không tìm thấy bảng điểm để xóa!'
                    : 'Lỗi: ' . $e->getMessage(),
            ], 500);
        } finally {
            if ($model !== null) {
                try {
                    $this->logService->ghiLogs(
                        $model->toArray(),
                        $thaoTac,
                        $this->currentUser->id(),
                        $this->currentUser->donViId()
                    );
                } catch (\Exception $logEx) {
                    \Log::error('Ghi log thất bại: ' . $logEx->getMessage());
                }


            }
        }
    }
    public function xoaHocSinhTN(Request $request)
    {
        $hocSinhTN = null;
        $thaoTac = 'Xóa học sinh tốt nghiệp';

        try {
            $validator = Validator::make($request->all(), [
                'BangDiemId' => 'required|string',
                'ma' => 'required|string',
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'Err' => true,
                    'Msg' => $validator->errors()->first(),
                ], 422);
            }

            $data = $validator->validated();
            $hocSinhTN = BangDiem_ChiTiet::find(new ObjectId($data['ma']));
            if (!$hocSinhTN) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Không tìm thấy chi tiết bảng điểm.',
                ], 404);
            }

            $hocSinhTN->delete();
            $bangdiem = BangDiem::find($request->BangDiemId);
            $hocSinhTNId = new ObjectId($data['ma']);

            if ($bangdiem && is_array($bangdiem->HocSinhTN)) {
                $hocSinhTNArray = $bangdiem->HocSinhTN;

                // Filter out the deleted hocSinhTNId
                $hocSinhTNArray = array_filter($hocSinhTNArray, function ($id) use ($hocSinhTNId) {
                    return (string) $id !== (string) $hocSinhTNId;
                });

                // Re-index array if needed
                $bangdiem->HocSinhTN = array_values($hocSinhTNArray);
                $bangdiem->save();
            }

            return response()->json([
                'Err' => false,
                'Msg' => 'Xóa chi tiết bảng điểm thành công.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi: ' . $e->getMessage(),
            ], 500);
        } finally {
            if ($hocSinhTN !== null) {
                try {
                    $this->logService->ghiLogs(
                        $hocSinhTN->toArray(),
                        $thaoTac,
                        $this->currentUser->id(),
                        $this->currentUser->donViId()
                    );
                } catch (\Exception $logEx) {
                    \Log::error('Ghi log thất bại: ' . $logEx->getMessage());
                }
            }
            if ($hocSinhTN !== null) {
                try {
                    $userID = (string) $this->currentUser->id();
                    $donViID = (string) $this->currentUser->donViId();
                    $methodName = 'Xóa';

                    // 1) Lookup “Xóa” action
                    $dt = ThaoTac::getByThaoTac($methodName, $userID)
                        ->firstOrFail();

                    // 2) Build “Nội dung” giống mẫu thứ 1
                    $doiTuong = DoiTuong::find(new ObjectId($hocSinhTN->HocSinhID));

                    $hoTen = $doiTuong->Hovaten;
                    $cccd = $doiTuong->CCCD;
                    $truong = optional($doiTuong->donViHoc)->TenDonVi ?? '';

                    $noiDung = sprintf(
                        'Xóa bảng điểm của học sinh',
                    );

                    // 3) Các trường còn lại
                    $noiDungChiTiet = sprintf(
                        'Xóa bảng điểm của học sinh %s, số CMND/CCCD: %s thuộc trường %s',
                        $hoTen,
                        $cccd,
                        $truong
                    );
                    $thamChieuID = (string) $request->BangDiemId;
                    $bangThamChieu = $hocSinhTN->getTable();
                    $duongDan = url()->current();

                    $userModel = $this->currentUser->user();
                    $nguoiThaoTac = $dt['NguoiThaoTac'];
                    $chucVu = $dt['ChucVu'];

                    // 4) Ghi NhatKyThaoTac
                    NhatKyThaoTac::luuNhatKyThaoTac(
                        $dt['MaThaoTac'],
                        $dt['ThaoTac'],
                        $dt['MauSac'],
                        $noiDung,
                        $noiDungChiTiet,
                        $thamChieuID,
                        $bangThamChieu,
                        $duongDan,
                        $nguoiThaoTac,
                        $chucVu,
                        $userID,
                        $donViID
                    );
                } catch (\Exception $nkEx) {
                    \Log::error($dt);

                    \Log::error('Ghi NhatKyThaoTac (Xóa HS TN) thất bại: ' . $nkEx->getMessage());
                }
            }

        }
    }

    public function getMonThiTrongBangDiem(Request $request)
    {
        $bangDiemId = $request->input('bangDiemId');
        if (!$bangDiemId) {
            return response()->json([
                'Err' => true,
                'Msg' => 'ID bảng điểm không được để trống',
            ], 400);
        }

        try {
            $bangDiem = BangDiem::with(['kyThi'])->find($bangDiemId);
            if (!$bangDiem) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Không tìm thấy bảng điểm với ID đã cho',
                ], 404);
            }

            $kyThi = $bangDiem->kyThi;
            $monThi = $kyThi ? $kyThi->MonThi : [];



            return response()->json([
                'Err' => false,
                'Result' => collect($monThi)->map(function (array $item) {
                    $mh = MonHoc::find($item['MonThiID']);  // or MonHoc::find(new ObjectId(...))
                    return [
                        'id' => $item['MonThiID'],
                        'maMonHoc' => $mh->maMonHoc ?? null,
                        'tenMonHoc' => $mh->tenMonHoc ?? null,
                        'heSo' => $item['DiemHeSo'] ?? null,
                        'diemLiet' => $item['DiemLiet'] ?? null,
                    ];
                }),
            ]);
        } catch (Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi: ' . $e->getMessage(),
            ], 500);
        }
    }
    public function getMonThiTrongKyThi(Request $request)
    {
        $kyThiId = $request->input('kyThiId');
        if (!$kyThiId) {
            return response()->json([
                'Err' => true,
                'Msg' => 'ID kỳ thi không được để trống',
            ], 400);
        }

        try {
            $kyThi = KyThi::find($kyThiId);
            if (!$kyThi) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Không tìm thấy kỳ thi với ID đã cho',
                ], 404);
            }

            // giả sử $kyThi->MonThi trả về mảng các item giống như trong BangDiem
            $monThiList = $kyThi->MonThi ?? [];

            $result = collect($monThiList)->map(function (array $item) {
                $mh = MonHoc::find($item['MonThiID']);
                return [
                    'id' => $item['MonThiID'],
                    'maMonHoc' => $mh->maMonHoc ?? null,
                    'tenMonHoc' => $mh->tenMonHoc ?? null,
                    'heSo' => $item['DiemHeSo'] ?? null,
                    'diemLiet' => $item['DiemLiet'] ?? null,
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi: ' . $e->getMessage(),
            ], 500);
        }
    }

    #region Nhận excel
    public function checkExcel(Request $request)
    {
        // Lấy đường dẫn file và tên sheet từ request
        $path = $request->input('path', '');
        $sheet = $request->input('sheet', '');
        $bangDiemId = $request->input('bangDiemId');

        // Kiểm tra đầu vào
        if (!$path || !$sheet) {
            return response()->json(['Err' => true, 'Msg' => 'Thiếu đường dẫn hoặc tên sheet'], 422);
        }

        // Xử lý đường dẫn file
        $urlPath = parse_url($path, PHP_URL_PATH);
        $urlPath = preg_replace('#^/+/#', '/', $urlPath);
        $relative = Str::after($urlPath, '/storage/');
        if (!Storage::disk('public')->exists($relative)) {
            return response()->json(['Err' => true, 'Msg' => 'Không tìm thấy file'], 404);
        }
        $fullPath = Storage::disk('public')->path($relative);

        try {
            // Đọc file excel
            $reader = IOFactory::createReaderForFile($fullPath);
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($fullPath);

            // Kiểm tra sheet tồn tại
            if (!$spreadsheet->sheetNameExists($sheet)) {
                return response()->json(['Err' => true, 'Msg' => "Không tìm thấy sheet '{$sheet}'"], 422);
            }

            $ws = $spreadsheet->getSheetByName($sheet);
            $allRows = $ws->toArray(null, true, true, false);
            if (count($allRows) < 2) {
                return response()->json(['Err' => true, 'Msg' => 'Không đủ dòng tiêu đề'], 422);
            }

            // Đọc dòng nhóm và dòng nhãn
            $groupRow = array_map('trim', $allRows[0]);
            $labelRow = array_map('trim', $allRows[1]);

            // Xác định vị trí nhóm động theo tiêu đề
            $startIdx = array_search('Điểm môn thi', $groupRow, true);
            $midIdx = array_search('Điểm cuối cấp', $groupRow, true);
            if ($startIdx === false || $midIdx === false) {
                return response()->json(['Err' => true, 'Msg' => 'Tiêu đề nhóm không đúng định dạng'], 422);
            }

            // Các cột tĩnh trước nhóm động
            $staticHeaders = array_slice($groupRow, 0, $startIdx);
            $countPerSubject = $midIdx - $startIdx;

            // Tên môn học dưới mỗi nhóm
            $sheetSubjects = array_slice($labelRow, $startIdx, $countPerSubject);
            $sheetSubjects2 = array_slice($labelRow, $midIdx, $countPerSubject);

            // Lấy danh sách môn học động (ưu tiên lấy từ DB nếu có)
            $dynamicSubjects = [];
            if ($bangDiemId && $this->isValidObjectId($bangDiemId)) {
                $bd = BangDiem::with('kyThi')->find($bangDiemId);
                if ($bd && $bd->kyThi) {
                    $dynamicSubjects = collect($bd->kyThi->MonThi)
                        ->map(fn($m) => MonHoc::find($m['MonThiID'])->tenMonHoc)
                        ->filter()->values()->toArray();
                }
            }
            if (empty($dynamicSubjects)) {
                $dynamicSubjects = $sheetSubjects;
            }

            // Thêm hậu tố cho nhóm thứ 2 để đảm bảo key duy nhất
            $subjects2Suffixed = array_map(fn($s) => "{$s} (cuối cấp)", $sheetSubjects2);

            // Các cột tĩnh phía sau
            $trailingHeaders = array_slice($groupRow, $midIdx + $countPerSubject);

            // Xây dựng tiêu đề đầy đủ
            $header = array_merge(
                $staticHeaders,
                $sheetSubjects,
                $subjects2Suffixed,
                $trailingHeaders
            );

            // Ghi log để debug
            Log::debug('checkExcel subjects:', $dynamicSubjects);
            Log::debug('checkExcel header:', $header);

            // Lấy các dòng dữ liệu (bỏ qua dòng tiêu đề)
            $dataRows = array_slice($allRows, 2);
            $dataRows = array_filter($dataRows, fn($r) => collect($r)->some(fn($c) => trim((string) $c) !== ''));
            $dataRows = array_values($dataRows);

            // Các cột bắt buộc phải có
            $required = array_merge(
                $staticHeaders,
                $dynamicSubjects,
                array_map(fn($s) => "{$s} (cuối cấp)", $dynamicSubjects),
                $trailingHeaders
            );

            // Kiểm tra thiếu cột
            if ($missing = array_diff($required, $header)) {
                return response()->json(['Err' => true, 'Msg' => 'Thiếu cột: ' . implode(', ', $missing)], 422);
            }

            // Giá trị hợp lệ cho dropdown
            $allowedGT = ['Nam', 'Nữ', 'Khác'];
            $allowedDV = DonVi::where('TrangThai', true)->pluck('TenDonVi')->toArray();
            $dropdown = ['Giới tính' => $allowedGT];

            // Kiểm tra dữ liệu từng dòng
            $assoc = [];
            $errors = [];
            foreach ($dataRows as $i => $row) {
                if (count($row) !== count($header)) {
                    $errors[] = "Dòng " . ($i + 3) . " sai số lượng cột";
                    continue;
                }
                $rec = array_combine($header, $row);
                foreach ($dropdown as $col => $allowed) {
                    $val = trim($rec[$col] ?? '');
                    if ($val !== '' && !in_array(mb_strtolower($val), array_map('mb_strtolower', $allowed), true)) {
                        $errors[] = "Dòng " . ($i + 3) . ": giá trị không hợp lệ ở cột {$col} '{$val}'";
                    }
                }
                $assoc[] = $rec;
            }

            // Lưu vào session để dùng cho bước sau
            session([
                'bangdiem_excel' => $assoc,
                'bangdiem_excel_subjects' => $sheetSubjects,
            ]);

            $final = [];

            foreach ($assoc as $i => $rec) {
                $mapped = $this->formatRow($rec, $dynamicSubjects);
                $rowErrors = [];

                foreach ($dropdown as $col => $allowed) {
                    $val = trim($rec[$col] ?? '');
                    if ($val !== '' && !in_array($val, $allowed, true)) {
                        $rowErrors[] = "{$col}: '{$val}' không hợp lệ";
                    }
                }

                if ($rowErrors) {
                    $mapped['TrangThai'] = 'Lỗi';
                    $mapped['DienGiaiTrangThai'] = implode('; ', $rowErrors);
                } else {
                    $mapped['DienGiaiTrangThai'] = '';
                }

                $final[] = $mapped;
            }

            session(['bangdiem_excel_formatted' => $final]);

            return response()->json([
                'Err' => false,
                'Total' => count($final),
                'Loi' => count(array_filter($final, fn($r) => $r['TrangThai'] === 'Lỗi')),
                'Result' => $final,
            ]);

        } catch (\Throwable $e) {
            // Lỗi đọc file
            return response()->json(['Err' => true, 'Msg' => 'Lỗi đọc file: ' . $e->getMessage()], 500);
        }
    }

    public function loadExcel(Request $request)
    {
        // Rehydrate dynamicSubjects from DB or session fallback
        $dynamicSubjects = [];
        $bangDiemId = $request->input('bangDiemId');
        if ($bangDiemId && $this->isValidObjectId($bangDiemId)) {
            $bd = BangDiem::with('kyThi')->find($bangDiemId);
            if ($bd && $bd->kyThi) {
                $dynamicSubjects = collect($bd->kyThi->MonThi)
                    ->map(fn($m) => MonHoc::find($m['MonThiID'])->tenMonHoc)
                    ->filter()->values()->toArray();
            }
        }
        if (empty($dynamicSubjects)) {
            $dynamicSubjects = session('bangdiem_excel_subjects', []);
        }

        $rows = collect(session('bangdiem_excel', []));
        $data = $rows->map(fn($r) => $this->formatRow($r, $dynamicSubjects));

        session(['bangdiem_excel_formatted' => $data->toArray()]);
        return response()->json($data);
    }

    protected function formatRow(array $r, array $subjects)
    {
        // Normalize Ngày sinh
        $raw = trim($r['Ngày sinh'] ?? '');
        if (is_numeric($raw)) {
            try {
                $dt = ExcelDate::excelToDateTimeObject((float) $raw);
            } catch (\Throwable $e) {
                $dt = null;
            }
        } elseif ($raw !== '') {
            $dt = $this->parseDateFlexible($raw);
        } else {
            $dt = null;
        }

        $out = [
            'SBD' => $r['Số báo danh'] ?? '',
            'HoVaTen' => $r['Họ và tên'] ?? '',
            'SoCMND' => $r['Số CMND/CCCD'] ?? '',
            'NgaySinh' => $dt?->format('Y-m-d') ?? null,
            'GioiTinh' => $r['Giới tính'] ?? '',
            'NoiSinh' => $r['Nơi sinh'] ?? '',
            'Truong' => $r['Học sinh trường'] ?? '',
            'PhongThi' => $r['Phòng thi'] ?? '',
            'DiaDiemThi' => $r['Địa điểm thi'] ?? '',
        ];

        // Inject scores with JS-friendly keys
        foreach ($subjects as $s) {
            $key = Str::slug($s, '_');
            $out['DiemMonThi'][$key] = $r[$s] ?? null;
            $out['DiemCuoiCap'][$key] = $r["{$s} (cuối cấp)"] ?? null;
        }

        $out['DiemUT'] = $r['Điểm ưu tiên'] ?? null;

        $out['GhiChu'] = $r['Ghi chú'] ?? '';
        $out['TrangThai'] = 'Chờ nhận';
        return $out;
    }


    function isValidObjectId($id)
    {
        return is_string($id) && preg_match('/^[a-f\d]{24}$/i', $id);
    }
    protected function parseDateFlexible($dateInput): ?Carbon
    {
        // Handle Excel serial date numbers
        if (is_numeric($dateInput)) {
            try {
                $dt = ExcelDate::excelToDateTimeObject((float) $dateInput);
                return Carbon::instance($dt);
            } catch (\Throwable $e) {
                // fall through to try parsing as string
            }
        }

        $dateString = trim((string) $dateInput);

        $formats = [
            'd/m/Y',
            'd-m-Y',
            'd/m/y',
            'd-m-y',
            'Y-m-d',
            'Y/m/d',
            'y-m-d',
            'y/m/d',
        ];

        foreach ($formats as $format) {
            try {
                $dt = Carbon::createFromFormat($format, $dateString);
                if ($dt !== false) {
                    return $dt;
                }
            } catch (\Exception $e) {
                // try next format
            }
        }

        // fallback to generic parse (may still fail)
        try {
            return Carbon::parse($dateString);
        } catch (\Exception $e) {
            return null;
        }

    }

    function findKeyByValue(array $array, string $value): ?string
    {
        $key = array_search(trim($value), $array);
        return $key !== false ? $key : null;
    }
    protected function findMonThiId(string $subjectName, string $bangDiemId): string
    {
        // Load the BangDiem with its KyThi → MonThi list
        $bd = BangDiem::with('kyThi')->find($bangDiemId);
        if (!$bd || !$bd->kyThi) {
            throw new \RuntimeException("Không tìm thấy BangDiem hoặc kỳ thi cho ID: {$bangDiemId}");
        }

        // Iterate each entry in the kyThi.MonThi array:
        foreach ($bd->kyThi->MonThi as $entry) {
            $mtId = (string) $entry['MonThiID'];
            $mh = MonHoc::find($entry['MonThiID']);
            if ($mh && $mh->tenMonHoc === $subjectName) {
                return $mtId;
            }
        }

        // If we get here, no match was found
        throw new \RuntimeException("Không tìm thấy MonThiID cho môn '{$subjectName}'");
    }

    protected function generateMaDoiTuong(): string
    {
        $kyhieuLoaiPhieu = 'MaDoiTuong';
        $bangDuLieu = 'doi_tuongs';
        $cotDuLieu = 'MaDoiTuong';

        $client = DB::connection('mongodb')->getMongoClient();
        $database = config('database.connections.mongodb.database');
        $collection = $client->selectCollection($database, $bangDuLieu);

        $cursor = $collection->find([
            $cotDuLieu => ['$type' => 'string']
        ], [
            'projection' => [$cotDuLieu => 1]
        ]);

        $maxNumeric = 0;
        foreach ($cursor as $doc) {
            $val = $doc[$cotDuLieu] ?? '';
            if (ctype_digit($val)) {
                $maxNumeric = max($maxNumeric, (int) $val);
            }
        }
        $next = $maxNumeric + 1;
        return str_pad((string) $next, 2, '0', STR_PAD_LEFT);
    }
    public function importExcel(Request $request)
    {
        $donViMap = DonVi::where('TrangThai', true)
            ->get(['MaDonVi', 'TenDonVi', '_id'])
            ->mapWithKeys(fn($d) => [
                // this must match exactly what your export used
                "{$d->MaDonVi} – {$d->TenDonVi}" => (string) $d->_id
            ])
            ->toArray();

        // 1) Pull in your zero-based indices
        $rowIndices = $request->input('rows', []);
        $sessionRows = session('bangdiem_excel_formatted', []);
        $quyetDinhId = $request->input('quyetDinhId');

        // 2) Basic sanity checks
        if (!$quyetDinhId || !$this->isValidObjectId($quyetDinhId)) {
            return response()->json(['Err' => true, 'Msg' => 'QuyetDinhID không hợp lệ'], 422);
        }
        if (!is_array($rowIndices) || empty($rowIndices)) {
            return response()->json(['Err' => true, 'Msg' => 'Không có hàng nào được chọn'], 200);
        }

        $quyetDinhOid = new ObjectId($quyetDinhId);
        $bd = BangDiem::find($quyetDinhId);
        $kyThiId = optional($bd)->KyThiID;
        if (!$kyThiId) {
            return response()->json(['Err' => true, 'Msg' => 'Không tìm thấy kỳ thi cho bảng điểm'], 422);
        }

        // Build slug→name map
        $slugMap = [];
        foreach (session('bangdiem_excel_subjects', []) as $name) {
            $ascii = Str::ascii($name);
            $slug = preg_replace('/[^a-z0-9]+/', '_', strtolower($ascii));
            $slugMap[$slug] = $name;
        }

        try {
            $out = [];
            foreach ($rowIndices as $origIndex) {
                // grab the _formatted_ row
                $r = $sessionRows[$origIndex] ?? null;
                if (!is_array($r)) {
                    $out[$origIndex] = ['Err' => true, 'Msg' => "Dữ liệu hàng {$origIndex} không tồn tại"];
                    continue;
                }

                // Validate
                $v = Validator::make($r, [
                    'SBD' => 'required',
                    'HoVaTen' => 'required',
                    'NgaySinh' => 'required',
                ]);
                if ($v->fails()) {
                    $out[$origIndex] = ['Err' => true, 'Msg' => $v->errors()->first()];
                    continue;
                }
                $sbd = trim($r['SBD']);
                if ($sbd === '') {
                    $sbd = $this->generateMaDoiTuong();
                }

                $truongLabel = trim($r['Truong'] ?? '');

                if (!isset($donViMap[$truongLabel])) {
                    $out[$origIndex] = [
                        'Err' => true,
                        'Msg' => "Dòng " . ($origIndex + 3) . ": Trường '{$truongLabel}' không có trong danh sách"
                    ];
                    continue;
                }
                $donViOid = new ObjectId($donViMap[$truongLabel]);

                // ——————————————
// 4) Similarly lookup gender’s ObjectId
                $gtName = trim($r['GioiTinh'] ?? '');
                $gt = GioiTinh::first(
                    ['TenGioiTinh' => $gtName]
                );
                if (!$gt->exists) {
                    $gt->save();
                }
                $gtOid = new ObjectId((string) $gt->_id);
                $maHocSinh = $this->generateMaDoiTuong();

                // Upsert DoiTuong
                $doi = DoiTuong::updateOrCreate(
                    ['CCCD' => $r['SoCMND']],
                    [
                        'Hovaten' => $r['HoVaTen'],
                        'MaDoiTuong' => $maHocSinh,
                        'CCCD' => $r['SoCMND'],
                        'Ngaysinh' => $this->parseDateFlexible($r['NgaySinh']),
                        'Gioitinh' => $gtOid,
                        'DonViID_Hoc' => $donViOid,
                        'Noisinh' => $r['NoiSinh'] ?? null,
                        'TrangThai' => true,
                        'NgayThaoTac' => now(),
                        'NgayCapNhat' => now(),
                    ]
                );

                // Build the two grade dictionaries
                $diemMT = [];
                foreach ($r['DiemMonThi'] ?? [] as $slug => $score) {
                    if ($score === null || !isset($slugMap[$slug]))
                        continue;
                    $monThiId = $this->findMonThiId($slugMap[$slug], $quyetDinhId);
                    $diemMT[$monThiId] = (float) $score;
                }

                $diemCC = [];
                foreach ($r['DiemCuoiCap'] ?? [] as $slug => $score) {
                    if ($score === null || !isset($slugMap[$slug]))
                        continue;
                    $monThiId = $this->findMonThiId($slugMap[$slug], $quyetDinhId);
                    $diemCC[$monThiId] = (float) $score;
                }
                if (
                    BangDiem_ChiTiet::where('HocSinhID', (string) $doi->_id)
                        ->where('BangDiemId', $quyetDinhOid)
                        ->exists()
                ) {
                    $out[$origIndex] = ['Err' => true, 'Msg' => "SBD '{$sbd}' đã tồn tại"];
                    continue;
                }
                // Prevent duplicate
                $exists = BangDiem_ChiTiet::where('HocSinhID', (string) $doi->_id)
                    ->where('BangDiemId', $quyetDinhOid)
                    ->exists();
                if ($exists) {
                    $out[$origIndex] = ['Err' => true, 'Msg' => 'Dữ liệu đã tồn tại'];
                    continue;
                }

                // Insert into BangDiem_ChiTiet
                BangDiem_ChiTiet::create([
                    'BangDiemId' => $quyetDinhOid,
                    'HocSinhID' => (string) $doi->_id,
                    'SBD' => $sbd,
                    'PhongThi' => $r['PhongThi'] ?? '',
                    'DiaDiemThi' => $r['DiaDiemThi'] ?? '',
                    'DiemMonThi' => $diemMT,
                    'DiemCuoiCap' => $diemCC,
                    'DiemUT' => (float) ($r['DiemUT'] ?? 0),
                    'GhiChu' => $r['GhiChu'] ?? '',
                    'KyThiID' => $kyThiId,
                ]);
                $studentCreated = $doi->wasRecentlyCreated;
                // we know we just did a create(), so:
                $detailCreated = true;

                if ($studentCreated && $detailCreated) {
                    $msg = 'Thêm mới học sinh và bảng điểm';
                } elseif ($studentCreated) {
                    $msg = 'Thêm mới học sinh';
                } else {
                    $msg = 'Cập nhật bảng điểm cho học sinh';
                }

                $out[$origIndex] = [
                    'Err' => false,
                    'Msg' => $msg,
                ];
            }

            // 3) Return a single envelope
            return response()->json([
                'Err' => false,
                'Result' => $out,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi khi import: ' . $e->getMessage(),
            ], 500);
        } finally {
            // 1) Lookup the “Thêm” action
            try {
                $userID = (string) $this->currentUser->id();
                $donViID = (string) $this->currentUser->donViId();
                $thName = 'Thêm';
                $action = ThaoTac::getByThaoTac($thName, $userID)
                    ->firstOrFail();

                // 2) Build nội dung per your spec
                $noiDung = 'Thêm mới bảng điểm học sinh';
                $noiDungChiTiet = 'Thêm mới đồng loạt thông tin bảng điểm học sinh từ chức năng nhận excel';
                $thamChieuID = $quyetDinhId;
                $bangThamChieu = 'bang_diems';    // or use the actual collection name
                $duongDan = url()->current();

                $maThaoTac = $action['MaThaoTac'];
                $thaoTacLabel = $action['ThaoTac'];
                $mauSac = $action['MauSac'];
                $nguoiThaoTac = $action['NguoiThaoTac'];
                $chucVu = $action['ChucVu'];

                // 3) Fire the log
                NhatKyThaoTac::luuNhatKyThaoTac(
                    $maThaoTac,
                    $thaoTacLabel,
                    $mauSac,
                    $noiDung,
                    $noiDungChiTiet,
                    $thamChieuID,
                    $bangThamChieu,
                    $duongDan,
                    $nguoiThaoTac,
                    $chucVu,
                    $userID,
                    $donViID
                );
            } catch (\Exception $logEx) {
                Log::error('Ghi NhatKyThaoTac (importExcel) thất bại: ' . $logEx->getMessage());
            }
        }
    }



    public function loadSheetNames(Request $request)
    {
        $path = $request->input('path');
        if (!$path) {
            return response()->json(['Err' => true, 'Msg' => 'Missing file path'], 422);
        }

        // 1) extract just the path part, e.g. "//storage/uploads/…"
        $urlPath = parse_url($path, PHP_URL_PATH);

        // 2) collapse multiple leading slashes: "/storage/uploads/…"
        $urlPath = preg_replace('#^/+/#', '/', $urlPath);

        // 3) grab everything after "/storage/" → "uploads/…"
        $relative = Str::after($urlPath, '/storage/');

        // 4) now check on the public disk
        if (!Storage::disk('public')->exists($relative)) {
            return response()->json(['Err' => true, 'Msg' => 'File not found on disk'], 404);
        }

        $fullPath = Storage::disk('public')->path($relative);

        try {
            // create the best reader for this file
            $reader = IOFactory::createReaderForFile($fullPath);
            // only list sheet names, no data
            $sheetNames = $reader->listWorksheetNames($fullPath);
            $Result = array_map(fn($name) => ['TenSheet' => $name], $sheetNames);

            $payload = [
                'CanhBao' => false,
                'Xem' => false,
                'Them' => false,
                'Sua' => false,
                'Xoa' => false,
                'InAn' => false,
                'Nap' => false,
                'Quyen1' => false,
                'Quyen2' => false,
                'Quyen3' => false,
                'Err' => false,
                'ErrCode' => '',
                'ErrCatch' => '',
                'Result' => $Result,
                'Msg' => '',
                'Logs' => '',
                'Redirect' => false,
                'RedirectUrl' => '',
            ];

            // encode as JSON string
            $json = json_encode($payload);

            // return it as a plain-text response
            return response($json, 200)
                ->header('Content-Type', 'text/html');
        } catch (\Throwable $e) {
            // return the real exception message so we can see why it fails
            return response()->json([
                'Err' => true,
                'Msg' => 'Error reading file: ' . $e->getMessage()
            ], 500);
        }
    }
    public function downloadTemplate(Request $request)
    {

        $bangdiemId = $request->input('bangDiemId');
        if (!$bangdiemId || !$this->isValidObjectId($bangdiemId)) {
            return Excel::download(
                new ChiTietBangDiemExport([]),
                'MauExcel_BangDiem.xlsx'
            );
        }

        $bangDiem = BangDiem::with(['kyThi'])->find($bangdiemId);
        if (!$bangDiem) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không tìm thấy bảng điểm với ID đã cho',
            ], 404);
        }

        $kyThi = $bangDiem->kyThi;
        $monThi = $kyThi ? $kyThi->MonThi : [];



        $monThiArray = collect($monThi)->map(function (array $item) {
            $mh = MonHoc::find($item['MonThiID']);  // or MonHoc::find(new ObjectId(...))
            return [
                'id' => $item['MonThiID'],
                'maMonHoc' => $mh->maMonHoc ?? null,
                'tenMonHoc' => $mh->tenMonHoc ?? null,
                'heSo' => $item['DiemHeSo'] ?? null,
                'diemLiet' => $item['DiemLiet'] ?? null,
            ];
        })->toArray();


        return Excel::download(
            new ChiTietBangDiemExport($monThiArray),
            'MauExcel_BangDiem.xlsx'
        );
    }
    #endregion
    #endregion
}
use Maatwebsite\Excel\Concerns\{FromCollection, WithHeadings, ShouldAutoSize};
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

class ChiTietBangDiemExport implements FromCollection, WithHeadings, ShouldAutoSize, WithStyles, WithEvents
{
    protected array $monThi;

    public function __construct(array $monThi)
    {
        $this->monThi = $monThi;
    }

    /** No data rows, just headings */
    public function collection()
    {
        return collect();
    }

    /** Build a flat list of headings:
     *   [ staticBefore..., *tenMonHoc(), *tenMonHoc(), staticAfter... ]
     *   the first block is DiemMonThi, second is DiemCuoiCap
     */
    public function headings(): array
    {

        $staticBefore = [
            'Số báo danh',        // was Mã học sinh
            'Họ và tên',
            'Số CMND/CCCD',
            'Ngày sinh',
            'Giới tính',
            'Nơi sinh',
            'Học sinh trường',
            'Phòng thi',          // new
            'Địa điểm thi',       // new
        ];
        $staticAfter = [
            'Điểm ưu tiên',
            'Ghi chú',
        ];

        $n = count($this->monThi);

        // Row 1: repeat the group titles
        $row1 = array_merge(
            $staticBefore,
            array_fill(0, $n, 'Điểm môn thi'),
            array_fill(0, $n, 'Điểm cuối cấp'),
            $staticAfter
        );

        // Row 2: blank under staticBefore, then each subject name, then blank under staticAfter
        $row2 = array_merge(
            array_fill(0, count($staticBefore), ''),
            array_column($this->monThi, 'tenMonHoc'),
            array_column($this->monThi, 'tenMonHoc'),
            array_fill(0, count($staticAfter), '')
        );

        return [
            $row1,
            $row2,
        ];
    }

    /** Style the header row across however many columns we now have */
    public function styles(Worksheet $sheet): array
    {
        $rows = $this->headings();
        $row1 = $rows[0];                            // first heading row

        // $colCount1 = count($rows[0]);  // total columns in row 1
        // $colCount2 = count($rows[1]);  // same for row 2
        $colCount = count($row1);
        $lastCol = $this->numToColumn($colCount);
        $lastCol = $this->numToColumn($colCount);

        $styles = [
            // Group headers (row 1)
            "A1:{$lastCol}1" => [
                'font' => [
                    'name' => 'Times New Roman',
                    'bold' => true,
                    'size' => 12,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'D9E1F2'],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                    'wrapText' => true,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000'],
                    ],
                ],
            ],
            // Sub‑headers (row 2)
            "A2:{$lastCol}2" => [
                'font' => [
                    'name' => 'Times New Roman',
                    'bold' => true,
                    'size' => 11,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F2F2F2'],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                    'wrapText' => true,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000'],
                    ],
                ],
            ],
        ];

        // your list of required headers
        $required = [
            'Họ và tên',
            'Số CMND/CCCD',
            'Ngày sinh',
            'Học sinh trường',
            'Điểm môn thi',
            'Điểm cuối cấp',
        ];

        // loop over row1 and add a red‑font style for any required heading
        foreach ($row1 as $i => $heading) {
            if (in_array($heading, $required, true)) {
                $col = $this->numToColumn($i + 1);
                $styles["{$col}1"] = [
                    'font' => [
                        'color' => ['rgb' => 'FF0000'],
                    ],
                ];
            }
        }

        return $styles;
    }

    /** Convert 1-based column index to letter (A, B, …, AA, AB, …) */
    protected function numToColumn(int $num): string
    {
        $letters = '';
        while ($num > 0) {
            $num--;
            $letters = chr(65 + ($num % 26)) . $letters;
            $num = intdiv($num, 26);
        }
        return $letters;
    }

    /** Add your fixed‐list and dynamic dropdowns */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();

                $staticBeforeCount = 9;
                $n = count($this->monThi);


                $staticAfterCount = count([
                    'Điểm ưu tiên',   // ← 2 columns now
                    'Ghi chú',
                ]);

                //
                // 1) Merge your “group” headers across row 1:
                //
                if ($n > 0) {
                    // DiemMonThi block
                    $firstMonThiCol = $this->numToColumn($staticBeforeCount + 1);
                    $lastMonThiCol = $this->numToColumn($staticBeforeCount + $n);
                    $sheet->mergeCells("{$firstMonThiCol}1:{$lastMonThiCol}1");

                    // DiemCuoiCap block
                    $firstCuoiCol = $this->numToColumn($staticBeforeCount + $n + 1);
                    $lastCuoiCol = $this->numToColumn($staticBeforeCount + $n + $n);
                    $sheet->mergeCells("{$firstCuoiCol}1:{$lastCuoiCol}1");

                    // merge each trailing static column vertically
                    $startTrailing = $staticBeforeCount + $n * 2 + 1;
                    for ($j = 0; $j < $staticAfterCount; $j++) {
                        $col = $this->numToColumn($startTrailing + $j);
                        $sheet->mergeCells("{$col}1:{$col}2");
                    }
                }

                // --- always merge the 9 leading columns (A–G) over rows 1–2 ---
                for ($i = 1; $i <= $staticBeforeCount; $i++) {
                    $col = $this->numToColumn($i);
                    $sheet->mergeCells("{$col}1:{$col}2");
                }

                // a) the 7 leading columns (A–G)
                for ($i = 1; $i <= $staticBeforeCount; $i++) {
                    $col = $this->numToColumn($i);
                    $sheet->mergeCells("{$col}1:{$col}2");
                }

                // b) the 5 trailing columns
                $startTrailing = $staticBeforeCount + $n * 2 + 1;
                for ($j = 0; $j < $staticAfterCount; $j++) {
                    $col = $this->numToColumn($startTrailing + $j);
                    $sheet->mergeCells("{$col}1:{$col}2");
                }

                // Example: set up “Giới tính” in column E
                $this->applyListDropdown_value($sheet, 'E', ['Nam', 'Nữ', 'Khác']);

                try {
                    $truongOptions = DonVi::where('TrangThai', true)
                        ->get(['MaDonVi', 'TenDonVi'])
                        ->map(function ($d) {
                            return $d->MaDonVi . ' – ' . $d->TenDonVi;
                        })
                        ->toArray();
                } catch (\Throwable $e) {
                    $truongOptions = [];
                }
                // in your AfterSheet callback, *before* calling applyListDropdown...
                $parent = $event->sheet->getDelegate()->getParent();

                // 1) Create or grab a hidden sheet for lists
                $listSheet = $parent->getSheetByName('Lists')
                    ?? $parent->createSheet()->setTitle('Lists');
                $listSheet->setSheetState(Worksheet::SHEETSTATE_VISIBLE);

                // 2) Dump your DonVi names into column A
                $columnData = array_map(fn($v) => [$v], $truongOptions);

                $listSheet->fromArray(
                    $columnData,
                    null,
                    'A1'
                );

                // 3) Define a named range covering them
                $count = count($truongOptions);
                $range = "'Lists'!\$A\$1:\$A\${$count}";
                $parent->addNamedRange(
                    new NamedRange(
                        'DonViList',
                        $listSheet,
                        $range,
                        false,
                        null
                    )
                );

                $this->applyListDropdown_range($sheet, 'G', 'DonViList');

                // helper defined below
            },
        ];
    }

    /**
     * Áp dụng xác thực dữ liệu kiểu danh sách (dropdown) cho một cột trên worksheet với các giá trị cụ thể.
     *
     * @param Worksheet $sheet Đối tượng worksheet cần áp dụng dropdown.
     * @param string $col Tên cột (ví dụ: 'A', 'B', ...) để áp dụng dropdown.
     * @param array $options Mảng các giá trị sẽ hiển thị trong dropdown.
     *
     * @return void
     */
    protected function applyListDropdown_value(Worksheet $sheet, string $col, array $options): void
    {
        for ($row = 2; $row <= 1000; $row++) {
            $cell = $sheet->getCell("{$col}{$row}");
            $dv = $cell->getDataValidation();
            $dv->setType(DataValidation::TYPE_LIST);
            $dv->setErrorStyle(DataValidation::STYLE_STOP);
            $dv->setAllowBlank(true);
            $dv->setShowDropDown(true);
            $dv->setFormula1('"' . implode(',', $options) . '"');
            $cell->setDataValidation($dv);
        }
    }
    /**
     * Áp dụng xác thực dữ liệu kiểu danh sách (dropdown) cho một cột trên worksheet sử dụng một vùng dữ liệu đã đặt tên (named range).
     *
     * @param Worksheet $sheet Đối tượng worksheet cần áp dụng dropdown.
     * @param string $col Tên cột (ví dụ: 'A', 'B', ...) để áp dụng dropdown.
     * @param string $rangeName Tên của vùng dữ liệu đã đặt tên (named range) dùng làm nguồn cho dropdown.
     *
     * @return void
     */
    protected function applyListDropdown_range(Worksheet $sheet, string $col, string $rangeName, $allowBlank = false): void
    {
        for ($row = 2; $row <= 1000; $row++) {
            $dv = $sheet
                ->getCell("{$col}{$row}")
                ->getDataValidation();
            $dv->setType(DataValidation::TYPE_LIST);
            $dv->setFormula1($rangeName);
            $dv->setAllowBlank($allowBlank);
            $dv->setShowDropDown(true);
            $dv->setErrorStyle(DataValidation::STYLE_STOP);
        }
    }
}
