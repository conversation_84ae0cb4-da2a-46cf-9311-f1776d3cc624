<?php

namespace App\Http\Controllers\QuanLy;

use App\Models\DanhMuc\CapHoc;
use App\Models\DanhMuc\ChucVu;
use App\Models\DanhMuc\DonViTinh;
use App\Models\DanhMuc\HinhThucNhanPhoi;
use App\Models\DanhMuc\LoaiPhoiVanBangChungChi;
use App\Models\DanhMuc\NhanVien;
use App\Models\QuanLy\DoiTuong;
use App\Models\QuanLy\YeuCauCSNDVBCC;
use Str;
use Storage;
use Carbon\Carbon;
use App\Services\ThongBao;
use Illuminate\Http\Request;
use App\Models\DanhMuc\DonVi;
use App\Services\DungChungDb;
use App\Services\DungChungNoDb;
use App\Models\DanhMuc\TrangThai;
use App\Http\Controllers\Controller;
use App\Services\CurrentUserService;
use MongoDB\BSON\ObjectId; // from mongodb/mongodb
use App\Models\quanly\ThaoTac;
use App\Models\QuanLy\NhatKyThaoTac;
use Log;

class YeuCauCSNDVBCCController extends Controller
{
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;
    protected CurrentUserService $currentUser;

    public function __construct(DungChungDb $dungChungDb, CurrentUserService $currentUser, DungChungNoDb $logService)
    {
        $this->dungChungDb = $dungChungDb;
        $this->logService = $logService;
        $this->currentUser = $currentUser;
    }


    public function index()
    {
        return view('quanly.YeuCauCSNDVBCC.index');
    }
    function isMongoId($id)
    {
        return is_string($id) && preg_match('/^[a-f\d]{24}$/i', $id);
    }

    function safeObjectId($id)
    {
        return $this->isMongoId($id) ? new ObjectId($id) : null;
    }

    /**
     *
     */
    public function luuThongTin(Request $request)
    {
        $msg = '';
        $YeuCauCSNDVBCC = null;
        $didLogAction = false;
        $thaoTac = '';
        try {

            $NgayLap = $request->filled('NgayLap')
                ? Carbon::createFromFormat('d/m/Y', $request->input('NgayLap'))
                : null;
            $NguoiLapID = $this->safeObjectId($request->input('NguoiLapID'));
            $ChucVuNguoiLapID = $this->safeObjectId($request->input('ChucVuNguoiLapID'));
            $HocSinhID = $this->safeObjectId($request->input('HocSinhID'));
            $LoaiVBCCID = $this->safeObjectId($request->input('LoaiVBCCID'));
            $DonViNhanID = $this->safeObjectId($request->input('DonViNhanID'));
            $NgayCap = $request->filled('NgayCap')
                ? Carbon::createFromFormat('d/m/Y', $request->input('NgayCap'))
                : null;
            $SoHieu = $request->input('SoHieu');
            $SoVaoSo = $request->input('SoVaoSo');
            $LyDoDieuChinh = $request->input('LyDoDieuChinh');
            $GhiChu = $request->input('GhiChu');
            $DinhKem = $request->input('DinhKem') ?? '';
            $donViId = $this->safeObjectId($this->currentUser->donViId());
            $userId = $this->safeObjectId(auth()->id());
            $now = now();
            $id = $this->safeObjectId($request->input('YeuCauCSNDVBCCID'));
            if ($request->isMethod('POST')) {

                $didLogAction = true;
                $paths = $this->saveFiles($DinhKem);
                $thaoTac = 'Tạo mới đơn yêu cầu chỉnh sửa văn bằng, chứng chỉ';

                $YeuCauCSNDVBCC = YeuCauCSNDVBCC::create([
                    'NgayLap' => $NgayLap,
                    'NguoiLapID' => $NguoiLapID,
                    'ChucVuNguoiLapID' => $ChucVuNguoiLapID,
                    'HocSinhID' => $HocSinhID,
                    'LoaiVBCCID' => $LoaiVBCCID,
                    'DonViNhanID' => $DonViNhanID,
                    'NgayCap' => $NgayCap,
                    'SoHieu' => $SoHieu,
                    'SoVaoSo' => $SoVaoSo,
                    'LyDoDieuChinh' => $LyDoDieuChinh,
                    'GhiChu' => $GhiChu,
                    'DinhKem' => $paths,
                    'UserID_ThaoTac' => $userId,
                    'UserID_CapNhat' => $userId,
                    'DonViID_CapNhat' => $donViId,
                    'DonViID_ThaoTac' => $donViId,
                    'TrangThaiXuLyID' => '41',
                    'NguoiXuLyID' => null,
                    'NgayXuLy' => null,
                    'NoiDungXuLy' => null,
                    'ChucVuNguoiXuLyID' > null,
                    'DonViTiepNhanXuLyID' => null,
                    'NgayThaoTac' => $now,
                    'NgayCapNhat' => $now,
                    'DonViGuiID' => $donViId,
                ]);

                return response()->json([
                    'Err' => false,
                    'Msg' => 'Thêm mới đơn yêu cầu chỉnh sửa văn bằng, chứng chỉ thành công!'
                ]);
            }

            if ($request->isMethod('PUT') || $request->isMethod('patch')) {
                $YeuCauCSNDVBCC = YeuCauCSNDVBCC::find($id);
                $thaoTac = 'Cập nhật đơn yêu cầu chỉnh sửa văn bằng, chứng chỉ ';
                if (!$YeuCauCSNDVBCC) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy đơn yêu cầu cần cập nhật!'
                    ]);
                }

                if (!empty($YeuCauCSNDVBCC->DinhKem)) {
                    $paths = explode('|', $YeuCauCSNDVBCC->DinhKem);
                    foreach ($paths as $path) {
                        $relativePath = str_replace('/storage/', 'public/', $path);
                        if (Storage::exists($relativePath)) {
                            Storage::delete($relativePath);
                        }
                    }
                }

                $paths = $this->saveFiles($DinhKem);

                $YeuCauCSNDVBCC->update(attributes: [
                    'NgayLap' => $NgayLap,
                    'NguoiLapID' => $NguoiLapID,
                    'ChucVuNguoiLapID' => $ChucVuNguoiLapID,
                    'HocSinhID' => $HocSinhID,
                    'LoaiVBCCID' => $LoaiVBCCID,
                    'DonViNhanID' => $DonViNhanID,
                    'NgayCap' => $NgayCap,
                    'SoHieu' => $SoHieu,
                    'SoVaoSo' => $SoVaoSo,
                    'LyDoDieuChinh' => $LyDoDieuChinh,
                    'GhiChu' => $GhiChu,
                    'DinhKem' => $paths,
                    'UserID_ThaoTac' => $userId,
                    'UserID_CapNhat' => $userId,
                    'DonViID_CapNhat' => $donViId,
                    'DonViID_ThaoTac' => $donViId,
                    'TrangThaiXuLyID' => '41',
                    'NguoiXuLyID' => null,
                    'NgayXuLy' => null,
                    'NoiDungXuLy' => null,
                    'ChucVuNguoiXuLyID' > null,
                    'DonViTiepNhanXuLyID' => null,
                    'NgayThaoTac' => $now,
                    'NgayCapNhat' => $now,
                    'DonViGuiID' => $donViId,
                ]);

                $changed = $YeuCauCSNDVBCC->getChanges();
                if (count($changed)) {
                    $didLogAction = true;
                }

                return response()->json([
                    'Err' => false,
                    'Msg' => 'Cập nhật đơn yêu cầu thành công!'
                ]);
            }

            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Phương thức HTTP không hợp lệ!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        } finally {
            Log::info('Ghi NhatKyThaoTac: ' . ($YeuCauCSNDVBCC ? $YeuCauCSNDVBCC->_id : 'null') . ', didLogAction: ' . ($didLogAction ? 'true' : 'false'));

            if ($didLogAction) {
                try {
                    $NguoiXuLyID = $this->safeObjectId($request->input('NguoiLapID'));
                    $ChucVuNguoiXuLyID = $this->safeObjectId($request->input('ChucVuNguoiLapID'));
                    $TenNhanVien = NhanVien::where('_id', $NguoiXuLyID)->value('tenNhanVien') ?? '';
                    $TenChucVu = ChucVu::where('_id', $ChucVuNguoiXuLyID)->value('tenChucVu') ?? '';
                    $method = $request->isMethod('post') ? 'Thêm' : 'Sửa';
                    $donViId = $this->safeObjectId($this->currentUser->donViId());
                    $NgayXuLy = $request->filled('NgayLap') ? Carbon::createFromFormat('d/m/Y', $request->input('NgayLap')) : null;
                    $SoPhieu = $request->input('SoPhieu') ?? '';
                    if (!$YeuCauCSNDVBCC && isset($id)) {
                        $YeuCauCSNDVBCC = YeuCauCSNDVBCC::find($id);
                    }

                    if (!$YeuCauCSNDVBCC) {
                        Log::warning('Không tìm thấy đơn để ghi nhật ký thao tác!');
                        return;
                    }
                    $noiDungChiTiet = sprintf(
                        '%s đơn đều chỉnh Văn bằng, chứng chỉ',
                        $thaoTac,
                        $SoPhieu,
                        $NgayXuLy,
                    );

                    $duLieuThaoTac = ThaoTac::getFirstByThaoTac($method, (string) $this->currentUser->id());
                    NhatKyThaoTac::luuNhatKyThaoTac(
                        $duLieuThaoTac['MaThaoTac'],
                        $duLieuThaoTac['ThaoTac'],
                        $duLieuThaoTac['MauSac'],
                        $thaoTac,
                        $noiDungChiTiet,
                        (string) $YeuCauCSNDVBCC->_id,
                        $YeuCauCSNDVBCC->getTable(),
                        url()->current(),
                        $TenNhanVien,
                        $TenChucVu,
                        (string) $this->currentUser->id(),
                        (string) $this->currentUser->donViId()
                    );
                } catch (\Throwable $t) {
                    \Log::error('Ghi NhatKyThaoTac thất bại: ' . $t->getMessage());
                    \Log::error($t->getTraceAsString());
                }
            }
        }
    }
    protected function saveFiles(string $paths): string
    {
        $donViid = $this->currentUser->donViId();
        $username = $this->currentUser->username();
        $disk = Storage::disk('public');

        $permanentBase = "uploads/{$donViid}/{$username}/files/YeuCauCSNDVBCC";
        $results = [];

        foreach (explode('|', $paths) as $p) {
            $p = trim($p);
            if (!$p) {
                continue;
            }

            $urlPath = parse_url($p, PHP_URL_PATH);

            if (Str::startsWith($urlPath, "/storage/{$permanentBase}/")) {
                $results[] = $urlPath;
                continue;
            }

            $diskKey = preg_replace('#^/storage/#', '', $urlPath);

            $name = pathinfo($diskKey, PATHINFO_FILENAME);
            $ext = pathinfo($diskKey, PATHINFO_EXTENSION);
            $slug = Str::slug($name);
            $newName = "{$slug}-" . time() . ".{$ext}";
            $newKey = "{$permanentBase}/{$newName}";

            $disk->move($diskKey, $newKey);

            $results[] = '/storage/' . $newKey;
        }

        return implode('|', $results);
    }
    public function getListTrangThaiCapBang()
    {
        try {
            $result = TrangThai::whereIn('MaTrangThai', ['40', '41', '42', '32'])
                ->where('TrangThai', true)
                ->orderBy('MaTrangThai', 'asc')
                ->get();

            $data = $result->map(function ($item) {
                return [
                    'id' => (string) $item->_id,
                    'code' => $item->MaTrangThai,
                    'name' => $item->TenTrangThai ?? "Chưa có tên",
                    'color' => $item->MauSac ?? "#000000",
                    'parent_id' => $item->TrangThaiID_Cha ? (string) $item->TrangThaiID_Cha : null,
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    public function getAll(Request $request)
    {
        $donViId = $this->safeObjectId($this->currentUser->donViId());
        try {
            $query = YeuCauCSNDVBCC::query();

            $query->where('DonViGuiID', $donViId);

            if ($request->filled('LoaiPhoiID_Loc')) {
                $query->where('LoaiPhoiID', new ObjectId($request->input('LoaiPhoiID_Loc')));
            }

            if ($request->filled('TrangThaiXuLyID_Loc')) {
                $query->where('TrangThaiXuLyID', operator: $request->input('TrangThaiXuLyID_Loc'));
            }

            if ($request->filled('TuNgay_Loc') || $request->filled('DenNgay_Loc')) {
                $tuNgay = $request->filled('TuNgay_Loc')
                    ? Carbon::createFromFormat('d/m/Y', $request->input('TuNgay_Loc'))->startOfDay()
                    : null;

                $denNgay = $request->filled('DenNgay_Loc')
                    ? Carbon::createFromFormat('d/m/Y', $request->input('DenNgay_Loc'))->endOfDay()
                    : null;

                if ($tuNgay && $denNgay) {
                    $query->whereBetween('NgayLap', [$tuNgay, $denNgay]);
                } elseif ($tuNgay) {
                    $query->where('NgayLap', '>=', $tuNgay);
                } elseif ($denNgay) {
                    $query->where('NgayLap', '<=', $denNgay);
                }
            }

            if ($request->filled('SearchKey')) {
                $keyword = $request->input('SearchKey');
                $regex = new \MongoDB\BSON\Regex($keyword, 'i');

                $nguoiIds = NhanVien::where('TenNhanVien', 'like', $regex)->pluck('_id')->toArray();
                $donViIds = DonVi::where('TenDonVi', 'like', $regex)->pluck('_id')->toArray();

                $query->where(function ($q) use ($regex, $nguoiIds, $donViIds) {
                    $q->orWhere('LyDoXinCap', 'like', $regex)
                        ->orWhere('GhiChu', 'like', $regex)
                        ->orWhere('TenLoaiPhoi', 'like', $regex)
                        ->orWhere('TenTrangThaiXuLy', 'like', $regex);

                    if (!empty($nguoiIds)) {
                        $q->orWhereIn('NguoiLapID', $nguoiIds)
                            ->orWhereIn('NguoiXuLyID', $nguoiIds);
                    }

                    if (!empty($donViIds)) {
                        $q->orWhereIn('DonViGuiID', $donViIds)
                            ->orWhereIn('DonViNhanID', $donViIds);
                    }
                });
            }

            $sortField = $request->input('CbSapXep', default: 'YeuCauCSNDVBCCID');

            $result = $query->get();

            if ($sortField === 'NgayLap') {
                $result = $result->sortBy('NgayLap')->values();
            } elseif ($sortField === 'TrangThaiXuLyID') {
                $order = ['40' => 0, '41' => 1, '42' => 2];
                $result = $result->sortBy(function ($item) use ($order) {
                    return $order[$item->TrangThaiXuLyID] ?? 999;
                })->values();
            } else {
                $result = $result->sortBy($sortField)->values();
            }

            $result = $this->formatDonYeuCaus($result);

            return response()->json([
                'Err' => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }

    public function formatDonYeuCaus($collection)
    {
        return $collection->map(function ($item) {

            $item->txtNgayLap = optional($item->NgayLap)->format('d/m/Y') ?? '';
            $item->TenNguoiLap = optional(NhanVien::find($item->NguoiLapID))->tenNhanVien ?? '';
            $item->TenChucVuNguoiLap = optional(ChucVu::find($item->ChucVuNguoiLapID))->tenChucVu ?? '';

            $item->TenHocSinh = optional(DoiTuong::find($item->HocSinhID))->Hovaten ?? '';

            $item->TenLoaiPhoi = optional(LoaiPhoiVanBangChungChi::find($item->LoaiVBCCID))->TenLoaiPhoiVanBangChungChi ?? '';

            $donViNhan = DonVi::find($item->DonViNhanID);
            $item->TenDonViNhan = optional($donViNhan)->TenDonVi ?? '';
            $item->MaDonViNhan = optional($donViNhan)->MaDonVi ?? '';

            $donViGui = DonVi::find($item->DonViGuiID);
            $item->TenDonViGui = optional($donViGui)->TenDonVi ?? '';
            $item->MaDonViGui = optional($donViGui)->MaDonVi ?? '';

            $item->TenTepDinhKem = basename($item->DinhKem ?? '');
            $item->LinkDinhKem = $item->DinhKem ? asset($item->DinhKem) : null;

            $item->TenChucVuNguoiXuLy = optional(ChucVu::find($item->ChucVuNguoiXuLyID))->tenChucVu ?? '';
            $item->txtNgayXuLy = optional($item->NgayXuLy)->format('d/m/Y') ?? '';
            $item->TenNguoiXuLy = optional(NhanVien::find($item->NguoiXuLyID))->tenNhanVien ?? '';

            $item->TenDonViTiepNhanXuLy = optional(DonVi::find($item->DonViTiepNhanXuLyID))->TenDonVi ?? '';
            $item->TenNguoiTiepNhanXuLy = optional(NhanVien::find($item->NguoiTiepNhanXuLyID))->tenNhanVien ?? '';
            $item->txtNgayTiepNhanXuLy = optional($item->NgayTiepNhanXuLy)->format('d/m/Y') ?? '';
            $item->TenChucVuNguoiTiepNhanXuLy = optional(ChucVu::find($item->ChucVuNguoiTiepNhanXuLyID))->tenChucVu ?? '';

            $trangThaiXuLy = TrangThai::where("MaTrangThai", $item->TrangThaiXuLyID)->first();
            $item->TenTrangThaiXuLy = optional($trangThaiXuLy)->TenTrangThai ?? 'Chưa xác định';
            $item->MauSacTrangThaiXuLy = optional($trangThaiXuLy)->MauSac ?? '#ffffff';

            $item->TenHinhThucNhanPhoi = optional(HinhThucNhanPhoi::find($item->HinhThucNhanPhoiID))->tenHinhThucNhanPhoi ?? '';

            if (!empty($item->PhoiVBCC) && is_array($item->PhoiVBCC)) {
                $item->PhoiVBCC = array_map(function ($phoi) {
                    $loaiPhoi = LoaiPhoiVanBangChungChi::find($phoi['LoaiPhoiVBCCID'] ?? '');
                    $donViTinh = DonViTinh::find($phoi['DonViTinhID'] ?? '');

                    return array_merge($phoi, [
                        'TenLoaiPhoiVanBangChungChi' => optional($loaiPhoi)->TenLoaiPhoiVanBangChungChi ?? '',
                        'TenDonViTinh' => optional($donViTinh)->TenDonViTinh ?? '',
                    ]);
                }, $item->PhoiVBCC);
            }
            return $item;

        });
    }

    public function loadDuLieuSua(Request $request)
    {
        $id = $request->input('id');
        $YeuCauCSNDVBCCID = YeuCauCSNDVBCC::find($id);
        if (!$YeuCauCSNDVBCCID) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Không tìm thấy đơn yêu cầu!'
            ]);
        }
        $YeuCauCSNDVBCCID = $this->formatDonYeuCaus(collect([$YeuCauCSNDVBCCID]))->first();
        return response()->json([
            'Err' => false,
            'Result' => $YeuCauCSNDVBCCID
        ]);
    }

    public function GuiDon(Request $request)
    {
        $YeuCauCSNDVBCC = null;
        $YeuCauCSNDVBCCID = $this->safeObjectId($request->input('YeuCauCSNDVBCCID'));
        $NguoiXuLyID = $this->safeObjectId($request->input('NguoiXuLyID'));
        $ChucVuNguoiXuLyID = $this->safeObjectId($request->input('ChucVuNguoiXuLyID'));
        $DonViTiepNhanXuLyID = $this->safeObjectId($request->input('DonViTiepNhanXuLyID'));
        $TrangThaiXuLyID = $request->input('TrangThaiXuLyID');
        $NgayXuLy = $request->filled('NgayXuLy') ? Carbon::createFromFormat('d/m/Y', $request->input('NgayXuLy')) : null;
        $NoiDungXuLy = $request->input('NoiDungXuLy');

        try {
            if ($request->isMethod('post')) {
                $YeuCauCSNDVBCC = YeuCauCSNDVBCC::find($YeuCauCSNDVBCCID);
                if (!$YeuCauCSNDVBCC) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                    ]);
                }
                $YeuCauCSNDVBCC->update([
                    'NguoiXuLyID' => $NguoiXuLyID
                    ,
                    'NgayXuLy' => $NgayXuLy
                    ,
                    'NoiDungXuLy' => $NoiDungXuLy
                    ,
                    'ChucVuNguoiXuLyID' => $ChucVuNguoiXuLyID
                    ,
                    'TrangThaiXuLyID' => $TrangThaiXuLyID
                    ,
                    'DonViTiepNhanXuLyID' => $DonViTiepNhanXuLyID
                ]);

                return response()->json([
                    'Err' => false,
                    'Msg' => 'Cập nhật dữ liệu thành công!'
                ]);
            }

            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Phương thức HTTP không hợp lệ!'
            ]);

        } catch (\Exception $e) {
            $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        } finally {
            $thaoTac = 'Gửi đơn đơn yêu cầu chỉnh sửa văn bằng, chứng chỉ';
            try {

                $TenNhanVien = NhanVien::where('_id', $NguoiXuLyID)->value('tenNhanVien') ?? '';
                $TenChucVu = ChucVu::where('_id', $ChucVuNguoiXuLyID)->value('tenChucVu') ?? '';
                $NgayXuLy = $request->filled('NgayXuLy') ? Carbon::createFromFormat('d/m/Y', $request->input('NgayXuLy')) : null;
                $method = $request->isMethod('post') ? 'Thêm' : 'Sửa';
                if (!$YeuCauCSNDVBCCID && isset($id)) {
                    $YeuCauCSNDVBCCID = YeuCauCSNDVBCC::find($id);
                }

                if (!$YeuCauCSNDVBCCID) {
                    Log::warning('Không tìm thấy đơn để ghi nhật ký thao tác!');
                    return;
                }
                $noiDungChiTiet = sprintf(
                    '%s đơn yêu cầu điều chỉnh (Ngày lập: %s)',
                    $thaoTac,
                    $NgayXuLy
                );

                $duLieuThaoTac = ThaoTac::getFirstByThaoTac($method, (string) $this->currentUser->id());
                NhatKyThaoTac::luuNhatKyThaoTac(
                    $duLieuThaoTac['MaThaoTac'],
                    $duLieuThaoTac['ThaoTac'],
                    $duLieuThaoTac['MauSac'],
                    $thaoTac,
                    'Đơn yêu cầu chỉnh sửa nội dung Văn bằng, chứng chỉ vào ngày ' . $NgayXuLy,
                    (string) $YeuCauCSNDVBCC->_id,
                    $YeuCauCSNDVBCC->getTable(),
                    url()->current(),
                    $TenNhanVien,
                    $TenChucVu,
                    (string) $this->currentUser->id(),
                    (string) $this->currentUser->donViId()
                );
            } catch (\Throwable $t) {
                \Log::error('Ghi NhatKyThaoTac thất bại: ' . $t->getMessage());
                \Log::error($t->getTraceAsString());
            }

        }
    }

    public function getThongKe(Request $request)
    {
        try {
            $donViId = $this->safeObjectId($this->currentUser->donViId());
            
            // Base query - same as getAll
            $query = YeuCauCSNDVBCC::query();
            $query->where('DonViGuiID', $donViId);

            // Apply same filters as getAll
            if ($request->filled('LoaiPhoiID_Loc')) {
                $query->where('LoaiPhoiID', new ObjectId($request->input('LoaiPhoiID_Loc')));
            }

            if ($request->filled('TrangThaiXuLyID_Loc')) {
                $query->where('TrangThaiXuLyID', $request->input('TrangThaiXuLyID_Loc'));
            }

            if ($request->filled('TuNgay_Loc') || $request->filled('DenNgay_Loc')) {
                $tuNgay = $request->filled('TuNgay_Loc')
                    ? Carbon::createFromFormat('d/m/Y', $request->input('TuNgay_Loc'))->startOfDay()
                    : null;

                $denNgay = $request->filled('DenNgay_Loc')
                    ? Carbon::createFromFormat('d/m/Y', $request->input('DenNgay_Loc'))->endOfDay()
                    : null;

                if ($tuNgay && $denNgay) {
                    $query->whereBetween('NgayLap', [$tuNgay, $denNgay]);
                } elseif ($tuNgay) {
                    $query->where('NgayLap', '>=', $tuNgay);
                } elseif ($denNgay) {
                    $query->where('NgayLap', '<=', $denNgay);
                }
            }

            if ($request->filled('SearchKey')) {
                $keyword = $request->input('SearchKey');
                $regex = new \MongoDB\BSON\Regex($keyword, 'i');

                $nguoiIds = NhanVien::where('TenNhanVien', 'like', $regex)->pluck('_id')->toArray();
                $donViIds = DonVi::where('TenDonVi', 'like', $regex)->pluck('_id')->toArray();

                $query->where(function ($q) use ($regex, $nguoiIds, $donViIds) {
                    $q->orWhere('LyDoXinCap', 'like', $regex)
                        ->orWhere('GhiChu', 'like', $regex)
                        ->orWhere('TenLoaiPhoi', 'like', $regex)
                        ->orWhere('TenTrangThaiXuLy', 'like', $regex);

                    if (!empty($nguoiIds)) {
                        $q->orWhereIn('NguoiLapID', $nguoiIds)
                            ->orWhereIn('NguoiXuLyID', $nguoiIds);
                    }

                    if (!empty($donViIds)) {
                        $q->orWhereIn('DonViGuiID', $donViIds)
                            ->orWhereIn('DonViNhanID', $donViIds);
                    }
                });
            }

            // Get statistics by status
            $trangThaiIDs = ['40', '41', '42', '32'];
            $counts = [];
            $TS = 0;

            foreach ($trangThaiIDs as $id) {
                $statusQuery = clone $query;
                $statusQuery->where('TrangThaiXuLyID', $id);
                $counts[$id] = $statusQuery->count();
                $TS += $counts[$id];
            }

            // Rest of the statistics logic remains the same...
            $trangThais = TrangThai::whereIn('MaTrangThai', $trangThaiIDs)->get()->keyBy('MaTrangThai');
            $resultTrangThai = [];

            foreach ($trangThaiIDs as $id) {
                $info = $trangThais[$id] ?? null;
                $resultTrangThai[] = [
                    'TrangThaiXuLyID' => $id,
                    'SoLuong' => $counts[$id],
                    'TenTrangThai' => $info->TenTrangThai ?? '[Không xác định]',
                    'MauSac' => $info->MauSac ?? '#cccccc',
                ];
            }

            return response()->json([
                'Err' => false,
                'Result' => [
                    'TS' => $TS,
                    'ThongKeTrangThai' => $resultTrangThai,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không thể lấy thống kê!',
                'debug' => $e->getMessage(),
            ]);
        }
    }


    public function xoa(Request $request)
    {
        $id = $request->input('ma');
        $model = YeuCauCSNDVBCC::whereKey($id)->firstOrFail();

        if (!empty($model->DinhKem)) {
            $paths = [];

            if (is_string($model->DinhKem)) {
                $paths = preg_split('/[|,]/', $model->DinhKem);
            }

            foreach ($paths as $path) {
                $cleanPath = str_replace('\\', '/', trim($path));

                $relativePath = str_replace('/storage/', '', $cleanPath);

                $fullStoragePath = 'public/' . $relativePath;
                if (Storage::disk('public')->exists($relativePath)) {
                    Storage::disk('public')->delete($relativePath);
                } else {
                    Log::warning("Không tìm thấy file để xoá: $fullStoragePath");
                }
            }
        }

        if (!$model) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Không tìm thấy đơn đề nghị xóa để xóa!'
            ]);
        }

        $model->delete();

        return response()->json([
            'Err' => false,
            'Msg' => 'Xóa đơn đề nghị thành công!'
        ]);
    }
}

