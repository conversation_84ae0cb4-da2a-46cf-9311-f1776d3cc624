<?php

namespace App\Models\QuanLy;
use App\Models\DanhMuc\CapHoc;
use App\Models\DanhMuc\CapToChuc;
use App\Models\DanhMuc\ChucVu;
use App\Models\DanhMuc\KyThi;
use App\Models\DanhMuc\NhanVien;
use Carbon\Carbon;
use DateTimeInterface;
use MongoDB\Laravel\Eloquent\Model;
use MongoDB\BSON\ObjectId;
use App\Trait\TruongDuLieuExportable as Exportable;

class BangDiem extends Model
{
    use Exportable;
    protected $connection = 'mongodb';
    protected $collection = 'bangdiem';

    protected $fillable = [
        'SoQuyetDinh',
        'HoiDongID',
        'NgayKy',
        'NguoiKy',
        'ChucVuID_NK',
        "TrichYeu",
        'CoQuanBanHanh',
        'CapHocID',
        'KyThiID',
        'KhoaThiID',
        'HinhThucID',
        'NamHoc',

        'FileDinhKem',
        'TrangThai',

        'NgayBanHanh',
        'NhanVienID',//Nv ban hanh
        'ChucVuQL_BH',
        'NoiDungBH',
        'NgayThuHoi',

        'NhanVienID_thuhoi',
        'ChucVuID_thuhoi',
        'NoiDung_thuhoi',

        'GhiChu',
        'DonViID',
        'UserID',

        'FileDinhKemOCR',

    ];
    protected $casts = [
        'TrangThai' => 'string',
        'NgayKy' => 'datetime:d/m/Y',
        'NgayBanHanh' => 'datetime:d/m/Y',
        'NgayThuHoi' => 'datetime:d/m/Y',
    ];

    protected $appends = ['ten_cap_hoc'];

    protected function formatDate($value)
    {
        if (!$value) {
            return null;
        }

        try {
            return Carbon::parse($value)->format('d/m/Y');
        } catch (\Exception $e) {
            return $value;
        }
    }

    public function getQuyetDinhIDAttribute()
    {
        return $this->_id;
    }

    public function setQuyetDinhIDAttribute($value)
    {
        $this->_id = $value;
    }

    /**
     * Override the default date serialization format.
     * Laravel will call this for every date/datetime when converting to JSON.
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return Carbon::parse($date)->format('d/m/Y');
    }

    public function hoiDong()
    {
        return $this->belongsTo(\App\Models\DanhMuc\HoiDong::class, 'HoiDongID', '_id');
    }

    public function kyThi()
    {
        return $this->belongsTo(\App\Models\DanhMuc\KyThi::class, 'KyThiID', '_id');
    }

    public function chucVu()
    {
        return $this->belongsTo(\App\Models\DanhMuc\ChucVu::class, 'ChucVuID_NK', '_id');
    }

    /**
     * Relation to NhanVien who ban hanh (issued)
     */
    public function nhanVienBanHanh()
    {
        return $this->belongsTo(NhanVien::class, 'NhanVienID', '_id');
    }

    /**
     * Relation to ChucVu of NhanVien ban hanh
     */
    public function chucVuBanHanh()
    {
        // via NhanVien relation, eager load chucVu
        // or directly if you want (assuming ChucVuID is stored in NhanVien)
        return $this->belongsTo(ChucVu::class, 'ChucVuQL_BH', '_id');

    }

    // Or simpler approach is to eager load NhanVien with chucVu relation.

    // Accessor for Tên nhân viên ban hành
    public function getTenNguoiBHAttribute()
    {
        $showThongTinBH = $this->NgayThuHoi == null;
        if ($showThongTinBH)
            return $this->nhanVienBanHanh ? $this->nhanVienBanHanh->tenNhanVien : "";
        else
            return "";
    }

    // Accessor for Chức vụ nhân viên ban hành
    public function getChucVuNguoiBHAttribute()
    {
        $showThongTinBH = $this->NgayThuHoi == null;
        if ($showThongTinBH)
            return $this->chucVuBanHanh ? $this->chucVuBanHanh->tenChucVu : "";
        else
            return "";

    }

    /**
     * Accessor: Tên cấp học
     *  - ưu tiên lấy từ quan hệ capHoc
     *  - nếu chưa có, fallback sang kyThi->CapToChucID, 
     *    gán vào CapHocID và trả về
     */
    public function getTenCapHocAttribute()
    {
        // 1) Nếu đã eager-loaded quan hệ capHoc thì trả ngay
        if ($this->capHoc) {
            return $this->capHoc->tenCapHoc;
        }

        // 2) Nếu chưa có, nhưng kyThi thì lấy CapToChucID của kyThi
        if ($this->kyThi && $this->kyThi->CapHocID) {
            $capId = $this->kyThi->CapHocID;

            // Gán tạm vào thuộc tính để quan hệ sau này dùng đến
            $this->attributes['CapHocID'] = $capId instanceof ObjectId
                ? $capId
                : new ObjectId($capId);

            // Lấy model CapHoc
            $capHoc = CapHoc::find(new ObjectId($capId));
            return $capHoc ? $capHoc->tenCapHoc : null;
        }

        // 3) Không có gì để trả về
        return null;
    }


    public function hinhThucDaoTao()
    {
        return $this->belongsTo(\App\Models\DanhMuc\HinhThucDaoTao::class, 'HinhThucID', '_id');
    }

    public function donVi()
    {
        return $this->belongsTo(\App\Models\DanhMuc\DonVi::class, 'DonViID', '_id');
    }
    public function capHoc()
    {
        return $this->belongsTo(\App\Models\DanhMuc\CapHoc::class, 'CapHocId', '_id');
    }


    // Helper method to format date or return null if invalid

    public function toArray()
    {
        $array = parent::toArray();



        return $array;
    }
    public function TrangThaiLabel()
    {
        return $this->belongsTo(\App\Models\DanhMuc\TrangThai::class, 'TrangThai', 'MaTrangThai');
    }


    protected static function getExportFieldMap(): array
    {
        return [
            'NamTotNghiep' => 'NamTotNghiep',            // or map to existing NamHoc if you prefer
            'KyThi' => 'kyThi.TenKyThi',
            'KhoaThi' => 'KhoaThiID',
            'HoiDong' => 'HoiDongID',
            'TrichYeu' => 'TrichYeu',
            'QuyetDinh' => 'SoQuyetDinh',
            'DiemThi' => 'DiemThi',
            'NgayKy' => 'NgayKy',
            'NgayCapBang' => 'NgayCapBang',
            'HeDaoTao' => 'HeDaoTao',
            'HinhThucDaoTao' => 'hinhThucDaoTao.tenHinhThuc',
            'ChucVu' => 'chucVu.tenChucVu',
            'NguoiKy' => 'NguoiKy',
            'CoQuanBanHanh' => 'CoQuanBanHanh',
        ];
    }

}