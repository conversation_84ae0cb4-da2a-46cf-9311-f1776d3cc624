<?php

namespace App\Models\CongThongTin;

use MongoDB\Laravel\Eloquent\Model;

class ThietLapWebsite extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'thiet_lap_website';

    protected $fillable = [
        'Ma',
        'TenDonVi',
        '<PERSON>a<PERSON><PERSON>',
        'SoDienThoai',
        'Fax',
        'Email',
        'TenPhanMem',
        'TenPhienBan',
        'Website',
        'Facebook',
        'Youtube',
        'Banner',
        'NgayTao',
        'NgayCapNhat',
        'UserID_NguoiTao',
        'UserID_CapNhat',
        'NguoiTao',
        'DonViID',
        'AnhDaiDienBaiViet',
        'BannerLoginMoi',
        'BannerLoginNho',
        'FaviconUrl',
        'LogoUrl',
        'FooterLogoUrl',
        'NoiDungChanTrang',
        'TieuDeWebsite',
        'ck_YeuCauKiemDuyet',
        'ck_QuanTriNgayTrenWebsite',
        'ck_KhongHoiKhiRoiTrangChinhSua',
        'ck_KhoaKhongChoCopyNoiDung',
        'AdminTimeOut',
        'ck_XacThucTaiKhoan',
        'ck_TamNgungTaiKhoan',
        'ck_TuDongDuyetBai',
        'ck_KiemDuyetBinhLuan',
        'ck_CoDinhKhungWebsite',
        'ck_KhoaBaiDang',
        'ck_CoNutLenDauTrang',
    ];

    protected $casts = [
        'NgayTao' => 'datetime',
        'NgayCapNhat' => 'datetime',
        'ck_YeuCauKiemDuyet' => 'boolean',
        'ck_TuDongDuyetBai' => 'boolean',
        'ck_QuanTriNgayTrenWebsite' => 'boolean',
        'ck_KhongHoiKhiRoiTrangChinh' => 'boolean',
        'ck_KhoaKhongChoCopyNoiDung' => 'boolean',
        'ck_XacThucTaiKhoan' => 'boolean',
        'ck_TamNgungTaiKhoan' => 'boolean',
        'ck_KiemDuyetBinhLuan' => 'boolean',
        'ck_CoDinhKhungWebsite' => 'boolean',
        'ck_KhoaBaiDang' => 'boolean',
        'ck_CoNutLenDauTrang' => 'boolean',
    ];

    public function scopeCurrent($query)
    {
        return $query->first();
    }
    public function scopeOfDonVi($query, $donViId)
    {
        return $query->where('DonViID', $donViId);
    }

    public function scopeMoiNhat($query)
    {
        return $query->orderByDesc('NgayCapNhat');
    }

    public function scopeCoFacebook($query)
    {
        return $query->whereNotNull('Facebook')->where('Facebook', '!=', '');
    }

    public function scopeCoYoutube($query)
    {
        return $query->whereNotNull('Youtube')->where('Youtube', '!=', '');
    }

    public function scopeHasWebsite($query)
    {
        return $query->whereNotNull('Website')->where('Website', '!=', '');
    }

    public function scopeSearchByTenDonVi($query, $keyword)
    {
        return $query->where('TenDonVi', 'like', '%' . $keyword . '%');
    }
    public function scopeCreatedBy($query, $userId)
    {
        return $query->where('UserID_NguoiTao', $userId);
    }
}
